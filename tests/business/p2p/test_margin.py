from datetime import timedelta
from pprint import pprint

import pytest
from flask import g
from flask_babel import force_locale

from app import Language
from app.common import list_country_codes_3
from app.models import User, P2pFairPrice, db, P2pMarginCountry, P2pMerchant, P2pUserMargin, P2pMarginHistory, P2pUserMarginHistory
from app.utils import today, now

USER_ID = 20073


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.lang = Language.ZH_HANS_CN.value
            g.auth_user = g.user = User.query.get(USER_ID)
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('module_setup')
class TestP2pMargin:

    def test_brush_margin_data(self, tcontext):
        with tcontext:
            for code in list_country_codes_3():
                row = P2pMarginCountry(
                    country_code=code,
                    amount=0,
                )
                db.session.add(row)
            db.session.commit()
            m_model = P2pMerchant
            m_model.query.update({P2pMerchant.margin_status: m_model.MarginStatus.VALID})
            db.session.commit()

            merchants = m_model.query.all()
            user_ids = [i.user_id for i in merchants]
            for user_id in user_ids:
                u_row = P2pUserMargin(
                    user_id=user_id,
                    grace_deadline=today() + timedelta(days=30),
                )
                db.session.add(u_row)
            db.session.commit()

    def test_merchant_detail(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            url = '/res/p2p/user/merchant'
            resp = client.get(url)
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_update_to_merchant(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            url = '/res/p2p/user/merchant'
            resp = client.post(url)
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_merchant_can_cancel(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            url = '/res/p2p/user/merchant/cancel'
            resp = client.get(url)
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_merchant_post_cancel(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            url = '/res/p2p/user/merchant/cancel'
            resp = client.post(url)
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_merchant_spot(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            url = '/res/p2p/user/merchant/spot'
            resp = client.put(url, json={
                "spot_status": "OPEN"
            })
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_merchant_margin_payment(self, tcontext):
        with tcontext:
            P2pMarginHistory.query.filter_by(from_id=USER_ID).delete()
            client = tcontext.app.test_client()
            url = '/res/p2p/user/merchant/margin/payment'
            data = {
                "amount": 100
            }
            resp = client.post(url, json=data)
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_get_admin_margin_country(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            url = '/admin/p2p/margin/country'
            data = dict(
                country_code='CHN',
            )
            resp = client.get(url, query_string=data)
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_put_admin_margin_country(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            url = '/admin/p2p/margin/country'
            data = dict(
                id=6,
                amount=150
            )
            resp = client.put(url, json=data)
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_get_admin_margin_history(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            url = '/admin/p2p/margin/history'
            now_ = now()
            data = dict(
                type=P2pMarginHistory.Type.PAYMENT.name,
                user_id=20073,
                start_at=(now_ - timedelta(days=30)).timestamp() * 1000,
                end_at=(now_ + timedelta(days=30)).timestamp() * 1000,
            )
            resp = client.get(url, query_string=data)
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_put_admin_margin_history(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            url = '/admin/p2p/margin/history'
            data = dict(
                id=13,
                remark='123'
            )
            resp = client.patch(url, json=data)
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_put_user_person_margin(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            url = f'/admin/p2p/margin/merchant/{USER_ID}'
            data = dict(
                margin_type=P2pUserMargin.MarginType.PERSON.name,
                require_margin=100
            )
            resp = client.put(url, json=data)
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_put_user_country_margin(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            url = f'/admin/p2p/margin/merchant/{USER_ID}'
            data = dict(
                margin_type=P2pUserMargin.MarginType.COUNTRY.name,
                require_margin=100
            )
            resp = client.patch(url, json=data)
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_get_user_country_margin(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            url = f'/admin/p2p/margin/user'
            resp = client.get(url)
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_update_grace_margin_status(self, tcontext):
        with tcontext:
            from app.schedules.p2p.margin import update_grace_margin_status
            update_grace_margin_status()

    def test_update_user_margin_status(self, tcontext):
        with tcontext:
            from app.schedules.p2p.margin import update_user_margin_status
            update_user_margin_status()

    def test_send_margin_payment_message(self, tcontext):
        with tcontext:
            from app.schedules.p2p.margin import send_margin_payment_message
            send_margin_payment_message()

    def test_send_message(self, tcontext):
        with tcontext:
            from app.business.p2p.message import P2pMarginShortfallMessage, P2pMarginPaymentMessage, \
                send_p2p_margin_change_message, P2pMerchantCancelMessage
            from app.utils import today, now
            from app import Language
            from app.business import UserPreferences

            # for lang in Language:
            #     UserPreferences(USER_ID).language = lang
                # P2pMarginPaymentMessage().send_message(USER_ID, now())
            P2pMarginShortfallMessage().send_message(USER_ID, now())
            # send_p2p_margin_change_message(USER_ID, amount=0, old_amount=100)
            # send_p2p_margin_change_message(USER_ID, amount=100, old_amount=10)
            P2pMerchantCancelMessage().send_message(USER_ID)

    def test_send_user_margin_message(self, tcontext):
        with tcontext:
            from app.business.p2p.message import P2pMerCompensationMessage, P2pMarginExcessRefundMessage, P2pUserCompensationMessage, P2pMarginPenaltyMessage
            from app.business import UserPreferences

            # USER_ID = 1679
            # for msg in [P2pMerCompensationMessage, P2pMarginExcessRefundMessage, P2pUserCompensationMessage, P2pMarginPenaltyMessage]:
            #     for lang in Language:
            #         UserPreferences(USER_ID).language = lang
            #         msg().send_message(USER_ID, 100)

            USER_ID = 1679
            for lang in Language:
                if lang == Language.PL_PL:
                    UserPreferences(USER_ID).language = lang
                    P2pMerCompensationMessage().send_message(USER_ID, 100)
                    P2pMarginExcessRefundMessage().send_message(USER_ID, 100)
                    P2pUserCompensationMessage().send_message(USER_ID, 100)
                    P2pMarginPenaltyMessage().send_message(USER_ID, 100)

    def test_fixup_margin_payment_process(self, tcontext):
        with tcontext:
            from app.schedules.p2p.margin import fixup_margin_trans_process
            fixup_margin_trans_process()

    def test_fixup_margin_refund_process(self, tcontext):
        with tcontext:
            from app.business.p2p.margin import P2pUserMarginHistoryBiz
            P2pUserMarginHistoryBiz.fixup_margin_trans_process()

    def test_update_kyc_country_require_margin(self, tcontext):
        with tcontext:
            from app.schedules.p2p.margin import update_kyc_country_require_margin
            update_kyc_country_require_margin()

    def test_merchant_canceling_refund(self, tcontext):
        with tcontext:
            from app.schedules.p2p.margin import merchant_canceling_refund
            merchant_canceling_refund()

    def test_margin_balance_check(self, tcontext):
        with tcontext:
            from app.schedules.p2p.margin import margin_balance_check_task
            margin_balance_check_task()

    def test_user_margin_negative_check_task(self, tcontext):
        with tcontext:
            from app.schedules.p2p.margin import user_margin_negative_check_task
            user_margin_negative_check_task()

    def test_group_p2p_margin(self, tcontext):
        with tcontext:
            from app.business.account_pl import group_p2p_margin
            start = now() - timedelta(days=180)
            end = now()
            group_p2p_margin(start, end)

    def test_get_admin_user_margin_history(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            url = '/admin/p2p/margin/user_history'
            now_ = now()
            data = dict(
                user_id=USER_ID,
                start_at=(now_ - timedelta(days=365)).timestamp() * 1000,
                end_at=now_.timestamp() * 1000,
                page=1,
                limit=10
            )
            resp = client.get(url, query_string=data)
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_post_mer_excess_refund(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            url = '/admin/p2p/margin/user_history'
            data = dict(
                biz_type=P2pUserMarginHistory.BizType.EXCESS_REFUND.name,
                user_id=USER_ID,
                amount=100,
                biz_remark="测试超额返还"
            )
            resp = client.post(url, json=data)
            pprint(resp.json)
            assert resp.json["code"] == 0
    
    def test_post_mer_compensation(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            url = '/admin/p2p/margin/user_history'
            order_id = "2024052141961657"
            data = dict(
                biz_type=P2pUserMarginHistory.BizType.MER_COMPENSATION.name,
                user_id=USER_ID,
                order_id=order_id,
                amount=100,
                biz_remark="测试赔付"
            )
            resp = client.post(url, json=data)
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_patch_margin_history(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            url = '/admin/p2p/margin/user_history'
            data = dict(
                id=107,
                remark="测试赔付备注"
            )
            resp = client.patch(url, json=data)
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_audit_history(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            his_id = 107
            # 测试审核接口
            url = f'/admin/p2p/margin/audit_history/{his_id}'
            resp = client.patch(url)
            pprint(resp.json)
            assert resp.json["code"] == 0
            
            # 验证记录状态已更新
            updated_history = P2pUserMarginHistory.query.get(his_id)
            assert updated_history.status == P2pUserMarginHistory.Status.PROCESS
