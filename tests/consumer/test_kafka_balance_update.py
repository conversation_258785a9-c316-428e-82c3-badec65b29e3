#!/usr/bin/python
# -*- coding: utf-8 -*-
from decimal import Decimal
import pytest

from flask import current_app, g
from undecorated import undecorated
from tests.common.mock_mysql import patch_mysql
from tests.common.mock_redis import patch_redis

from app.models import db
from app.models.asset_cost import AssetCost
from app.models.user import SubAccountAssetTransfer, User
from app.common.constants import AccountBalanceType
from app.utils.amount import quantize_amount
from tests.common.t_common import default_lang

USER_ID = ********
SUB_USER_ID = ********

MARGIN_ACCOUNT_ID = 2 # BTCUSDT


import time

def retry(max_attempts=10, delay=1):
    """
    一个装饰器，如果被装饰的函数返回 False，则重新执行函数，
    返回 True 则结束。

    Args:
        max_attempts (int): 最大重试次数。默认为 3。
        delay (int/float): 每次重试之间的延迟（秒）。默认为 1。
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            attempts = 0
            while attempts < max_attempts:
                result = func(*args, **kwargs)
                if result:
                    return result  # 函数返回有值，成功，结束
                else:
                    attempts += 1
                    current_app.logger.info(f"函数 '{func.__name__}' 返回 False，正在重试... (尝试 {attempts}/{max_attempts})")
                    if attempts < max_attempts:
                        time.sleep(delay)
            current_app.logger.info(f"函数 '{func.__name__}' 在 {max_attempts} 次尝试后仍未成功。")
            return False  # 达到最大重试次数仍未成功
        return wrapper
    return decorator

@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        # test_users = []
        with tcontext:
            g.lang = default_lang
            g.auth_user = g.user = User.query.get(USER_ID)
        yield tcontext
    finally:
        with tcontext:
            pass

def _get_price_amount(user_id, account_type, asset, allow_none=True):
    record = AssetCost.query.filter(
        AssetCost.user_id == user_id,
        AssetCost.account_type == account_type,
        AssetCost.asset == asset
    ).first()
    if not record:
        if allow_none:
            return Decimal(), Decimal()
        raise ValueError(f"AssetCost record not found for user_id: {user_id}, account_type: {account_type}, asset: {asset}")
    return record.price, record.amount

@retry()
def monitor_cost_update(user_id, account_type, asset, amount):
    db.session.rollback()
    _, new_amount = _get_price_amount(user_id, account_type, asset, allow_none=False)
    if new_amount == amount:
        return False
    return True

def cal_price(added_amount, added_price, current_amount, current_price):
    """
    计算新的平均价格
    :param added_amount: 新增的数量
    :param added_price: 新增的价格
    :param current_amount: 当前账户的数量
    :param current_price: 当前账户的价格
    :return: 新的平均价格
    """
    current_app.logger.warning(f"cal_price: added_amount={added_amount}, "
                               f"added_price={added_price}, "
                               f"current_amount={current_amount}, "
                               f"current_price={current_price}")
    if added_amount <= 0:
        raise ValueError("Added amount must be greater than 0")

    if current_amount + added_amount == 0:
        return Decimal()
    price = (current_price * current_amount + added_price * added_amount) / (current_amount + added_amount)
    return quantize_amount(price, 8)

# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
class TestBalanceUpdate:

    @classmethod
    def _check_account_transfer(cls, source_account_type: AccountBalanceType, 
                   target_account_type: AccountBalanceType, 
                   amount: Decimal,
                   asset: str,
                   business_func, *args, **kwargs):
            user_id = g.user.id
            source_price, source_amount = _get_price_amount(user_id, source_account_type, asset, allow_none=False)
            target_price, target_amount = _get_price_amount(user_id, target_account_type, asset)

            business_func(*args, **kwargs)

            monitor_cost_update(user_id, source_account_type, asset, source_amount)

            time.sleep(5)
            db.session.rollback()
            new_source_price, new_source_amount = _get_price_amount(user_id, source_account_type, asset, allow_none=False)
            new_target_price, new_target_amount = _get_price_amount(user_id, target_account_type, asset)

            assert new_source_amount == source_amount - amount, f"Expected source amount {source_amount - amount}, got {new_source_amount}"
            assert new_target_amount == target_amount + amount, f"Expected target amount {target_amount + amount}, got {new_target_amount}"

            expected_target_price = cal_price(amount, source_price, target_amount, target_price)
            assert new_target_price == expected_target_price, f"Expected target price {expected_target_price}, got {new_target_price}"
    
    @classmethod
    def _check_user_account_transfer(cls, from_user_id, to_user_id, from_account_type: AccountBalanceType,
                                     to_account_type: AccountBalanceType,
                                     transfer_amount: Decimal, tranfer_asset: str, business_func, *args, **kwargs):
            source_price, source_amount = _get_price_amount(from_user_id, from_account_type, tranfer_asset, allow_none=False)
            target_price, target_amount = _get_price_amount(to_user_id, to_account_type, tranfer_asset)

            business_func(*args, **kwargs)

            monitor_cost_update(from_user_id, from_account_type, tranfer_asset, source_amount)
            time.sleep(5)
            db.session.rollback()

            new_source_price, new_source_amount = _get_price_amount(from_user_id, from_account_type, tranfer_asset, allow_none=False)
            new_target_price, new_target_amount = _get_price_amount(to_user_id, to_account_type, tranfer_asset)

            assert new_source_amount == source_amount - transfer_amount, f"Expected source amount {source_amount - transfer_amount}, got {new_source_amount}"
            assert new_target_amount == target_amount + transfer_amount, f"Expected target amount {target_amount + transfer_amount}, got {new_target_amount}"

            expected_target_price = cal_price(transfer_amount, source_price, target_amount, target_price)
            assert new_target_price == expected_target_price, f"Expected target price {expected_target_price}, got {new_target_price}"


    def test_handle_price_for_transfer_with_price_records_perp_transfer(self, tcontext):
        """测试合约划转业务"""

        from app.business.perpetual.balance import perpetual_transfer_in, perpetual_transfer_out

        with tcontext:
            user_id = g.user.id
            asset, amount = "ETH", Decimal("0.005")
            current_app.logger.info("SPOT -> PERPETUAL")
            self._check_account_transfer(AccountBalanceType.SPOT, AccountBalanceType.PERPETUAL, amount, asset, perpetual_transfer_in, user_id, asset, amount)

            current_app.logger.info("PERPETUAL -> SPOT")
            self._check_account_transfer(AccountBalanceType.PERPETUAL, AccountBalanceType.SPOT, Decimal("0.05"), asset, perpetual_transfer_out, user_id, asset, Decimal("0.05"))

    
    def test_handle_price_for_transfer_with_price_records_margin_transfer(self, tcontext):
        """测试杠杆划转业务"""

        from app.business.margin.transfer import MarginTransferOperation
        from app.business import SPOT_ACCOUNT_ID

        with tcontext:
            user_id = g.user.id
            current_app.logger.info("SPOT -> MARGIN")
            asset, amount = "BTC", Decimal("0.01")
            op = MarginTransferOperation(user_id, SPOT_ACCOUNT_ID, MARGIN_ACCOUNT_ID, asset, amount)
            self._check_account_transfer(AccountBalanceType.SPOT, AccountBalanceType.MARGIN, amount, asset, op.transfer)

            current_app.logger.info("MARGIN -> SPOT")
            op = MarginTransferOperation(user_id, MARGIN_ACCOUNT_ID, SPOT_ACCOUNT_ID, asset, Decimal("0.003"))
            self._check_account_transfer(AccountBalanceType.MARGIN, AccountBalanceType.SPOT, Decimal("0.003"), asset, op.transfer)

    
    def test_handle_price_for_transfer_with_price_records_sub_transfer(self, tcontext):
        """测试子账号划转业务"""

        from app.business.sub_account import SubAccountManager

        with tcontext:
            user_id = g.user.id
            current_app.logger.info("SPOT -> Sub Account Spot") 
            asset, amount = "ETH", Decimal("0.01")
            self._check_user_account_transfer(user_id, SUB_USER_ID, AccountBalanceType.SPOT, AccountBalanceType.SPOT, amount, asset,
                                              SubAccountManager.transfer_asset,
                                              main_user_id=user_id,
                                              source_user_id=user_id,
                                              source_account_type=SubAccountAssetTransfer.AccountType.SPOT,
                                              target_user_id=SUB_USER_ID,
                                              target_account_type=SubAccountAssetTransfer.AccountType.SPOT,
                                              asset=asset, 
                                              amount=amount)

            current_app.logger.info("SPOT -> Sub Account Perpetual")
            self._check_user_account_transfer(user_id, SUB_USER_ID, AccountBalanceType.SPOT, AccountBalanceType.PERPETUAL, amount, asset,
                                        SubAccountManager.transfer_asset,
                                        main_user_id=user_id,
                                        source_user_id=user_id,
                                        source_account_type=SubAccountAssetTransfer.AccountType.SPOT,
                                        target_user_id=SUB_USER_ID,
                                        target_account_type=SubAccountAssetTransfer.AccountType.PERPETUAL,
                                        asset=asset, amount=amount)
    
    def test_handle_price_for_transfer_with_price_records_copy_transfer(self, tcontext):
        """测试带价划转业务"""

        from app.api.frontend.copy_trading import FollowerSettingsResource

        with tcontext:
            user_id = g.user.id
            current_app.logger.info("SPOT -> Copy Trading")
            asset, amount = "USDT", Decimal("22")
            _, source_amount = _get_price_amount(user_id, AccountBalanceType.SPOT, asset, allow_none=False)
            undecorated(FollowerSettingsResource.put)(FollowerSettingsResource, trader_id="AFF3EED4", add_margin_amount=amount)
            monitor_cost_update(user_id, AccountBalanceType.SPOT, asset, source_amount)
            _, new_source_amount = _get_price_amount(user_id, AccountBalanceType.SPOT, asset, allow_none=False)
            assert new_source_amount == source_amount - amount, f"Expected source amount {source_amount - amount}, got {new_source_amount}"



    
