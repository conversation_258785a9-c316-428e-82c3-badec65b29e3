import sys
from app import create_app


def main():
    from app.consumer.kafka_balance_update import run_balance_update_kafka_consumer, run_spot_deals_kafka_consumer

    type_ = ""
    if len(sys.argv) == 2:
        type_ = sys.argv[1]
    if type_ == "spot_deals":
        run_spot_deals_kafka_consumer()
    elif type_ == "balance_update":
        run_balance_update_kafka_consumer()
    else:
        print("Usage: python run_balance_cost.py [spot_deals|balance_update]")


if __name__ == '__main__':

    app = create_app()
    with app.app_context():
        main()
