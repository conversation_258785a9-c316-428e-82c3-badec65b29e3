from app.api.frontend.wallet import *

def do_withdrawal(user: User,
                    asset: str,
                    chain: Optional[str],
                    address: str,
                    memo: str,
                    amount: Decimal,
                    fee: Decimal,
                    remark: str):
    w_type = Withdrawal.Type.ON_CHAIN
    extra = {}
    fee_asset = asset
    recipient_id = None
    with CacheLock(LockKeys.user_withdrawal(user.id)):
        db.session.rollback()
        withdrawable = get_user_withdrawable_amount(user.id, asset)
        if fee_asset == asset:
            if amount + fee > withdrawable['withdrawable_asset']:
                raise InsufficientBalance
        else:
            balances = ServerClient().get_user_balances(user.id, asset=fee_asset)
            fee_asset_avai = balances.get(fee_asset, {}).get('available', Decimal())
            if fee > fee_asset_avai:
                raise InsufficientBalance

        withdrawal = Withdrawal(
            user_id=user.id,
            type=w_type,
            chain=chain,
            asset=asset,
            address=address,
            amount=amount,
            fee=fee,
            fee_asset=fee_asset,
            memo=memo,
            attachment=AddressExtraConfigs.dump_extra(extra),
            recipient_user_id=recipient_id,
            remark=remark
        )
        db.session.add(withdrawal)
        db.session.flush()
        db.session.commit()
        return withdrawal


def confirm_withdrawal(withdrawal):
    if withdrawal is None or withdrawal.status != Withdrawal.Status.CREATED:
        raise InvalidLink
    user_id = withdrawal.user_id
    withdrawal_id = withdrawal.id

    with CacheLock(LockKeys.user_withdrawal(user_id)), \
            CacheLock(LockKeys.withdrawal(withdrawal.id)):
        db.session.rollback()
        withdrawal = Withdrawal.query.get(withdrawal_id)
        if withdrawal.status != Withdrawal.Status.CREATED:
            raise InvalidLink

        asset = withdrawal.asset
        amount = withdrawal.amount
        fee_asset = withdrawal.fee_asset
        fee = withdrawal.fee

        server_client = ServerClient()
        try:
            withdrawable = get_user_withdrawable_amount(user_id, asset)
            if asset == fee_asset:
                if amount + fee > withdrawable['withdrawable_asset']:
                    raise InsufficientBalance
            else:
                balances = server_client.get_user_balances(user_id, asset=fee_asset)
                fee_asset_avai = balances.get(fee_asset, {}).get('available', Decimal())
                if fee > fee_asset_avai:
                    raise InsufficientBalance

            limit = get_user_daily_withdrawal_limit(user_id)
            if limit['withdrawn_usd'] >= (limit['limit_usd']) * Decimal("1.01"):
                raise WithdrawalLimitExceeded

        except Exception:
            withdrawal.status = Withdrawal.Status.CANCELLED
            db.session.commit()
            raise

        try:
            if fee > 0:
                server_client.add_user_balance(
                    user_id, fee_asset, -fee, BalanceBusiness.WITHDRAWAL_FEE,
                    withdrawal_id)
            server_client.add_user_balance(
                user_id, asset, -amount, BalanceBusiness.WITHDRAWAL,
                withdrawal_id)
        except server_client.BadResponse as e:
            if e.code == server_client.ResponseCode.INSUFFICIENT_BALANCE:
                withdrawal.status = Withdrawal.Status.CANCELLED
                db.session.commit()
                raise InsufficientBalance
            raise ServiceUnavailable

        withdrawal.approved_by_user_at = now()
        withdrawal.status = Withdrawal.Status.AUDITED # 直接已审核
        db.session.add(WithdrawalFeeHistory(
            withdrawal_id=withdrawal.id,
            user_id=user_id,
            asset=fee_asset,
            amount=fee,
            fee_type=WithdrawalFeeHistory.FeeType.FEE
        ))
        db.session.commit()

    return {}


def new_withdrawal(user_id, asset, chain, address, amount, memo, remark):
    ac_conf = get_asset_chain_config(asset, chain)
    fee = ac_conf.withdrawal_fee
    user = User.query.get(user_id)
    if user.is_sub_account:
        raise ValueError
    if amount is None:
        balances = ServerClient().get_user_balances(user.id, asset=asset)
        amount = balances[asset]['available']
        amount -= fee
    amount = quantize_amount(amount, ac_conf.withdrawal_precision)
    if amount <= 0:
        raise ValueError
    print('new withdrawal', f'{asset}-{chain}', amount, 'fee', fee)
    w = do_withdrawal(user, asset, chain, address, memo, amount, fee, remark)
    print(w.id)
    confirm_withdrawal(w)

data = """
1521908,USDT-TRC20： TQpBGoRUAp4aMFJGNAiB2G3J8tz65ZBmYU
1593374,USDT-TRC20：TQFuCynXS157SiPiARDUkWrbJEi7EsrUNt
1538912,USDT-TRC20：TFyrARXbCEcLTFkEMKUUwMyPzX3UNF2rC9
1431460,USDT-TRC20： TEsGvj4yG3wyDYTaJPGqy73sQMuPWHe11U
1084639,USDT-TRC20：TT7WsMGJfqCCaZXt9vaJfa5HbSjNTZSKQb
2637705,USDT-TRC20：TPgQPLM2wbVSXgGUM8qCRx3WSmVvWgDNDe
1747459,USDT-TRC20：TYc48bBcAnsVoZbMeH6Y6urQy2wLmk2Bym
1223490,USDT-ERC20：******************************************
4635858,USDT-ERC20：******************************************
1762239,ETH-ERC20：******************************************
"""

for line in data.split("\n"):
    line = line.strip()
    if not line:
        continue
    user_id, info = line.split(",")
    user_id = int(user_id)
    chain, address = info.split('：')
    address = address.strip()
    asset, chain = chain.split('-')
    amount = None
    memo = ""
    remark = "withdrawal of newyork clean"
    new_withdrawal(user_id, asset, chain, address, amount, memo, remark)
