import time
from decimal import Decimal

from flask import current_app

from app.models import BalanceUpdateBusiness
from app.business import PriceManager, ServerClient
from app.caches import MarketCache
from app.common import OrderSideType, OrderOption
from app.business.order import Order
from app.utils import quantize_amount, amount_to_str

order_ids = []

def sell_asset(user_id, asset, asset_balance):
    market_list = MarketCache.list_online_markets()
    market = f'{asset}USDT'
    if market not in market_list:
        current_app.logger.error(f'{market} not exists')
        return False
    
    market_cache = MarketCache(market).dict

    order_price = ServerClient().market_last(market) * Decimal('0.99')
    order_price = quantize_amount(order_price, market_cache["quote_asset_precision"])

    order_amount = asset_balance
    order_amount = min(order_amount, asset_balance)
    order_amount = quantize_amount(order_amount, market_cache["base_asset_precision"])
    # 卖单
    rs = ServerClient().put_limit_order(
            user_id=user_id,
            market=market,
            side=OrderSideType.SELL,
            amount=amount_to_str(order_amount),
            price=amount_to_str(order_price),
            taker_fee_rate='0',
            maker_fee_rate='0',
            source=Order.OrderSourceType.SYSTEM.value,
            fee_asset=None,
            fee_discount='1',
            account_id=0,
            option=OrderOption.WITHOUT_ORDER_MIN_AMOUNT,
        )
    order_ids.append(rs['id'])
    current_app.logger.info(f'place sell order {order_amount} {asset} at price {order_price}')
    return True


def loop_sell_asset(user_id, assets):
    prices = PriceManager.assets_to_usd()
    while True:
        ServerClient().cancel_user_all_order(user_id, -1, None)

        balances = ServerClient().get_user_balances(user_id)
        placed_order = False
        for asset, bs in balances.items():
            if asset == 'USDT' or asset not in assets:
                continue
            asset_balance = bs['available']

            if prices.get(asset, 0) * asset_balance <= 0.1:
                continue

            if not sell_asset(user_id, asset, asset_balance):
                continue
            placed_order = True

        if not placed_order:
            break
        time.sleep(20)
    
    sum_income(user_id, order_ids)


def sum_income(user_id, orders):
    total = 0
    client = ServerClient()
    for order_id in orders:
        order = client.finished_order_detail(user_id, order_id)
        if not order:
            continue
        total += Decimal(order['deal_money'])
    print(total)


def transfer_assets(from_user, to_user, assets):
    c = ServerClient()
    balances = c.get_user_balances(from_user)
    for asset, amount in assets.items():
        if balances[asset]['available'] < amount:
            # print(f"{asset} balance not enough")
            assets[asset] = balances[asset]['available']
            # return

    for asset, amount in assets.items():
        print(f"transfer {amount} {asset}")
        bid = BalanceUpdateBusiness.new_id(from_user, asset, -amount)
        c.add_user_balance(from_user, asset, -amount, 'system', bid)
        bid = BalanceUpdateBusiness.new_id(to_user, asset, amount)
        c.add_user_balance(to_user, asset, amount, 'system', bid)

def transfer_back(from_user, to_user, assets):
    c = ServerClient()
    balances = c.get_user_balances(from_user)
    for asset in assets:
        amount = balances[asset]['available']
        print(f"transfer {amount} {asset}")
        bid = BalanceUpdateBusiness.new_id(from_user, asset, -amount)
        c.add_user_balance(from_user, asset, -amount, 'system', bid)
        bid = BalanceUpdateBusiness.new_id(to_user, asset, amount)
        c.add_user_balance(to_user, asset, amount, 'system', bid)

data = """
"""

assets = {}
for line in data.splitlines():
    if not (line := line.strip()):
        continue
    asset, amount = line.split(',')
    assets[asset] = Decimal(amount)

# <EMAIL> to subaccount.
transfer_assets(515390, 6581938, assets)
loop_sell_asset(6581938, list(assets.keys()))
transfer_back(6581938, 515390, ['USDT'])
