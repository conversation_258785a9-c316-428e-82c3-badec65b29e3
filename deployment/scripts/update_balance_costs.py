from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
import json
import os
import sys
from decimal import Decimal
from typing import Any, Dict, Iterable, List, Tuple

from flask import current_app



abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

# 只处理资产市值(USD)大于这个值的用户
USER_VALUE_THRESHOLD = Decimal("100")

# 刷数据起始时间
START_DAY_COUNT = 365

# 每次处理的记录数
RECORD_BATCH = 5000000

class System(Enum):
    SPOT = 'spot'
    PERPETUAL = 'perpetual'

@dataclass
class BalanceUpdateRecord:
    user_id: int
    account_id: int
    asset: str
    business: str
    price: Decimal
    amount: Decimal
    balance: Decimal
    timestamp: int
    system: System
    detail: Dict[str, Any] | None = None


def get_account_type(account_id):
    from app.common.constants import AccountBalanceType
    from app.consumer.kafka_balance_update import MAX_ORDER_ACCOUNT_ID, SPOT_ACCOUNT_ID, InvestmentAccount
    from app.models.staking import StakingAccount

    if account_id == SPOT_ACCOUNT_ID:
        return AccountBalanceType.SPOT
    if SPOT_ACCOUNT_ID < account_id < MAX_ORDER_ACCOUNT_ID:
        return AccountBalanceType.MARGIN
    if account_id in (StakingAccount.ACCOUNT_ID, InvestmentAccount.ACCOUNT_ID):
        return AccountBalanceType.INVESTMENT
    if InvestmentAccount.ACCOUNT_ID < account_id < StakingAccount.ACCOUNT_ID: 
        return AccountBalanceType.PLEDGE
    raise ValueError(f"No account type found!. {account_id}")


def get_balance_history_id_info(system: System, db_idx: int, table_idx: int, ts: int):
    if system == System.SPOT:
        db_class = TradeHistoryDB
    else:
        db_class = PerpetualHistoryDB

    _BATCH = 100000

    ts -= ts % 86400
    _db, table_name = db_class.DBS[db_idx], f'balance_history_{table_idx}'
    flag = False
    last_id = 0
    fields = ['id', 'time']
    table = _db.table(table_name)
    min_id = 0
    min_time = max_time = 0
    while not flag:
        if last_id:
            where_ = f'id < {last_id}'
        else:
            where_ = None
        records = table.select(
                    *fields,
                    where=where_,
                    limit=_BATCH,
                    force_index='PRI',
                    order_by='id DESC'
                )
        if not records:
            break
        last_id = records[-1][0]
        for r in records:
            id_, time = r
            if time < ts:
                min_id = id_
                min_time = time
                flag = True
                break
    
    max_record = table.select('id', 'time', order_by='id DESC', limit=1)[0]
    max_id, max_time = max_record
    current_app.logger.warning(f"get_balance_history_id_info {system.name} db and table: {db_idx} {table_idx} "
                               f"{ts} {min_id} min time and id: {min_time} {max_id} "
                               f"max time and id: {max_time} {max_id}")
    return dict(
        min_id=min_id,
        max_id=max_id,
        min_time=min_time,
        max_time=max_time,
    )
    

# get_user_amm_assets
# get_amm_assets_slice

def _get_balance_history(db_idx: int, 
                         table_idx: int, 
                         user_ids: Iterable[int], 
                         spot_start_id: int, spot_end_id: int,
                         perpetual_start_id: int, perpetual_end_id: int,
                         price_range_map: Dict[datetime, Dict[str, Decimal]]) -> List[BalanceUpdateRecord]:

    from app.business.external_dbs import PerpetualHistoryDB, TradeHistoryDB
    from app.common.constants import BalanceBusiness
    from app.models.amm import LiquidityHistory
    from app.utils.date_ import timestamp_to_datetime
    from app.utils.iterable import batch_iter


    AMM_BUSINESS_TYPES = (
        BalanceBusiness.ADD_LIQUIDITY,
        BalanceBusiness.REMOVE_LIQUIDITY
    )
    all_business_type_values = {item.value for item in BalanceBusiness}

    def _get_spot_balance_history(db_idx: int, table_idx: int, user_ids: Iterable[int], start_id: int, end_id: int):        
        fields = ['id', 'time', 'user_id', 'account', 'asset', 'business', 'change', 'balance', 'detail']

        db_, table_name = TradeHistoryDB.DBS[db_idx], f'balance_history_{table_idx}'
        table = db_.table(table_name)
        
        user_id_str = ','.join(user_ids)
        where = f'user_id in ({user_id_str}) '
        where += f' AND id >= {start_id} AND id < {end_id}'
        records = table.select(
            *fields,
            where=where,
            force_index='PRI'
        )
        return list(dict(zip(fields, record)) for record in records)


    def _get_perpetual_balance_history(db_idx: int, table_idx: int, user_ids: Iterable[int], start_id: int, end_id: int) -> List[BalanceUpdateRecord]:
        fields = ['id', 'time', 'user_id', 'asset', 'business', 'change', 'balance']
        db_, table_name = PerpetualHistoryDB.DBS[db_idx], f'balance_history_{table_idx}'
        table = db_.table(table_name)
        
        user_id_str = ','.join(user_ids)
        where = f'user_id in ({user_id_str}) '
        where += f' AND id >= {start_id} AND id < {end_id}'
        records = table.select(
            *fields,
            where=where,
            force_index='PRI'
        )
        records = list(dict(zip(fields, record)) for record in records)
        result = []
        for item in records:
            if item['business'] not in all_business_type_values:
                continue

            ts = int(item['time'])
            dt = timestamp_to_datetime(ts).date()
            price = price_range_map.get(dt, {}).get(item['asset'], 0)
            business = BalanceBusiness(item['business'])

            detail = item.get('detail', {})
            detail['system'] = 'perpetual'
            record = BalanceUpdateRecord(
                user_id=item['user_id'],
                asset=item['asset'],
                business=business,
                price=price,
                amount=Decimal(item['change']),
                balance=Decimal(item['balance']),
                timestamp=item['time'],
                system=System.PERPETUAL,
                detail=detail
            )
            result.append(record)
        return result

    def _process_spot_balance_history(records) -> List[BalanceUpdateRecord]:
        if not records:
            return []
        # 处理amm
        amm_businesses = {item.value for item in AMM_BUSINESS_TYPES}
        business_id_map = dict() # id -> business_id
        business_id_reverse_map = dict() # business_id -> id
        
        amm_records = []
        for item in records:
            if item['business'] in amm_businesses:
                detail = item['detail']
                try:
                    detail = json.loads(detail)
                except:
                    detail = {}
                id_ = detail.get('id')
                if id_:
                    business_id_map[item['id']] = id_
                    business_id_reverse_map[id_] = item['id']
                
                amm_records.append(item)
        business_ids = set(business_id_map.values())

        business_id_user_map = dict()
        for ids in batch_iter(business_ids, 1000):
            tmp = LiquidityHistory.query.filter(
                LiquidityHistory.id.in_(ids)
            ).with_entities(
                LiquidityHistory.id,
                LiquidityHistory.user_id,
            ).all()
            business_id_user_map.update(dict(tmp))
        
        for item in amm_records:
            id_ = business_id_map.get(item['id'])
            user_id = business_id_user_map.get(id_)
            if not user_id:
                continue
            item['user_id'] = user_id
        
        records = [item for item in records if item['id'] not in business_id_reverse_map]
        records.extend(amm_records)
        result = []
        for item in records:
            if item['business'] not in all_business_type_values:
                continue
            business = BalanceBusiness(item['business'])
            record = BalanceUpdateRecord(
                user_id=item['user_id'],
                account_id=item['account'],
                asset=item['asset'],
                business=business,
                price=Decimal(item['price']),
                amount=Decimal(item['change']),
                balance=Decimal(item['balance']),
                timestamp=item['time'],
                system=System.SPOT
            )
            result.append(record)
        return result

    
    spot_records, perpetual_records = [], []
    if spot_start_id <= spot_end_id:
        spot_records = _get_spot_balance_history(db_idx, table_idx, user_ids, spot_start_id, spot_end_id)
        spot_records = _process_spot_balance_history(spot_records)
    if perpetual_start_id < perpetual_end_id:
        perpetual_records = _get_perpetual_balance_history(db_idx, table_idx, user_ids, perpetual_start_id, perpetual_end_id)
    records = spot_records + perpetual_records
    records.sort(key=lambda x: x.timestamp)
    return records


def update_price(records: List[BalanceUpdateRecord], 
                 result_map: Dict[Tuple, Dict], 
                 price_range_map: Dict[datetime, Dict[str, Decimal]]) -> None:
    
    from app.common.constants import AccountBalanceType
    from app.consumer.kafka_balance_update import ACCOUNT_TRANSFER_BUSINESS_TYPE_MAP, MARKET_PRICE_BUSINESS_TYPES, NO_PRICE_UPDATE_BUSINESS_TYPES, SUB_ACCOUNT_BUSINESS_TYPES, TRANSFER_WITH_PRICE_BUSINESS_TYPES, ZERO_COST_BUSINESS_TYPES, BalanceUpdateHandler
    from app.utils.date_ import timestamp_to_datetime

    def _handle_price_for_zero_cost_record(record) -> None:
        record.price = Decimal()
    
    def _handle_price_for_market_price_record(record) -> None:
        d = timestamp_to_datetime(record.timestamp).date()
        price_map = price_range_map.get(d)
        if not price_map:
            return
        record.price = price_map.get(record.asset, Decimal())
    
    def _handle_price_for_transfer_with_price_record(item) -> None:
        """
        带价划转
        """
        item: BalanceUpdateRecord
        d = timestamp_to_datetime(item.timestamp).date()
        
        price_map = price_range_map.get(d)
        if not price_map:
            return
        business = item.business
        account_type = BalanceUpdateHandler._get_account_type(item)

        # 如果是划转
        if type_:= ACCOUNT_TRANSFER_BUSINESS_TYPE_MAP.get(business):
            if account_type == type_:
                other_account_type = AccountBalanceType.SPOT
            else:
                other_account_type = type_
            other_result = result_map.get(item.user_id, {}).get((other_account_type, item.asset), {})
        
        # 如果是子账号划转，直接取当时的价格
        elif business in SUB_ACCOUNT_BUSINESS_TYPES:
            other_result = {}
        price = other_result.get('price', Decimal())

        if not price:
            price = price_map.get(item.asset, Decimal())
            current_app.logger.error(
                f"get price for transfer with price record failed, user: {item.user_id}, "
                f"business: {business}, account: {account_type}, asset: {item.asset} using price: {price}"
            )
        item.price = price


    def _handle_price_for_no_price_update_record(record) -> None:
        # 对于不需要更新价格的业务类型，不做任何处理
        pass

    for item in records:
        item: BalanceUpdateRecord

        d = timestamp_to_datetime(item.timestamp).date()
        price = price_range_map.get(d, {}).get(item.asset, Decimal())

        account_type = BalanceUpdateHandler._get_account_type(item)
        if not result_map['user_id'][account_type, item.asset]['price']:
            result_map['user_id'][account_type, item.asset]['price'] = price
            result_map['user_id'][account_type, item.asset]['amount'] = item.balance

        if item.business in ZERO_COST_BUSINESS_TYPES:
            _handle_price_for_zero_cost_record(item)
        elif item.business in MARKET_PRICE_BUSINESS_TYPES:
            _handle_price_for_market_price_record(item)
        elif item.business in TRANSFER_WITH_PRICE_BUSINESS_TYPES:
            _handle_price_for_transfer_with_price_record(item)
        elif item.business in NO_PRICE_UPDATE_BUSINESS_TYPES:
            _handle_price_for_no_price_update_record(item)
        else:
            pass


def update_record(result_map):
    from app.models.asset_cost import AssetCost
    from app.models.base import db
    from app.utils.iterable import batch_iter

    all_user_ids = list(result_map)
    record_map = dict()
    for ids in batch_iter(all_user_ids, 5000):
        records = AssetCost.query.filter(
            AssetCost.user_id.in_(ids)
        ).all()

        for record in records:
            record: AssetCost
            record_map[(record.user_id, record.account_type, record.asset)] = record
        commit_records = []
        for user_id in ids:
            for (account_type, asset), data in result_map[user_id].items():

                record = record_map.get((user_id, account_type, asset))
                if record:
                    record.price = data['price']
                else:
                    commit_records.append(AssetCost(
                        user_id=user_id,
                        account_type=account_type,
                        asset=asset,
                        price=data['price'],
                        amount=data['amount']
                    ))
            db.session.bulk_save_objects(commit_records)
        db.session.commit()


def initiate_data(ts, price_map, min_user_id=1) -> List[int]:

    from app.business.clients.server import SPOT_ACCOUNT_ID
    from app.business.external_dbs import PerpetualLogDB, TradeLogDB
    from app.common.constants import AccountBalanceType
    from app.models.asset_cost import AssetCost
    from app.models.base import db
    from app.utils.amount import quantize_amount
    from app.utils.iterable import batch_iter

    spot_balances = TradeLogDB.get_user_balances(ts)
    perpetual_balances = PerpetualLogDB.get_user_balances(ts)
    user_value_map = defaultdict(Decimal)
    user_asset_map = defaultdict(set)
    for item in spot_balances:
        user_id, asset, account_id, amount = item
        user_value_map[user_id] += quantize_amount(amount * price_map.get(asset), 2)
        if account_id == SPOT_ACCOUNT_ID:
            user_asset_map[user_id].add(asset)
    value_user_ids = {user_id for user_id, value in user_value_map.items() if value >= USER_VALUE_THRESHOLD}

    res = defaultdict(lambda: defaultdict(lambda: {'price': Decimal(), 'amount': Decimal()}))
    for item in spot_balances:
        user_id, asset, account_id, amount = item

        if user_id not in value_user_ids:
            continue
        account_type = get_account_type(account_id)
        res[user_id][(account_type, asset)]['amount'] += amount
        res[user_id][(account_type, asset)]['price'] = price_map.get(asset, Decimal()) 
    
    for item in perpetual_balances:
        user_id, asset, amount = item

        if user_id not in value_user_ids:
            continue
        res[user_id][(AccountBalanceType.PERPETUAL, asset)]['price'] = price_map.get(asset, Decimal())
        res[user_id][(AccountBalanceType.PERPETUAL, asset)]['amount'] = amount 
    
    all_user_ids = list(res)
    all_user_ids.sort()
    all_user_ids = [user_id for user_id in all_user_ids if user_id >= min_user_id]

    for ids in batch_iter(all_user_ids, 5000):
        current_app.logger.warning(f"initiate_data {ids[0]} {ids[-1]}")
        commit_records = []
        for user_id in ids:
            for (account_type, asset), data in res[user_id].items():
                commit_records.append(AssetCost(
                    user_id=user_id,
                    account_type=account_type,
                    asset=asset,
                    price=data['price'],
                    amount=data['amount']
                ))
        db.session.bulk_save_objects(commit_records)
        db.session.commit()
    return all_user_ids


def fix_data(ts):
    from app.business.external_dbs import TradeHistoryDB
    from app.business.prices import PriceManager
    from app.consumer.kafka_balance_update import BalanceUpdateHandler
    from app.models.wallet import AssetPrice
    from app.utils.date_ import timestamp_to_datetime
    from app.business.external_dbs import TradeHistoryDB
    from app.business.prices import PriceManager

    today_ts = ts - ts % 86400
    start_ts = today_ts - 86400 * START_DAY_COUNT

    end_date = timestamp_to_datetime(start_ts).date()
    start_date = timestamp_to_datetime(start_ts).date()
    price_range_map = AssetPrice.get_close_price_range_map(start_date, end_date)

    price_map = PriceManager.assets_to_usd()
    all_user_ids = initiate_data(ts, price_map)

    table_user_map = defaultdict(list)
    for user_id in all_user_ids.keys():
        db_idx, table_idx = TradeHistoryDB.user_hash(user_id)
        table_user_map[(db_idx, table_idx)].append(user_id)
    
    for key, user_ids in table_user_map.items():
        db_idx, table_idx = key
        current_app.logger.warning(f"Querying get_balance_history_id_info spot: {db_idx} {table_idx}")
        spot_info = get_balance_history_id_info(System.SPOT, db_idx, table_idx, start_ts)
        current_app.logger.warning(f"Query get_balance_history_id_info finished spot: {db_idx} {table_idx}")

        current_app.logger.warning(f"Querying get_balance_history_id_info perpetual: {db_idx} {table_idx}")
        perpetual_info = get_balance_history_id_info(System.PERPETUAL, db_idx, table_idx, start_ts)
        current_app.logger.warning(f"Query get_balance_history_id_info finished perpetual: {db_idx} {table_idx}")

        spot_min_id, spot_max_id = spot_info['min_id'], spot_info['max_id']
        perpetual_min_id, perpetual_max_id = perpetual_info['min_id'], perpetual_info['max_id']
        curr_spot_id = spot_min_id
        curr_perpetual_id = perpetual_min_id
        result_map = defaultdict(lambda: defaultdict(lambda: {'price': Decimal(), 'amount': Decimal()}))
        while curr_spot_id <= spot_max_id or curr_perpetual_id <= perpetual_max_id:
            stop_spot_id = curr_spot_id + RECORD_BATCH
            stop_perpetual_id = curr_perpetual_id + RECORD_BATCH
            records = _get_balance_history(db_idx, table_idx, user_ids, 
                                           curr_spot_id, stop_spot_id, curr_perpetual_id, stop_perpetual_id, 
                                           price_range_map)
            curr_spot_id = stop_spot_id
            curr_perpetual_id = stop_perpetual_id
            update_price(records, result_map, price_range_map)

            BalanceUpdateHandler._update_result_map(records, result_map)
            update_record(result_map)
        

def delete_all_data_for_test():
    from app.models.base import db
    from app.models.asset_cost import AssetCost
    print(AssetCost.query.delete())
    db.session.commit()

def update_spot_data_for_test():
    from app.models.asset_cost import AssetCost
    from app.business.external_dbs import TradeLogDB
    from app.business.prices import PriceManager
    from app.models.base import db
    from app.utils.amount import quantize_amount
    from app.utils.date_ import current_timestamp

    ts = current_timestamp(to_int=True)
    balances = TradeLogDB.get_user_balances(ts)
    price_map = PriceManager.assets_to_usd()
    user_value_map = defaultdict(Decimal)
    for item in balances:
        user_id, asset, account_id, amount = item
        user_value_map[user_id] += quantize_amount(amount * price_map.get(asset, 0), 2)
    
    user_value_map = {user_id: value for user_id, value in user_value_map.items() if value >= USER_VALUE_THRESHOLD}
    print("SPOT balance count", len(balances))
    
    added = set()
    for item in balances:
        if item[0] not in user_value_map:
            continue

        user_id, asset, account_id, amount = item
        account_type = get_account_type(account_id)
        if (user_id, account_type, asset) in added:
            continue
        added.add((user_id, account_type, asset))
        record = AssetCost(
            user_id=user_id,
            account_type=account_type,
            asset=asset
        )
        record.price = quantize_amount(price_map.get(asset, 0), 8)
        record.amount = quantize_amount(amount, 8)
        db.session.add(record)
    db.session.commit()


def update_perpetual_data_for_test():
    from app.models.asset_cost import AssetCost
    from app.business.external_dbs import PerpetualLogDB
    from app.business.prices import PriceManager
    from app.common.constants import AccountBalanceType
    from app.models.base import db
    from app.utils.amount import quantize_amount
    from app.utils.date_ import current_timestamp

    ts = current_timestamp(to_int=True)
    balances = PerpetualLogDB.get_user_balances(ts)
    price_map = PriceManager.assets_to_usd()
    user_value_map = defaultdict(Decimal)
    for item in balances:
        user_id, asset, amount = item
        user_value_map[user_id] += quantize_amount(amount * price_map.get(asset, 0), 2)
    
    user_value_map = {user_id: value for user_id, value in user_value_map.items() if value >= USER_VALUE_THRESHOLD}
    print("PERP balance count", len(balances))
    for item in balances:


        if item[0] not in user_value_map:
            continue
        user_id, asset, amount = item
        record = AssetCost(
            user_id=user_id,
            account_type=AccountBalanceType.PERPETUAL,
            asset=asset
        )
        record.price = quantize_amount(price_map.get(asset, 0), 8)
        record.amount = quantize_amount(amount, 8)
        db.session.add(record)
    db.session.commit()



def test():
    from app.business.lock import CacheLock, LockKeys
    from app.models.base import db

    with CacheLock(LockKeys.balance_cost_update(), wait=30):
        db.session.rollback()
        delete_all_data_for_test()
        print("All data deleted.")
        update_spot_data_for_test()
        print("Spot data updated successfully.")
        update_perpetual_data_for_test()

def main():
    fix_data()

if __name__ == '__main__':
    from app import create_app

    with create_app().app_context():
        test()
