from collections import defaultdict
import os
import sys
from decimal import Decimal
from typing import Iterable

from app.business.external_dbs import PerpetualHistoryDB, TradeHistoryDB


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

USER_VALUE_THRESHOLD = Decimal("100")
START_DAY_COUNT = 365

def get_account_type(account_id):
    from app.common.constants import AccountBalanceType
    from app.consumer.kafka_balance_update import MAX_ORDER_ACCOUNT_ID, SPOT_ACCOUNT_ID, InvestmentAccount
    from app.models.staking import StakingAccount

    if account_id == SPOT_ACCOUNT_ID:
        return AccountBalanceType.SPOT
    if SPOT_ACCOUNT_ID < account_id < MAX_ORDER_ACCOUNT_ID:
        return AccountBalanceType.MARGIN
    if account_id in (StakingAccount.ACCOUNT_ID, InvestmentAccount.ACCOUNT_ID):
        return AccountBalanceType.INVESTMENT
    if InvestmentAccount.ACCOUNT_ID < account_id < StakingAccount.ACCOUNT_ID: 
        return AccountBalanceType.PLEDGE
    raise ValueError(f"No account type found!. {account_id}")

# get_user_amm_assets


def _get_balance_history(db_idx: int, table_idx: int, user_ids: Iterable[int], start_id: int, end_id: int):

    def _get_spot_balance_history(db_idx: int, table_idx: int, user_ids: Iterable[int], start_id: int, end_id: int):
        fields = ['id', 'time', 'user_id', 'account', 'asset', 'business', 'change', 'balance', 'detail']

        db_, table_name = TradeHistoryDB.DBS[db_idx], f'balance_history_{table_idx}'
        table = db_.table(table_name)
        
        user_id_str = ','.join(user_ids)
        where = f'user_id in ({user_id_str}) '
        where += f' AND id >= {start_id} AND id < {end_id}'
        records = table.select(
            *fields,
            where=where,
            force_index='PRI'
        )
        return list(dict(zip(fields, record)) for record in records)


    def _get_perpetual_balance_history(db_idx: int, table_idx: int, user_ids: Iterable[int], start_id: int, end_id: int):
        fields = ['id', 'time', 'user_id', 'asset', 'business', 'change', 'balance']

        db_, table_name = PerpetualHistoryDB.DBS[db_idx], f'balance_history_{table_idx}'
        table = db_.table(table_name)
        
        user_id_str = ','.join(user_ids)
        where = f'user_id in ({user_id_str}) '
        where += f' AND id >= {start_id} AND id < {end_id}'
        records = table.select(
            *fields,
            where=where,
            force_index='PRI'
        )
        return list(dict(zip(fields, record)) for record in records)
    
    def _process_spot_balance_history(records):
        pass
    
    spot_records = _get_spot_balance_history(db_idx, table_idx, user_ids, start_id, end_id)

    _process_spot_balance_history(spot_records)
    perpetual_records = _get_perpetual_balance_history(db_idx, table_idx, user_ids, start_id, end_id)

def update_spot_data():
    from app.business.external_dbs import TradeHistoryDB, TradeLogDB
    from app.business.prices import PriceManager
    from app.consumer.kafka_balance_update import SPOT_ACCOUNT_ID
    from app.utils.amount import quantize_amount
    from app.utils.date_ import current_timestamp

    ts = current_timestamp(to_int=True)
    today_ts = ts - ts % 86400

    balances = TradeLogDB.get_user_balances(ts)
    price_map = PriceManager.assets_to_usd()
    user_value_map = defaultdict(Decimal)
    user_asset_map = defaultdict(set)
    for item in balances:
        user_id, asset, account_id, amount = item
        user_value_map[user_id] += quantize_amount(amount * price_map.get(asset), 2)
        if account_id == SPOT_ACCOUNT_ID:
            user_asset_map[user_id].add(asset)
    
    user_value_map = {user_id: value for user_id, value in user_value_map.items() if value >= USER_VALUE_THRESHOLD}
    user_asset_map = {user_id: assets for user_id, assets in user_asset_map.items() if user_id in user_asset_map}

    TradeHistoryDB.get_balance_history_users


def delete_all_data_for_test():
    from app.models.base import db
    from app.models.asset_cost import AssetCost
    print(AssetCost.query.delete())
    db.session.commit()

def update_spot_data_for_test():
    from app.models.asset_cost import AssetCost
    from app.business.external_dbs import TradeLogDB
    from app.business.prices import PriceManager
    from app.models.base import db
    from app.utils.amount import quantize_amount
    from app.utils.date_ import current_timestamp

    ts = current_timestamp(to_int=True)
    balances = TradeLogDB.get_user_balances(ts)
    price_map = PriceManager.assets_to_usd()
    user_value_map = defaultdict(Decimal)
    for item in balances:
        user_id, asset, account_id, amount = item
        user_value_map[user_id] += quantize_amount(amount * price_map.get(asset, 0), 2)
    
    user_value_map = {user_id: value for user_id, value in user_value_map.items() if value >= USER_VALUE_THRESHOLD}
    print("SPOT balance count", len(balances))
    
    added = set()
    for item in balances:
        if item[0] not in user_value_map:
            continue

        user_id, asset, account_id, amount = item
        account_type = get_account_type(account_id)
        if (user_id, account_type, asset) in added:
            continue
        added.add((user_id, account_type, asset))
        record = AssetCost(
            user_id=user_id,
            account_type=account_type,
            asset=asset
        )
        record.price = quantize_amount(price_map.get(asset, 0), 8)
        record.amount = quantize_amount(amount, 8)
        db.session.add(record)
    db.session.commit()


def update_perpetual_data_for_test():
    from app.models.asset_cost import AssetCost
    from app.business.external_dbs import PerpetualLogDB
    from app.business.prices import PriceManager
    from app.common.constants import AccountBalanceType
    from app.models.base import db
    from app.utils.amount import quantize_amount
    from app.utils.date_ import current_timestamp

    ts = current_timestamp(to_int=True)
    balances = PerpetualLogDB.get_user_balances(ts)
    price_map = PriceManager.assets_to_usd()
    user_value_map = defaultdict(Decimal)
    for item in balances:
        user_id, asset, amount = item
        user_value_map[user_id] += quantize_amount(amount * price_map.get(asset, 0), 2)
    
    user_value_map = {user_id: value for user_id, value in user_value_map.items() if value >= USER_VALUE_THRESHOLD}
    print("PERP balance count", len(balances))
    for item in balances:


        if item[0] not in user_value_map:
            continue
        user_id, asset, amount = item
        record = AssetCost(
            user_id=user_id,
            account_type=AccountBalanceType.PERPETUAL,
            asset=asset
        )
        record.price = quantize_amount(price_map.get(asset, 0), 8)
        record.amount = quantize_amount(amount, 8)
        db.session.add(record)
    db.session.commit()


def main():
    from app.business.lock import CacheLock, LockKeys
    from app.models.base import db

    with CacheLock(LockKeys.balance_cost_update(), wait=30):
        db.session.rollback()
        delete_all_data_for_test()
        print("All data deleted.")
        update_spot_data_for_test()
        print("Spot data updated successfully.")
        update_perpetual_data_for_test()


if __name__ == '__main__':
    from app import create_app

    with create_app().app_context():
        main()
