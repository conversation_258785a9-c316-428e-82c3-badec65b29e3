[program:coinex_backend_celery_worker_default]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "default" -c 5 -n default@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_default.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_prices]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "prices" -c 20 -n prices@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_prices.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_margin]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "margin" -c 50 -n margin@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_margin.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_credit]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "credit" -c 10 -n credit@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_credit.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_investment]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "investment" -c 10 -n investment@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_investment.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_perpetual]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "perpetual" -c 10 -n perpetual@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_perpetual.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_market_maker]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "market_maker" -c 10 -n market_maker@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_market_maker.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_risk_control]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "risk_control" -c 30 -n risk_control@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_risk_control.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_exchange]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "exchange" -c 20 -n exchange@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_exchange.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_viabtc_pool]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "viabtc_pool" -c 10 -n viabtc_pool@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_viabtc_pool.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_wallet]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "wallet" -c 10 -n wallet@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_wallet.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_strategy]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "strategy" -c 10 -n strategy@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_strategy.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_pledge]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "pledge" -c 30 -n pledge@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_pledge.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_sms]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "sms" -c 20 -n sms@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_sms.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_email]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "email" -c 20 -n email@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_email.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_push]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "push" -c 20 -n push@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_push.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_email_push]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "email_push" -c 10 -n email_push@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_email_push.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_daily]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "daily" -c 10 -n daily@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_daily.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_statistic]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "statistic" -c 10 -n statistic@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_statistic.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_profit_loss]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "profit_loss" -c 10 -n profit_loss@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_profit_loss.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_user_tag]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "user_tag" -c 10 -n user_tag@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_user_tag.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_price_notice]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "price_notice" -c 10 -n price_notice@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_price_notice.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30


[program:coinex_backend_celery_worker_realtime_income]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "realtime_income" -c 10 -n realtime_income@%%h --without-gossip --without-mingle --without-heartbeat  --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_realtime_income.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_activity]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "activity" -c 10 -n activity@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_activity.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_gift]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "gift" -c 10 -n gift@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_gift.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_real_time]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "real_time" -c 20 -n real_time@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_real_time.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_push_tag]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "push_tag" -c 10 -n push_tag@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_push_tag.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_report]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "report" -c 20 -n report@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_report.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_p2p]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "p2p" -c 20 -n p2p@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_p2p.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30


[program:coinex_backend_celery_worker_copy_trading]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "copy_trading" -c 20 -n copy_trading@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_copy_trading.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_ai]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "ai" -c 10 -n ai@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_ai.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_translation]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "translation" -c 10 -n translation@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_translation.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_vip]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "vip" -c 10 -n vip@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_vip.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_backend_celery_worker_third_exchange]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "third_exchange" -c 30 -n third_exchange@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_third_exchange.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30


[program:coinex_backend_celery_worker_reward_center]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_backend/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "reward_center" -c 20 -n reward_center@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_backend/
stdout_logfile = /var/log/coinex_backend/worker_reward_center.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

