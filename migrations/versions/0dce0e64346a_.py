"""empty message

Revision ID: 0dce0e64346a
Revises: ce22c149d484
Create Date: 2025-07-10 17:06:15.344361

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
import app.models

# revision identifiers, used by Alembic.
revision = '0dce0e64346a'
down_revision = 'ce22c149d484'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('asset_cost',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('updated_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('account_type', app.models.base.StringEnum('SPOT', 'MARGIN', 'INVESTMENT', 'PERPETUAL', 'AMM', 'PLEDGE', 'STAKING'), nullable=False),
    sa.Column('asset', sa.String(length=32), nullable=False),
    sa.Column('price', mysql.DECIMAL(precision=26, scale=8), nullable=False),
    sa.Column('amount', mysql.DECIMAL(precision=26, scale=8), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'account_type', 'asset', name='user_id_account_type_asset_uniq')
    )
    op.create_table('asset_cost_update_history',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('updated_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('asset', sa.String(length=32), nullable=False),
    sa.Column('price', mysql.DECIMAL(precision=26, scale=8), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('asset_cost_update_history', schema=None) as batch_op:
        batch_op.create_index('idx_user_id_asset', ['user_id', 'asset'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('p2p_mer_act', schema=None) as batch_op:
        batch_op.add_column(sa.Column('asset', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=32), nullable=False, comment='资产交易区'))

    with op.batch_alter_table('asset_cost_update_history', schema=None) as batch_op:
        batch_op.drop_index('idx_user_id_asset')

    op.drop_table('asset_cost_update_history')
    op.drop_table('asset_cost')
    # ### end Alembic commands ###
