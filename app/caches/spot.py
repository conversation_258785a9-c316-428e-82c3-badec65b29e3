# -*- coding: utf-8 -*-
import copy
from decimal import Decimal
from collections import defaultdict
from typing import List, Dict, Any, Tuple
import json
import datetime

from sqlalchemy import or_, func

from .kline import AssetRecentChangeRateCache, AssetRecent7dChangeRateCache, AssetRecent30dChangeRateCache
from ..models import Market, User, MarginAccount, CetDestroyReport
from .base import StringCache, HashCache, SetCache
from .shadow import DBShadowCache
from ..config import config
from ..utils import DefaultDictWithArg, amount_to_str, now, RESTClient, current_timestamp, safe_div, \
    today_timestamp_utc, group_by, quantize_amount, timestamp_to_date
from ..utils.parser import JsonEncoder

_online_markets = StringCache('online_markets')
_offline_markets = StringCache('offline_markets')
_markets_price_precision = HashCache('_markets_price_precision')


class MarketCache(DBShadowCache):
    model = Market
    pk_fields = 'name',

    MAX_STOP_ORDER_COUNT = 100

    @classmethod
    def market_sort_func(cls, base_asset, quote_asset):
        from app.assets import AssetUtils
        assets = AssetUtils.TOP_ASSETS
        return (
            assets.index(base_asset) if base_asset in assets else len(assets),
            base_asset,
            assets.index(quote_asset) if quote_asset in assets else len(assets),
            quote_asset
        )

    def __init__(self, market_name: str):
        super().__init__(market_name)

    @property
    def dict(self):
        value = super().dict
        value['default_depth'] = Decimal(value['default_depth'])
        # 升序排列
        value['depths'] = (tuple(sorted(map(Decimal, depths.split(','))))
                           if isinstance((depths := value['depths']), str)
                           else depths)
        return value

    @classmethod
    def list_online_markets(cls) -> List[str]:
        return value.split(':') if (value := _online_markets.read()) else []

    @classmethod
    def list_offline_markets(cls) -> List[str]:
        return value.split(':') if (value := _offline_markets.read()) else []

    @classmethod
    def list_online_markets_by_asset(cls, asset: str = None) -> List[str]:
        return [detail['name'] for detail in cls.online_markets_detail(
        ).values() if detail['base_asset'] == asset
                ] if asset else cls.online_markets_detail()

    @classmethod
    def online_markets_detail(cls) -> Dict[str, Any]:
        market_list = cls.list_online_markets()
        q = Market.query.with_entities(
            Market.status,
            Market.name,
            Market.base_asset,
            Market.base_asset_precision,
            Market.quote_asset,
            Market.quote_asset_precision,
            Market.trading_area,
        ).all()
        market_data_map = {v.name: v for v in q}
        result = {}
        for v in market_list:
            if v not in market_data_map:
                continue
            _data = market_data_map[v]
            result[v] = dict(
                status=_data.status,
                name=_data.name,
                base_asset=_data.base_asset,
                base_asset_precision=_data.base_asset_precision,
                quote_asset=_data.quote_asset,
                quote_asset_precision=_data.quote_asset_precision,
                trading_area=_data.trading_area,
            )
        return result

    @classmethod
    def refresh_all(cls):

        online = []
        offline = []
        for row in Market.query:
            name = row.name
            cls(name).object = row
            if row.status in {Market.Status.ONLINE,
                              Market.Status.BIDDING,
                              Market.Status.COUNTING_DOWN}:
                online.append(
                    dict(base_asset=row.base_asset,
                         quote_asset=row.quote_asset,
                         name=row.name,
                         base_asset_precision=row.base_asset_precision,
                         quote_asset_precision=row.quote_asset_precision,
                         )
                )
            else:
                offline.append(
                    dict(base_asset=row.base_asset,
                         quote_asset=row.quote_asset,
                         name=row.name,
                         base_asset_precision=row.base_asset_precision,
                         quote_asset_precision=row.quote_asset_precision,
                         )
                )

        for cache, markets in zip(
                (_online_markets, _offline_markets),
                (online, offline)
        ):
            sorted_markets = sorted(markets,
                                    key=lambda x: cls.market_sort_func(
                                        x["base_asset"],
                                        x["quote_asset"]
                                    ))
            _list = [v["name"] for v in sorted_markets]
            cache.value = ':'.join(_list)
        precision_cache = _markets_price_precision
        saving_data = {
            _detail['name']: _detail['quote_asset_precision']
            for _detail in online + offline
        }
        if saving_data:
            precision_cache.save(saving_data)

    @classmethod
    def get_market_price_precision(cls, market: str):
        return _markets_price_precision.hget(market)

    def check_order_permission(self,
                               user_type: User.UserType,
                               *,
                               is_api: bool = False) -> bool:
        shadow = self.shadow
        return Market.check_order_permission(
            shadow.trading_disabled,
            shadow.mode,
            shadow.status,
            user_type,
            shadow.countdown_ended_at,
            is_api=is_api
        )

    @classmethod
    def check_stop_order_count(cls, user_id: int, market: str, account_id: int) -> bool:
        from app.business.clients import ServerClient
        c = ServerClient()
        r = c.user_pending_stop_orders(user_id, market=market, page=1,
                                       limit=cls.MAX_STOP_ORDER_COUNT, side=0,
                                       account_id=account_id)
        if len(r) < cls.MAX_STOP_ORDER_COUNT:
            return True
        return False


class MarketStatusCache(HashCache):

    def __init__(self):
        super().__init__(None)


class CetCirculationCache(HashCache):
    CSC_CET_INFO_HOST = "https://www.coinex.net"

    def __init__(self):
        super().__init__(None)
        self.client = RESTClient(self.CSC_CET_INFO_HOST)

    def reload(self):
        from app.business import ServerClient

        response = self.client.get("/res/cet")
        if (code := response.get("code")) != 0:
            raise RESTClient.BadResponse(code, response.get("message", response))

        data = response["data"]
        # CET总量：取CSC的CET流通量
        total_supply = Decimal(data["liquidity"])
        # 待销毁量
        buyback_balance = ServerClient().get_user_balances(config["CET_BUYBACK_USER_ID"], "CET")
        ready_destroy = (buyback_balance["CET"]["available"] + buyback_balance["CET"]["frozen"])
        # 流通量 = CET总量 - 待销毁量
        remain = total_supply - Decimal(ready_destroy)
        # 累计销毁量：销毁记录的累加值（和接口保持一致）
        already_destroy = (
                CetDestroyReport.query.with_entities(func.sum(CetDestroyReport.current_destroy_amount)).scalar()
                or Decimal()
        )
        # 100亿
        total = Decimal('100_0000_0000')
        cache_data = {
            'total': amount_to_str(total),
            "total_supply": amount_to_str(total_supply),
            "remain": amount_to_str(remain),
            "ready_destroy": amount_to_str(ready_destroy),
            "already_destroy": amount_to_str(already_destroy),
        }
        self.hmset(cache_data)
        return cache_data

    def read_aside(self):
        if not self.value:
            self.reload()
            # self.expire(3600)  # 定时刷

        if self.value:
            return self.value

        return {}


class IlliquidMarketCache(SetCache):

    def __init__(self):
        super().__init__(None)


class MarketLastPriceCache(HashCache):

    def __init__(self):
        super().__init__(None)


class BiddingStopOrderMarketCache(StringCache):
    TTL = 86400

    def __init__(self, market: str):
        super().__init__(market)


class MarketViewCache(StringCache):
    """市场列表API视图缓存"""

    def __init__(self, include_offline_visible: bool):
        pk = None
        if include_offline_visible:
            pk = 'offline_visible'
        super().__init__(pk)

    @classmethod
    def reload(cls):
        cls.reload_with_ov(include_offline_visible=False)
        cls.reload_with_ov(include_offline_visible=True)

    @classmethod
    def reload_with_ov(cls, include_offline_visible) -> Dict:

        from app.caches import PreTradingMarketCache

        markets = {}
        offline_markets = {}
        pre_markets = set(PreTradingMarketCache.list_all_markets())
        quote_to_base = defaultdict(list)

        for market_name in MarketCache.list_online_markets():
            cache = MarketCache(market_name).dict
            markets[market_name] = cls.format_market(cache, pre_markets)
            quote_to_base[cache['quote_asset']].append(market_name)

        for market_name in MarketCache.list_offline_markets():
            cache = MarketCache(market_name).dict
            format_item = cls.format_market(cache, pre_markets)
            if include_offline_visible and cache.get('offline_visible'):
                markets[market_name] = format_item
                quote_to_base[cache['quote_asset']].append(market_name)
            else:
                offline_markets[market_name] = format_item

        data = {
            'default_trading_area': Market.TradingArea.DEFAULT.value,
            'trading_area': [v for v in Market.TradingArea.quotes() if v in quote_to_base],
            'trading_area_item': {
                'USDⓈ': []
            },
            'markets': list(markets.keys()),
            'trading_area_market': quote_to_base,
            'market_info': markets,
            'offline_market_info': offline_markets
        }
        cls(include_offline_visible).set(json.dumps(data, cls=JsonEncoder))
        return data

    @classmethod
    def format_market(cls, market: dict, pre_markets: set[str]):
        from app.assets import try_get_asset_config
        from app.caches import AmmMarketCache
        from app.models import AmmMarket

        def to_timestamp(_dt):
            return int(_dt.timestamp()) if _dt is not None else 0

        min_order_amount = DefaultDictWithArg(
            lambda asset: (a.min_order_amount or Decimal()
                           if (a := try_get_asset_config(asset)) is not None
                           else Decimal()))
        if (status := market['status']) is Market.Status.BIDDING:
            status = 'bidding'
        elif status is Market.Status.COUNTING_DOWN:
            status = 'countdown'
        else:
            status = 'pass'
        # app端兼容
        amm_type = 'normal'
        if AmmMarketCache.has(market["name"]):
            amm_market = AmmMarket.query.filter(AmmMarket.name == market["name"]).first()
            if amm_market.amm_type == AmmMarket.AmmType.INFINITE:
                amm_type = 'amm'
        return dict(
            market=market['name'],
            trading_area=market['trading_area'].value,
            trade_type='spot',
            default_merge=amount_to_str(market['default_depth']),
            merge=market['depths'],
            buy_asset_type=market['quote_asset'],
            buy_asset_type_places=market['quote_asset_precision'],
            sell_asset_type=market['base_asset'],
            sell_asset_type_places=market['base_asset_precision'],
            maker_fee_rate=market['maker_fee_rate'],
            taker_fee_rate=market['taker_fee_rate'],
            least_amount=min_order_amount[market['base_asset']],
            price_rate=market['max_price_deviation'],
            create_time=to_timestamp(market['created_at']),
            start_time=to_timestamp(market['started_at']),
            end_time=to_timestamp(market['ended_at']),
            bidding_stop_cancel_time=to_timestamp(
                market['bidding_matching_started_at']),
            bidding_end_time=to_timestamp(market['bidding_ended_at']),
            countdown_end_time=to_timestamp(market['countdown_ended_at']),
            status=status,
            offline_visible=bool(market['status'] == Market.Status.OFFLINE and market.get('offline_visible')),
            is_pre=market["name"] in pre_markets,
            # app兼容
            market_type=amm_type
        )


class InternalMarketViewCache(StringCache):
    """市场列表内部API视图缓存"""

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls) -> List:
        from app.business.clients import SPOT_ACCOUNT_ID
        from app.business.pledge.helper import get_pledge_account_id_assets_dict
        from app.business.order import PriceVerifySettings

        markets = []
        margin_markets = {
            v.name: v.id
            for v in MarginAccount.query.filter(
                MarginAccount.status == MarginAccount.StatusType.OPEN
            )
        }
        margin_sell_match_rate = PriceVerifySettings.market_margin_sell_server_match
        margin_buy_match_rate = PriceVerifySettings.market_margin_buy_server_match

        normal_sell_match_rate = PriceVerifySettings.market_normal_sell_server_match
        normal_buy_match_rate = PriceVerifySettings.market_normal_buy_server_match

        p_account_id_assets_dict = get_pledge_account_id_assets_dict()

        row: Market
        for row in Market.query \
                .filter(Market.status.notin_([Market.Status.OFFLINE]),
                        or_(Market.started_at.is_(None),
                            Market.started_at <= now())):
            if (min_amount := row.min_order_amount) <= 0:
                continue

            account_ids = [SPOT_ACCOUNT_ID]
            if row.name in margin_markets:
                account_ids.append(margin_markets[row.name])
                sell_match_rate = margin_sell_match_rate
                buy_match_rate = margin_buy_match_rate
            else:
                sell_match_rate = normal_sell_match_rate
                buy_match_rate = normal_buy_match_rate
            p_account_ids = []
            for _p_account_id, _p_assets in p_account_id_assets_dict.items():
                m_assets = {row.base_asset, row.quote_asset}
                if set(_p_assets) & m_assets == m_assets:
                    # 质押账户中包含市场的2个币种，则允许交易
                    p_account_ids.append(_p_account_id)
            if p_account_ids:
                account_ids.extend(p_account_ids)

            markets.append(dict(
                name=row.name,
                maker_fee_rate=row.maker_fee_rate,
                taker_fee_rate=row.taker_fee_rate,
                stock=dict(
                    name=row.base_asset,
                    prec=row.base_asset_precision
                ),
                money=dict(
                    name=row.quote_asset,
                    prec=row.quote_asset_precision
                ),
                sell_match_rate=sell_match_rate,
                buy_match_rate=buy_match_rate,
                min_amount=min_amount,
                default_merge=row.default_depth,
                accounts=account_ids,
            ))

        cls().set(json.dumps(markets, cls=JsonEncoder))

        return markets


class MarketFeeViewCache(StringCache):

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls) -> Dict:
        from app.caches import AmmMarketCache
        markets_fees = {}
        for market_name in MarketCache.list_online_markets():
            cache = MarketCache(market_name).dict
            markets_fees[market_name] = dict(
                maker_fee_rate=cache["maker_fee_rate"],
                taker_fee_rate=cache["taker_fee_rate"],
                depths=cache["depths"],
                is_amm_market=AmmMarketCache.has(market_name)
            )
        cls().set(json.dumps(markets_fees, cls=JsonEncoder))
        return markets_fees


class MonthlyMarketChangeRateCache(HashCache):

    def __init__(self):
        super().__init__(None)

    def read_aside(self):
        return self.value


class ExchangeConfigCache(StringCache):
    """ 兑换档位配置 """

    DEFAULT_CONFIGS = [
        # 最小深度市值USD, 目标价格幅度, 最大兑换USD
        (Decimal("500000"), Decimal("0.01"), Decimal("500000")),
        (Decimal("200000"), Decimal("0.015"), Decimal("200000")),
        (Decimal("100000"), Decimal("0.02"), Decimal("100000")),
        (Decimal("50000"), Decimal("0.025"), Decimal("50000")),
        (Decimal("20000"), Decimal("0.03"), Decimal("20000")),
        (Decimal("10000"), Decimal("0.035"), Decimal("10000")),
        (Decimal("5000"), Decimal("0.04"), Decimal("5000")),
        (Decimal("2000"), Decimal("0.045"), Decimal("2000")),
        (Decimal("1000"), Decimal("0.05"), Decimal("1000")),
        (Decimal("500"), Decimal("0.055"), Decimal("500")),
        (Decimal("200"), Decimal("0.06"), Decimal("200")),
    ]

    def __init__(self):
        super().__init__(None)

    def get_sorted_configs(self) -> List[Tuple[Decimal, Decimal, Decimal]]:
        data = self.read()
        if not data:
            return self.DEFAULT_CONFIGS

        data = json.loads(data)
        results = []
        for d in data:
            results.append((Decimal(d["min_depth_usd"]), Decimal(d["price_deviation"]), Decimal(d["max_exchange_usd"])))

        # item: (最小深度市值USD, 目标价格幅度, 最大兑换USD)
        results.sort(key=lambda item: item[0], reverse=True)  # 按照最小深度市值USD降序排列
        return results


class ExchangeMarketDepthCache(HashCache):
    """ 兑换市场深度缓存（admin展示） """

    def __init__(self):
        super().__init__(None)

    @classmethod
    def save_to_cache(cls, market_depth_usd_map: Dict[str, Decimal]):
        cache = cls()
        old_keys = cache.hkeys()

        cache_data = {str(k): str(v) for k, v in market_depth_usd_map.items()}
        cache.hmset(cache_data)

        del_keys = {x for x in old_keys if x not in cache_data}
        if del_keys:
            cache.hdel(*del_keys)


class ExchangeMarketCache(HashCache):
    """ 兑换市场 """

    def __init__(self):
        super().__init__(None)

    @classmethod
    def calc_price_deviation(cls, configs: List[Tuple[Decimal, Decimal, Decimal]], depth_usd: Decimal) -> Tuple[
        Decimal, Decimal]:
        # 计算 目标价格幅度、最大价值范围
        for _depth_usd, _price_deviation, _max_usd in configs:
            if depth_usd >= _depth_usd:
                return _price_deviation, _max_usd
        return Decimal(), Decimal()

    @classmethod
    def save_to_cache(cls, market_depth_usd_map: Dict[str, Decimal]):
        configs = ExchangeConfigCache().get_sorted_configs()
        min_depth_usd = configs[-1][0]

        market_exchange_info_map = {}
        market_depth_usd_map = {m: u for m, u in market_depth_usd_map.items() if u >= min_depth_usd}
        for market, depth_usd in market_depth_usd_map.items():
            price_deviation, max_exchange_usd = cls.calc_price_deviation(configs, depth_usd)
            if not price_deviation or not max_exchange_usd:
                continue
            market_exchange_info_map[market] = {
                "price_deviation": price_deviation,
                "max_exchange_usd": max_exchange_usd,
            }

        cache = cls()
        if not market_exchange_info_map:
            cache.delete()
            return

        old_keys = cache.hkeys()
        cache_data = {str(k): json.dumps(v, cls=JsonEncoder) for k, v in market_exchange_info_map.items()}
        cache.hmset(cache_data)
        del_keys = {x for x in old_keys if x not in cache_data}
        if del_keys:
            cache.hdel(*del_keys)


class ExchangeVisibleAssetsCache(StringCache):
    """ 兑换的可见币种列表（下拉列表，即使市场深度不足的币种也会返回） """

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        # 返回有上架市场的币种
        rows = Market.query.filter(
            Market.status == Market.Status.ONLINE,
        ).with_entities(
            Market.base_asset,
            Market.quote_asset,
        ).all()
        res_assets = set()
        for r in rows:
            res_assets.add(r.base_asset)
            res_assets.add(r.quote_asset)
        cls().save(json.dumps(list(res_assets)))
        return res_assets


class ExchangeAvailableAssetsCache(StringCache):
    """ 支持兑换的币种列表 """

    def __init__(self):
        super().__init__(None)


class ExchangeTargetAssetCache(HashCache):
    """ 支持兑换的目标币种 {source_asset1: target_asset_list1, ... } """
    def __init__(self):
        super().__init__(None)


class ExchangeAssetCache(StringCache):
    """ 支持兑换的币种，兼容接口保留 """

    def __init__(self):
        super().__init__(None)

    @classmethod
    def save_to_cache(cls, market_depth_usd_map: Dict[str, Decimal]):
        from app.business.exchange import MID_ASSETS

        # 币种：取所有市场中最小的深度市值
        configs = ExchangeConfigCache().get_sorted_configs()
        min_depth_usd = configs[-1][0]

        asset_markets_map = defaultdict(list)
        market_info_dict = MarketCache.online_markets_detail()
        market_depth_usd_map = {
            market: usd for market, usd in market_depth_usd_map.items()
            if usd >= min_depth_usd and market in market_info_dict
        }
        for market in market_depth_usd_map.keys():
            market_info = market_info_dict[market]
            asset_markets_map[market_info["quote_asset"]].append(market)
            asset_markets_map[market_info["base_asset"]].append(market)

        asset_targets_info_map = {}
        for asset, markets in asset_markets_map.items():
            targets = {}  # 该币种支持直接兑换的目标币种
            for market in markets:
                depth_usd = market_depth_usd_map[market]
                price_deviation, max_exchange_usd = ExchangeMarketCache.calc_price_deviation(configs, depth_usd)
                if not price_deviation or not max_exchange_usd:
                    continue

                market_info = market_info_dict[market]
                quote_asset = market_info["quote_asset"]
                base_asset = market_info["base_asset"]
                if quote_asset == asset:
                    targets[base_asset] = max_exchange_usd
                elif base_asset == asset:
                    targets[quote_asset] = max_exchange_usd

            if targets:
                asset_targets_info_map[asset] = targets

        # 补充跨市场的target_assets
        usdt = "USDT"
        usdt_mid_asset_targets_info_map = copy.deepcopy(asset_targets_info_map)
        if usdt in usdt_mid_asset_targets_info_map:
            usdt_targets = usdt_mid_asset_targets_info_map[usdt]
            for _asset, _target_info in usdt_mid_asset_targets_info_map.items():
                if _asset != usdt and usdt in _target_info:
                    usdt_max_exchange = _target_info[usdt]
                    for k, v in usdt_targets.items():
                        if k in _target_info:
                            _target_info[k] = max(_target_info[k], min(v, usdt_max_exchange))
                        else:
                            _target_info[k] = min(v, usdt_max_exchange)
                    _target_info.pop(_asset, None)

        for mid_asset in MID_ASSETS:
            if mid_asset in asset_targets_info_map:
                mid_targets = asset_targets_info_map[mid_asset]
                for _asset, _target_info in asset_targets_info_map.items():
                    if _asset != mid_asset and mid_asset in _target_info:
                        mid_max_exchange = _target_info[mid_asset]
                        for k, v in mid_targets.items():
                            if k in _target_info:
                                # 币种之间最大兑换，接口会选择多条路径：一个路径中的多个市场先取最小，然后多条路径再取最大
                                _target_info[k] = max(_target_info[k], min(v, mid_max_exchange))
                            else:
                                _target_info[k] = min(v, mid_max_exchange)
                        _target_info.pop(_asset, None)

        # to list-dict
        result = []
        for asset, target_info in asset_targets_info_map.items():
            result.append(
                {
                    "asset": asset,
                    "targets": [{"asset": k, "max_usd": v} for k, v in target_info.items()],
                }
            )
        usdt_mid_result = []  # 老接口返回全量数据，所以只返回中间币种是usdt的 限制下数据量
        for asset, target_info in usdt_mid_asset_targets_info_map.items():
            usdt_mid_result.append(
                {
                    "asset": asset,
                    "targets": [{"asset": k, "max_usd": v} for k, v in target_info.items()],
                }
            )

        cache = cls()
        ai_cache = ExchangeAvailableAssetsCache()
        target_cache = ExchangeTargetAssetCache()
        if not result:
            cache.delete()
            ai_cache.delete()
            target_cache.delete()
            return

        cache.set(json.dumps(usdt_mid_result, cls=JsonEncoder))
        ai_cache.save(json.dumps(list(asset_targets_info_map)))
        target_dump_data = {
            asset: json.dumps([{"asset": k, "max_usd": v} for k, v in target_info.items()], cls=JsonEncoder)
            for asset, target_info in asset_targets_info_map.items()
        }
        target_cache.save(target_dump_data)


class AutoInvestConfigCache(StringCache):
    """ 定投档位配置 """

    DEFAULT_CONFIGS = [
        # 最小深度市值USD, 目标价格幅度, 最大兑换USD
        (Decimal("500000"), Decimal("0.01"), Decimal("500000")),
        (Decimal("200000"), Decimal("0.015"), Decimal("200000")),
        (Decimal("100000"), Decimal("0.02"), Decimal("100000")),
        (Decimal("50000"), Decimal("0.025"), Decimal("50000")),
        (Decimal("20000"), Decimal("0.03"), Decimal("20000")),
        (Decimal("10000"), Decimal("0.035"), Decimal("10000")),
        (Decimal("5000"), Decimal("0.04"), Decimal("5000")),
        (Decimal("2000"), Decimal("0.045"), Decimal("2000")),
        (Decimal("1000"), Decimal("0.05"), Decimal("1000")),
        (Decimal("500"), Decimal("0.055"), Decimal("500")),
        (Decimal("200"), Decimal("0.06"), Decimal("200")),
    ]

    def __init__(self):
        super().__init__(None)

    def get_sorted_configs(self) -> List[Tuple[Decimal, Decimal, Decimal]]:
        data = self.read()
        if not data:
            return self.DEFAULT_CONFIGS

        data = json.loads(data)
        results = []
        for d in data:
            results.append((Decimal(d["min_depth_usd"]), Decimal(d["price_deviation"]), Decimal(d["max_usd"])))

        # item: (最小深度市值USD, 目标价格幅度, 最大兑换USD)
        results.sort(key=lambda item: item[0], reverse=True)  # 按照最小深度市值USD降序排列
        return results

# 定投

class AutoInvestMarketDepthCache(HashCache):
    """ 定投市场深度缓存（admin展示） """

    def __init__(self):
        super().__init__(None)

    @classmethod
    def save_to_cache(cls, market_depth_usd_map: Dict[str, Decimal]):
        cache = cls()
        cache_data = {str(k): str(v) for k, v in market_depth_usd_map.items()}
        cache.save(cache_data)


class AutoInvestMarketCache(HashCache):
    """ 定投市场 """

    def __init__(self):
        super().__init__(None)

    @classmethod
    def calc_price_deviation(cls, configs: List[Tuple[Decimal, Decimal, Decimal]], depth_usd: Decimal) -> Tuple[
        Decimal, Decimal]:
        # 计算 目标价格幅度、最大价值范围
        for _depth_usd, _price_deviation, _max_usd in configs:
            if depth_usd >= _depth_usd:
                return _price_deviation, _max_usd
        return Decimal(), Decimal()

    @classmethod
    def save_to_cache(cls, market_depth_usd_map: Dict[str, Decimal]):
        configs = AutoInvestConfigCache().get_sorted_configs()
        min_depth_usd = configs[-1][0]

        market_info_map = {}
        market_depth_usd_map = {m: u for m, u in market_depth_usd_map.items() if u >= min_depth_usd}
        for market, depth_usd in market_depth_usd_map.items():
            price_deviation, max_usd = cls.calc_price_deviation(configs, depth_usd)
            if not price_deviation or not max_usd:
                continue
            market_info_map[market] = {
                "price_deviation": price_deviation,
                "max_usd": max_usd,
            }

        cache = cls()
        if not market_info_map:
            cache.delete()
            return
        cache_data = {str(k): json.dumps(v, cls=JsonEncoder) for k, v in market_info_map.items()}
        cache.save(cache_data)


class OnlineMarketAssetCache(SetCache):
    """ 有上架市场的币种 """

    def __init__(self):
        super().__init__(None)

    def reload(self):
        markets_info = MarketCache.online_markets_detail()
        online_assets = {i['base_asset'] for i in markets_info.values()}
        self.save(online_assets)


class ExchangeRankCache(StringCache):
    """ 热门兑换榜 (源币种-目标币种 维度) """

    def __init__(self):
        super().__init__(None)


class ExchangeTargetAssetRankCache(StringCache):
    """ 热门兑换目标币种榜 (目标币种 维度) """

    def __init__(self):
        super().__init__(None)


class ExchangePreviewPathCache(StringCache):
    """ 兑换路径预览缓存 """

    TTL = 3600

    def __init__(self, source_asset: str, target_asset: str, source_amount: Decimal):
        amount_str = amount_to_str(source_amount, 8)
        key_ = f"{source_asset}:{target_asset}:{amount_str}"
        super().__init__(key_)

    def save_path(self, path: list[str]):
        val_ = json.dumps(path)
        self.set(val_)
        self.expire(self.TTL)

    def get_path(self) -> list[str]:
        val_ = self.get()
        path = json.loads(val_) if val_ else []
        return path


class SpotMarketUpDownCache(StringCache):
    """行情大数据：现货相关信息"""

    POSITIVE = [11, 10, 7, 5, 3]
    NEGATIVE = [-11, -10, -7, -5, -3]
    ALL_INTERVAL = POSITIVE + NEGATIVE

    def __init__(self):
        super().__init__(None)

    def read(self) -> Dict:
        data = super().read()
        if not data:
            return {}
        else:
            return json.loads(data)

    @classmethod
    def reload(cls):

        cache_dict = {
            '1': AssetRecentChangeRateCache().get_rates(),
            '7': AssetRecent7dChangeRateCache().hgetall(),
            '30': AssetRecent30dChangeRateCache().hgetall(),
        }

        ret = dict()

        def g_f(x: str):
            percent_x = quantize_amount(Decimal(x), 4) * 100
            if percent_x > 0:
                # 准备返回的值
                last_val = cls.POSITIVE[0]
                for val in cls.POSITIVE:
                    if percent_x > val:
                        return last_val
                    last_val = val
                # 0 < x < 3，return 3
                return last_val
            elif percent_x < 0:
                last_val = cls.NEGATIVE[0]
                for val in cls.NEGATIVE:
                    if percent_x < val:
                        return last_val
                    last_val = val
                return last_val
            else:
                return 0

        assets = set()
        for market_name in MarketCache.list_online_markets():
            cache = MarketCache(market_name).dict
            assets |= {cache["quote_asset"], cache["base_asset"]}

        for interval, cache in cache_dict.items():
            # 过滤没有市场的货币
            filter_cache = {k: v for k, v in cache.items() if k in assets}
            tmp = group_by(g_f, filter_cache.values())
            tmp.pop(0, '')
            ret[interval] = tmp

        cls().set(json.dumps(ret, cls=JsonEncoder))


class SpotMarketDealCache(StringCache):
    """行情大数据：现货24小时交易额"""

    def __init__(self):
        super().__init__(None)

    def read(self) -> Dict:
        data = super().read()
        if not data:
            return self.get_init_data()
        else:
            return json.loads(data)

    @classmethod
    def get_init_data(cls):
        return {
            "price_up_nums": 0,
            "price_down_nums": 0,
            "spot_deal_rate": 0.0,
            "spot_deal_24": '0'
        }

    @classmethod
    def reload(cls):
        from .admin import MarketRealDealSpotAmountCache, MarketRealDealSpotAmountHistoryCache, MARKET_DATA_UPDATE_TIME

        ret = cls.get_init_data()
        rate_cache_24 = SpotMarketUpDownCache().read().get("1")
        if rate_cache_24:
            ret["price_up_nums"] = sum([len(v) for k, v in rate_cache_24.items() if Decimal(k) > 0])
            ret["price_down_nums"] = sum([len(v) for k, v in rate_cache_24.items() if Decimal(k) < 0])

        cur = current_timestamp(to_int=True)
        cur = cur - cur % MARKET_DATA_UPDATE_TIME

        hour_24, _ = MarketRealDealSpotAmountCache().read_aside()
        usd_24 = cls.get_hour_usd(hour_24)

        hour_48 = MarketRealDealSpotAmountHistoryCache(cur - 86400).read_aside()
        if hour_48:
            usd_48 = cls.get_hour_usd(hour_48)
            ret["spot_deal_rate"] = float(round(safe_div(usd_24 - usd_48, usd_48), 4))
        else:
            ret["spot_deal_rate"] = 0
        ret["spot_deal_24"] = amount_to_str(usd_24, 2)

        cls().save(json.dumps(ret, cls=JsonEncoder))

    @classmethod
    def get_hour_usd(cls, data: dict):
        return sum(Decimal(i["total_usd"]) for i in data.values())


class SpotBuySellDistributionCache(HashCache):
    """统计每小时买卖分布的差额"""
    ttl = 86400 * 2
    buy_amount = "total_buy_amount"
    sell_amount = "total_sell_amount"
    buy_gap = "buy_gap"
    sell_gap = "sell_gap"

    def __init__(self, timestamp):
        super().__init__(timestamp)

    def read(self) -> Dict:
        data = super().read()
        if data:
            return data
        else:
            return {
                self.buy_amount: "0",
                self.sell_amount: "0",
                self.buy_gap: "0",
                self.sell_gap: "0",
            }

    @classmethod
    def reload(cls, timestamp):
        from ..business import PriceManager
        from ..schedules.reports.spot_trade_market import list_coin_trade_summary

        hour = 3600
        last_data = cls(timestamp - hour).read()

        # 传进来的 timestamp 是以每小时的准点时间戳
        if timestamp == today_timestamp_utc():
            # 获取昨天的日期，每日0点应该统计过去23-24点的数据，这个缓存更新是每小时30分，昨天的数据肯定已经计算完毕
            date = timestamp_to_date(timestamp) - datetime.timedelta(days=1)
        else:
            date = timestamp_to_date(timestamp)

        trade_summary_list = list_coin_trade_summary(date)
        total_sell_amount, total_buy_amount = Decimal(0), Decimal(0)
        for item in trade_summary_list:
            market, stock_asset, money_asset, deal_amount, deal_user_list, deal_count, deal_volume, \
                taker_buy_amount, taker_sell_amount, taker_buy_count, taker_sell_count = item
            price = PriceManager.asset_to_usd(stock_asset)
            total_buy_amount += Decimal(taker_buy_amount) * price
            total_sell_amount += Decimal(taker_sell_amount) * price

        if timestamp == today_timestamp_utc() + hour:
            # 数据源是日报，所以每日1点到0点的差额等于总和
            buy_gap = total_buy_amount
            sell_gap = total_sell_amount
        else:
            # 兼容没有数据的情况，第一次上线初始数据为空，gap 为 0
            buy_gap = total_buy_amount - Decimal(last_data[cls.buy_amount]) if last_data[cls.buy_amount] else 0
            sell_gap = total_sell_amount - Decimal(last_data[cls.sell_amount]) if last_data[cls.sell_amount] else 0

        cls(timestamp).save({
            cls.buy_amount: amount_to_str(total_buy_amount, 2),
            cls.sell_amount: amount_to_str(total_sell_amount, 2),
            cls.buy_gap: amount_to_str(buy_gap, 2),
            cls.sell_gap: amount_to_str(sell_gap, 2),
        })


class SpotBigBookingCache(StringCache):
    """大额挂单缓存"""
    ttl = 86400 * 30

    def __init__(self, user_id, asset, side):
        key = self.format_key(user_id, asset, side)
        super().__init__(key)

    @staticmethod
    def format_key(user_id, asset, side):
        return ":".join(str(i) for i in [user_id, asset, side.value])

    def read_one(self) -> Decimal:
        val = self.get()
        return Decimal(val) if val else Decimal(0)

    def set_one(self, amount: Decimal):
        self.set(str(amount), ex=self.ttl)
