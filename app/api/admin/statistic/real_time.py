# -*- coding: utf-8 -*-
import io
from collections import defaultdict
from datetime import timedelta
import json
from decimal import Decimal
from enum import Enum
from itertools import chain

from flask import send_file, g
from werkzeug.datastructures import MultiDict

from app.business.security import SecuritySettingType
from app.caches.kline import AssetRankCache
from app.caches.security import SecurityStatisticsCache
from app.models.amm import AmmMarket
from app.models.market_liquidity import (ExchangeMarketPrices, MarketBpsData, MarketType,
                                         MarketLiquidityStatistic, Exchange, MarketLiquidityFilter)
from app.schedules.statistics.nation_active_user import update_language_active_user_schedule, \
    update_nation_active_user_schedule, update_nation_trade_fee_schedule, update_language_trade_fee_schedule
from app.schedules.statistics.nation_register_user import (update_language_register_user_schedule,
                                                           update_nation_register_user_schedule)
from flask_restx import marshal
from flask_restx.fields import Integer, Raw
from openpyxl.workbook import Workbook
from sqlalchemy import func
from webargs import fields

from app.api.common import Namespace, respond_with_code, Resource, ex_fields
from app.api.common.fields import Enum<PERSON>ield
from app.business.credit import get_loan_asset_report
from app.business.margin.helper import MarginAssetStatistic
from app.business.utils import AssetComparator
from app.caches.admin import RealTimeAssetStatisticCache, \
    AssetSummaryStatisticCache, \
    RealtimeUserStatisticCache, MarketRealDealContractAmountCache, \
    MarketRealDealSpotAmountCache, MarketRealDealSummaryCache, RealtimeDepositWithdrawStatisticCache, \
    ActiveUserStatisticCache, RealTimeActiveAssetCache, \
    SpotStageDepthCache, PerpetualStageDepthCache, BinancePerpetualStageDepthCache, \
    OKXPerpetualStageDepthCache, \
    HuobiPerpetualStageDepthCache, BybitPerpetualStageDepthCache, \
    KucoinPerpetualStageDepthCache, BitgetPerpetualStageDepthCache, \
    GatePerpetualStageDepthCache, NoActiveUserStatisticCache, \
    RealTimePerpetualMonitorCache, SignPriceBasisRateCache, BidAskDiffRateCache, \
    Depth1LongShortRateCache, Depth4LongShortRateCache, MarginIndexPriceBasisRateCache, MarginBidAskDiffRateCache, \
    MarginDepth1LongShortRateCache, MarginDepth4LongShortRateCache, RealTimeMarginMonitorCache, \
    RealTimeMarginLiquidationCache, RealTimePerpetualLiquidationCache
from app.caches.statistics import DepositWithdrawRetentionRateCache, \
    AssetUsersStatisticCache, AssetUsersStatisticForLanguageCache, LanguageUserStatisticsCache, \
    NationUserStatisticsCache, NationTradeFeeStatisticsCache, LanguageTradeFeeStatisticsCache, \
    PerpetualProfitLossStatisticsCache, AmbassadorStatisticsCache
from app.caches import PerpetualMarketCache, PerpetualUserAnalysisCache, MarketCache
from app.common import PrecisionEnum, get_country, Language, language_cn_names, AreaInfo, AREAS_MAPPING
from app.models import User, \
    ReferralHistory, VipUser, MarketMaker, \
    SpotAssetTradeSummary, PerpetualAssetTradeSummary, UserActivenessReport, \
    Ambassador, NationTradeUserReport, UserTradeFeeSummary, \
    LanguageTradeUserReport, UserTradeSummary, AssetTag, AssetTagRelation, db, Market, PerpetualMarket
from app.models.daily import DailyAssetBusinessReport, DailyPerpetualMarketReport, DailySpotTradeCoinReport, \
    PledgeAssetSynthetic
from app.models.activeness import TradeType
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectStatistic
from app.schedules.statistics.realtime_asset import RealTimeDepthCalculator, DEPOSIT_WITHDRAW_STATISTIC_TIME_TYPES
from app.utils import amount_to_str, datetime_to_time, export_xlsx, format_percent
from app.utils.amount import quantize_amount
from app.utils.date_ import now, today, timestamp_to_date, get_utc8_today_timestamp
from app.assets import list_all_assets
from app.exceptions import InvalidArgument, RecordNotFound
from app.api.admin.report.perpetual import get_time_display_str, time_range_display_map
from app.utils.format import safe_div
from app.utils.iterable import group_by

ns = Namespace('Realtime')


COIN_PLACES = PrecisionEnum.COIN_PLACES.value


@ns.route('/assets')
@respond_with_code
class RealtimeAssetResource(Resource):
    account_types = {
        'ALL': 'ALL',
        'SPOT': '现货',
        'MARGIN': '杠杆',
        'PERPETUAL': '合约',
        'PLEDGE': '借贷',
        'INVESTMENT': '理财',
        'STAKING': '质押',
        'AMM': 'AMM',
    }

    @classmethod
    @ns.use_kwargs(dict(
        sort_name=EnumField(['balance_usd', 'valid_user_count', 'normal_user_balance_usd']),
        account_type=EnumField(account_types),
        asset=fields.String,
        tag_id=fields.Integer,
    ))
    def get(cls, **kwargs):
        """统计-资产统计"""
        account_type = kwargs.get("account_type") or "ALL"
        asset = kwargs.get('asset')
        all_tags = AssetTag.query.filter(
            AssetTag.status != AssetTag.StatusType.INVALID
        ).order_by(AssetTag.rank.asc()).all()
        tag_data = [dict(tag_id=v.id, name=v.display_name) for v in all_tags]
        real_time_asset_statistic = RealTimeAssetStatisticCache(account_type, kwargs.get('tag_id')).read()
        if not real_time_asset_statistic:
            return dict(
                records=[],
                balance_usd_pie_chart_data=[],
                nu_balance_usd_pie_chart_data=[],
                timestamp=0,
                account_types=cls.account_types,
                assets=list_all_assets(),
                tag_data=tag_data
            )
        data = json.loads(real_time_asset_statistic['data'])

        records = [{
            'user_count': real_time_asset_statistic['all_user_count'],
            'valid_user_count': real_time_asset_statistic['all_valid_user_count'],
            'balance_usd': real_time_asset_statistic['all_balance_usd'],
            'balance': '-',
            'asset': 'ALL',
            'balance_proportion': 1,
            'normal_user_balance': "-",
            'normal_user_balance_usd': real_time_asset_statistic['all_normal_user_balance_usd'],
            'outer_maker_user_balance_usd': real_time_asset_statistic['all_outer_maker_user_balance_usd'],
            'tag_ids': []
        }]
        if sort_name := kwargs.get('sort_name'):
            # 缓存已按balance_usd更新
            if sort_name in ['valid_user_count', 'normal_user_balance_usd']:
                data.sort(key=lambda x: Decimal(x[sort_name]), reverse=True)
        records.extend(data)
        for i, r in enumerate(records, start=0):
            r["rank"] = i
        timestamp = float(real_time_asset_statistic['timestamp'])
        if asset:
            records_map = {r['asset']: r for r in records}
            record = records_map.get(asset)
            records = [record] if record else []

        tag_query = AssetTagRelation.query.join(AssetTag).filter(
            AssetTagRelation.tag_id == AssetTag.id,
            AssetTagRelation.status == AssetTagRelation.StatusType.PASSED
        ).all()  # 标签状态为关闭的也展示
        asset_tag_data = MultiDict([(v.asset, v.tag_id) for v in tag_query])
        records = [{
            **r,
            'tag_ids': asset_tag_data.getlist(r["asset"])
        } for r in records]

        def _get_pie_chart_data(key_):
            sorted_data = sorted(data, key=lambda x: Decimal(x[key_]), reverse=True)
            pie_limit = 20
            pie_chart_data = [[v['asset'], amount_to_str(v[key_], PrecisionEnum.CASH_PLACES)] for v in sorted_data[:pie_limit]]
            if data[pie_limit:]:
                pie_chart_data.append(
                    ['other', amount_to_str(sum([Decimal(v[key_]) for v in data[pie_limit:]]), PrecisionEnum.CASH_PLACES)]
                )
            return pie_chart_data

        return dict(
            records=records,
            balance_usd_pie_chart_data=_get_pie_chart_data('balance_usd'),
            nu_balance_usd_pie_chart_data=_get_pie_chart_data('normal_user_balance_usd'),
            outer_maker_user_balance_usd_pie_chart_data=_get_pie_chart_data('outer_maker_user_balance_usd'),
            timestamp=timestamp,
            account_types=cls.account_types,
            assets=list_all_assets(),
            tag_data=tag_data
        )


@ns.route('/active-assets')
@respond_with_code
class RealtimeActiveAssetsResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String,
    ))
    def get(cls, **kwargs):
        """统计-活跃资产统计"""
        real_time_asset_statistic_cache = RealTimeActiveAssetCache()
        real_time_asset_statistic = real_time_asset_statistic_cache.read()
        data = json.loads(real_time_asset_statistic['data'])
        asset = kwargs['asset'] if kwargs.get('asset') else 'ALL'
        records = data[asset]
        timestamp = float(real_time_asset_statistic['timestamp'])
        return dict(
            records=records,
            timestamp=timestamp,
            asset_list=list_all_assets()
        )


@ns.route('/deals')
@respond_with_code
class RealDealsResource(Resource):
    @classmethod
    def get(cls):
        """统计-实时成交"""
        spot_data, exchange_data = MarketRealDealSpotAmountCache().read_aside()
        contract_data = MarketRealDealContractAmountCache().get_data_lis()
        sum_by_column = lambda total, column: sum([Decimal(v[column]) for v in total.values()])
        get_percent = lambda total, p: Decimal(p) / Decimal(total) if Decimal(total) > Decimal(0) else Decimal()
        total_spot_usd = sum_by_column(spot_data, "real_usd")

        spot_summary, per_summary, summary_start, summary_end = MarketRealDealSummaryCache().read_aside()
        if spot_summary:
            total_spot_user = spot_summary['deal_users']
            total_spot_deal_count = spot_summary['deal_count']
            tps_of_spot = amount_to_str(total_spot_deal_count / (summary_end - summary_start), 2)
        else:
            total_spot_user = tps_of_spot = 0
        if per_summary:
            total_contract_deal_count = per_summary["deal_count"]
            tps_of_contract = amount_to_str(total_contract_deal_count / (summary_end - summary_start), 2)
        else:
            tps_of_contract = 0

        spot_update_ts = max([i["update_time"] for i in spot_data.values()] if spot_data else [0])
        per_update_ts = max([i["update_time"] for i in contract_data] if contract_data else [0])
        update_ts = max([spot_update_ts, per_update_ts, summary_end])

        all_spot_data = dict(
            market_count=sum([i['market_count'] for i in spot_data.values()]),
            real_deal='--',
            real_usd=str(sum_by_column(spot_data, 'real_usd')),
            total_deal='--',
            total_usd=str(sum_by_column(spot_data, 'total_usd')),
            percent='1',
            user_count=total_spot_user,
            rate='--',
            update_time='--'
        )
        area_spot_data = sorted([
                dict(value,
                     trade_area=key,
                     percent=str(get_percent(
                         total_spot_usd, value['real_usd']))
                     )
                for key, value in spot_data.items()
            ],
                key=lambda x: -Decimal(x['real_usd']))
        spot_data_ret = [dict(all_spot_data, trade_area='ALL')] + area_spot_data

        return dict(
            update_ts=update_ts,
            tps_of_spot=tps_of_spot,
            tps_of_contract=tps_of_contract,
            spot_data=spot_data_ret,
            contract_data=contract_data,
            exchange_data=exchange_data,
        )


@ns.route('/deposit_withdraw')
@respond_with_code
class RealDepositWithdrawResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        time_type=ex_fields.EnumField(
            enum=DEPOSIT_WITHDRAW_STATISTIC_TIME_TYPES, required=True),
    ))
    def get(cls, **kwargs):
        """统计-实时充提"""
        result = RealtimeDepositWithdrawStatisticCache(kwargs['time_type']).read()
        data = json.loads(result)
        records = sorted(data['data'], key=lambda x: int(x['deposit_user_count']),
                         reverse=True)

        for record in records:
            record['real_income'] = amount_to_str(
                Decimal(record['deposit_amount']) - Decimal(record['withdraw_amount']), 8)
            record['real_income_usd'] = amount_to_str(
                Decimal(record['deposit_usd']) - Decimal(record['withdraw_usd']), 2)
        timestamp = float(data['timestamp'])
        total_real_income_usd = sum(Decimal(r['real_income_usd']) for r in records)
        return dict(
            records=records,
            total_real_income_usd=total_real_income_usd,
            timestamp=timestamp
        )


@ns.route('/margin-liquidation')
@respond_with_code
class RealtimeMarginLiquidationResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        time_type=ex_fields.EnumField(enum=['15m', '1h', '6h', '1d', '3d'], required=True),
    ))
    def get(cls, **kwargs):
        """统计-实时统计-杠杆实时爆仓"""
        cache = RealTimeMarginLiquidationCache()
        data = json.loads(cache.hget('data')).get(kwargs['time_type'])
        records = data.get('markets')
        statistics = data.get('statistics')
        timestamp = cache.hget('timestamp')

        return dict(
            statistics=statistics,
            records=records,
            timestamp=int(timestamp)
        )


@ns.route('/perpetual-liquidation')
@respond_with_code
class RealtimePerpetualLiquidationResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        time_type=ex_fields.EnumField(enum=['15m', '1h', '6h', '1d', '3d'], required=True),
    ))
    def get(cls, **kwargs):
        """统计-实时统计-合约实时爆仓"""
        cache = RealTimePerpetualLiquidationCache()
        data = json.loads(cache.hget('data')).get(kwargs['time_type'])
        records = data.get('markets')
        statistics = data.get('statistics')
        timestamp = cache.hget('timestamp')

        return dict(
            statistics=statistics,
            records=records,
            timestamp=int(timestamp)
        )


def get_cn_names():
    return {lang.name: lang_cn for lang, lang_cn in language_cn_names().items()}


class PieChartUtil:

    @classmethod
    def get_pie_data(cls, pie_params, items, with_top=True):
        pie_chart_datas = {}
        for pie_param in pie_params:
            pie_chart_datas.update({
                pie_param['pie_keys'][1]: {
                    'pie_chart_data': cls.get_pie_chart_data(
                        pie_keys=pie_param['pie_keys'],
                        items=items,
                        with_top=with_top
                    ),
                    **pie_param
                }
            })
        return pie_chart_datas

    @classmethod
    def get_pie_chart_data(
            cls,
            pie_keys: (str, str),
            items: [dict],
            *,
            with_top: bool = True,
            top: int = 20,
    ) -> [(str, int | float)]:
        if not pie_keys:
            return []
        pie_chart_data = []
        x, y = pie_keys
        real_all_sum = 0
        for idx, item in enumerate(items):
            if idx == 0:
                real_all_sum = Decimal(item[y])
                continue
            if item[y] != 0:
                pie_chart_data.append([item[x], Decimal(item[y])])
        pie_chart_data.sort(key=lambda t: t[1], reverse=True)
        all_sum = sum(item[1] for item in pie_chart_data)
        if with_top:
            ret = pie_chart_data[:top]
            # top_sum = sum(item[1] for item in ret)
            other_sum = sum(item[1] for item in pie_chart_data[top:])
            if real_all_sum > all_sum:
                ret.append(['other', real_all_sum - all_sum + other_sum])
            elif other_sum:
                ret.append(['other', other_sum])
        else:
            ret = pie_chart_data
            if real_all_sum > all_sum:
                ret.append(['other', real_all_sum - all_sum])
        return ret


@ns.route('/user-register')
@respond_with_code
class UserRegisterResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        time_type=ex_fields.EnumField(
            enum=['7d', '30d', '90d', '180d', '365d', 'all'], required=True),
        group_type=ex_fields.EnumField(
            enum=['nation', 'language', 'area'], missing='nation'
        ),
        area=ex_fields.EnumField(enum=AreaInfo),
        sort_by=ex_fields.EnumField(
            enum=['total_counts', 'increase_counts'], missing='total_counts'
        ),
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """
        统计-注册用户-国籍分布
        统计-注册用户-语言分布
        """
        parma_area = kwargs.get('area')
        areas_mapping = {k.name: [m.info.iso_3 for m in v]
                         for k, v in AREAS_MAPPING.items()}
        areas = {k.name: k.value for k in AREAS_MAPPING.keys()}
        sort_by = kwargs['sort_by']

        pie_params = []
        match kwargs['group_type']:
            case 'nation':
                format_data = cls.get_nation_data(kwargs['time_type'], sort_by, parma_area)
                pie_params.extend([
                    {'pie_keys': ('country_cn_name', 'total_counts')},
                    {'pie_keys': ('country_cn_name', 'increase_counts')},
                ])
            case 'area':
                format_data = cls.get_area_data(kwargs['time_type'], sort_by)
                pie_params.extend([
                    {'pie_keys': ('area_name', 'total_counts')},
                    {'pie_keys': ('area_name', 'increase_counts')},
                ])
            case 'language':
                format_data = cls.get_language_data(kwargs['time_type'], sort_by)
                pie_params.extend([
                    {'pie_keys': ('language_cn_name', 'total_counts')},
                    {'pie_keys': ('language_cn_name', 'increase_counts')},
                ])
            case _:
                format_data = []
        if kwargs['export']:
            return export_xlsx(
                filename='register_users_distribution',
                data_list=format_data,
                export_headers=cls.get_export_headers(typ=kwargs['group_type'])
            )
        return dict(
            records=format_data,
            areas=areas,
            areas_mapping=areas_mapping,
            pie_chart_datas=PieChartUtil.get_pie_data(
                pie_params, format_data, True if kwargs['group_type'] == 'nation' else False)
        )

    @classmethod
    def get_area_data(cls, time_type, sort_by):
        _cache_cls = NationUserStatisticsCache
        cache = _cache_cls(_cache_cls.Type.REGISTER)
        if not cache.exists():
            update_nation_register_user_schedule.delay()
            return []
        records = json.loads(cache.read())[time_type]
        country_area_mapping = {
            m.info.iso_3: k
            for k, v in AREAS_MAPPING.items() for m in v
        }
        count_result = defaultdict(int)
        kyc_result = defaultdict(int)
        increase_result = defaultdict(int)
        for record in records:
            area = country_area_mapping.get(record['location_code'], '')
            if not area:
                continue
            count_result[area] += record['total_counts']
            kyc_result[area] += record['kyc_counts']
            increase_result[area] += record.get('increase_counts', 0)
        total = sum(count_result.values())
        kyc_total = sum(kyc_result.values())
        increase_total = sum(increase_result.values())
        total_data = {
            'area': '',
            'area_name': '全部',
            'total_counts': total,
            'kyc_counts': kyc_total,
            'increase_counts': increase_total,
            'rank': '-',
            'proportion': '100%',
            'kyc_proportion': amount_to_str(
                        (kyc_total / total if total else 0) * 100, 2) + '%',
            'increase_proportion': '100%',
        }
        def get_proportion(_data: Decimal | int, _total: Decimal | int) -> str:
            percent = _data / _total * 100 if _total else 0
            return f'{percent:.2f}%'

        area_data = []
        for area in AREAS_MAPPING:
            area_data.append(
                {
                    'area': area.name,
                    'area_name': area.value,
                    'total_counts': count_result[area],
                    'kyc_counts': kyc_result[area],
                    'increase_counts': increase_result[area],
                    'proportion': get_proportion(count_result[area], total),
                    'kyc_proportion': amount_to_str(
                        (kyc_result[area] / count_result[area] if count_result[area] else 0) * 100, 2) + '%',
                    'increase_proportion': get_proportion(increase_result[area], increase_total),
                }
            )
        area_data.sort(key=lambda x: x[sort_by], reverse=True)
        for i, x in enumerate(area_data, 1):
            x['rank'] = i
        area_data.insert(0, total_data)
        return area_data

    @classmethod
    def get_nation_data(cls, time_type, sort_by, area = None):
        _cache_cls = NationUserStatisticsCache
        cache = _cache_cls(_cache_cls.Type.REGISTER)
        if not cache.exists():
            update_nation_register_user_schedule.delay()
            return []
        records = json.loads(cache.read())[time_type]
        if area:
            c_codes = {i.info.iso_3 for i in AREAS_MAPPING.get(area, [])}
            records = [r for r in records if r['location_code'] in c_codes]
        total = sum(x['total_counts'] for x in records) if records else 0
        kyc_total = sum(x['kyc_counts'] for x in records) if records else 0
        increase_total = sum(x.get('increase_counts', 0) for x in records) if records else 0
        total_data = {
            'country_cn_name': '',
            'location_code': '-',
            'total_counts': total,
            'kyc_counts': kyc_total,
            'increase_counts': increase_total,
            'rank': '-',
            'proportion': '100%',
            'kyc_proportion': amount_to_str(
                        (kyc_total / total if total else 0) * 100, 2) + '%',
            'increase_proportion': '100%',
        }
        format_data = []
        for rank, record in enumerate(records):
            format_data.append(
                {
                    'country_cn_name': (c.cn_name if (c := get_country(
                        record['location_code'])) else '其他'),
                    'location_code': record['location_code'],
                    'total_counts': record['total_counts'],
                    'increase_counts': record.get('increase_counts', 0),
                    'rank': rank + 1,
                    'proportion': amount_to_str(
                        (record['total_counts'] / total) * 100, 2) + '%',
                    'kyc_proportion': amount_to_str(
                        (record['kyc_counts'] / record['total_counts'] \
                            if record['total_counts'] else 0) * 100, 2) + '%',
                    'increase_proportion': amount_to_str(
                        (record.get('increase_counts', 0) / increase_total \
                            if increase_total else 0) * 100, 2) + '%',
                }
            )
        format_data.sort(key=lambda x: x[sort_by], reverse=True)
        for i, x in enumerate(format_data, 1):
            x['rank'] = i
        format_data.insert(0, total_data)
        return format_data

    @classmethod
    def get_language_data(cls, time_type, sort_by):
        Cache = LanguageUserStatisticsCache
        cache = Cache(Cache.Type.REGISTER)
        if not cache.exists():
            update_language_register_user_schedule.delay()
            return []
        records = json.loads(cache.read())[time_type]
        records.sort(key=lambda x: x[sort_by], reverse=True)
        return records

    @classmethod
    def get_export_headers(cls, typ):
        if typ == 'nation':
            export_headers = (
                {"field": "rank", Language.ZH_HANS_CN: "排名"},
                {"field": "country_cn_name", Language.ZH_HANS_CN: "国家"},
                {"field": "total_counts", Language.ZH_HANS_CN: "注册人数"},
                {"field": "proportion", Language.ZH_HANS_CN: "占比"},
                {"field": "kyc_proportion", Language.ZH_HANS_CN: "KYC用户占比"},
                {"field": "increase_counts", Language.ZH_HANS_CN: "新增交易用户"},
                {"field": "increase_proportion", Language.ZH_HANS_CN: "占比"},
            )
        else:
            export_headers = (
                {"field": "rank", Language.ZH_HANS_CN: "排名"},
                {"field": "language_cn_name", Language.ZH_HANS_CN: "语言"},
                {"field": "total_counts", Language.ZH_HANS_CN: "注册人数"},
                {"field": "proportion", Language.ZH_HANS_CN: "占比"},
            )
        return export_headers


@ns.route("/margin-markets")
@respond_with_code
class MarginMarketResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            order=ex_fields.EnumField(
                ["count", "unpay_count", "usd_balance", "usd_unpay_amount", "real_leverage"],
                missing="usd_balance",
            ),
            order_type=ex_fields.EnumField(["desc", "asc"], missing="desc"),
            asset=fields.String,
        )
    )
    def get(cls, **kwargs):
        """ 币币-杠杆-杠杆资产统计-市场 """
        order_field, order_type = kwargs["order"], kwargs["order_type"]
        timestamp, data = MarginAssetStatistic.get_real_time_market_statistic_data()
        all_info = data.pop("__ALL__") if '__ALL__' in data else {}
        records = []
        for market, value in data.items():
            value["market"] = market
            records.append(value)

        all_item = {
            "market": "ALL",
            "count": "-",
            "unpay_count": "-",
            "real_leverage": 0,
            "usd_balance": 0,
            "usd_unpay_amount": 0,
        }
        all_item.update(all_info)
        records.sort(key=lambda x: Decimal(x[order_field]), reverse=True if order_type == "desc" else False)
        records.insert(0, all_item)
        return dict(
            records=records,
            timestamp=float(timestamp),
        )


@ns.route('/margin-assets')
@respond_with_code
class MarginAssetResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        order=ex_fields.EnumField(
            ["unpay_count", "unpay_amount", "usd_unpay_amount"], missing="usd_unpay_amount"),
        order_type=ex_fields.EnumField(["desc", "asc"], missing="desc")
    ))
    def get(cls, **kwargs):
        """币币-杠杆-杠杆资产统计-币种"""
        order_field, order_type = kwargs["order"], kwargs["order_type"]
        timestamp, data = \
            MarginAssetStatistic.get_real_time_total_balance()
        all_usd_balance = data.pop("usd_balance")
        all_usd_unpay_balance = data.pop("usd_unpay_amount")
        records = []
        for asset, value in data.items():
            value.pop("market_data")
            value["asset"] = asset
            records.append(value)

        all_item = {
            "asset": "ALL",
            "count": "-",
            "unpay_count": "-",
            "balance": "-",
            "unpay_amount": "-",
            "usd_balance": all_usd_balance,
            "usd_unpay_amount": all_usd_unpay_balance,
        }

        records.sort(key=lambda x: Decimal(x[order_field]), reverse=True if order_type == 'desc' else False)

        records.insert(0, all_item)

        return dict(
            records=records,
            timestamp=float(timestamp),
        )


@ns.route('/margin-asset')
@respond_with_code
class MarginAssetDetailResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        order=ex_fields.EnumField(
            ["unpay_count", "unpay_amount", "usd_unpay_amount"], missing="usd_unpay_amount"),
        order_type=ex_fields.EnumField(["desc", "asc"], missing="desc"),
        asset=fields.String
    ))
    def get(cls, **kwargs):
        """币币-杠杆-杠杆资产统计-币种详情"""
        order_field, order_type = kwargs["order"], kwargs["order_type"]
        timestamp, data = \
            MarginAssetStatistic.get_real_time_total_balance()
        data.pop("usd_balance"), data.pop("usd_unpay_amount")
        asset_data = data[kwargs["asset"]]
        market_data: list = asset_data["market_data"]
        asset_item = {
            "market_name": "ALL",
            "asset": "-",
            "count": "-",
            "unpay_count": "-",
            "balance": "-",
            "unpay_amount": "-",
            "usd_balance": asset_data["usd_balance"],
            "usd_unpay_amount": asset_data["usd_unpay_amount"],
        }
        for m in market_data:
            m["asset"] = kwargs["asset"]  # 插入币种列
        market_data.sort(key=lambda x: Decimal(x[order_field]), reverse=True if order_type == 'desc' else False)
        market_data.insert(0, asset_item)
        return dict(
            records=market_data,
            timestamp=float(timestamp),
            assets=list(data.keys())
        )


@ns.route('/loan-assets')
@respond_with_code
class LoanAssetResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String,
        in_margin=fields.Boolean(missing=None),
        in_pledge=fields.Boolean(missing=None),
    ))
    def get(cls, **kwargs):
        """统计-借贷资产"""
        return get_loan_asset_report(kwargs.get('asset', ''), kwargs['in_margin'], kwargs['in_pledge'])


@ns.route('/summary-assets')
@respond_with_code
class SummaryAssetsResource(Resource):

    account_types = {
        'spot': '现货',
        'margin': '杠杆',
        'investment': '理财',
        'perpetual': '合约',
        'pledge': '借贷',
    }

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String,
        account_type=fields.String
    ))
    def get(cls, **kwargs):
        """统计-实时资产"""
        asset = kwargs.get('asset')
        account_type = kwargs.get('account_type')
        if account_type and account_type not in cls.account_types:
            raise InvalidArgument
        if not account_type:
            account_type = 'all'
        cache = AssetSummaryStatisticCache()
        result = json.loads(cache.hget(account_type))
        assets = list_all_assets()
        items = []
        if asset:
            if item := result.get(asset):
                items.append(item)
        else:
            for asset in assets:
                if item := result.get(asset):
                    items.append(item)

        timestamp = cache.hget('timestamp')
        return {
            'assets': assets,
            'account_types': cls.account_types,
            'timestamp': int(timestamp),
            'data': items,
        }


@ns.route('/users')
@respond_with_code
class UsersResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):

        """统计-实时用户"""

        def format_level_data(data_map, type_user_count):
            format_result = [{
                'title': value['title'],
                'count': value['total_user'],
                'proportion': (
                        amount_to_str(value['total_user'] * 100 / type_user_count, 2) + '%'
                ) if type_user_count else '0%',
                'daily_increase': value['daily_increase'],
                'monthly_increase': value['monthly_increase']
            } for level, value in data_map.items()]
            return format_result

        result = RealtimeUserStatisticCache().read()
        data = json.loads(result)
        total_user = data["total_user"]

        def get_total_increase_data(key_word):

            data_map = json.loads(data[key_word])
            count = Decimal()
            daily_increase = Decimal()
            monthly_increase = Decimal()
            for v in data_map.values():
                count += Decimal(v['total_user'])
                daily_increase += Decimal(v['daily_increase'])
                monthly_increase += Decimal(v['monthly_increase'])
            return data_map, count, {
                "count": count,
                "proportion": f"{amount_to_str(count * 100 / total_user, 2)}%",
                "daily_increase": daily_increase,
                "monthly_increase": monthly_increase
            }

        proportion_func = lambda x: f"{amount_to_str(data[x] * 100 / total_user, 2)}%"
        simple_proportion_func = lambda x: f"{amount_to_str(Decimal(x) * 100 / total_user, 2)}%"

        vip_map, vip_count, vip_dict = get_total_increase_data("vip_map")
        ambassador_map, ambassador_count, ambassador_dict = get_total_increase_data("ambassador_map")

        spot_market_maker_map, spot_market_maker_count, spot_market_maker_dict =\
            get_total_increase_data("spot_market_maker_map")

        perpetual_market_maker_map, perpetual_market_maker_count, perpetual_market_maker_dict = \
            get_total_increase_data("perpetual_market_maker_map")
        user_count_data = defaultdict(lambda: dict(
            total_count=0,
            today_new_count=0,
            this_month_new_count=0,
        ))
        user_count_data.update(json.loads(data.get('user_count_data', '{}')))

        records = list()
        # 拼成list直接给前段展示
        records.append(
            {
                'title': '总注册用户', 'count': data['total_user'], 'proportion': '100%', 'daily_increase': data['daily_increase_user'],
                'monthly_increase': data['monthly_increase_user']
            }
        )
        _valid_user_data = user_count_data['valid_user']
        records.append(
            {
                'title': '总有效用户',
                'count': _valid_user_data['total_count'],
                'proportion': simple_proportion_func(_valid_user_data['total_count']),
                'daily_increase': _valid_user_data['today_new_count'],
                'monthly_increase': _valid_user_data['this_month_new_count']
            }
        )
        _sub_user_data = user_count_data['sub_user']
        records.append(
            {
                'title': '总子账户',
                'count': _sub_user_data['total_count'],
                'proportion': simple_proportion_func(_sub_user_data['total_count']),
                'daily_increase': _sub_user_data['today_new_count'],
                'monthly_increase': _sub_user_data['this_month_new_count']
            }
        )
        _total_app_data = user_count_data['app_user']
        records.append(
            {
                'title': '总APP用户',
                'count': _total_app_data['total_count'],
                'proportion': simple_proportion_func(_total_app_data['total_count']),
                'daily_increase': _total_app_data['today_new_count'],
                'monthly_increase': _total_app_data['this_month_new_count']
            }
        )

        records.append(
            {
                'title': '总2FA用户', 'count': data['user_bind_2fa'], 'proportion': proportion_func('user_bind_2fa'),
                'daily_increase': data['daily_increase_user_bind_2fa'], 'monthly_increase': data['monthly_increase_user_bind_2fa']
            }
        )
        records.append(
            {
                'title': '总认证用户', 'count': data['total_kyc'], 'proportion': proportion_func('total_kyc'),
                'daily_increase': data['daily_increase_kyc'], 'monthly_increase': data['monthly_increase_kyc']
            }
        )
        records.append(
            {
                'title': '总充值用户', 'count': data['total_deposit'], 'proportion': proportion_func('total_deposit'),
                'daily_increase': data['daily_increase_deposit'], 'monthly_increase': data['monthly_increase_deposit']
            }
        )
        records.append(
            {
                'title': '总交易用户', 'count': data['total_trade_user'], 'proportion': proportion_func('total_trade_user'),
                'daily_increase': data['daily_increase_trade_user'], 'monthly_increase': data['monthly_increase_trade_user']
            }
        )
        records.append(
            {
                'title': '总杠杆用户', 'count': data['total_margin_user'], 'proportion': proportion_func('total_margin_user'),
                'daily_increase': data['daily_increase_margin_user'], 'monthly_increase': data['monthly_increase_margin_user']
            }
        )
        records.append(
            {
                'title': '总币币用户', 'count': data['total_spot_user'], 'proportion': proportion_func('total_spot_user'),
                'daily_increase': data['daily_increase_spot_user'], 'monthly_increase': data['monthly_increase_spot_user']
            }
        )
        records.append(
            {
                'title': '总合约用户', 'count': data['total_perpetual_user'], 'proportion': proportion_func('total_perpetual_user'),
                'daily_increase': data['daily_increase_perpetual_user'], 'monthly_increase': data['monthly_increase_perpetual_user']
            }
        )
        records.append(
            {
                'title': '总理财用户', 'count': data['total_investment'], 'proportion': proportion_func('total_investment'),
                'daily_increase': data['daily_increase_investment'], 'monthly_increase': data['monthly_increase_investment']
            }
        )
        key_mapping = dict(
            fiat_user="总法币用户",
            p2p_user="总P2P用户",
            p2p_merchant="总P2P商家",
            exchange_user="总兑换用户",
            strategy_user="总策略交易",
            amm_user="总AMM用户",
            staking_user="总质押用户",
            pledge_user="总借贷用户",
        )
        for key, title in key_mapping.items():
            _key_data = user_count_data[key]
            records.append(
                {
                    'title': title,
                    'count': _key_data['total_count'],
                    'proportion': simple_proportion_func(_key_data['total_count']),
                    'daily_increase': _key_data['today_new_count'],
                    'monthly_increase': _key_data['this_month_new_count']
                }
            )

        records.append({'title': '总VIP用户', **vip_dict})
        records.append({'title': '总现货做市商', **spot_market_maker_dict})
        records.append({'title': '总合约做市商', **perpetual_market_maker_dict})
        records.append({'title': '大使用户', **ambassador_dict})
        _total_api_data = user_count_data['api_user']
        records.append(
            {
                'title': '总API用户',
                'count': _total_api_data['total_count'],
                'proportion': simple_proportion_func(_total_api_data['total_count']),
                'daily_increase': _total_api_data['today_new_count'],
                'monthly_increase': _total_api_data['this_month_new_count']
            }
        )

        vip_records = format_level_data(vip_map, vip_count)
        ambassador_records = format_level_data(ambassador_map, ambassador_count)
        perpetual_mm_records = format_level_data(perpetual_market_maker_map, perpetual_market_maker_count)
        spot_mm_records = format_level_data(spot_market_maker_map, spot_market_maker_count)
        if kwargs['export']:
            return cls.do_export_xlsx(
                records,
                vip_records,
                ambassador_records,
                perpetual_mm_records,
                spot_mm_records
            )
        return dict(
            timestamp=data['update_time'],
            records=records,
            vip_records=vip_records,
            ambassador_records=ambassador_records,
            perpetual_mm_records=perpetual_mm_records,
            spot_mm_records=spot_mm_records
        )

    @classmethod
    def do_export_xlsx(cls, records, vip_records, ambassador_records, perpetual_mm_records, spot_mm_records):
        import io
        from openpyxl import Workbook
        from flask import send_file

        table_headers = (
            {"field": "title", "name": "类型"},
            {"field": "count", "name": "数量"},
            {"field": "proportion", "name": "百分比"},
            {"field": "daily_increase", "name": "本日新增"},
            {"field": "monthly_increase", "name": "本月新增"},
        )
        table_body_mapping = {
            '实时用户': records,
            'VIP用户明细': vip_records,
            '现货做市商明细': spot_mm_records,
            '合约做市商明细': perpetual_mm_records,
            '大使用户明细': ambassador_records,
        }
        wb = Workbook()
        table_names = [header['name'] for header in table_headers]
        table_fields = [header['field'] for header in table_headers]
        for name, body in table_body_mapping.items():
            ws = wb.create_sheet(name)
            if name == '实时用户':
                wb.active = ws
            ws.append(table_names)
            for row in body:
                ws.append([row[field] for field in table_fields])

        stream = io.BytesIO()
        wb.save(stream)
        stream.seek(0)
        now_date = now().strftime("%Y%m%d")
        return send_file(
            stream,
            download_name=f'{now_date}_realtime_users.xlsx',
            as_attachment=True
        )


@ns.route('/deposit-withdraw-retention-rate')
@respond_with_code
class DepositWithdrawRetentionRateResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String
    ))
    def get(cls, **kwargs):
        """统计-净留存统计"""
        cache = DepositWithdrawRetentionRateCache()
        update_time = int(int(cache.ttl()) - cache.expired_seconds + now().timestamp())
        if asset := kwargs.get('asset'):
            cache_data = cache.hget(asset)
            cache_data = [dict(asset=asset, **json.loads(cache_data))] if cache_data else []
        else:
            cache_data = [dict(asset=asset, **json.loads(detail))
                        for asset, detail in cache.hgetall().items()]
            cache_data = sorted(cache_data, key=lambda x: AssetComparator(x['asset']))
        return dict(
            update_time=update_time,
            items=cache_data,
            assets=list_all_assets()
        )


@ns.route('/active-user')
@respond_with_code
class ActiveUserResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        time_type=ex_fields.EnumField(
            enum=['7d', '30d', '90d', '180d', '365d'], required=True),
        group_type=ex_fields.EnumField(
            enum=['nation', 'language', 'area'], missing='nation'
        ),
        area=ex_fields.EnumField(enum=AreaInfo),
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """
        统计-活跃用户-国籍分布
        统计-活跃用户-语言分布
        """
        parma_area = kwargs.get('area')
        areas_mapping = {k.name: [m.info.iso_3 for m in v]
                         for k, v in AREAS_MAPPING.items()}
        areas = {k.name: k.value for k in AREAS_MAPPING.keys()}
        pie_params = []
        match kwargs['group_type']:
            case 'nation':
                result = cls.get_nation_data(kwargs['time_type'], parma_area)
                pie_params.append({
                    'pie_keys': ('location', 'user_count'),
                })
            case 'area':
                result = cls.get_area_data(kwargs['time_type'])
                pie_params.append({
                    'pie_keys': ('area_name', 'user_count')
                })
            case 'language':
                result = cls.get_language_data(kwargs['time_type'])
                pie_params.append({
                    'pie_keys': ('language_cn_name', 'user_count')
                })
            case _:
                result = []

        if kwargs['export']:
            return export_xlsx(
                filename='active_users_distribution',
                data_list=result,
                export_headers=cls.get_export_headers(typ=kwargs['group_type'])
            )
        return dict(
            records=result,
            areas=areas,
            areas_mapping=areas_mapping,
            pie_chart_datas=PieChartUtil.get_pie_data(
                pie_params, result, True if kwargs['group_type'] == 'nation' else False)
        )

    @classmethod
    def get_nation_data(cls, time_type, area = None):
        _cache_cls = NationUserStatisticsCache
        cache = _cache_cls(_cache_cls.Type.ACTIVE)
        if not cache.exists():
            update_nation_active_user_schedule.delay()
            return []
        records = json.loads(cache.read())[time_type]
        if area:
            c_codes = {i.info.iso_3 for i in AREAS_MAPPING.get(area, [])}
            new_records = [r for r in records if r['location_code'] in c_codes]
            new_total = sum([r['user_count'] for r in new_records])
            new_kyc_total = sum([r.get('kyc_count', 0) for r in new_records])
            for r in new_records:
                r['proportion'] = '{:.2f}%'.format(r['user_count'] / new_total * 100)
                r['kyc_proportion'] = '{:.2f}%'.format(r.get('kyc_count', 0) / r['user_count'] * 100) if r['user_count'] else '0%',
            new_records.insert(0, dict(
                location_code='-',
                location='全部',
                user_count=new_total,
                proportion='100%',
                kyc_proportion='{:.2f}%'.format(new_kyc_total / new_total * 100) if new_total else '0%',
                rank='-'
            ))
            records = new_records
        return records

    @classmethod
    def get_area_data(cls, time_type):
        _cache_cls = NationUserStatisticsCache
        cache = _cache_cls(_cache_cls.Type.ACTIVE, _cache_cls.DataType.AREA)
        if not cache.exists():
            update_nation_active_user_schedule.delay()
            return []
        return json.loads(cache.read())[time_type]

    @classmethod
    def get_language_data(cls, time_type):
        Cache = LanguageUserStatisticsCache
        cache = Cache(Cache.Type.ACTIVE)
        if not cache.exists():
            update_language_active_user_schedule.delay()
            return []
        return json.loads(cache.read())[time_type]

    @classmethod
    def get_export_headers(cls, typ):
        if typ == 'nation':
            export_headers = (
                {"field": "rank", Language.ZH_HANS_CN: "排名"},
                {"field": "location", Language.ZH_HANS_CN: "国家"},
                {"field": "user_count", Language.ZH_HANS_CN: "活跃用户数"},
                {"field": "proportion", Language.ZH_HANS_CN: "占比"},
                {"field": "kyc_proportion", Language.ZH_HANS_CN: "KYC用户占比"},
            )
        else:
            export_headers = (
                {"field": "rank", Language.ZH_HANS_CN: "排名"},
                {"field": "language_cn_name", Language.ZH_HANS_CN: "语言"},
                {"field": "user_count", Language.ZH_HANS_CN: "注册人数"},
                {"field": "proportion", Language.ZH_HANS_CN: "占比"},
            )
        return export_headers


@ns.route('/asset-user')
@respond_with_code
class AssetUserResource(Resource):
    account_types = {
        'ALL': 'ALL',
        'SPOT': '现货',
        'MARGIN': '杠杆',
        'PERPETUAL': '合约',
        'PLEDGE': '借贷',
        'INVESTMENT': '理财',
        'STAKING': '质押',
        'AMM': 'AMM',
    }

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String,
        account_type=EnumField(account_types),
        sort_type=ex_fields.EnumField(['asc', 'desc']),
        sort_name=ex_fields.EnumField(['user_count', 'amount']),
        group_type=ex_fields.EnumField(
            enum=['nation', 'area', 'language'], missing='nation'
        ),
        area=ex_fields.EnumField(enum=AreaInfo),
        export=fields.Boolean(missing=False),

    ))
    def get(cls, **kwargs):
        """
        统计-资产用户-国籍分布
        统计-资产用户-语言分布
        """
        if not (asset := kwargs.get('asset')):
            asset = 'ALL'
        account_type = kwargs.get("account_type") or "ALL"
        parma_area = kwargs.get('area')
        areas_mapping = {k.name: [m.info.iso_3 for m in v]
                         for k, v in AREAS_MAPPING.items()}
        areas = {k.name: k.value for k in AREAS_MAPPING.keys()}
        result, result_0 = [], {}
        timestamp = None
        pie_params = []
        match kwargs['group_type']:
            case 'nation':
                cache = AssetUsersStatisticCache(account_type)
                timestamp = cache.hget('update_ts')
                data = cache.hget(asset)
                data = json.loads(data) if data else {}
                data = {k: v for k, v in data.items() if k}
                result, result_0 = cls.get_assemble_nation_data_and_result_0(data, parma_area)
                pie_params.extend([
                    {'pie_keys': ('location', 'user_count')},
                    {'pie_keys': ('location', 'amount')},
                ])
            case 'area':
                cache = AssetUsersStatisticCache(account_type)
                timestamp = cache.hget('update_ts')
                data = cache.hget(asset)
                data = json.loads(data) if data else {}
                data = {k: v for k, v in data.items() if k}
                result, result_0 = cls.get_assemble_area_data_and_result_0(data, asset)
                pie_params.extend([
                    {'pie_keys': ('area_name', 'user_count')},
                    {'pie_keys': ('area_name', 'amount')},
                ])
            case 'language':
                cache = AssetUsersStatisticForLanguageCache(account_type)
                timestamp = cache.hget('update_ts')
                data = cache.hget(asset)
                data = json.loads(data) if data else {}
                data = {k: v for k, v in data.items() if k}
                result, result_0 = cls.get_assemble_language_data_and_result_0(data)
                pie_params.extend([
                    {'pie_keys': ('language_cn_name', 'user_count')},
                    {'pie_keys': ('language_cn_name', 'amount')},
                ])

        if result:
            if kwargs.get('sort_name') == 'amount':
                sort_key = lambda x: Decimal(x['amount'])
            else:
                sort_key = lambda x: int(x['user_count'])
            reverse = kwargs.get('sort_type', 'desc') == 'desc'
            result.sort(key=sort_key, reverse=reverse)
            for i, x in enumerate(result, 1):
                x['rank'] = i

            result.insert(0, result_0)

            if kwargs['export']:
                return export_xlsx(
                    filename='asset_users_distribution',
                    data_list=result,
                    export_headers=cls.get_export_headers(typ=kwargs['group_type'])
                )
        return dict(
            records=result,
            assets=list_all_assets(),
            areas=areas,
            areas_mapping=areas_mapping,
            account_types=cls.account_types,
            timestamp=int(timestamp) if timestamp else None,
            pie_chart_datas=PieChartUtil.get_pie_data(
                pie_params, result, True if kwargs['group_type'] == 'nation' else False)
        )

    @classmethod
    def get_assemble_nation_data_and_result_0(cls, data, area):
        total_count, total_amount, total_balance, \
            total_kyc_user_count, total_kyc_user_amount, total_kyc_user_balance = cls._get_total_data(data)
        if area:
            c_codes = {i.info.iso_3 for i in AREAS_MAPPING.get(area, [])}
            data = {k:v for k, v in data.items() if k in c_codes}
            total_count = sum(v['user_count'] for v in data.values())
            total_amount = sum(Decimal(v['amount']) for v in data.values())
            total_balance = sum(Decimal(v.get('balance', 0)) for v in data.values())
            total_kyc_user_count = sum(v.get('kyc_user_count', 0) for v in data.values())
            total_kyc_user_amount = sum(Decimal(v.get('kyc_user_amount', 0)) for v in data.values())
        result = [dict(
            location_code=k,
            location=c.cn_name if (c := get_country(k)) else '其他',
            user_count=v['user_count'],
            user_count_proportion='{:.2f}%'.format(v['user_count'] / total_count * 100 if total_count else 0),
            amount=v['amount'],
            balance=v.get('balance') or '-',
            amount_proportion='{:.2f}%'.format(Decimal(v['amount']) / total_amount * 100 if total_amount else 0),
            kyc_user_proportion='{:.2f}%'.format(v['kyc_user_count'] / v['user_count'] * 100 \
                                                 if v['user_count'] else 0),
            kyc_user_amount_proportion='{:.2f}%'.format(Decimal(v['kyc_user_amount']) / Decimal(v['amount']) * 100 \
                                                        if Decimal(v['amount']) else 0),
        ) for k, v in data.items()]

        result_0 = dict(
                location_code='-',
                location='全部',
                user_count=total_count,
                user_count_proportion='100%',
                amount=total_amount,
                balance=total_balance or '-',
                amount_proportion='100%',
                kyc_user_proportion='{:.2f}%'.format(total_kyc_user_count / total_count * 100 \
                                                 if total_count else 0),
                kyc_user_amount_proportion='{:.2f}%'.format(Decimal(total_kyc_user_amount) / total_amount * 100 \
                                                        if total_amount else 0),
                rank='-',
            )
        return result, result_0

    @classmethod
    def get_assemble_area_data_and_result_0(cls, data, asset):
        country_area_mapping = {
            m.info.iso_3: k
            for k, v in AREAS_MAPPING.items() for m in v
        }
        area_result = defaultdict(lambda: defaultdict(Decimal))
        for location_code, item in data.items():
            area = country_area_mapping.get(location_code)
            if not area:
                continue
            area_result[area]["user_count"] += Decimal(item["user_count"])
            area_result[area]["amount"] += Decimal(item["amount"])
            area_result[area]["balance"] += Decimal(item.get("balance", 0))
            area_result[area]["kyc_user_count"] += Decimal(item.get("kyc_user_count", 0))
            area_result[area]["kyc_user_amount"] += Decimal(item.get("kyc_user_amount", 0))
        total_data = {
            'area': '',
            'area_name': '全部',
            'user_count': sum([v['user_count'] for v in area_result.values()]),
            'amount': sum([v['amount'] for v in area_result.values()]),
            'balance': sum([v['balance'] for v in area_result.values()]),
            'rank': '-',
            'proportion': '100%',
            'user_count_proportion': '100%',
            'amount_proportion': '100%',
            'kyc_user_proportion': '',
            'kyc_user_amount_proportion': '',
        }
        total_kyc_count = sum([v["kyc_user_count"] for v in area_result.values()])
        total_kyc_amount = sum([v["kyc_user_amount"] for v in area_result.values()])

        def get_proportion(_data: Decimal | int, _total: Decimal | int) -> str:
            percent = _data / _total * 100 if _total else 0
            return f'{percent:.2f}%'

        total_data['kyc_user_proportion'] = get_proportion(total_kyc_count, total_data['user_count'])
        total_data['kyc_user_amount_proportion'] = get_proportion(total_kyc_amount, total_data["amount"])
        if asset == 'ALL':
            total_count, total_amount, total_balance, \
                total_kyc_user_count, total_kyc_user_amount, total_kyc_user_balance = cls._get_total_data(data)
            total_data['user_count'] = total_count
            total_data['amount'] = total_amount
            total_data['balance'] = total_balance
            kyc_user_proportion = get_proportion(total_kyc_user_count, total_count)
            kyc_user_amount_proportion = get_proportion(total_kyc_user_amount, total_amount)
            total_data['kyc_user_proportion'] = kyc_user_proportion
            total_data['kyc_user_amount_proportion'] = kyc_user_amount_proportion
        area_data = []
        for area in AREAS_MAPPING:
            area_data.append({
                'area': area.name,
                'area_name': area.value,
                'user_count': area_result[area]['user_count'],
                'user_count_proportion':
                    get_proportion(area_result[area]['user_count'], total_data['user_count']),
                'amount': area_result[area]['amount'],
                'balance': area_result[area]['balance'],
                'amount_proportion': get_proportion(area_result[area]['amount'], total_data['amount']),
                'kyc_user_proportion':  get_proportion(area_result[area]['kyc_user_count'], area_result[area]["user_count"]),
                'kyc_user_amount_proportion':  get_proportion(area_result[area]['kyc_user_amount'], area_result[area]['amount']),
                }
            )

        return area_data, total_data

    @classmethod
    def get_assemble_language_data_and_result_0(cls, data):
        total_count, total_amount, total_balance, _, _, _ = cls._get_total_data(data)
        cn_names = get_cn_names()
        result = [dict(
            language_cn_name=cn_names.get(k) or '其他',
            language=k,
            user_count=v['user_count'],
            user_count_proportion='{:.2f}%'.format(v['user_count'] / total_count * 100 if total_count else 0),
            amount=v['amount'],
            balance=v.get('balance') or '-',
            amount_proportion='{:.2f}%'.format(Decimal(v['amount']) / total_amount * 100 if total_amount else 0)
        ) for k, v in data.items()]

        result_0 = dict(
            language_cn_name='-',
            language='全部',
            user_count=total_count,
            user_count_proportion='100%',
            amount=total_amount,
            balance=total_balance or '-',
            amount_proportion='100%',
            rank='-'
        )
        return result, result_0

    @classmethod
    def _get_total_data(cls, data):
        total_map = data.pop('total', {})
        if total_map:
            total_count = total_map.get('user_count', 0)
            total_amount = Decimal(total_map.get('amount', 0))
            total_balance = 0

            total_kyc_user_count = total_map.get('kyc_user_count', 0)
            total_kyc_user_amount = Decimal(total_map.get('kyc_user_amount', 0))
            total_kyc_user_balance = 0
        else:
            total_count = sum(v['user_count'] for v in data.values())
            total_amount = sum(Decimal(v['amount']) for v in data.values())
            total_balance = sum(Decimal(v['balance']) for v in data.values())

            total_kyc_user_count = sum(v.get('kyc_user_count', 0) for v in data.values())
            total_kyc_user_amount = sum(Decimal(v.get('kyc_user_amount', 0)) for v in data.values())
            total_kyc_user_balance = sum(Decimal(v.get('kyc_user_balance', 0)) for v in data.values())
        return total_count, total_amount, total_balance, \
                total_kyc_user_count, total_kyc_user_amount, total_kyc_user_balance

    @classmethod
    def get_export_headers(cls, typ):
        if typ == 'nation':
            export_headers = (
                {"field": "rank", Language.ZH_HANS_CN: "排名"},
                {"field": "location", Language.ZH_HANS_CN: "国家"},
                {"field": "user_count", Language.ZH_HANS_CN: "资产用户数"},
                {"field": "user_count_proportion", Language.ZH_HANS_CN: "用户占比"},
                {"field": "kyc_user_proportion", Language.ZH_HANS_CN: "KYC用户占比"},
                {"field": "amount", Language.ZH_HANS_CN: "总资产（USD）"},
                {"field": "amount_proportion", Language.ZH_HANS_CN: "资产占比"},
                {"field": "kyc_user_amount_proportion", Language.ZH_HANS_CN: "KYC用户资产占比"},
            )
        else:
            export_headers = (
                {"field": "rank", Language.ZH_HANS_CN: "排名"},
                {"field": "language", Language.ZH_HANS_CN: "语言"},
                {"field": "user_count", Language.ZH_HANS_CN: "资产用户数"},
                {"field": "user_count_proportion", Language.ZH_HANS_CN: "用户占比"},
                {"field": "amount", Language.ZH_HANS_CN: "总资产（USD）"},
                {"field": "amount_proportion", Language.ZH_HANS_CN: "资产占比"},
            )
        return export_headers


@ns.route('/user-trade')
@respond_with_code
class UserTradeResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        time_type=ex_fields.EnumField(
            enum=['7d', '30d', '90d', '180d', '365d'], required=True),
        asset=fields.String,
        market=fields.String,
        account_type=fields.String,
        sort_type=ex_fields.EnumField(
            enum=['asc', 'desc'], required=True),
        sort_name=ex_fields.EnumField(
            enum=['user_count', 'trade_value_usd', 'avg_trade_value_usd'], required=True),
        group_type=ex_fields.EnumField(
            enum=['nation', 'area', 'language'], missing='nation'
        ),
        area=ex_fields.EnumField(enum=AreaInfo),
        export=fields.Boolean(missing=False),

    ))
    def get(cls, **kwargs):
        """
        统计-交易用户-国籍分布
        统计-交易用户-语言分布
        """
        areas_mapping = {k.name: [m.info.iso_3 for m in v]
                         for k, v in AREAS_MAPPING.items()}
        areas = {k.name: k.value for k in AREAS_MAPPING.keys()}
        result, result_0 = [], {}
        pie_params = []
        match kwargs['group_type']:
            case 'nation':
                records = cls.fetch_records_by(kwargs, model=NationTradeUserReport)
                area = kwargs.get('area', None)
                result, result_0 = cls.get_assemble_nation_data_and_result_0(records, areas_mapping.get(area))
                pie_params.extend([
                    {'pie_keys': ('location', 'user_count')},
                    {'pie_keys': ('location', 'str_trade_value_usd')},
                ])
            case 'area':
                records = cls.fetch_records_by(kwargs, model=NationTradeUserReport)
                result, result_0 = cls.get_assemble_area_data_and_result_0(records, kwargs.get('asset', 'ALL'))
                pie_params.extend([
                    {'pie_keys': ('area_name', 'user_count')},
                    {'pie_keys': ('area_name', 'str_trade_value_usd')},
                ])
            case 'language':
                records = cls.fetch_records_by(kwargs, model=LanguageTradeUserReport)
                records = [item for item in records if item.language not in ('',)]
                result, result_0 = cls.get_assemble_language_data_and_result_0(records)
                pie_params.extend([
                    {'pie_keys': ('language_cn_name', 'user_count')},
                    {'pie_keys': ('language_cn_name', 'str_trade_value_usd')},
                ])

        sort_type = kwargs['sort_type']
        sort_name = kwargs['sort_name']
        _reversed = sort_type == 'desc'
        result.sort(key=lambda x: x[sort_name], reverse=_reversed)
        for i, r in enumerate(result, 1):
            r['rank'] = i
        result.insert(0, result_0)

        if kwargs['export']:
            return export_xlsx(
                filename='trade_users_distribution',
                data_list=result,
                export_headers=cls.get_export_headers(typ=kwargs['group_type'])
            )

        return dict(
            records=result,
            assets=list_all_assets(),
            markets=PerpetualMarketCache().get_market_list(),
            areas_mapping=areas_mapping,
            areas=areas,
            pie_chart_datas=PieChartUtil.get_pie_data(
                pie_params, result, True if kwargs['group_type'] == 'nation' else False)
        )

    @classmethod
    def fetch_records_by(cls, kwargs, model):
        asset, market, trade_type, time_range, country_coeds = cls.get_query_params(kwargs, model)
        q = model.query.filter(
            model.asset == asset,
            model.market == market,
            model.trade_type == trade_type,
            model.time_range == time_range
        )
        if country_coeds is not None:
            q = q.filter(model.country.in_(country_coeds))
        return q.all()

    @classmethod
    def get_query_params(cls, kwargs, model):
        asset = kwargs.get('asset')
        if not asset:
            asset = None
        market = kwargs.get('market')
        if not market:
            market = None
        trade_type = kwargs.get('account_type')
        time_range = model.Range.SEVEN_DAYS
        if kwargs['time_type'] == '30d':
            time_range = model.Range.THIRTY_DAYS
        elif kwargs['time_type'] == '90d':
            time_range = model.Range.NINETY_DAYS
        elif kwargs['time_type'] == '180d':
            time_range = model.Range.ONE_HUNDRED_EIGHTY_DAYS
        elif kwargs['time_type'] == '365d':
            time_range = model.Range.THREE_HUNDRED_SIXTY_FIVE_DAYS

        if trade_type == 'spot':
            trade_type = TradeType.SPOT
            market = None
        elif trade_type == 'perpetual':
            trade_type = TradeType.PERPETUAL
            asset = None
        elif trade_type == 'exchange':
            trade_type = TradeType.EXCHANGE
            asset, market = None, None
        else:
            trade_type = asset = market = None
        area = kwargs.get('area')
        if area:
            country_coeds = {i.info.iso_3 for i in AREAS_MAPPING.get(area, [])}
        else:
            country_coeds = None
        return asset, market, trade_type, time_range, country_coeds

    @classmethod
    def get_assemble_nation_data_and_result_0(cls, reports, filter_contries=None):
        if filter_contries:
            reports = [item for item in reports if item.country in filter_contries]

        result = []
        total_trade_user_count = sum(report.user_count for report in reports)
        total_trade_kyc_user_count = sum(report.kyc_user_count for report in reports)
        total_trade_value_usd = sum(report.trade_usd for report in reports)
        for report in reports:
            report: NationTradeUserReport
            avg_trade_value_usd = (report.trade_usd / report.user_count) if report.user_count else 0
            result.append(dict(
                location_code=report.country,
                location=c.cn_name if (c := get_country(report.country)) else '其他',
                user_count=report.user_count,
                kyc_user_count=report.kyc_user_count,
                trade_value_usd=report.trade_usd,
                avg_trade_value_usd=avg_trade_value_usd,
                str_avg_trade_value_usd=amount_to_str(avg_trade_value_usd, 2),
                str_trade_value_usd=amount_to_str(report.trade_usd, 2),
                proportion=amount_to_str(report.user_count / total_trade_user_count * 100, 4) + '%',
                kyc_user_proportion=amount_to_str((report.kyc_user_count / report.user_count if report.user_count else 0) * 100, 2) + '%',
                trade_value_proportion=amount_to_str(report.trade_usd / total_trade_value_usd * 100, 4) + '%',
            ))

        result_0 = dict(
            location_code='-',
            location='全部',
            user_count=total_trade_user_count,
            kyc_user_count=total_trade_kyc_user_count,
            str_avg_trade_value_usd=amount_to_str((total_trade_value_usd / total_trade_user_count) if total_trade_user_count else 0, 2),
            str_trade_value_usd=amount_to_str(total_trade_value_usd, 2),
            kyc_user_proportion=amount_to_str((total_trade_kyc_user_count / total_trade_user_count \
                                               if total_trade_user_count else 0) * 100, 2) + '%',
            proportion='100%',
            trade_value_proportion='100%',
            rank='-'
        )
        return result, result_0

    @classmethod
    def get_assemble_area_data_and_result_0(cls, reports, asset):

        country_area_mapping = {
            m.info.iso_3: k
            for k, v in AREAS_MAPPING.items() for m in v
        }
        area_result = defaultdict(lambda: defaultdict(Decimal))
        for item in reports:
            item: NationTradeUserReport
            location_code = item.country
            area = country_area_mapping.get(location_code)
            if not area:
                continue
            area_result[area]["user_count"] += Decimal(item.user_count)
            area_result[area]["kyc_user_count"] += Decimal(item.kyc_user_count)
            area_result[area]["trade_value_usd"] += Decimal(item.trade_usd)
        total_data = {
            'area': '',
            'area_name': '全部',
            'user_count': sum([v['user_count'] for v in area_result.values()]),
            'trade_value_usd': sum([v['trade_value_usd'] for v in area_result.values()]),
            'kyc_user_count': sum([v['kyc_user_count'] for v in area_result.values()]),
            'rank': '-',
            'proportion': '100%',
            'str_trade_value_usd': amount_to_str(sum([v['trade_value_usd'] for v in area_result.values()]), 2),
            'kyc_user_proportion': '',
            'trade_value_proportion': '100%',
        }

        def get_proportion(_data: Decimal | int, _total: Decimal | int) -> str:
            percent = _data / _total * 100 if _total else 0
            return f'{percent:.2f}%'

        total_data['kyc_user_proportion'] = get_proportion(total_data['kyc_user_count'], total_data['user_count'])
        if asset == 'ALL':
            total_trade_user_count = sum(report.user_count for report in reports)
            total_trade_kyc_user_count = sum(report.kyc_user_count for report in reports)
            total_trade_value_usd = sum(report.trade_usd for report in reports)
            total_data['user_count'] = total_trade_user_count
            total_data['kyc_user_count'] = total_trade_kyc_user_count
            total_data['trade_value_usd'] = total_trade_value_usd
            total_data['str_avg_trade_value_usd'] = amount_to_str(
                (total_trade_value_usd / total_trade_user_count) if total_trade_user_count else 0, 2),
            total_data['str_trade_value_usd'] = amount_to_str(total_trade_value_usd, 2)
            total_data['kyc_user_proportion'] = get_proportion(total_trade_kyc_user_count, total_trade_user_count)
        area_data = []
        for area in AREAS_MAPPING:
            avg_trade_value_usd = (
                (area_result[area]['trade_value_usd'] / area_result[area]['user_count'])
                if area_result[area]['user_count'] else 0
            )
            area_data.append({
                'area': area.name,
                'area_name': area.value,
                'user_count': area_result[area]['user_count'],
                'trade_value_usd': area_result[area]['trade_value_usd'],
                'avg_trade_value_usd': avg_trade_value_usd,
                'str_avg_trade_value_usd': amount_to_str(avg_trade_value_usd, 2),
                'str_trade_value_usd': amount_to_str(area_result[area]['trade_value_usd'], 2),
                'kyc_user_count': area_result[area]['kyc_user_count'],
                'proportion':
                    get_proportion(area_result[area]['user_count'], total_data['user_count']),
                'kyc_user_proportion':  get_proportion(area_result[area]['kyc_user_count'], area_result[area]['user_count']),
                'trade_value_proportion':  get_proportion(area_result[area]['trade_value_usd'], total_data['trade_value_usd']),
                }
            )

        return area_data, total_data

    @classmethod
    def get_assemble_language_data_and_result_0(cls, reports):
        result = []
        total_trade_user_count = sum(report.user_count for report in reports)
        total_trade_value_usd = sum(report.trade_usd for report in reports)
        cn_names = get_cn_names()
        for report in reports:
            report: LanguageTradeUserReport
            avg_trade_value_usd = (report.trade_usd / report.user_count) if report.user_count else 0
            result.append(dict(
                language_cn_name=cn_names.get(report.language.upper()) or '其他',
                language=report.language,
                user_count=report.user_count,
                trade_value_usd=report.trade_usd,
                avg_trade_value_usd=avg_trade_value_usd,
                str_avg_trade_value_usd=amount_to_str(avg_trade_value_usd, 2),
                str_trade_value_usd=amount_to_str(report.trade_usd, 2),
                proportion=amount_to_str(report.user_count / total_trade_user_count * 100, 4) + '%',
                trade_value_proportion=amount_to_str(report.trade_usd / total_trade_value_usd * 100, 4) + '%',
            ))
        result_0 = dict(
            language_cn_name='全部',
            language='-',
            user_count=total_trade_user_count,
            str_avg_trade_value_usd=amount_to_str(
                (total_trade_value_usd / total_trade_user_count) if total_trade_user_count else 0, 2),
            str_trade_value_usd=amount_to_str(total_trade_value_usd, 2),
            proportion='100%',
            trade_value_proportion='100%',
            rank='-'
        )
        return result, result_0

    @classmethod
    def get_export_headers(cls, typ):
        if typ == 'nation':
            export_headers = (
                {"field": "rank", Language.ZH_HANS_CN: "排名"},
                {"field": "location", Language.ZH_HANS_CN: "国家"},
                {"field": "user_count", Language.ZH_HANS_CN: "交易用户数"},
                {"field": "proportion", Language.ZH_HANS_CN: "占比"},
                {"field": "kyc_user_proportion", Language.ZH_HANS_CN: "KYC用户占比"},
                {"field": "str_trade_value_usd", Language.ZH_HANS_CN: "交易总额（USD）"},
                {"field": "trade_value_proportion", Language.ZH_HANS_CN: "交易额占比"},
                {"field": "str_avg_trade_value_usd", Language.ZH_HANS_CN: "人均交易额（USD）"},
            )
        elif typ == 'area':
            export_headers = (
                {"field": "rank", Language.ZH_HANS_CN: "排名"},
                {"field": "area", Language.ZH_HANS_CN: "地区"},
                {"field": "user_count", Language.ZH_HANS_CN: "交易用户数"},
                {"field": "proportion", Language.ZH_HANS_CN: "占比"},
                {"field": "kyc_user_proportion", Language.ZH_HANS_CN: "KYC用户占比"},
                {"field": "str_trade_value_usd", Language.ZH_HANS_CN: "交易总额（USD）"},
                {"field": "trade_value_proportion", Language.ZH_HANS_CN: "交易额占比"},
                {"field": "str_avg_trade_value_usd", Language.ZH_HANS_CN: "人均交易额（USD）"},
            )
        else:
            export_headers = (
                {"field": "rank", Language.ZH_HANS_CN: "排名"},
                {"field": "language_cn_name", Language.ZH_HANS_CN: "语言"},
                {"field": "user_count", Language.ZH_HANS_CN: "交易用户数"},
                {"field": "proportion", Language.ZH_HANS_CN: "占比"},
                {"field": "str_trade_value_usd", Language.ZH_HANS_CN: "交易总额（USD）"},
                {"field": "trade_value_proportion", Language.ZH_HANS_CN: "交易额占比"},
                {"field": "str_avg_trade_value_usd", Language.ZH_HANS_CN: "人均交易额（USD）"},
            )
        return export_headers


@ns.route("/trade-fee")
@respond_with_code
class UserTradeFee(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        time_type=ex_fields.EnumField(enum=['7d', '30d', '90d'], required=True),
        sort_type=ex_fields.EnumField(enum=['asc', 'desc'], required=True),
        sort_name=ex_fields.EnumField(enum=['spot', 'perpetual', 'all'], required=True),
        group_type=ex_fields.EnumField(
            enum=['nation', 'area', 'language'], missing='nation'
        ),
        area=ex_fields.EnumField(enum=AreaInfo),
        export=fields.Boolean(missing=False),

    ))
    def get(cls, **kwargs):
        """
        统计-收入统计-国籍分布
        统计-收入统计-语言分布
        """
        result = []
        areas_mapping = {k.name: [m.info.iso_3 for m in v]
                         for k, v in AREAS_MAPPING.items()}
        areas = {k.name: k.value for k in AREAS_MAPPING.keys()}
        pie_params = []
        match kwargs['group_type']:
            case 'nation':
                result = cls.assemble_nation_data_by(kwargs)
                pie_params.extend([
                    {'pie_keys': ('location', 'spot_fee')},
                    {'pie_keys': ('location', 'perpetual_fee')},
                    {'pie_keys': ('location', 'all_fee')},
                ])
            case 'area':
                result = cls.assemble_area_data_by(kwargs)
                pie_params.extend([
                    {'pie_keys': ('area_name', 'spot_fee')},
                    {'pie_keys': ('area_name', 'perpetual_fee')},
                    {'pie_keys': ('area_name', 'all_fee')},
                ])
            case 'language':
                result = cls.assemble_language_data_by(kwargs)
                pie_params.extend([
                    {'pie_keys': ('language_cn_name', 'spot_fee')},
                    {'pie_keys': ('language_cn_name', 'perpetual_fee')},
                    {'pie_keys': ('language_cn_name', 'all_fee')},
                ])

        if kwargs['export']:
            return export_xlsx(
                filename='trade_fee_users_distribution',
                data_list=result,
                export_headers=cls.get_export_headers(typ=kwargs['group_type'])
            )

        return dict(
            data=result,
            areas=areas,
            areas_mapping=areas_mapping,
            pie_chart_datas=PieChartUtil.get_pie_data(
                pie_params, result, True if kwargs['group_type'] == 'nation' else False)
        )

    @classmethod
    def assemble_nation_data_by(cls, kwargs):
        cache_cls = NationTradeFeeStatisticsCache
        cache = cache_cls()
        if not cache.exists():
            update_nation_trade_fee_schedule.delay()
            return []
        data = json.loads(cache.read())[kwargs['time_type']]
        system_dict, code_dict = data.get('system_dict', {}), data.get('code_dict', {})
        if area := kwargs.get('area'):
            country_codes = {i.info.iso_3 for i in AREAS_MAPPING.get(area, [])}
            code_dict = {k:v for k, v in code_dict.items() if k in country_codes}
            system_dict['spot'] = sum([Decimal(v.get('spot', 0)) for v in code_dict.values()])
            system_dict['perpetual'] = sum([Decimal(v.get('perpetual', 0)) for v in code_dict.values()])
        code_dict = cls._get_sorted_code_dict_by(kwargs, code_dict)
        result = cls._assemble_nation_result_data(system_dict, code_dict)
        return result

    @classmethod
    def assemble_area_data_by(cls, kwargs):
        cache_cls = NationTradeFeeStatisticsCache
        cache = cache_cls()
        if not cache.exists():
            update_nation_trade_fee_schedule.delay()
            return []
        data = json.loads(cache.read())[kwargs['time_type']]
        system_dict, code_dict = data.get('system_dict', {}), data.get('code_dict', {})
        code_dict = cls._get_sorted_code_dict_by(kwargs, code_dict)
        result = cls._assemble_area_result_data(system_dict, code_dict)
        if sort_name := kwargs['sort_name']:
            _map = {
                'spot': 'spot_fee',
                'perpetual': 'perpetual_fee'
            }
            if sort_name == 'all':
                lbd_func = lambda x: Decimal(x.get(_map['spot'], Decimal())) + Decimal(x.get(_map['perpetual'], Decimal()))
            else:
                lbd_func = lambda x: Decimal(x.get(_map[sort_name], Decimal()))
            result = sorted(
                result,
                key=lbd_func,
                reverse=kwargs["sort_type"] == "desc"
            )
        for index, _ in enumerate(result):
            if index == 0:
                continue
            result[index]["rank"] = index
        return result

    @classmethod
    def _get_sorted_code_dict_by(cls, kwargs, code_dict):
        if sort_name := kwargs['sort_name']:
            if sort_name == 'all':
                lambda_func = lambda x: Decimal(x[1].get('spot', Decimal())) + Decimal(x[1].get('perpetual', Decimal()))
            else:
                lambda_func = lambda x: Decimal(x[1][sort_name]) if sort_name in x[1].keys() else Decimal()
            code_dict = sorted(
                code_dict.items(),
                key=lambda_func,
                reverse=kwargs["sort_type"] == "desc"
            )
        return code_dict

    @classmethod
    def _assemble_nation_result_data(cls, system_dict, code_dict):
        all_spot_fee, all_perpetual = system_dict.get(UserTradeFeeSummary.System.SPOT.value, Decimal()), \
                                      system_dict.get(UserTradeFeeSummary.System.PERPETUAL.value, Decimal())

        all_spot_fee = Decimal(all_spot_fee)
        all_perpetual = Decimal(all_perpetual)
        result = [{
            "rank": "-",
            "location": "全部",
            "location_code": "-",
            "spot_fee": amount_to_str(all_spot_fee, 2),
            "spot_rate": "100%",
            "perpetual_fee": amount_to_str(all_perpetual, 2),
            "perpetual_rate": "100%",
            "all_fee": amount_to_str(all_spot_fee + all_perpetual, 2)
        }]

        for index, data in enumerate(code_dict):
            code, item = data
            spot, perpetual = item.get(UserTradeFeeSummary.System.SPOT.value, Decimal()), \
                              item.get(UserTradeFeeSummary.System.PERPETUAL.value, Decimal())
            spot = Decimal(spot)
            perpetual = Decimal(perpetual)
            result.append(dict(
                rank=index + 1,
                location=c.cn_name if (c := get_country(code)) else '其他',
                location_code=code or '',
                all_fee=amount_to_str(spot + perpetual, 2),
                spot_fee=amount_to_str(spot, 2),
                spot_rate=amount_to_str((spot / all_spot_fee) * 100, 2) + "%" if all_spot_fee else "0%",
                perpetual_fee=amount_to_str(perpetual, 2),
                perpetual_rate=amount_to_str((perpetual / all_perpetual) * 100, 2) + "%" if all_perpetual else "0%"
            ))
        return result

    @classmethod
    def _assemble_area_result_data(cls, system_dict, code_dict):
        all_spot_fee, all_perpetual = system_dict.get(UserTradeFeeSummary.System.SPOT.value, Decimal()), \
                                      system_dict.get(UserTradeFeeSummary.System.PERPETUAL.value, Decimal())
        all_spot_fee = Decimal(all_spot_fee)
        all_perpetual = Decimal(all_perpetual)
        total_result = {
            "rank": "-",
            "area": "",
            'area_name': '全部',
            "all_fee": quantize_amount(all_spot_fee + all_perpetual, PrecisionEnum.CASH_PLACES),
            "spot_fee": quantize_amount(all_spot_fee, PrecisionEnum.CASH_PLACES),
            "spot_rate": "100%",
            "perpetual_fee": quantize_amount(all_perpetual, PrecisionEnum.CASH_PLACES),
            "perpetual_rate": "100%"
        }
        country_area_mapping = {
            m.info.iso_3: k
            for k, v in AREAS_MAPPING.items() for m in v
        }
        area_result = defaultdict(lambda: defaultdict(Decimal))

        for index, data in enumerate(code_dict):
            code, item = data
            if code not in country_area_mapping:
                continue
            area = country_area_mapping[code]
            spot, perpetual = item.get(UserTradeFeeSummary.System.SPOT.value, Decimal()), \
                              item.get(UserTradeFeeSummary.System.PERPETUAL.value, Decimal())
            area_result[area]['spot_fee'] += Decimal(spot)
            area_result[area]['perpetual_fee'] += Decimal(perpetual)
            area_result[area]['all_fee'] += Decimal(spot) + Decimal(perpetual)
        result = []
        all_spot_fee = sum([v['spot_fee'] for v in area_result.values()])
        all_perpetual = sum([v['perpetual_fee'] for v in area_result.values()])
        for area in AREAS_MAPPING:
            result.append(dict(
                area=area.name,
                area_name=area.value,
                all_fee=amount_to_str(area_result[area]['all_fee'], 2),
                spot_fee=amount_to_str(area_result[area]['spot_fee'], 2),
                spot_rate=amount_to_str((area_result[area]['spot_fee'] / all_spot_fee) * 100, 2) + "%" if all_spot_fee else "0%",
                perpetual_fee=amount_to_str(area_result[area]['perpetual_fee'], 2),
                perpetual_rate=amount_to_str((area_result[area]['perpetual_fee'] / all_perpetual) * 100, 2) + "%" if all_perpetual else "0%"
            ))
        result.insert(0, total_result)
        return result

    @classmethod
    def assemble_language_data_by(cls, kwargs):
        cache_cls = LanguageTradeFeeStatisticsCache
        cache = cache_cls()
        if not cache.exists():
            update_language_trade_fee_schedule.delay()
            return []
        data = json.loads(cache.read())[kwargs['time_type']]
        system_dict, code_dict = data.get('system_dict', {}), data.get('code_dict', {})
        code_dict = cls._get_sorted_code_dict_by(kwargs, code_dict)
        result = cls._assemble_language_result_data(system_dict, code_dict)
        return result

    @classmethod
    def _assemble_language_result_data(cls, system_dict, code_dict):
        all_spot_fee, all_perpetual = system_dict.get(UserTradeFeeSummary.System.SPOT.value, Decimal()), \
                                      system_dict.get(UserTradeFeeSummary.System.PERPETUAL.value, Decimal())
        all_spot_fee = Decimal(all_spot_fee)
        all_perpetual = Decimal(all_perpetual)
        result = [{
            "rank": "-",
            "language_cn_name": "全部",
            "all_fee": amount_to_str(all_spot_fee + all_perpetual, 2),
            "spot_fee": amount_to_str(all_spot_fee, 2),
            "spot_rate": "100%",
            "perpetual_fee": amount_to_str(all_perpetual, 2),
            "perpetual_rate": "100%"
        }]
        cn_names = get_cn_names()
        for index, data in enumerate(code_dict):
            code, item = data
            spot, perpetual = item.get(UserTradeFeeSummary.System.SPOT.value, Decimal()), \
                              item.get(UserTradeFeeSummary.System.PERPETUAL.value, Decimal())
            spot = Decimal(spot)
            perpetual = Decimal(perpetual)
            language = (cn_names.get(code.upper()) or '其他') if code else '其他'
            result.append(dict(
                rank=index + 1,
                language_cn_name=language,
                all_fee=amount_to_str(spot + perpetual, 2),
                spot_fee=amount_to_str(spot, 2),
                spot_rate=amount_to_str((spot / all_spot_fee) * 100, 2) + "%" if all_spot_fee else "0%",
                perpetual_fee=amount_to_str(perpetual, 2),
                perpetual_rate=amount_to_str((perpetual / all_perpetual) * 100, 2) + "%" if all_perpetual else "0%"
            ))
        return result

    @classmethod
    def get_export_headers(cls, typ):
        if typ == 'nation':
            export_headers = (
                {"field": "rank", Language.ZH_HANS_CN: "排名"},
                {"field": "location", Language.ZH_HANS_CN: "国家"},
                {"field": "all_fee", Language.ZH_HANS_CN: "总手续费（USD）"},
                {"field": "spot_fee", Language.ZH_HANS_CN: "现货手续费（USD）"},
                {"field": "spot_rate", Language.ZH_HANS_CN: "占比"},
                {"field": "perpetual_fee", Language.ZH_HANS_CN: "合约手续费（USD）"},
                {"field": "perpetual_rate", Language.ZH_HANS_CN: "占比"},
            )
        else:
            export_headers = (
                {"field": "rank", Language.ZH_HANS_CN: "排名"},
                {"field": "language_cn_name", Language.ZH_HANS_CN: "语言"},
                {"field": "all_fee", Language.ZH_HANS_CN: "总手续费（USD）"},
                {"field": "spot_fee", Language.ZH_HANS_CN: "现货手续费（USD）"},
                {"field": "spot_rate", Language.ZH_HANS_CN: "占比"},
                {"field": "perpetual_fee", Language.ZH_HANS_CN: "合约手续费（USD）"},
                {"field": "perpetual_rate", Language.ZH_HANS_CN: "占比"},
            )
        return export_headers


@ns.route('/referral-rank')
@respond_with_code
class ReferralRankResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        time_type=ex_fields.EnumField(
            enum=['7d', '30d', '90d'], required=True),
        export=fields.Boolean,
    ))
    def get(cls, **kwargs):
        """统计-邀请排行"""
        time_type = kwargs['time_type']
        end_time = today()
        if time_type == '7d':
            start_time = end_time - timedelta(days=7)
        elif time_type == '30d':
            start_time = end_time - timedelta(days=30)
        elif time_type == '90d':
            start_time = end_time - timedelta(days=90)

        records = ReferralHistory.query.filter(
            ReferralHistory.created_at >= start_time,
            ReferralHistory.created_at <= end_time
        ).group_by(
            ReferralHistory.referrer_id
        ).with_entities(
            ReferralHistory.referrer_id,
            func.count('*')
        ).all()
        total_referrer = len(records)
        total = sum(x for _,x in records)
        result = [dict(
            user_id=x[0],
            count=x[1],
            proportion='{:.2f}%'.format(x[1] / total * 100)
        )for x in records]
        result.sort(key=lambda x: x['count'], reverse=True)
        result = result[:200]
        user_ids = [item['user_id'] for item in result]
        user_email_map = {item.id: item.email for item in User.query.filter(User.id.in_(user_ids))}

        ambassador_data = Ambassador.query.filter(
            Ambassador.user_id.in_(user_ids),
            Ambassador.status == Ambassador.Status.VALID).all()

        ambassador_ids = {i.user_id for i in ambassador_data}

        for i, x in enumerate(result, 1):
            x['rank'] = i
            x['email'] = user_email_map[x['user_id']]
            x['is_ambassador'] = x['user_id'] in ambassador_ids

        if kwargs.get('export'):
            export_headers = (
                {"field": "rank", Language.ZH_HANS_CN: "排名"},
                {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
                {"field": "email", Language.ZH_HANS_CN: "邮箱"},
                {"field": "count", Language.ZH_HANS_CN: "被邀请用户数"},
                {"field": "proportion", Language.ZH_HANS_CN: "被邀请用户数占比"},
                {"field": "is_ambassador", Language.ZH_HANS_CN: "是否为大使"},
            )

            return export_xlsx(
                filename='ambassador_agent_list',
                data_list=result,
                export_headers=export_headers
            )

        return dict(
            records=result,
            total_referrer=total_referrer,
            total_referree=total,
        )


@ns.route('/trade-ratio')
@respond_with_code
class TradeRatioResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        time_type=ex_fields.EnumField(
            enum=['7d', '30d', '90d'], required=True),
        trade_type=ex_fields.EnumField(
            enum=['spot', 'perpetual'], required=True),
    ))
    def get(cls, **kwargs):
        """统计-交易成交比例"""

        def get_user_format_data(records, vip_level_map):

            vip_level_trade_map = defaultdict(Decimal)

            for user_id, item in records.items():
                if user_id in vip_level_map:
                    level = vip_level_map[user_id]
                    vip_level_trade_map[level] += item.trade_amount
                else:
                    # vip表没有的用户
                    level = 0
                    vip_level_trade_map[level] += item.trade_amount
            all_vip_user_deal_amount = sum(vip_level_trade_map.values())
            all_taker_amount = sum([item.taker_amount for item in records.values()])
            all_maker_amount = sum([item.maker_amount for item in records.values()])

            return dict(
                taker_ratio=amount_to_str((all_taker_amount / (all_taker_amount + all_maker_amount) * 100), 2) + "%",
                maker_ratio=amount_to_str((all_maker_amount / (all_taker_amount + all_maker_amount) * 100), 2) + "%",
                all_taker_amount=amount_to_str((all_taker_amount), 2),
                all_maker_amount=amount_to_str((all_maker_amount), 2),
                level0_ratio=amount_to_str((vip_level_trade_map[0] / all_vip_user_deal_amount) * 100, 2) + "%",
                level1_ratio=amount_to_str((vip_level_trade_map[1] / all_vip_user_deal_amount) * 100, 2) + "%",
                level2_ratio=amount_to_str((vip_level_trade_map[2] / all_vip_user_deal_amount) * 100, 2) + "%",
                level3_ratio=amount_to_str((vip_level_trade_map[3] / all_vip_user_deal_amount) * 100, 2) + "%",
                level4_ratio=amount_to_str((vip_level_trade_map[4] / all_vip_user_deal_amount) * 100, 2) + "%",
                level5_ratio=amount_to_str((vip_level_trade_map[5] / all_vip_user_deal_amount) * 100, 2) + "%",
            )

        time_type = kwargs['time_type']
        trade_type = kwargs['trade_type']
        end_time = today()
        if time_type == '7d':
            start_time = end_time - timedelta(days=7)
        elif time_type == '30d':
            start_time = end_time - timedelta(days=30)
        elif time_type == '90d':
            start_time = end_time - timedelta(days=90)

        user_query = User.query.filter(User.user_type.in_([
            User.UserType.INTERNAL_MAKER,
            User.UserType.EXTERNAL_MAKER,
            User.UserType.EXTERNAL_SPOT_MAKER,
            User.UserType.EXTERNAL_CONTRACT_MAKER,
            User.UserType.EXTERNAL,
        ])).all()
        user_ids = [item.id for item in user_query]

        records = UserTradeSummary.query.filter(
            UserTradeSummary.report_date >= start_time,
            UserTradeSummary.report_date <= end_time,
            UserTradeSummary.system == trade_type,
        ).group_by(
            UserTradeSummary.user_id,
        ).with_entities(
            UserTradeSummary.user_id,
            func.sum(UserTradeSummary.trade_amount).label('trade_amount'),
            func.sum(UserTradeSummary.taker_amount).label('taker_amount'),
            func.sum(UserTradeSummary.maker_amount).label('maker_amount'),
        ).all()

        vip_user_query = VipUser.query.with_entities(VipUser.user_id, VipUser.level).all()
        vip_level_map = {item.user_id: item.level for item in vip_user_query}
        maker_user_query = MarketMaker.query.filter(MarketMaker.maker_type == trade_type).all()
        maker_level_map = {item.user_id: item.level for item in maker_user_query}
        normal_user_records = {item.user_id: item for item in records if item.user_id not in user_ids}
        maker_user_records = {item.user_id: item for item in records if item.user_id in user_ids}
        normal_user_data = get_user_format_data(normal_user_records, vip_level_map)
        maker_user_data = get_user_format_data(maker_user_records, maker_level_map)
        return dict(
            normal_user_data=[normal_user_data],
            maker_user_data=[maker_user_data],
        )


@ns.route('/perpetual-trade-rank')
@respond_with_code
class PerpetualRankResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        time_type=fields.String(required=True),
        sort_name=fields.String(required=True),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """统计-永续市场交易增长排名"""
        time_type = kwargs['time_type']
        sort_name = kwargs['sort_name']
        page = kwargs['page']
        limit = kwargs['limit']
        _delta = int(time_type)
        end_time = today()
        start_time = end_time - timedelta(days=_delta)

        records = PerpetualAssetTradeSummary.query.filter(
            PerpetualAssetTradeSummary.report_date >= start_time,
            PerpetualAssetTradeSummary.report_date <= end_time,
        ).with_entities(
            PerpetualAssetTradeSummary.market,
            func.sum(PerpetualAssetTradeSummary.trade_amount).label('trade_amount'),
        ).group_by(
            PerpetualAssetTradeSummary.market
        ).all()

        market_records = PerpetualAssetTradeSummary.query.filter(
            PerpetualAssetTradeSummary.report_date == start_time,
        ).with_entities(
            PerpetualAssetTradeSummary.market,
            func.sum(PerpetualAssetTradeSummary.trade_amount).label('trade_amount'),
        ).group_by(
            PerpetualAssetTradeSummary.market
        ).all()

        trade_amount_map = defaultdict(lambda: {
            'trade_amount': Decimal(),
        })
        for item in records:
            trade_amount_map[item.market]['trade_amount'] = item.trade_amount / _delta
        for item in market_records:
            trade_amount_map[item.market]['trade_amount'] -= item.trade_amount
            trade_amount_map[item.market]['growth_rate'] = (
                trade_amount_map[item.market]['trade_amount'] / item.trade_amount * 100 if item.trade_amount else 0
            )

        all_reports = DailyPerpetualMarketReport.query.filter(
            DailyPerpetualMarketReport.report_date >= start_time,
            DailyPerpetualMarketReport.report_date <= end_time,
        ).group_by(DailyPerpetualMarketReport.market).with_entities(
            DailyPerpetualMarketReport.market,
            func.sum(DailyPerpetualMarketReport.deal_user_count).label('deal_user_count'),
        ).all()
        user_count_map = dict(all_reports)
        first_day_reports = DailyPerpetualMarketReport.query.filter(
            DailyPerpetualMarketReport.report_date == start_time
        ).group_by(DailyPerpetualMarketReport.market).with_entities(
            DailyPerpetualMarketReport.market,
            func.sum(DailyPerpetualMarketReport.deal_user_count).label('deal_user_count'),
        ).all()
        first_day_user_count_map = dict(first_day_reports)
        user_map = defaultdict(lambda: defaultdict(Decimal))
        for market, user_count in user_count_map.items():
            user_map[market]['user_count'] = user_count/_delta
        for market, user_count in first_day_user_count_map.items():
            user_map[market]['user_count'] -= user_count
            user_map[market]['growth_rate'] = user_map[market]['user_count'] / user_count * 100 if user_count else 0

        result = [{
            'market': k,
            'trade_growth': quantize_amount(v['trade_amount'], 2),
            'growth_rate': v['growth_rate'],
            'str_growth_rate': f'{quantize_amount(v["growth_rate"], 2)}%',
            'deal_user_growth': quantize_amount(user_map[k]['user_count'], 2),
            'deal_user_growth_rate': quantize_amount(user_map[k]['growth_rate'], 2),
        } for k, v in trade_amount_map.items() if 'growth_rate' in v]
        result.sort(key=lambda x: x[sort_name], reverse=True)
        for idx, item in enumerate(result):
            item['rank'] = idx + 1

        return dict(
            records=result[(page-1)*limit:page*limit],
            total=len(result),
        )


@ns.route('/spot-asset-trade-rank')
@respond_with_code
class SpotAssetTradeRankResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        time_type=fields.String(required=True),
        sort_name=fields.String(required=True),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """统计-币币资产交易增长排名"""
        time_type = kwargs['time_type']
        sort_name = kwargs['sort_name']
        page = kwargs['page']
        limit = kwargs['limit']
        delta_ = int(time_type)
        end_time = today()
        start_time = end_time - timedelta(days=delta_)

        records = SpotAssetTradeSummary.query.filter(
            SpotAssetTradeSummary.report_date >= start_time,
            SpotAssetTradeSummary.report_date <= end_time,
        ).with_entities(
            SpotAssetTradeSummary.stock_asset,
            func.sum(SpotAssetTradeSummary.trade_amount).label('trade_amount'),
        ).group_by(
            SpotAssetTradeSummary.stock_asset
        ).all()

        asset_records = SpotAssetTradeSummary.query.filter(
            SpotAssetTradeSummary.report_date == start_time,
        ).with_entities(
            SpotAssetTradeSummary.stock_asset,
            func.sum(SpotAssetTradeSummary.trade_amount).label('trade_amount'),
        ).group_by(
            SpotAssetTradeSummary.stock_asset
        ).all()
        trade_amount_map = defaultdict(lambda: {
            'trade_amount': Decimal(),
        })
        for item in records:
            trade_amount_map[item.stock_asset]['trade_amount'] = item.trade_amount/delta_
        for item in asset_records:
            trade_amount_map[item.stock_asset]['trade_amount'] -= item.trade_amount
            trade_amount_map[item.stock_asset]['growth_rate'] = trade_amount_map[item.stock_asset]['trade_amount']/item.trade_amount*100

        all_coin_reports = DailySpotTradeCoinReport.query.filter(
            DailySpotTradeCoinReport.report_date >= start_time,
            DailySpotTradeCoinReport.report_date <= end_time,
        ).group_by(DailySpotTradeCoinReport.coin).with_entities(
            DailySpotTradeCoinReport.coin,
            func.sum(DailySpotTradeCoinReport.deal_user_count).label('deal_user_count'),
        ).all()

        first_day_coin_reports = DailySpotTradeCoinReport.query.filter(
            DailySpotTradeCoinReport.report_date == start_time,
        ).group_by(DailySpotTradeCoinReport.coin).with_entities(
            DailySpotTradeCoinReport.coin,
            func.sum(DailySpotTradeCoinReport.deal_user_count).label('deal_user_count'),
        ).all()

        all_business_reports = DailyAssetBusinessReport.query.filter(
            DailyAssetBusinessReport.report_date >= start_time,
            DailyAssetBusinessReport.report_date <= end_time,
        ).group_by(DailyAssetBusinessReport.asset).with_entities(
            DailyAssetBusinessReport.asset,
            func.sum(DailyAssetBusinessReport.asset_user_count).label('asset_user_count'),
        ).all()
        asset_count_map = dict(all_business_reports)
        first_day_business_reports = DailyAssetBusinessReport.query.filter(
            DailyAssetBusinessReport.report_date == start_time
        ).group_by(DailyAssetBusinessReport.asset).with_entities(
            DailyAssetBusinessReport.asset,
            func.sum(DailyAssetBusinessReport.asset_user_count).label('asset_user_count'),
        ).all()
        first_day_asset_count_map = dict(first_day_business_reports)

        asset_user_map = defaultdict(lambda: defaultdict(Decimal))
        for item in all_coin_reports:
            asset = item.coin
            asset_user_map[asset]['asset_user_count'] = asset_count_map.get(asset, 0) / delta_
            asset_user_map[asset]['spot_user_count'] = item.deal_user_count / delta_
        for item in first_day_coin_reports:
            asset = item.coin
            asset_user_count = first_day_asset_count_map.get(asset, 0)
            asset_user_map[asset]['asset_user_count'] -= asset_user_count
            asset_user_map[asset]['spot_user_count'] -= item.deal_user_count
            asset_user_map[asset]['spot_growth_rate'] = (
                asset_user_map[asset]['spot_user_count'] / item.deal_user_count * 100 if item.deal_user_count else 0
            )
            asset_user_map[asset]['asset_growth_rate'] = (
                asset_user_map[asset]['asset_user_count'] / asset_user_count * 100 if asset_user_count else 0
            )

        result = [{
            'asset': k,
            'trade_growth': quantize_amount(v['trade_amount'], 2),
            'growth_rate': v['growth_rate'],
            'str_growth_rate': f'{quantize_amount(v["growth_rate"], 2)}%',
            'deal_user_growth_rate': quantize_amount(asset_user_map[k]['spot_growth_rate'], 2),
            'asset_user_growth_rate': quantize_amount(asset_user_map[k]['asset_growth_rate'], 2),
            'deal_user_growth': quantize_amount(asset_user_map[k]['spot_user_count'], 2),
            'asset_user_growth': quantize_amount(asset_user_map[k]['asset_user_count'], 2),
        } for k, v in trade_amount_map.items() if 'growth_rate' in v]
        result.sort(key=lambda x: x[sort_name], reverse=True)
        for idx, item in enumerate(result):
            item['rank'] = idx + 1

        return dict(
            records=result[(page - 1) * limit:page * limit],
            total=len(result),
        )


@ns.route('/active-users')
@respond_with_code
class ActiveUserStatisticResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        type=ex_fields.EnumField(enum=UserActivenessReport.Type, required=True),
        range=fields.Integer(required=True)
    ))
    def get(cls, **kwargs):
        """统计-近30天活跃用户"""
        cache = ActiveUserStatisticCache()
        statistics = cache.read()
        data = json.loads(statistics['data'])
        timestamp = float(statistics['timestamp'])
        days_map = {'1': '1天', '1-3': '1-3天', '3-7': '3-7天',
                     '7-15': '7-15天', '15': '15天以上'}
        for record in data:
            record['active_days'] = days_map[record['active_days']]
        sum_record = {
            'active_days': '合计',
            'active_users': sum(item['active_users'] for item in data),
            'active_user_percentage': 1,
            'trade_users': sum(item['trade_users'] for item in data),
            'trade_user_percentage': 1,
            'spot_users': sum(item['spot_users'] for item in data),
            'spot_user_percentage': 1,
            'perpetual_users': sum(item['perpetual_users'] for item in data),
            'perpetual_user_percentage': 1,
        }
        data.append(sum_record)

        # 图表数据:
        chart_type = kwargs['type']
        start_time = timestamp_to_date(timestamp)
        active_reports = UserActivenessReport.query.filter(
            UserActivenessReport.report_date < start_time,
            UserActivenessReport.report_date >= start_time - timedelta(days=kwargs['range']),
            UserActivenessReport.type == chart_type
        ).all()

        result_map = defaultdict(dict)
        for report in active_reports:
            result_map[report.time_range][report.report_date] = report.count

        res = []
        for range_ in UserActivenessReport.Range:
            map_ = result_map[range_]
            curr = []
            for time_, val in map_.items():
                curr.append((datetime_to_time(time_) * 1000, val))
            curr.sort(key=lambda x: x[0])
            res.append(curr)
        return dict(
            records=data,
            timestamp=timestamp,
            active_types={item.name: item.value for item in UserActivenessReport.Type},
            chart_data=dict(
                data=res,
            )
        )


@ns.route('/real-depth')
@respond_with_code
class RealtimeDepthResource(Resource):
    caches = {
        'CoinEx': PerpetualStageDepthCache,
        'Binance': BinancePerpetualStageDepthCache,
        'OKX': OKXPerpetualStageDepthCache,
        'Huobi': HuobiPerpetualStageDepthCache,
        'Bybit': BybitPerpetualStageDepthCache,
        'Kucoin': KucoinPerpetualStageDepthCache,
        'Gate': GatePerpetualStageDepthCache,
        'Bitget': BitgetPerpetualStageDepthCache,
    }

    export_headers = (
        {"field": "market", Language.ZH_HANS_CN: "市场"},
        {"field": "exchange", Language.ZH_HANS_CN: "交易所"},
        {"field": "0.002", Language.ZH_HANS_CN: "±0.2%深度"},
        {"field": "0.005", Language.ZH_HANS_CN: "±0.5%深度"},
        {"field": "0.01", Language.ZH_HANS_CN: "±1%深度"},
        {"field": "0.02", Language.ZH_HANS_CN: "±2%深度"},
        {"field": "0.05", Language.ZH_HANS_CN: "±5%深度"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        trade_type=ex_fields.EnumField(enum=['spot', 'perpetual'], required=True),
        market=fields.String,
        exchange=fields.String,
        export=fields.Boolean(missing=False),
        sort_type=ex_fields.EnumField(enum=['asc', 'desc'], required=True),
        sort_name=ex_fields.EnumField(enum=list(map(amount_to_str, RealTimeDepthCalculator.stage_list)),
                                      missing=amount_to_str(RealTimeDepthCalculator.stage_list[0]))
    ))
    def get(cls, **kwargs):
        """实时深度-现货合约"""
        trade_type = kwargs["trade_type"]
        if trade_type == "spot":
            return cls._get_spot_depth_data(kwargs)
        else:
            return cls._get_perpetual_depth_data(kwargs)

    @classmethod
    def _get_spot_depth_data(cls, kwargs):
        cache_data = SpotStageDepthCache().read()
        data = json.loads(cache_data["data"])
        market_list = list(data.keys())
        update_time = cache_data["update_time"]
        data = json.loads(cache_data["data"])
        result = []
        if (market := kwargs.get("market")) and market in market_list:
            data = {market: data[market]}
        for market, delta_data in data.items():
            market_data = {"market": market}
            for key, value in delta_data.items():
                market_data.update({
                    key: Decimal(value)
                })
            result.append(market_data)
        result.sort(key=lambda x: Decimal(x[kwargs["sort_name"] or amount_to_str(RealTimeDepthCalculator.stage_list[-1])]),
                    reverse=True if kwargs["sort_type"] == 'desc' else False)
        return dict(
            timestamp=update_time,
            items=result,
            markets=market_list,
            stage_list={str(i): f"±{format_percent(i)}深度" for i in RealTimeDepthCalculator.stage_list}
        )

    @classmethod
    def _get_perpetual_depth_data(cls, kwargs):
        exchange = kwargs.get('exchange')
        targets = {}
        if exchange:
            targets[exchange] = cls.caches[exchange]
        else:
            targets = cls.caches.copy()

        update_time = None
        market_set = set()
        result = []
        for exchange, cache_cls in targets.items():
            cache_data = cache_cls().read()
            data = json.loads(cache_data["data"])
            market_list = list(data.keys())
            market_set.update(market_list)
            update_time = cache_data["update_time"]
            data = json.loads(cache_data["data"])
            if market := kwargs.get("market"):
                if market in market_list:
                    data = {market: data[market]}
                else:
                    continue
            for market, delta_data in data.items():
                market_data = {"market": market, "exchange": exchange}
                for key, value in delta_data.items():
                    market_data.update({
                        key: Decimal(value)
                    })
                result.append(market_data)
        result.sort(key=lambda x: Decimal(x[kwargs["sort_name"] or amount_to_str(RealTimeDepthCalculator.stage_list[-1])]),
                    reverse=True if kwargs["sort_type"] == 'desc' else False)
        for r in result:
            #  格式 r: {"market": market, "exchange": exchange, '0.002': xxx, '0.001': xxx, ...}
            for i, stage in enumerate(RealTimeDepthCalculator.stage_list):
                if stage == RealTimeDepthCalculator.stage_list[-1]:
                    continue
                k = amount_to_str(stage)
                v = r[k]

                equal_to_later_values = True
                j = i + 1
                for idx in range(j, len(RealTimeDepthCalculator.stage_list)):
                    k1 = amount_to_str(RealTimeDepthCalculator.stage_list[idx])
                    v1 = r[k1]
                    if v != v1:
                        equal_to_later_values = False
                        break
                if not equal_to_later_values:
                    continue

                for idx in range(j, len(RealTimeDepthCalculator.stage_list)):
                    _k = amount_to_str(RealTimeDepthCalculator.stage_list[idx])
                    r[_k] = '/'
                break

        if kwargs.get('export'):
            return export_xlsx(
                filename='real_time_depth_report',
                data_list=result,
                export_headers=cls.export_headers
            )
        return dict(
            timestamp=update_time,
            items=result,
            exchanges=list(cls.caches.keys()),
            markets=list(market_set),
            stage_list={str(i): f"±{format_percent(i)}深度" for i in RealTimeDepthCalculator.stage_list}
        )


@ns.route('/perpetual-users')
@respond_with_code
class PerpetualPositionUserResource(Resource):

    @classmethod
    def get(cls, **kwargs):
        """报表-合约报表-合约用户统计"""
        data = PerpetualUserAnalysisCache().read()
        if not data:
            return {}
        data = json.loads(data)
        for item in data['items']:
            item['average_position_time'] = get_time_display_str(item['average_position_time'])
            if item['duration'] in time_range_display_map:
                item['duration'] = time_range_display_map[item['duration']]
        return data


@ns.route('/no-active-users')
@respond_with_code
class NoActiveUserResource(Resource):

    @classmethod
    def get(cls, **kwargs):
        """统计-不活跃用户统计"""
        cache = NoActiveUserStatisticCache()
        cache_data = cache.read()
        return {
            "interval_map": cls.format_interval(cache.interval_list),
            **cache_data
        }

    @classmethod
    def format_interval(cls, interval_list: list):
        interval_map = {}
        for idx, v in enumerate(interval_list):
            if v == interval_list[0]:
                _str = f"X <= {v}"
            else:
                _str = f"{interval_list[idx-1]} < X <= {v}"
            interval_map[v] = _str
        interval_map[-1] = f"X > {interval_list[-1]}"
        interval_map[-2] = f"总计"
        return interval_map


class MonitorMixin:
    marshal_fields = None
    coinex_export_fields = None
    third_export_fields = None
    export_headers = None
    filename_suffix = None

    @classmethod
    def _get(cls, cache_cls, **kwargs):
        cache = cache_cls()
        records = cache.read_aside()
        if market := kwargs['market']:
            items = [records[market]]
        else:
            items = list(records.values())
        items = marshal(items, cls.marshal_fields)
        sorted_items = sorted(items, key=lambda x: x[kwargs['sort_type']], reverse=True)
        if kwargs['export']:
            return cls._export(sorted_items)

        else:
            return {
                'items': sorted_items,
                'markets': list(records.keys())
            }

    @classmethod
    def _export(cls, sorted_items):
        wb = Workbook()
        ws = wb.worksheets[0]
        c_length = len(cls.coinex_export_fields)
        t_length = len(cls.third_export_fields)
        ws.merge_cells(
            start_row=1,
            start_column=1,
            end_row=1,
            end_column=c_length
        )
        ws.cell(1, 1, 'CoinEx')
        ws.merge_cells(
            start_row=1,
            start_column=c_length+1,
            end_row=1,
            end_column=c_length+t_length
        )
        ws.cell(1, c_length+1, '竞品交易所')
        ws.append(cls.export_headers)
        for item in sorted_items:
            tmp = []
            third_exchange_data = item.pop('third_exchange')
            for field in cls.coinex_export_fields:
                val = item[field]
                tmp.append(val)
            for field in cls.third_export_fields:
                val = third_exchange_data.get(field, '-')
                tmp.append(val)
            ws.append(tmp)
        stream = io.BytesIO()
        wb.save(stream)
        stream.seek(0)
        now_date = now().strftime("%Y%m%d")
        return send_file(
            stream,
            download_name=f'{now_date}_{cls.filename_suffix}.xlsx',
            as_attachment=True
        )


@ns.route('/perpetual-monitor')
@respond_with_code
class PerpetualMonitorResource(MonitorMixin, Resource):
    export_headers = [
        '市场',
        '最新价格',
        '指数价格',
        '标记价格',
        '指数价格基差率（bp）',
        '标记价格基差率（bp）',
        '盘口价差',
        '盘口价差偏离率（bp）',
        '结算周期（H）',
        '当期资金费率',
        '预估下期资金费率',
        '+0.2%深度（USD）',
        '-0.2%深度（USD）',
        '0.2%深度多空比',
        '+2%深度（USD）',
        '-2%深度（USD）',
        '2%深度多空比',
        '过去1小时穿仓损失',

        '交易所名称',
        '指数价格',
        '标记价格',
        '最新价格',
        '指数价格基差率（bp）',
        '结算周期（H）',
        '当期资金费率',
    ]
    coinex_export_fields = [
        'market',
        'last_price',
        'index_price',
        'sign_price',
        'index_price_basis_rate',
        'sign_price_basis_rate',
        'bid_ask_diff',
        'bid_ask_diff_rate',
        'funding_interval',
        'funding_rate',
        'funding_rate_predict',
        'depth1_pos',
        'depth1_neg',
        'depth_1_long_short_rate',
        'depth4_pos',
        'depth4_neg',
        'depth_4_long_short_rate',
        'insurance_loss',
    ]
    third_export_fields = [
        'exchange',
        'index_price',
        'sign_price',
        'last_price',
        'index_price_basis_rate',
        'funding_interval',
        'funding_rate',
    ]
    marshal_fields = {
        'market': ex_fields.String,
        'funding_rate': ex_fields.DecimalType,
        'funding_rate_predict': ex_fields.DecimalType,
        'funding_interval': Integer,
        'bid_ask_diff': ex_fields.DecimalType,
        'bid_ask_diff_rate': ex_fields.DecimalType,
        'last_price': ex_fields.DecimalType,
        'sign_price': ex_fields.DecimalType,
        'index_price': ex_fields.DecimalType,
        'sign_price_basis_rate': ex_fields.DecimalType,
        'index_price_basis_rate': ex_fields.DecimalType,
        'depth1_neg': ex_fields.DecimalType,
        'depth1_pos': ex_fields.DecimalType,
        'depth4_neg': ex_fields.DecimalType,
        'depth4_pos': ex_fields.DecimalType,
        'depth_1_long_short_rate': ex_fields.DecimalType,
        'depth_4_long_short_rate': ex_fields.DecimalType,
        'insurance_loss': ex_fields.DecimalType,
        'third_exchange': Raw,
    }
    filename_suffix = 'perpetual_monitor'

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(missing=''),
        sort_type=fields.String(missing='sign_price_basis_rate'),
        export=fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """统计-实时统计-合约市场数据"""
        return cls._get(RealTimePerpetualMonitorCache, **kwargs)


@ns.route('/perpetual-monitor-series')
@respond_with_code
class PerpetualMonitorSeriesResource(Resource):
    class SeriesType(Enum):
        sign_price_basis_rate = "标记价格基差率"
        bid_ask_diff_rate = "盘口价差偏离率"
        depth_1_long_short_rate = "0.2%深度多空比"
        depth_4_long_short_rate = "2%深度多空比"

    field_cache_dic = {
        SeriesType.sign_price_basis_rate: SignPriceBasisRateCache,
        SeriesType.bid_ask_diff_rate: BidAskDiffRateCache,
        SeriesType.depth_1_long_short_rate: Depth1LongShortRateCache,
        SeriesType.depth_4_long_short_rate: Depth4LongShortRateCache,
    }

    @classmethod
    @ns.use_kwargs(dict(
        series_type=ex_fields.EnumField(SeriesType, required=True),
        market=fields.String(required=True)
    ))
    def get(cls, **kwargs):
        """统计-实时统计-指标历史曲线"""
        cache = cls.field_cache_dic[kwargs['series_type']]()
        cached_data = cache.read_aside()
        return cached_data[kwargs['market']]


@ns.route('/margin-monitor')
@respond_with_code
class MarginMonitorResource(MonitorMixin, Resource):
    export_headers = [
        '市场',
        '最新价格',
        '指数价格',
        '指数价格基差率（bp）',
        '盘口价差',
        '盘口价差偏离率（bp）',
        '+0.2%深度（USD）',
        '-0.2%深度（USD）',
        '0.2%深度多空比',
        '+2%深度（USD）',
        '-2%深度（USD）',
        '2%深度多空比',
        '过去1小时穿仓损失',

        '交易所名称',
        '最新价格',
        '指数价格',
        '指数价格基差率（bp）',
    ]
    coinex_export_fields = [
        'market',
        'last_price',
        'index_price',
        'index_price_basis_rate',
        'bid_ask_diff',
        'bid_ask_diff_rate',
        'depth1_pos',
        'depth1_neg',
        'depth_1_long_short_rate',
        'depth4_pos',
        'depth4_neg',
        'depth_4_long_short_rate',
        'insurance_loss',
    ]
    third_export_fields = [
        'exchange',
        'last_price',
        'index_price',
        'index_price_basis_rate',
    ]
    marshal_fields = {
        'market': ex_fields.String,
        'bid_ask_diff': ex_fields.DecimalType,
        'bid_ask_diff_rate': ex_fields.DecimalType,
        'last_price': ex_fields.DecimalType,
        'index_price': ex_fields.DecimalType,
        'index_price_basis_rate': ex_fields.DecimalType,
        'depth1_neg': ex_fields.DecimalType,
        'depth1_pos': ex_fields.DecimalType,
        'depth4_neg': ex_fields.DecimalType,
        'depth4_pos': ex_fields.DecimalType,
        'depth_1_long_short_rate': ex_fields.DecimalType,
        'depth_4_long_short_rate': ex_fields.DecimalType,
        'insurance_loss': ex_fields.DecimalType,
        'third_exchange': Raw,
    }
    filename_suffix = 'margin_monitor'

    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(missing=''),
        sort_type=fields.String(missing='index_price_basis_rate'),
        export=fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """统计-实时统计-杠杆市场数据"""
        return cls._get(RealTimeMarginMonitorCache, **kwargs)


@ns.route('/margin-monitor-series')
@respond_with_code
class MarginMonitorSeriesResource(Resource):
    class SeriesType(Enum):
        index_price_basis_rate = "指数价格基差率"
        bid_ask_diff_rate = "盘口价差偏离率"
        depth_1_long_short_rate = "0.2%深度多空比"
        depth_4_long_short_rate = "2%深度多空比"

    field_cache_dic = {
        SeriesType.index_price_basis_rate: MarginIndexPriceBasisRateCache,
        SeriesType.bid_ask_diff_rate: MarginBidAskDiffRateCache,
        SeriesType.depth_1_long_short_rate: MarginDepth1LongShortRateCache,
        SeriesType.depth_4_long_short_rate: MarginDepth4LongShortRateCache,
    }

    @classmethod
    @ns.use_kwargs(dict(
        series_type=ex_fields.EnumField(SeriesType, required=True),
        market=fields.String(required=True)
    ))
    def get(cls, **kwargs):
        """统计-实时统计-杠杆指标历史曲线"""
        cache = cls.field_cache_dic[kwargs['series_type']]()
        cached_data = cache.read_aside()
        return cached_data[kwargs['market']]


@ns.route('/perpetual-profit-loss')
@respond_with_code
class PerpetualProfitLossResource(Resource):
    cache_cls = PerpetualProfitLossStatisticsCache

    @classmethod
    @ns.use_kwargs(dict(
        range=EnumField(cache_cls.Range, required=True),
    ))
    def get(cls, **kwargs):
        """统计-用户统计-合约盈亏统计"""
        range_ = kwargs['range']
        cache_data = cls.cache_cls(range_).read()
        if not cache_data:
            return dict(
                records={
                    'total': {
                        'user_count': '-',
                        'deal_usd': '-',
                        'profit_usd': '-',
                        'expense_usd': '-',
                        'net_profit': '-',
                    }
                },
                timestamp=None,
                ranges=cls.cache_cls.Range,
            )
        data = json.loads(cache_data)
        timestamp = float(data['last_updated_ts'])
        return dict(
            records=data,
            timestamp=timestamp,
            ranges=cls.cache_cls.Range,
        )


@ns.route('/security')
@respond_with_code
class SecuritySettingStatisticsResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        range=EnumField(SecurityStatisticsCache.TimeRange, required=True),
    ))
    def get(cls, **kwargs):
        """统计-用户统计-安全设置统计"""

        business_types={item.name: item.value for item in SecuritySettingType}
        types = {
            "login": "登录设置",
            "2fa": "2FA设置",
            "advance": "高级设置"
        }
        data = SecurityStatisticsCache(kwargs['range']).read()
        if not data:
            return dict(business_types=business_types, types=types, data=[])
        return dict(
            data=json.loads(data)['data'],
            business_types=business_types,
            types=types
        )


@ns.route('/ambassador')
@respond_with_code
class AmbassadorStatisticsResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        ambassador_type=EnumField(AmbassadorStatisticsCache.AmbassadorType, required=True),
    ))
    def get(cls, **kwargs):
        """统计-用户统计-大使统计"""
        ambassador_type = kwargs['ambassador_type']
        return dict(
            data=AmbassadorStatisticsCache().get_data(ambassador_type),
            ambassador_types=AmbassadorStatisticsCache.AmbassadorType
        )


@ns.route("/asset-synthetic")
@respond_with_code
class AssetSyntheticStatisticsResource(Resource):
    MAX_LIMIT = 200

    export_headers = (
        {"field": "idx", Language.ZH_HANS_CN: "序号"},
        {"field": "asset", Language.ZH_HANS_CN: "币种"},
        {"field": "cmc_rank", Language.ZH_HANS_CN: "CMC排名"},
        {"field": "circulation_usd", Language.ZH_HANS_CN: "流通市值（USD）"},
        {"field": "site_balance_usd", Language.ZH_HANS_CN: "站内资产总市值（USD）"},
        {"field": "individual_balance_usd", Language.ZH_HANS_CN: "站内散户资产总市值（USD）"},
        {"field": "valid_user_count", Language.ZH_HANS_CN: "资产用户数（阈值以上）"},
        {"field": "is_margin", Language.ZH_HANS_CN: "是否上线杠杆"},
        {"field": "is_perpetual", Language.ZH_HANS_CN: "是否上线合约"},
        {"field": "spot_exchange_list", Language.ZH_HANS_CN: "现货上所"},
        {"field": "recent_30d_deal_usd", Language.ZH_HANS_CN: "近30天成交市值"},
        {"field": "recent_30d_change_rate", Language.ZH_HANS_CN: "近30天涨跌幅"},
        {"field": "recent_30d_depth_usd_5pct_avg", Language.ZH_HANS_CN: "近30天现货±5%深度平均市值(USD)"},
        {"field": "recent_30d_avg_loan_amount", Language.ZH_HANS_CN: "近30天平均借贷余额数量"},
        {"field": "recent_30d_avg_loan_usd", Language.ZH_HANS_CN: "近30天平均借贷余额市值（USD）"},
        {"field": "recent_90d_liquidation_insurance_usd", Language.ZH_HANS_CN: "近90日穿仓垫付金额(USD)"},
        {"field": "circulating_supply", Language.ZH_HANS_CN: "流通数量/2万"},
        {"field": "base_max_loan", Language.ZH_HANS_CN: "单用户最大可借币数"},
        {"field": "pledge_limit_usd", Language.ZH_HANS_CN: "质押币质押市值上限(USD)"},
        {"field": "collateral_ratio_stage", Language.ZH_HANS_CN: "折价率档位"},
        {"field": "is_pledge_asset", Language.ZH_HANS_CN: "是否质押币种"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String,
        is_margin=fields.Boolean,
        is_perpetual=fields.Boolean,
        is_pledge_asset=fields.Boolean,
        sort_by=EnumField(PledgeAssetSynthetic.SortBy, missing=PledgeAssetSynthetic.SortBy.CIRCULATION_USD),
        is_export=fields.Boolean
    ))
    def get(cls, **kwargs):
        """统计-资产统计-借贷资产评估表"""
        query = PledgeAssetSynthetic.query
        if asset := kwargs.get("asset"):
            query = query.filter(
                PledgeAssetSynthetic.asset == asset
            )
        if (is_margin := kwargs.get("is_margin")) is not None:
            query = query.filter(
                PledgeAssetSynthetic.is_margin == is_margin
            )
        if (is_perpetual := kwargs.get("is_perpetual")) is not None:
            query = query.filter(
                PledgeAssetSynthetic.is_perpetual == is_perpetual
            )
        if (is_pledge_asset := kwargs.get("is_pledge_asset")) is not None:
            query = query.filter(
                PledgeAssetSynthetic.is_pledge_asset == is_pledge_asset
            )
        query = query.order_by(PledgeAssetSynthetic.circulation_usd.desc())
        pag = query.paginate(1, cls.MAX_LIMIT, error_out=False)
        items = pag.items
        if sort_by := kwargs.get("sort_by"):
            def _sort_data(syn: PledgeAssetSynthetic):
                if sort_by != PledgeAssetSynthetic.SortBy.CMC_RANK:
                    return -getattr(syn, sort_by.name.lower())
                if syn.cmc_rank:
                    return 0, syn.cmc_rank
                else:
                    return 1, syn.asset

            items = sorted(pag.items, key=_sort_data)

        result = []
        is_export = kwargs.get("is_export")
        for idx, i in enumerate(items):
            result.append({
                "idx": idx + 1,
                **cls._to_dict(i, is_export)
            })

        if is_export:
            return export_xlsx(
                filename='asset_synthetic_statistic_data',
                data_list=result,
                export_headers=cls.export_headers
            )
        assets = [i for i, in query.with_entities(PledgeAssetSynthetic.asset).all()]
        return dict(
            items=result,
            sort_bys=PledgeAssetSynthetic.SortBy,
            assets=assets
        )

    @classmethod
    def _to_dict(cls, row: PledgeAssetSynthetic, is_export: bool) -> dict:
        return dict(
            asset=row.asset,
            cmc_rank=row.cmc_rank,
            circulation_usd=amount_to_str(
                row.circulation_usd, 2, with_separator=True
            ),
            site_balance_usd=amount_to_str(
                row.site_balance_usd, 2, with_separator=True
            ),
            individual_balance_usd=amount_to_str(
                row.individual_balance_usd, 2, with_separator=True
            ),
            valid_user_count=amount_to_str(row.valid_user_count, with_separator=True),
            is_margin="是" if row.is_margin else "否",
            is_perpetual="是" if row.is_perpetual else "否",
            spot_exchange_list=([
                {"name": i} for i in row.spot_exchange_list.split("、")
            ] if row.spot_exchange_list else []) if not is_export else row.spot_exchange_list,
            recent_30d_deal_usd=amount_to_str(
                row.recent_30d_deal_usd, 2, with_separator=True
            ),
            recent_30d_change_rate=amount_to_str(row.recent_30d_change_rate * 100, 2) + "%",
            recent_30d_depth_usd_5pct_avg=amount_to_str(
                row.recent_30d_depth_usd_5pct_avg, PrecisionEnum.COIN_PLACES, with_separator=True
            ),
            recent_30d_avg_loan_usd=amount_to_str(
                row.recent_30d_avg_loan_usd, 2, with_separator=True
            ),
            recent_30d_avg_loan_amount=f"""{amount_to_str(
                row.recent_30d_avg_loan_amount, PrecisionEnum.COIN_PLACES, with_separator=True
            )}  {row.asset}""",
            recent_90d_liquidation_insurance_usd=amount_to_str(
                row.recent_90d_liquidation_insurance_usd, 2, with_separator=True
            ),
            circulating_supply=amount_to_str(
                row.circulating_supply, PrecisionEnum.COIN_PLACES, with_separator=True
            ),
            base_max_loan=row.base_max_loan if row.base_max_loan else "/",
            pledge_limit_usd=amount_to_str(
                row.pledge_limit_usd, 2, with_separator=True
            ) if row.pledge_limit_usd else "/",
            collateral_ratio_stage=amount_to_str(
                row.collateral_ratio_stage * 100, 2
            ) + "%" if row.collateral_ratio_stage else "/",
            is_pledge_asset="是" if row.is_pledge_asset else "否",
            remark=row.remark,
        )

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String(required=True),
        remark=fields.String(required=True)
    ))
    def put(cls, **kwargs):
        """统计-资产统计-借贷资产评估表-修改备注"""
        syn = PledgeAssetSynthetic.query.filter(
            PledgeAssetSynthetic.asset == kwargs["asset"]
        ).first()
        if not syn:
            raise RecordNotFound
        old_data = syn.to_dict(enum_to_name=True)
        syn.remark = kwargs['remark']
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectStatistic.PledgeAssetSynthetic,
            old_data=old_data,
            new_data=syn.to_dict(enum_to_name=True),
        )


class MarketLiquidityMixin:

    class DataMode(Enum):
        REAL_TIME = '实时'
        LAST_FOUR_HOURS = '近4小时均值'
        YESTERDAY = '昨日均值'

    @classmethod
    def _fmt_row(cls, rec):
        percent_fields = ['bid_ask_1', 'bid_ask_10', 'price_volatility', 
                          'third_depth_tolerance_rate', 
                          'third_continuity_tolerance_rate']
        decimal_fields = ['depth_tolerance_02', 'depth_tolerance_05',
                          'depth_tolerance_10', 'depth_tolerance_20', 
                          'depth_tolerance_50', 'final_depth_tolerance',
                          'bps_01', 'bps_05', 'bps_10', 'bps_200'
                          ]
        int_fields = ['final_continuity_tolerance', 'continuity_tolerance_02', 'continuity_tolerance_05',
                      'continuity_tolerance_10', 'continuity_tolerance_20', 'continuity_tolerance_50']
        for field, v in rec.items():
            if v is None:
                rec[field] = '-'
            elif field in percent_fields:
                rec[field] = format_percent(v, 4)
            elif field in decimal_fields:
                if v > 1:
                    rec[field] = amount_to_str(v, 0, with_separator=True)
                else:
                    rec[field] = amount_to_str(v, 2)
            elif field in int_fields:
                rec[field] = int(v)
        return rec


@ns.route('/market-liquidity-statistic')
@respond_with_code
class MarketLiquidityStatisticResource(Resource, MarketLiquidityMixin):

    export_headers = (
        {'field': 'market', Language.ZH_HANS_CN: '市场名称'},
        {'field': 'bid_ask_1', Language.ZH_HANS_CN: 'Bid&Ask 1（盘口价差）'},
        {'field': 'price_volatility', Language.ZH_HANS_CN: '价格波动率'},
        {'field': 'final_depth_tolerance', Language.ZH_HANS_CN: '参照区间深度'},
        {'field': 'third_depth_tolerance_rate', Language.ZH_HANS_CN: '参照区间深度占比'},
        {'field': 'final_continuity_tolerance', Language.ZH_HANS_CN: '参照区间挂单评分'},
        {'field': 'third_continuity_tolerance_rate', Language.ZH_HANS_CN: '参照区间挂单评分占比'},
    )
    depth_headers = (
        {'field': 'market', Language.ZH_HANS_CN: '市场名称'},
        {'field': 'depth_tolerance_02', Language.ZH_HANS_CN: '±0.2%深度'},
        {'field': 'depth_tolerance_05', Language.ZH_HANS_CN: '±0.5%深度'},
        {'field': 'depth_tolerance_10', Language.ZH_HANS_CN: '±1%深度'},
        {'field': 'depth_tolerance_20', Language.ZH_HANS_CN: '±2%深度'},
        {'field': 'depth_tolerance_50', Language.ZH_HANS_CN: '±5%深度'},
    )

    class SortName(Enum):
        MARKET = 'market'
        BID_ASK_1 = 'bid_ask_1'
        PRICE_VOLATILITY = 'price_volatility'
        DEPTH_TOLERANCE_02 = 'depth_tolerance_02'
        DEPTH_TOLERANCE_05 = 'depth_tolerance_05'
        DEPTH_TOLERANCE_10 = 'depth_tolerance_10'
        DEPTH_TOLERANCE_20 = 'depth_tolerance_20'
        DEPTH_TOLERANCE_50 = 'depth_tolerance_50'
        FINAL_CONTINUITY_TOLERANCE = 'final_continuity_tolerance'
        FINAL_DEPTH_TOLERANCE = 'final_depth_tolerance'
        third_depth_tolerance_rate = 'third_depth_tolerance_rate'
        third_continuity_tolerance_rate = 'third_continuity_tolerance_rate'


    class SortDirection(Enum):
        ASC = 'asc'
        DESC = 'desc'

    @classmethod
    @ns.use_kwargs(dict(
        market_type=fields.Enum(MarketType, by_value=True, required=True),
        market=fields.String,
        sort_name=fields.Enum(SortName, by_value=True, missing=SortName.MARKET),
        sort_direction=fields.Enum(SortDirection, by_value=True, missing=SortDirection.DESC),
        use_advanced_condition=fields.Boolean(missing=False),
        show_depth=fields.Boolean(missing=False),
        export=fields.Boolean(missing=False),
        trade_area=fields.String,
        exchange=fields.Enum(Exchange, by_value=True, missing=Exchange.MEXC),
        is_amm_market=fields.Boolean,
        data_mode=fields.Enum(MarketLiquidityMixin.DataMode, missing=MarketLiquidityMixin.DataMode.REAL_TIME),
    ))
    def get(cls, **kwargs):
        """统计-实时统计-实时流动性监控"""
        market_type = kwargs['market_type']
        first_rec = MarketLiquidityStatistic.query.filter(
            MarketLiquidityStatistic.market_type == market_type,
            MarketLiquidityStatistic.exchange == Exchange.CoinEx,
        ).order_by(MarketLiquidityStatistic.id.desc()).first()
        if not first_rec:
            return dict(
                    data=[],
                    extra={
                            'markets': [],
                            'filter_options': {'and_filters': [], 'or_filters': []},
                        },
                    updated_at=None,
                    )
        updated_at = first_rec.created_at
        report_time = first_rec.report_time
        sort_name, sort_direction = kwargs['sort_name'], kwargs['sort_direction']
        data_mode = kwargs['data_mode']
        if data_mode == MarketLiquidityMixin.DataMode.LAST_FOUR_HOURS:
            data = cls.get_last_four_hours_data(kwargs, report_time)
        elif data_mode == MarketLiquidityMixin.DataMode.YESTERDAY:
            data = cls.get_yesterday_data(kwargs)
        elif data_mode == MarketLiquidityMixin.DataMode.REAL_TIME:
            data = cls.get_real_time_data(kwargs, report_time)
        else:
            raise InvalidArgument(message='未知的统计数据模式')

        def sort_func(value):
            if value is None or value in ('-', '/'):
                return Decimal('0')
            return value

        
        reverse = True if sort_direction == cls.SortDirection.DESC else False

        data.sort(key=lambda x: sort_func(x.get(sort_name.value)), reverse=reverse)
        filter_options, has_options = cls.get_filter_options(market_type)
        if kwargs.get('use_advanced_condition') and has_options:
            res = cls.filter_by_saved_condition(data, filter_options, market_type)
        else:
            res = data
        for rec in res:
            cls._fmt_row(rec)
        if kwargs.get('export'):
            return cls.export_data(res, kwargs.get('show_depth'))
        if market_type == MarketType.SPOT:
            markets = MarketCache.list_online_markets()
        else:
            markets = PerpetualMarketCache().get_market_list()
        extra = {
            'markets': markets,
            'filter_options': filter_options,
        }
        return dict(
            data=res,
            extra=extra,
            updated_at=updated_at,
        )
    
    @classmethod
    def get_last_four_hours_data(cls, kwargs, end):
        start = end - 4 * 3600
        return cls._get_period_avg_data(start, end, kwargs)
    
    @classmethod
    def get_yesterday_data(cls, kwargs):
        # 获取UTC+8时区的今天0点时间戳
        end = get_utc8_today_timestamp()
        start = end - 86400
        return cls._get_period_avg_data(start, end, kwargs)

    @classmethod
    def get_real_time_data(cls, kwargs, report_time):
        start = report_time - 1
        return cls._get_period_avg_data(start, report_time, kwargs)

    @classmethod
    def _get_period_avg_data(cls, start, end, kwargs):
        market_type = kwargs['market_type']
        markets = cls.get_markets(kwargs)


        query = MarketLiquidityStatistic.query.filter(
            MarketLiquidityStatistic.market_type == market_type,
            MarketLiquidityStatistic.exchange == Exchange.CoinEx,
            MarketLiquidityStatistic.report_time >= start,
            MarketLiquidityStatistic.report_time <= end,
        ).with_entities(
            MarketLiquidityStatistic.market,
            func.avg(MarketLiquidityStatistic.bid_ask_1).label('bid_ask_1'),
            func.avg(MarketLiquidityStatistic.price_volatility).label('price_volatility'),
            func.avg(MarketLiquidityStatistic.depth_tolerance_02).label('depth_tolerance_02'),
            func.avg(MarketLiquidityStatistic.depth_tolerance_05).label('depth_tolerance_05'),
            func.avg(MarketLiquidityStatistic.depth_tolerance_10).label('depth_tolerance_10'),
            func.avg(MarketLiquidityStatistic.depth_tolerance_20).label('depth_tolerance_20'),
            func.avg(MarketLiquidityStatistic.depth_tolerance_50).label('depth_tolerance_50'),
            func.avg(MarketLiquidityStatistic.final_depth_tolerance).label('final_depth_tolerance'),
            func.avg(MarketLiquidityStatistic.final_continuity_tolerance).label('final_continuity_tolerance'),
        ).group_by(
            MarketLiquidityStatistic.market
        )
        third_exchange = kwargs['exchange']
        third_exchange_query = MarketLiquidityStatistic.query.filter(
            MarketLiquidityStatistic.market_type == market_type,
            MarketLiquidityStatistic.exchange == third_exchange,
            MarketLiquidityStatistic.report_time >= start,
            MarketLiquidityStatistic.report_time <= end,
        ).with_entities(
            MarketLiquidityStatistic.market,
            func.avg(MarketLiquidityStatistic.final_depth_tolerance).label('final_depth_tolerance'),
            func.avg(MarketLiquidityStatistic.final_continuity_tolerance).label('final_continuity_tolerance'),
        ).group_by(
            MarketLiquidityStatistic.market
        )
        if markets is not None:
            query = query.filter(MarketLiquidityStatistic.market.in_(markets))
            third_exchange_query = third_exchange_query.filter(MarketLiquidityStatistic.market.in_(markets))
        data = []
        third_exchange_data = third_exchange_query.all()
        third_exchange_market_data_dic = {i.market: i for i in third_exchange_data}

        records = query.all()
        for row in records:
            market = row.market
            item = {
                'market': market,
                'bid_ask_1': row.bid_ask_1,
                'price_volatility': row.price_volatility,
                'depth_tolerance_02': row.depth_tolerance_02,
                'depth_tolerance_05': row.depth_tolerance_05,
                'depth_tolerance_10': row.depth_tolerance_10,
                'depth_tolerance_20': row.depth_tolerance_20,
                'depth_tolerance_50': row.depth_tolerance_50,
                'final_depth_tolerance': row.final_depth_tolerance,
                'final_continuity_tolerance': row.final_continuity_tolerance,
            }
            t_row = third_exchange_market_data_dic.get(market)
            if not t_row:
                item['third_exchange_final_depth_tolerance'] = None
                item['third_depth_tolerance_rate'] = None
                item['third_exchange_final_continuity_tolerance'] = None
                item['third_continuity_tolerance_rate'] = None
            else:
                t_depth_tolerance = t_row.final_depth_tolerance
                t_continuity_tolerance = t_row.final_continuity_tolerance
                item['third_exchange_final_depth_tolerance'] = t_depth_tolerance
                item['third_exchange_final_continuity_tolerance'] = t_continuity_tolerance
                if row.final_depth_tolerance is None or t_depth_tolerance is None:
                    item['third_depth_tolerance_rate'] = None
                else:
                    item['third_depth_tolerance_rate'] = safe_div(row.final_depth_tolerance, t_depth_tolerance)
                if row.final_continuity_tolerance is None or t_continuity_tolerance is None:
                    item['third_continuity_tolerance_rate'] = None
                else:
                    item['third_continuity_tolerance_rate'] = safe_div(row.final_continuity_tolerance,
                                                                       t_continuity_tolerance)
            data.append(item)
        return data

    @classmethod
    def get_markets(cls, kwargs):
        market_type = kwargs['market_type']
        trade_area = kwargs.get('trade_area')
        is_amm_market = kwargs.get('is_amm_market')
        market = kwargs.get('market')
        if not trade_area and is_amm_market is None and not market:
            return None
        if market_type == MarketType.SPOT:
            query = Market.query.filter(Market.status == Market.Status.ONLINE)
            if trade_area:
                trade_area = getattr(Market.TradingArea, trade_area)
                query = query.filter(Market.trading_area == trade_area)
            if is_amm_market is not None:
                recs = AmmMarket.query.filter(AmmMarket.status == AmmMarket.Status.ONLINE).with_entities(
                    AmmMarket.name).all()
                amm_markets = [i[0] for i in recs]
                if is_amm_market:
                    query = query.filter(Market.name.in_(amm_markets))
                else:
                    query = query.filter(Market.name.notin_(amm_markets))
            markets = [i[0] for i in query.with_entities(Market.name).all()]
            if market:
                return list(set(markets) & {market})
            else:
                return markets
        else:
            if not trade_area and not market:
                return None
            query = PerpetualMarket.query.filter(PerpetualMarket.status == PerpetualMarket.StatusType.OPEN)
            if trade_area:
                query = query.filter(PerpetualMarket.quote_asset == trade_area)
            if market:
                query = query.filter(PerpetualMarket.name == market)
            markets = [i[0] for i in query.with_entities(PerpetualMarket.name).all()]
            return markets
        

    @classmethod
    def get_filter_options(cls, market_type):
        user_id = g.user.id
        rec = MarketLiquidityFilter.query.filter(
            MarketLiquidityFilter.user_id == user_id,
            MarketLiquidityFilter.market_type == market_type
        ).first()
        if not rec:
            return dict(and_filters=[], or_filters=[]), False
        and_filters = json.loads(rec.and_filters)
        or_filters = json.loads(rec.or_filters)
        return dict(and_filters=and_filters, or_filters=or_filters), True

    @classmethod
    def filter_by_saved_condition(cls, data, filter_options, market_type):

        and_filters, or_filters = filter_options['and_filters'], filter_options['or_filters']
        filter_fields = {item['field'] for item in chain(and_filters, or_filters)}
        market_cap_rank_dic, market_leverage_dic = dict(), dict()
        if 'market_cap_rank' in filter_fields:
            market_cap_rank_dic = cls.get_market_cap_dic(market_type)
        if 'market_leverage' in filter_fields:
            market_leverage_dic = cls.get_market_leverage_dic()
        res = []
        for record in data:
            if cls.pass_filter(record, and_filters, or_filters, market_cap_rank_dic, market_leverage_dic):
                res.append(record)
        return res

    @classmethod
    def get_market_cap_dic(cls, market_type):
        if market_type == MarketType.SPOT:
            recs = Market.query.filter(Market.status == Market.Status.ONLINE).with_entities(
                Market.name,
                Market.base_asset
            ).all()
        else:
            recs = PerpetualMarket.query.filter(
                PerpetualMarket.status == PerpetualMarket.StatusType.OPEN
            ).with_entities(
                PerpetualMarket.name,
                PerpetualMarket.base_asset
            ).all()
        market_asset_dic = dict(recs)
        asset_rank = AssetRankCache('real_circulation_usd').read_assets()
        res = dict()
        for market, asset in market_asset_dic.items():
            if asset in asset_rank:
                rank = asset_rank.index(asset) + 1
            else:
                rank = 0
            res[market] = rank
        return res

    @classmethod
    def get_market_leverage_dic(cls):
        recs = PerpetualMarket.query.filter(
            PerpetualMarket.status == PerpetualMarket.StatusType.OPEN).with_entities(
            PerpetualMarket.name,
            PerpetualMarket.leverages
        ).all()
        res = dict()
        for market, leverage_str in recs:
            lev_lis = list(map(Decimal, leverage_str.split(',')))
            lev_lis.sort()
            res[market] = lev_lis[-1]
        return res

    @classmethod
    def pass_filter(cls, record, and_filters, or_filters, market_cap_rank_dic, market_leverage_dic):

        def pass_compare(val_1, val_2):
            if operator == '>':
                return val_1 > val_2
            elif operator == '<':
                return val_1 < val_2
            elif operator == '>=':
                return val_1 >= val_2
            elif operator == '<=':
                return val_1 <= val_2
            elif operator == '=':
                return val_1 == val_2
            else:
                raise InvalidArgument(message='未知的操作符')

        if and_filters:
            for item in and_filters:
                field, operator, cmp_val_str = item['field'], item['operator'], item['value']
                val = cls.get_rec_val(record, field, market_cap_rank_dic, market_leverage_dic)
                if val is None:
                    return False
                if not pass_compare(val, Decimal(cmp_val_str)):
                    return False
        if or_filters:
            for item in or_filters:
                field, operator, cmp_val_str = item['field'], item['operator'], item['value']
                val = cls.get_rec_val(record, field, market_cap_rank_dic, market_leverage_dic)
                if val is None:
                    return False
                if pass_compare(val, Decimal(cmp_val_str)):
                    return True
            return False
        return True

    @classmethod
    def get_rec_val(cls, record, field, market_cap_rank_dic, market_leverage_dic):
        market = record['market']
        if field == 'market_cap_rank':
            val = market_cap_rank_dic.get(market)
        elif field == 'market_leverage':
            val = market_leverage_dic.get(market)
        else:
            val = record.get(field)
        return val

    @classmethod
    def export_data(cls, data, show_depth):
        if show_depth:
            export_headers = cls.depth_headers
        else:
            export_headers = cls.export_headers
        return export_xlsx(
            filename="market-liquidity",
            data_list=data,
            export_headers=export_headers,
        )


@ns.route('/market-liquidity-detail')
@respond_with_code
class MarketLiquidityDetailResource(Resource, MarketLiquidityMixin):

    display_exchange_count = 6
    bps_fields = ['bps_01', 'bps_05', 'bps_10', 'bps_200']


    @classmethod
    @ns.use_kwargs(dict(
        market_type=fields.Enum(MarketType, by_value=True, required=True),
        market=fields.String(required=True),
        data_mode=fields.Enum(MarketLiquidityMixin.DataMode, missing=MarketLiquidityMixin.DataMode.REAL_TIME),
    ))
    def get(cls, **kwargs):
        """统计-实时统计-市场流动性详情"""
        market_type, market = kwargs['market_type'], kwargs['market']

        first_rec = MarketLiquidityStatistic.query.filter(
            MarketLiquidityStatistic.market_type == market_type,
        ).order_by(MarketLiquidityStatistic.report_time.desc()).first()
        if not first_rec:
            return dict(
                data=[],
                markets=[],
                updated_at=None,
            )
        report_time = first_rec.report_time
        data_mode = kwargs['data_mode']
        if data_mode == MarketLiquidityMixin.DataMode.REAL_TIME:
            records = cls.get_real_time_data(market_type, market, report_time)
        elif data_mode == MarketLiquidityMixin.DataMode.LAST_FOUR_HOURS:
            records = cls.get_last_four_hours_data(market_type, market, report_time)
        elif data_mode == MarketLiquidityMixin.DataMode.YESTERDAY:
            records = cls.get_yesterday_data(market_type, market)
        else:
            raise InvalidArgument(message='Invalid data mode')
        priority_exchanges = cls.get_priority_exchanges(records, market_type)
        market_price_dic = cls.get_market_price_dic(market_type, market, priority_exchanges)

        res = []
        for record in records:
            market, exchange = record['market'], record['exchange']
            if exchange not in priority_exchanges:
                continue
            price = market_price_dic.get(exchange)
            record['sign_price'] = price
            cls._fmt_row(record)
            res.append(record)
        res.sort(key=lambda x: priority_exchanges.index(x['exchange']))
        market_recs = MarketLiquidityStatistic.query.filter(
            MarketLiquidityStatistic.market_type == market_type,
            MarketLiquidityStatistic.report_time == report_time,
        ).with_entities(
            func.distinct(MarketLiquidityStatistic.market)
        ).all()
        markets = [i[0] for i in market_recs]
        final_depth_tolerance_field = records[-1]['final_depth_tolerance_field'] if records else None
        final_continuity_tolerance_field = records[-1]['final_continuity_tolerance_field'] if records else None
        return dict(
            final_depth_tolerance_field=final_depth_tolerance_field,
            final_continuity_tolerance_field=final_continuity_tolerance_field,
            data=res,
            markets=markets,
            updated_at=first_rec.created_at,
        )
    
    @classmethod
    def get_real_time_data(cls, market_type, market, report_time):
        end = report_time + 1
        return cls.get_period_avg_data(market_type, market, report_time, end)
    
    @classmethod
    def get_last_four_hours_data(cls, market_type, market, end):
        start = end - 4 * 3600
        return cls.get_period_avg_data(market_type, market, start, end)

    @classmethod
    def get_yesterday_data(cls, market_type, market):
        # 获取UTC+8时区的今天0点时间戳
        end = get_utc8_today_timestamp()
        start = end - 86400
        return cls.get_period_avg_data(market_type, market, start, end)
    
    @classmethod
    def get_period_avg_data(cls, market_type, market, start, end):
        query = MarketLiquidityStatistic.query.filter(
            MarketLiquidityStatistic.market_type == market_type,
            MarketLiquidityStatistic.report_time >= start,
            MarketLiquidityStatistic.report_time <= end,
            MarketLiquidityStatistic.market == market
        ).all()
        records = [i.to_dict() for i in query]
        grouped_records = group_by(lambda x: x['exchange'], records)
        res = []
        d_fields = [
            'bid_ask_1',
            'bid_ask_10',
            'sign_price',
            'price_volatility',
            'depth_tolerance_02',
            'depth_tolerance_05',
            'depth_tolerance_10',
            'depth_tolerance_20',
            'depth_tolerance_50',
            'final_depth_tolerance',
            'continuity_tolerance_02',
            'continuity_tolerance_05',
            'continuity_tolerance_10',
            'continuity_tolerance_20',
            'continuity_tolerance_50',
            'final_continuity_tolerance',
        ]

        bps_dic = cls.get_exchange_bps_dic(market_type, market, start, end)
        for exchange, records in grouped_records.items():
            final_depth_tolerance_field = records[-1]['final_depth_tolerance_field']
            final_continuity_tolerance_field = records[-1]['final_continuity_tolerance_field']
            item = dict(exchange=exchange,
                        market=market,
                        final_depth_tolerance_field=final_depth_tolerance_field,
                        final_continuity_tolerance_field=final_continuity_tolerance_field,
                        )
            for field in d_fields:
                item[field] = sum(i.get(field, 0) or 0 for i in records) / len(records)
            for field in cls.bps_fields:
                item[field] = bps_dic[exchange].get(field)
            res.append(item)
        
        return res
    
    @classmethod
    def get_priority_exchanges(cls, records, market_type):
        """拥有最多盘口数据字段的交易所优先展示"""
        if market_type == MarketType.PERPETUAL:
            return MarketLiquidityStatistic.perpetual_exchanges_with_priority
        count_exchange_dic = defaultdict(list)
        bid_ask_fields = ['bid_ask_1', 'bid_ask_10', 'sign_price',
                          'price_volatility']
        for record in records:
            count = 0
            for field in bid_ask_fields:
                if record.get(field) is not None:
                    count += 1
            count_exchange_dic[count].append(record['exchange'])
        sorted_c_e = sorted(count_exchange_dic.items(), key=lambda x: x[0], reverse=True)
        select_exchanges = []
        for _, exchanges in sorted_c_e:
            if market_type == MarketType.SPOT:
                exchanges_with_priority = MarketLiquidityStatistic.spot_exchanges_with_priority
            else:
                exchanges_with_priority = MarketLiquidityStatistic.perpetual_exchanges_with_priority
            exchanges.sort(key=lambda x: exchanges_with_priority.index(x))
            for exchange in exchanges:
                if len(select_exchanges) >= cls.display_exchange_count:
                    break
                else:
                    select_exchanges.append(exchange)
        return select_exchanges

    @classmethod
    def get_market_price_dic(cls, market_type, market, priority_exchanges):
        res = dict()
        for exchange in priority_exchanges:
            first_rec = ExchangeMarketPrices.query.filter(
                ExchangeMarketPrices.market_type == market_type,
                ExchangeMarketPrices.market == market,
                ExchangeMarketPrices.exchange == exchange,
            ).order_by(ExchangeMarketPrices.report_time.desc()).first()
            if not first_rec:
                continue
            res[exchange] = first_rec.price
        return res

    @classmethod
    def get_exchange_bps_dic(cls, market_type, market, start, end):
        query = MarketBpsData.query.filter(
            MarketBpsData.market_type == market_type,
            MarketBpsData.market == market,
            MarketBpsData.report_time >= start,
            MarketBpsData.report_time <= end,
        ).all()
        records = [i.to_dict() for i in query]
        grouped_records = group_by(lambda x: x['exchange'], records)
        res = defaultdict(dict)
        for exchange, records in grouped_records.items():
            for field in cls.bps_fields:
                res[exchange][field] = sum(i.get(field, 0) or 0 for i in records) / len(records)
        return res


@ns.route('/market-liquidity-series')
@respond_with_code
class MarketLiquiditySeriesResource(Resource):
    bps_fields = ['bps_01', 'bps_05', 'bps_10', 'bps_200']

    @classmethod
    @ns.use_kwargs(dict(
        market_type=fields.Enum(MarketType, by_value=True, required=True),
        market=fields.String(required=True),
        field=fields.String(required=True),
    ))
    def get(cls, **kwargs):
        """统计-实时统计-市场流动性曲线"""
        field = kwargs['field']
        interval = 15 * 86400
        if field in cls.bps_fields:
            query_model = MarketBpsData
        else:
            query_model = MarketLiquidityStatistic
        market_type, market = kwargs['market_type'], kwargs['market']
        query = query_model.query.filter(
            query_model.market_type == market_type,
            query_model.market == market
        ).order_by(
            query_model.report_time.desc()
        )
        first_rec = query.first()
        if not first_rec:
            return dict(data=[])
        end = first_rec.report_time
        start = end - interval
        
        q_field = getattr(query_model, field)
        records = query.filter(
            query_model.report_time >= start,
            query_model.report_time <= end
        ).with_entities(
            query_model.exchange,
            query_model.report_time,
            q_field
        ).all()
        exchange_data = defaultdict(list)
        for exchange, report_time, value in records:
            exchange_data[exchange].append((report_time, value))
        res = []
        for exchange, data_lis in exchange_data.items():
            data_lis.sort(key=lambda x: x[0])
            res.append(dict(
                name=exchange,
                data=data_lis
            ))
        return dict(data=res)


@ns.route('/market-liquidity-filter')
@respond_with_code
class MarketLiquidityFilterResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        market_type=fields.Enum(MarketType, by_value=True, required=True),
        and_filters=fields.List(fields.Dict),
        or_filters=fields.List(fields.Dict),
    ))
    def put(cls, **kwargs):
        """统计-实时统计-市场流动性统计筛选条件"""
        user_id = g.user.id
        market_type = kwargs['market_type']
        and_filters, or_filters = kwargs.get('and_filters', []), kwargs.get('or_filters', [])
        rec = MarketLiquidityFilter.query.filter(
            MarketLiquidityFilter.user_id == user_id,
            MarketLiquidityFilter.market_type == market_type
        ).first()
        old_data = None
        if rec:
            old_data = rec.to_dict(enum_to_name=True)
            rec.and_filters = json.dumps(and_filters)
            rec.or_filters = json.dumps(or_filters)
        else:
            rec = MarketLiquidityFilter(
                user_id=user_id,
                market_type=market_type,
                and_filters=json.dumps(and_filters),
                or_filters=json.dumps(or_filters),
            )
            db.session.add(rec)
        db.session.commit()

        AdminOperationLog.new_add_or_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectStatistic.MarketLiquidityFilter,
            old_data=old_data,
            new_data=rec.to_dict(enum_to_name=True),
        )
