import json
import random

from webargs import fields

from flask import g

from app import config

from app.api.common import Resource
from app.api.common import Namespace
from app.api.common import respond_with_code
from app.api.common.fields import EnumField, TimestampField, PageField, LimitField
from app.business import <PERSON><PERSON><PERSON><PERSON>, LockKeys
from app.business.insight import get_participles
from app.business.push_statistic import UserTagGroupBiz
from app.business.push_statistic import app_push_user_statistic
from app.business.information import InformationClient
from app.caches.kline import AssetInformationCache

from app.common import Language, language_cn_names

from app.models import db
from app.models import AppPush
from app.models import AppPushContent
from app.models.mongo.insight import (
    CoinExInsightMySQL as CoinExInsight,
    CoinExInsightContentMySQL as CoinExInsightContent,
    ArticleType,
    ArticleStatus,
    ReportType,
    MAX_ADS_COUNT,
    MAX_COIN_COUNT,
)
from app.models.mongo.translation import TranslationTaskMySQL
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectOperation
from app.schedules.insight import update_insight_caches_task

from app.utils import url_join
from app.utils import current_timestamp
from app.exceptions import InvalidArgument

ns = Namespace('Insight')
lang_names = language_cn_names()


@ns.route('/push')
@respond_with_code
class PushResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.String(required=True),
        name=fields.String(required=True),
        push_type=EnumField(AppPush.PushType, default=AppPush.PushType.INFORMATION),
        push_time=TimestampField(required=True),
        remark=fields.String(required=False),
        ttl=fields.Integer(required=False),
    ))
    def post(cls, **kwargs):
        """资讯-推送PUSH"""
        if not (insight := CoinExInsight.query.filter_by(mongo_id=kwargs['id']).first()):
            raise InvalidArgument(message='invalid information id')
            
        insight_contents = CoinExInsightContent.query.filter_by(insight_id=kwargs['id']).all()
        if len(insight_contents) == 0:
            raise InvalidArgument(message='insight is empty')

        jump_id = config['INFORMATION_PUSH']['insight_jump_id']
        article_type = insight.article_type.name
        frontend_path = f"/mobile/insight/{article_type.lower()}/{insight.seo_url_keyword}-{insight.mongo_id}"
        url = url_join(config['SITE_URL'], frontend_path)

        name = kwargs['name']
        push_type = kwargs['push_type']
        push_time = kwargs['push_time']
        remark = kwargs.get('remark') or ''
        ttl = kwargs.get('ttl') or None

        if name == '':
            raise InvalidArgument(message='推送名称不能为空')

        groups = config['INFORMATION_PUSH']['group_id']
        if isinstance(groups, int):
            groups = [groups]
        groups = UserTagGroupBiz.filter_tag_group_ids(ids=groups)
        groups = json.dumps(groups) if groups else ''

        app_push = db.session_add_and_flush(AppPush(
            created_by=g.user.id,
            name=name,
            push_type=push_type,
            push_time=push_time,
            remark=remark,
            user_type=AppPush.UserType.TARGET_USER,
            ttl=ttl,
            groups=groups,
            jump_id=jump_id,
            status=AppPush.Status.CREATED,
        ))

        for insight_content in insight_contents:
            content = insight_content.content_text
            if len(content) > 500:
                content = content[:500] + '...'
            db.session.add(AppPushContent(
                app_push_id=app_push.id,
                lang=insight_content.lang,
                title=insight_content.title,
                url=url,
                content=content
            ))

        db.session.commit()

        app_push_user_statistic.delay(app_push.id)

        AdminOperationLog.new_send(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.Insight,
            detail=kwargs,
        )

        return dict(
            push_id=app_push.id,
        )


@ns.route('')
@respond_with_code
class ListResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        title=fields.String,
        article_type=EnumField(ArticleType),
        publish_start=TimestampField(required=False),
        publish_end=TimestampField(required=False),
        seo_url_keyword=fields.String,
        status=EnumField(ArticleStatus),
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """Insight-内容管理"""
        query = CoinExInsight.query.filter(
            CoinExInsight.status.in_([ArticleStatus.EFFECTIVE, ArticleStatus.DISABLED])
        ).order_by(CoinExInsight.is_top.desc(), CoinExInsight.is_ads.desc(), CoinExInsight.publish_at.desc())
        
        if title := kwargs.get('title'):
            query = query.filter(CoinExInsight.title.contains(title))
        if article_type := kwargs.get('article_type'):
            query = query.filter_by(article_type=article_type)
        if publish_start := kwargs.get('publish_start'):
            query = query.filter(CoinExInsight.publish_at >= publish_start)
        if publish_end := kwargs.get('publish_end'):
            query = query.filter(CoinExInsight.publish_at <= publish_end)
        if status := kwargs.get('status'):
            query = query.filter_by(status=status)
        if seo_url_keyword := kwargs.get('seo_url_keyword'):
            query = query.filter(CoinExInsight.seo_url_keyword.contains(seo_url_keyword))

        page = kwargs['page']
        limit = kwargs['limit']
        rows = query.offset((page - 1) * limit).limit(limit).all()
        total = query.count()
        
        items = []
        for row in rows:
            items.append(
                dict(
                    id=row.mongo_id,
                    index=row.order_number,
                    title=row.title,
                    article_type=row.article_type.name,
                    coins=row.coins,
                    read=row.read,
                    remark=row.remark,
                    enable=row.enable,
                    is_top=row.is_top,
                    is_ads=row.is_ads,
                    publish_at=row.publish_at,
                )
            )
        lang_names = language_cn_names()
        online_assets = AssetInformationCache().hkeys()
        return dict(
            items=items,
            total=total,
            assets=online_assets,
            article_types=ArticleType,
            report_types=ReportType,
            langs={lang.name: lang_names[lang] for lang in Language},
        )


class InsightMixin:

    @classmethod
    def _get_row(cls, object_id):
        if not (row := CoinExInsight.query.filter_by(mongo_id=object_id).first()):
            raise InvalidArgument(message='invalid id')
        return row


@ns.route('/<object_id>/enable')
@respond_with_code
class InsightEnableResource(InsightMixin, Resource):

    @classmethod
    def put(cls, object_id):
        """Insight-内容启用/禁用"""
        row = cls._get_row(object_id)
        old_data = row.to_dict()
        row.status = row.negation

        contents = CoinExInsightContent.query.filter_by(insight_id=object_id).all()
        content_map = {content.lang: content for content in contents}

        # 检查英文内容是否完整
        us_content = content_map.get(Language.EN_US)
        if invalid_msg := cls._check_valid(row, us_content, Language.EN_US):
            raise InvalidArgument(message=invalid_msg)

        # 检查是否有正在进行中的异步任务
        tasks = TranslationTaskMySQL.get_business_tasks(
            TranslationTaskMySQL.Business.INSIGHT,
            business_id=object_id
        )
        for task in tasks:
            if task and task.status != TranslationTaskMySQL.Status.FINISHED:
                raise InvalidArgument(message="异步翻译处理中，处理完毕后再启用")

        for content in contents:
            content.status = row.status
        
        db.session.commit()
        update_insight_caches_task.delay()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.Insight,
            old_data=old_data,
            new_data=row.to_dict(),
        )

        return dict()

    @classmethod
    def _check_valid(cls, row, content, lang):
        if ((row.status == ArticleStatus.EFFECTIVE) and
            (not content or not content.title or not content.content_html or
                (content.article_type != ArticleType.DAILY and not content.abstract))
        ):
            msg = f"[{lang_names[lang]}] 内容不完整，处理完毕后再启用"
            return msg
        else:
            return None


@ns.route('/<object_id>/ads/enable')
@respond_with_code
class InsightAdsEnableResource(InsightMixin, Resource):

    @classmethod
    def put(cls, object_id):
        """Insight-广告位启用/禁用"""
        row = cls._get_row(object_id)
        old_data = row.to_dict()
        set_value = not row.is_ads
        cls._validate(set_value, row)
        row.is_ads = set_value
        db.session.commit()
        update_insight_caches_task.delay()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.Insight,
            old_data=old_data,
            new_data=row.to_dict(),
        )

        return dict()

    @classmethod
    def _validate(cls, set_value: bool, row):
        if not set_value:
            return
        count = CoinExInsight.query.filter_by(is_ads=True).count()
        if row.article_type == ArticleType.DAILY:
            raise InvalidArgument(message='daily无法开启广告位')
        if count >= MAX_ADS_COUNT:
            raise InvalidArgument(message='最多只能设置 3个广告位')


@ns.route('/<object_id>/update-information')
@respond_with_code
class InsightUpdateInformationResource(InsightMixin, Resource):

    @classmethod
    def put(cls, object_id):
        """Insight-同步资讯数据"""
        row = cls._get_row(object_id)
        if row.publish_at.timestamp() > current_timestamp():
            raise InvalidArgument(message='无需同步，文章发布后币种资讯会主动收录当前文章')

        return InformationClient().update_insight(row.id)


@ns.route('/<object_id>')
@respond_with_code
class DetailResource(InsightMixin, Resource):

    @classmethod
    def get(cls, object_id):
        """Insight-内容详情"""
        lang_names = language_cn_names()
        online_assets = AssetInformationCache().hkeys()
        extra = dict(
            assets=online_assets,
            article_types=ArticleType,
            report_types=ReportType,
            langs={lang.name: lang_names[lang] for lang in Language},
        )
        if object_id == '0':
            return extra
        row = cls._get_row(object_id)
        ret = {
            'title': row.title,
            'seo_url_keyword': row.seo_url_keyword,
            'coins': row.coins,
            'publish_at': row.publish_at,
            'article_type': row.article_type.name,
            'report_type': row.report_type.name if row.report_type else None,
            'remark': row.remark,
        }
        content_rows = CoinExInsightContent.query.filter_by(insight_id=object_id).all()
        contents = {}
        for row in content_rows:
            contents[Language(row.lang).name] = {
                'id': row.mongo_id,
                'report': row.report,
                'report_url': row.report_url,
                'cover': row.cover,
                'cover_url': row.cover_url,
                'app_cover': row.app_cover,
                'app_cover_url': row.app_cover_url,
                'title': row.title,
                'abstract': row.abstract,
                'content_text': row.content_text,
                'content_html': row.content_html,
                'participles': row.participles,
                'title_participles': row.title_participles,
                'content_participles': row.content_participles,
            }
        missing_langs = set(lang.name for lang in Language) - set(contents.keys())
        for lang in missing_langs:
            contents[lang] = {
                'report': None,
                'report_url': None,
                'cover': None,
                'cover_url': None,
                'app_cover': None,
                'app_cover_url': None,
                'title': '',
                'abstract': '',
                'content_text': '',
                'content_html': '',
            }
        ret['contents'] = contents
        return dict(
            data=ret,
            **extra
        )

    @classmethod
    def delete(cls, object_id):
        """Insight-内容删除"""
        row = cls._get_row(object_id)
        row.status = ArticleStatus.DELETED
        contents = CoinExInsightContent.query.filter_by(insight_id=object_id).all()
        for content in contents:
            content.status = row.status
        
        db.session.commit()
        update_insight_caches_task.delay()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.Insight,
            detail=dict(id=object_id, title=row.title),
        )
        return dict()

    @classmethod
    @ns.use_kwargs(dict(
        title=fields.String(required=True),
        seo_url_keyword=fields.String(required=True),
        publish_at=TimestampField(is_ms=True),
        article_type=EnumField(ArticleType, required=True),
        report_type=EnumField(ReportType, required=False, allow_none=True),
        coins=fields.List(fields.String, missing=[]),
        remark=fields.String(allow_none=True),
        contents=fields.Dict(required=True),
        force_submit=fields.Bool
    ))
    def put(cls, object_id, **kwargs):
        """Insight-内容编辑"""
        with CacheLock(LockKeys.insight_operation(object_id), wait=False):
            cls._check(kwargs)
            cls._format(kwargs)
            cls._parse(kwargs)
            kwargs['coins_upper'] = [coin.upper() for coin in kwargs['coins']]
            
            if object_id == '0':
                kwargs['publish_at'] = kwargs['publish_at'].replace(microsecond=random.randint(0, 1000000))
                row = CoinExInsight(order_number=CoinExInsight.next_order_number())
                db.session.add(row)
            else:
                row = cls._get_row(object_id)
            
            old_data = row.to_dict()
            row.title = kwargs['title']
            row.publish_at = kwargs['publish_at']
            row.seo_url_keyword = kwargs['seo_url_keyword']
            row.article_type = kwargs['article_type']
            row.report_type = kwargs['report_type'] if kwargs['report_type'] else None
            row.remark = kwargs.get('remark')
            row.coins = kwargs['coins']
            
            if object_id == '0':
                db.session.flush()  # 获取新插入行的ID
                AdminOperationLog.new_add(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectOperation.Insight,
                    detail=row.to_dict(),
                )
            else:
                AdminOperationLog.new_edit(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectOperation.Insight,
                    old_data=old_data,
                    new_data=row.to_dict(),
                )
            
            try:
                content_rows = CoinExInsightContent.query.filter_by(insight_id=row.mongo_id).all()
                lang_contents = {x.lang: x for x in content_rows}
                for lang_str, content in kwargs['contents'].items():
                    if (not content['title'] or not content['content_html']) and not kwargs.get('force_submit'):
                        continue
                    lang = Language[lang_str]
                    content_row = lang_contents.get(lang)
                    if not content_row:
                        content_row = CoinExInsightContent(
                            insight_id=row.mongo_id,
                            article_type=row.article_type,
                            report_type=row.report_type,
                            lang=lang,
                            publish_at=row.publish_at,
                            status=row.status
                        )
                        db.session.add(content_row)
                    else:
                        content_row.article_type = row.article_type
                        content_row.report_type = row.report_type
                        content_row.publish_at = row.publish_at
                        content_row.status = row.status
                    
                    old_content_data = content_row.to_dict()
                    content_row.cover = content['cover']
                    content_row.app_cover = content['app_cover']
                    content_row.report = content.get('report')
                    content_row.title = content['title']
                    content_row.abstract = content.get('abstract')
                    content_row.content_text = content['content_text']
                    content_row.content_html = content['content_html']
                    content_row.participles = content['participles']
                    content_row.title_participles = content['title_participles']
                    content_row.content_participles = content['content_participles']
                    
                    AdminOperationLog.new_edit(
                        user_id=g.user.id,
                        ns_obj=OPNamespaceObjectOperation.Insight,
                        old_data=old_content_data,
                        new_data=content_row.to_dict(),
                        special_data=dict(lang=str(lang), insight_id=row.mongo_id),
                    )
                
                db.session.commit()
            except Exception as e:
                if object_id == '0':
                    # 对于新建的主文档，如果内容保存失败，需要删除
                    db.session.rollback()
                raise e

            update_insight_caches_task.delay()
            return dict(id=row.mongo_id)

    @classmethod
    def _check(cls, params):
        if len(coins := params['coins']) > MAX_COIN_COUNT:
            raise InvalidArgument(message='币种数量不得超过 5个')
        online_assets = AssetInformationCache().hkeys()
        for coin in coins:
            if coin not in online_assets:
                raise InvalidArgument(message='invalid coins')

    @classmethod
    def _format(cls, params):
        if params['article_type'] is ArticleType.DAILY:
            params['report_type'] = None

    @classmethod
    def _parse(cls, params):
        for lang_str, content in params['contents'].items():
            if not content['title'] or not content['content_html']:
                continue
            lang = Language[lang_str]
            participle = get_participles(lang, content['title'], content['content_html'])
            content['content_text'] = participle['text']
            content['participles'] = participle['participles']
            content['title_participles'] = participle['title_participles']
            content['content_participles'] = participle['content_participles']

    @classmethod
    def patch(cls, object_id):
        """Insight-内容置顶/取消置顶"""
        row = cls._get_row(object_id)
        if row.article_type == ArticleType.DAILY:
            raise InvalidArgument(message='DAILY 不支持置顶')

        old_data = row.to_dict()

        set_value = not row.is_top
        if set_value is True:
            cnt = CoinExInsight.query.filter_by(
                article_type=row.article_type,
                is_top=True
            ).count()
            if cnt >= 3:
                raise InvalidArgument(message=f'置顶超出最大限制，每个分类最多可置顶3篇内容')
        row.is_top = set_value
        db.session.commit()
        update_insight_caches_task.delay()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.Insight,
            old_data=old_data,
            new_data=row.to_dict(),
        )

        return dict()

    @classmethod
    def _validate(cls, set_value: bool):
        if not set_value:
            return
        count = CoinExInsight.query.filter_by(is_ads=True).count()
        if count >= MAX_ADS_COUNT:
            raise InvalidArgument(message='最多只能设置 3个广告位')


@ns.route('/participle')
@respond_with_code
class ParticipleResource(InsightMixin, Resource):

    @classmethod
    @ns.use_kwargs(dict(
        lang=EnumField(Language, required=True),
        title=fields.String(required=True),
        content=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        """Insight-内容详情-分词"""
        lang = kwargs['lang']
        title = kwargs['title']
        content = kwargs['content']
        return get_participles(lang, title, content)
