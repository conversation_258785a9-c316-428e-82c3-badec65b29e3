import json
from collections import defaultdict
from decimal import Decimal
from enum import Enum

from flask import g
from flask_babel import force_locale, _
from marshmallow import fields, Schema, post_load
from sqlalchemy import func

from app import Language
from app.api.common import Namespace, Resource, respond_with_code
from app.api.common.fields import EnumField, LimitField, PageField, ObjectIdField
from app.business import CacheLock, LockKeys
from app.business.clients.p2p_fair_price import P2pFiatConfigPriceBiz
from app.business.p2p.advertising import P2pAdvertisingBiz
from app.business.p2p.config import p2p_setting
from app.business.p2p.message import send_message_to_user_by_pay_channel_invalid_task
from app.business.p2p.pay_channel import PayChannelBus, pay_channel_invalid_remove_dependencies
from app.business.p2p.t_plus_n import RuleKey, Operator, UserIdentity, Choice
from app.business.p2p.utils import P2pUtils
from app.business.question_bank.question import QuestionGroupManage
from app.caches.p2p import (FiatPayChannelCache, P2pFiatMarketCache, P2pFiatCache, PayChannelCache,
                            CountryFiatCache, CountrySuggestPayChannelCache, P2pAssetConfigCache)
from app.common import get_country, language_name_cn_names, list_country_codes_3, get_code_to_cn_name, ADMIN_EXPORT_LIMIT
from app.exceptions import InvalidArgument, RecordNotFound
from app.models import User, ZERO_OBJECT_ID, P2pFairPrice, P2pTPlusNRule, db, P2pUserTPlusNRecord, \
    P2pUserTradeSummary, KycVerification, P2pUser
from app.models.mongo import AutoOfflineAdvReason
from app.models.mongo.base import Status
from app.models.mongo.p2p.config import P2pFiat, P2pFiatMarket, P2pAssetConfig
from app.models.mongo.p2p.pay_channel import FormModel, P2pPayChannelMySQL, P2pUserFeedbackChannelMySQL, \
    P2pFiatPayChannelMySQL, P2pCountryFiat, P2pCountrySuggestPayChannelMySQL, \
    FiatDataModel
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectFiat
from app.schedules.p2p.advertising import offline_advertising_by_asset_fiat_task, \
    update_advertising_by_pay_channel_invalid_task
from app.utils import batch_iter, now, max_length_validator, export_xlsx, group_by
from app.utils.helper import Struct

ns = Namespace('P2p - Config')

PAY_CHANNEL_COLORS = [
    "#2196F3",
    "#8BC34A",
    "#6852F4",
    "#0EAD98",
    "#FF6984",
    "#FFC107",
    "#03A9F4",
    "#F80EA8",
    "#793AE9",
    "#CDDC39",
    "#3852E4",
    "#4CAF50",
    "#9C27B0",
    "#00BCD4",
    "#FF9800",
    "#703107",
    "#F44336",
    "#8A4D1C",
    "#71717A",
    "#484852",
    "#B4B7BD",
]


class AdminStatusEnum(Enum):
    VALID = "开启"
    INVALID = "关闭"


@ns.route('/asset')
@respond_with_code
class P2pAssetConfigResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String,
        status=EnumField(Status)
    ))
    def get(cls, **kwargs):
        """p2p配置-p2p参数配置-获取"""
        query = P2pAssetConfig.query
        if asset := kwargs.get("asset"):
            query = query.filter(P2pAssetConfig.asset == asset)
        if status := kwargs.get("status"):
            query = query.filter(P2pAssetConfig.status == status)
        rows = query.order_by(P2pAssetConfig.id.desc()).all()

        f_model = P2pFiatMarket
        fiat_cfgs = f_model.query.filter(
            f_model.status == Status.VALID,
        ).with_entities(
            f_model.fiat,
            f_model.asset
        ).all()
        asset_fiats = group_by(lambda x: x.asset, fiat_cfgs)

        items = []
        for row in rows:
            item = row.to_dict(enum_to_name=True)
            item["fiats"] = [i.fiat for i in asset_fiats.get(row.asset, [])]
            items.append(item)

        return dict(
            items=items,
            statuses=AdminStatusEnum,
            assets=["USDT", "USDC", "BTC", "ETH"]
        )

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String(required=True),
        merchant_fee_rate=fields.Decimal(required=True, default=0),
        min_limit=fields.Decimal(required=True, default=0),
        max_limit=fields.Decimal(required=True, default=0),
        precision=fields.Integer(required=True, default=8),
    ))
    def post(cls, **kwargs):
        """p2p参数配置-p2p参数配置-新增"""
        struct = Struct(**kwargs)
        if P2pAssetConfig.query.filter(P2pAssetConfig.asset == struct.asset).first():
            raise InvalidArgument(message=f"{struct.asset} already exists")
        
        new_config = P2pAssetConfig(
            asset=struct.asset,
            merchant_fee_rate=struct.merchant_fee_rate,
            min_limit=struct.min_limit,
            max_limit=struct.max_limit,
            status=Status.VALID,
            precision=struct.precision
        )
        db.session.add(new_config)
        db.session.commit()
        
        P2pAssetConfigCache.reload()
        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PAssetConfig,
            detail=kwargs,
        )

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String(required=True),
        merchant_fee_rate=fields.Decimal(required=True, default=0),
        min_limit=fields.Decimal(required=True, default=0),
        max_limit=fields.Decimal(required=True, default=0),
        precision=fields.Integer(required=True, default=8),
    ))
    def put(cls, **kwargs):
        """p2p参数配置-p2p参数配置-修改"""
        struct = Struct(**kwargs)
        p2p_asset_config = P2pAssetConfig.query.filter(P2pAssetConfig.asset == struct.asset).first()
        if not p2p_asset_config:
            raise RecordNotFound(message=f"{struct.asset} not found")
        old_data = p2p_asset_config.to_dict(enum_to_name=True)
        
        p2p_asset_config.merchant_fee_rate = struct.merchant_fee_rate
        p2p_asset_config.min_limit = struct.min_limit
        p2p_asset_config.max_limit = struct.max_limit
        p2p_asset_config.precision = struct.precision
        db.session.commit()
        
        P2pAssetConfigCache.reload()
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PAssetConfig,
            old_data=old_data,
            new_data=p2p_asset_config.to_dict(enum_to_name=True),
        )

    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String(required=True),
        status=EnumField(Status, required=True)
    ))
    def patch(cls, **kwargs):
        """p2p参数配置-p2p参数配置-修改状态"""
        asset, status = kwargs["asset"], kwargs["status"]
        config = P2pAssetConfig.query.filter(P2pAssetConfig.asset == asset).first()
        if not config:
            raise RecordNotFound(message=f"{asset} not found")
        old_data = config.to_dict(enum_to_name=True)
        if status == Status.VALID:
            config.online_at = now()
        config.status = status
        config.updated_at = now()
        db.session.commit()
        
        P2pAssetConfigCache.reload()
        if config.status == Status.INVALID:
            offline_advertising_by_asset_fiat_task.delay(AutoOfflineAdvReason.ASSET_INVALID, asset)
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PAssetConfig,
            old_data=old_data,
            new_data=config.to_dict(enum_to_name=True),
        )


@ns.route("/settings")
@respond_with_code
class P2pTradeConfigResource(Resource):

    @classmethod
    def get_settings(cls):
        return {item['name']: item['value'] for item in p2p_setting.fields_and_values_json}

    @classmethod
    def get(cls):
        """p2p参数配置-p2p参数配置-获取交易配置"""
        ret = dict(
            settings=cls.get_settings(),
            question_groups={str(i.mongo_id): i.name for i in QuestionGroupManage.get_all_valid_query()}
        )
        return ret

    @classmethod
    @ns.use_kwargs(dict(
        settings=fields.Dict(required=True)
    ))
    def post(cls, **kwargs):
        """p2p参数配置-p2p参数配置-修改交易配置"""
        old_settings = cls.get_settings()
        settings = kwargs["settings"]
        for key, value in settings.items():
            if key == "pay_timout_list":
                value = list(map(int, value))
            try:
                setattr(p2p_setting, key, value)
            except (AttributeError, ValueError) as e:
                raise InvalidArgument(message=f'{e!r}')

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PTradeConfig,
            old_data=old_settings,
            new_data=settings,
        )
        

@ns.route("/fiat")
@respond_with_code
class P2pFiatResource(Resource):
    model = P2pFiat
    
    @classmethod
    @ns.use_kwargs(dict(
        fiat=fields.String,
        limit=LimitField(missing=100),
        page=PageField(missing=1)
    ))
    def get(cls, **kwargs):
        """p2p配置-法币配置-获取"""
        query = cls.model.query
        if fiat := kwargs.get("fiat"):
            query = query.filter(cls.model.fiat == fiat)
        paginate = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        return dict(
            total=paginate.total,
            items=[i.to_dict(enum_to_name=True) for i in paginate.items],
            statuses=Status,
        )
        
    @classmethod
    @ns.use_kwargs(dict(
        fiat=fields.String(required=True),
        fiat_symbol=fields.String(required=True),
        precision=fields.Integer(required=True),
    ))
    def post(cls, **kwargs):
        """p2p配置-法币配置-新增"""
        struct = Struct(**kwargs)
        fiat = struct.fiat
        fiat_config = cls.model.query.filter(cls.model.fiat == fiat).first()
        if fiat_config:
            raise RecordNotFound(message=f"{fiat} already exists") 
        fiat_config = P2pFiat(
            fiat=fiat,
            fiat_symbol=struct.fiat_symbol,
            precision=struct.precision,
        )
        db.session_add_and_commit(fiat_config)
        P2pFiatCache.reload()
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2pFiat,
            new_data=fiat_config.to_dict(enum_to_name=True),
        )
        
    @classmethod
    @ns.use_kwargs(dict(
        fiat=fields.String(required=True),
        status=EnumField(Status, required=True)
    ))
    def patch(cls, **kwargs):
        """p2p配置-法币配置-修改状态"""
        fiat, status = kwargs["fiat"], kwargs["status"]
        fiat_config = cls.model.query.filter(cls.model.fiat == fiat).first()
        old_data = fiat_config.to_dict(enum_to_name=True)
        if not fiat_config:
            raise RecordNotFound(message=f"{fiat} not found")
        fiat_config.status = status
        if status == Status.INVALID:
            # 下架所有市场，下架法币广告
            m_model = P2pFiatMarket
            markets = m_model.query.filter(m_model.fiat == fiat).with_entities(
                m_model.fiat,
                m_model.asset,
            ).all()
            for market in markets:
                market.status = Status.INVALID
            db.session.commit()
            
            P2pFiatMarketCache.refresh_all()
            
            offline_advertising_by_asset_fiat_task.delay(
                AutoOfflineAdvReason.FIAT_MARKET_INVALID,
                fiat=fiat,
            )
        P2pFiatCache.reload()
        
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PFiat,
            old_data=old_data,
            new_data=fiat_config.to_dict(enum_to_name=True),
        )
    

@ns.route("/fiat/market")
@respond_with_code
class P2pFiatMarketResource(Resource):
    model = P2pFiatMarket

    class RuleSchema(Schema):
        left = fields.Decimal(required=True, default=Decimal())
        right = fields.Decimal(required=True, default=Decimal())

        @post_load
        def to_rule(self, data, **kwargs):
            return {"left": data["left"], "right": data["right"]}

    @classmethod
    @ns.use_kwargs(dict(
        fiat=fields.String,
        asset=fields.String,
        status=EnumField(Status),
        limit=LimitField(missing=100),
        page=PageField(missing=1)
    ))
    def get(cls, **kwargs):
        """p2p配置-法币市场-获取"""
        limit, page = kwargs['limit'], kwargs['page']
        query = cls.model.query.order_by(
            cls.model.status.desc(),
            cls.model.fiat,
        )
        for field in ["status", "fiat", "asset"]:
            if value := kwargs.get(field):
                query = query.filter(getattr(cls.model, field) == value)
        total = query.count()
        
        items = []
        for item in query.paginate(page, limit, error_out=False).items:
            items.append(item.to_dict(enum_to_name=True))

        return dict(
            total=total,
            items=items,
            assets=cls.get_all_assets(),
            fiats=P2pFiatCache().get_all_valid_fiats(),
            price_types=P2pFiatMarket.PriceType,
            sources=P2pFiatMarket.Sources,
            statuses=Status,
        )

    @classmethod
    def get_all_assets(cls):
        a_model = P2pAssetConfig
        asset_rows = a_model.query.order_by(a_model.created_at).with_entities(a_model.asset).all()
        assets = [a.asset for a in asset_rows]
        return assets

    @classmethod
    @ns.use_kwargs(dict(
        fiat=fields.String(required=True),
        asset=fields.String(required=True),
        min_limit=fields.Decimal(required=True),
        max_limit=fields.Decimal(required=True),
        is_price_limit=fields.Boolean(required=True),
        price_type=EnumField(P2pFiatMarket.PriceType, required=True),
        manual_price=fields.Decimal(default=0),
        rule=fields.Nested(RuleSchema, required=True),
    ))
    def post(cls, **kwargs):
        """p2p配置-法币市场-新增/修改"""
        struct = Struct(**kwargs)
        fiat = struct.fiat
        asset = struct.asset
        fiat_config = cls.model.query.filter(
            cls.model.fiat == fiat,
            cls.model.asset == asset
        ).first()
        old_data = fiat_config.to_dict(enum_to_name=True) if fiat_config else None
        precision = P2pFiat.get_fiat_precision(struct.fiat)
        
        if not fiat_config:
            fiat_config = P2pFiatMarket(
                fiat=fiat,
                asset=struct.asset,
                precision=precision,
                min_limit=struct.min_limit,
                max_limit=struct.max_limit,
                is_price_limit=struct.is_price_limit,
                price_type=struct.price_type,
                manual_price=struct.manual_price,
                rule=struct.rule,
            )
            db.session.add(fiat_config)
        else:
            fiat_config.min_limit = struct.min_limit
            fiat_config.max_limit = struct.max_limit
            fiat_config.is_price_limit = struct.is_price_limit
            fiat_config.price_type = struct.price_type.name
            fiat_config.manual_price = struct.manual_price
            fiat_config.rule = struct.rule
            fiat_config.updated_at = now()
        
        db.session.commit()
        
        P2pFiatConfigPriceBiz.refresh_one_fiat_price(fiat, struct.asset)
        P2pFiatMarketCache(asset).refresh_one_fiat(fiat)
        if kwargs.get("is_price_limit"):
            P2pAdvertisingBiz.offline_advertising_by_price([struct.fiat])

        AdminOperationLog.new_add_or_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PFiatConfig,
            old_data=old_data,
            new_data=fiat_config.to_dict(enum_to_name=True),
            special_data=dict(fiat=struct.fiat),
        )

    @classmethod
    @ns.use_kwargs(dict(
        fiat=fields.String(required=True),
        asset=fields.String(required=True),
        status=EnumField(Status, required=True)
    ))
    def put(cls, **kwargs):
        """p2p配置-法币市场-修改状态"""
        fiat, status = kwargs["fiat"], kwargs["status"]
        asset = kwargs["asset"]
        fiat_config = cls.model.query.filter(
            cls.model.fiat == fiat,
            cls.model.asset == asset,
        ).first()
        if not fiat_config:
            raise InvalidArgument(message=f"{fiat} {asset} 配置不存在")
        fiat_config.status = status
        db.session.commit()
        P2pFiatMarketCache(asset).refresh_one_fiat(fiat)

        if status == Status.INVALID:
            offline_advertising_by_asset_fiat_task.delay(
                AutoOfflineAdvReason.FIAT_MARKET_INVALID.name,
                fiat=fiat,
                asset=asset
            )

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PFiatConfig,
            special_data=kwargs,
        )


class FormSchema(Schema):
    filed_type = EnumField(
        P2pPayChannelMySQL.Form.FiledType,
        required=True,
        default=P2pPayChannelMySQL.Form.FiledType.TEXT
    )
    key = fields.String(required=True)
    name = fields.String(required=True)
    field_name = fields.String(required=True)
    status = EnumField(Status, missing=Status.VALID)
    required = fields.Boolean(required=True)


class LangDataSchema(Schema):
    name = fields.String(required=True)
    name_field = fields.String(required=True)
    form_map = fields.Dict(keys=fields.String(), values=fields.String(), required=True)


@ns.route('/pay-channel')
@respond_with_code
class P2pPayChannelResource(Resource):
    class AdminActiveStatus(Enum):
        PENDING = "待上架"
        ACTIVE = '上架中'
        INACTIVE = '已下架'

    @classmethod
    @ns.use_kwargs(dict(
        id=ObjectIdField(),
        status=EnumField(P2pPayChannelMySQL.ActiveStatus),
        config_type=EnumField(P2pPayChannelMySQL.ConfigType, missing=P2pPayChannelMySQL.ConfigType.NORMAL),
        page=PageField(missing=1),
        limit=LimitField(missing=1000)
    ))
    def get(cls, **kwargs):
        """p2p参数配置-(地区)支付渠道配置表-获取列表数据"""
        page, limit = kwargs["page"], kwargs["limit"]
        query = P2pPayChannelMySQL.query.filter(
            P2pPayChannelMySQL.status == Status.VALID,
            P2pPayChannelMySQL.config_type == kwargs["config_type"]
        )
        if id_ := kwargs.get("id"):
            query = query.filter(P2pPayChannelMySQL.mongo_id == str(id_))
        if status := kwargs.get("status"):
            query = query.filter(P2pPayChannelMySQL.active_status == status)
        query = query.order_by(P2pPayChannelMySQL.rank)
        total = query.count()
        query = query.paginate(page, limit, error_out=False).items
        items = []
        for item in query:
            item_dict = item.to_dict(enum_to_name=True)
            item_dict["id"] = item_dict["mongo_id"]
            # 处理form字段
            filed_type_mapper = defaultdict(list)
            for f in item.form:
                filed_type_mapper[f.filed_type].append(f.field_name)
            items.append({
                **item_dict,
                **{k: ",".join(v) for k, v in filed_type_mapper.items()}
            })

        return dict(
            total=total,
            items=items,
            statuses=cls.AdminActiveStatus,
            pay_channals={
                str(i.mongo_id): i.name for i in
                P2pPayChannelMySQL.query.filter(
                    P2pPayChannelMySQL.config_type == kwargs["config_type"],
                    P2pPayChannelMySQL.status == Status.VALID
                ).with_entities(
                    P2pPayChannelMySQL.mongo_id,
                    P2pPayChannelMySQL.name,
                ).all()
            },
        )

    @classmethod
    def get_create_pay_channel_color(cls):
        config = P2pPayChannelMySQL.query.filter(P2pPayChannelMySQL.color.isnot(None)).order_by(P2pPayChannelMySQL.id.desc()).first()
        if not config:
            return PAY_CHANNEL_COLORS[0]
        idx = (PAY_CHANNEL_COLORS.index(config.color) + 1) % len(PAY_CHANNEL_COLORS)
        return PAY_CHANNEL_COLORS[idx]

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.Str(required=True),
        config_type=EnumField(P2pPayChannelMySQL.ConfigType, required=True),
        is_need_name=fields.Boolean(required=True),
        form=fields.Nested(FormSchema, many=True, required=True),
        lang_data=fields.Dict(values=fields.Nested(LangDataSchema))
    ))
    def post(cls, **kwargs):
        """p2p参数配置-(地区)支付渠道配置表-新增数据"""
        name = kwargs["name"]
        with CacheLock(LockKeys.p2p_put_pay_channel(name)):
            if P2pPayChannelMySQL.query.filter(P2pPayChannelMySQL.name == name).first():
                raise InvalidArgument(message=f"{name} 支付渠道已经存在")
            
            # 处理表单数据
            form_data = []
            for form_item in kwargs["form"]:
                form_data.append(FormModel(
                    filed_type=form_item["filed_type"].name,
                    key=form_item["key"],
                    name=form_item["name"],
                    field_name=form_item["field_name"],
                    status=form_item["status"].name,
                    required=form_item["required"],
                ))
            
            # 处理语言数据
            lang_data_dict = {}
            for lang_key, lang_form in kwargs["lang_data"].items():
                lang_data_dict[lang_key] = {
                    "name": lang_form["name"],
                    "name_field": lang_form["name_field"],
                    "form_map": lang_form["form_map"],
                }
            
            # 获取最后一条数据的rank
            last_data = P2pPayChannelMySQL.query.order_by(P2pPayChannelMySQL.rank.desc()).first()
            rank = last_data.rank + 1 if last_data else 0
            
            # 创建新的支付渠道
            pay_channel = P2pPayChannelMySQL(
                name=name,
                config_type=kwargs["config_type"],
                is_need_name=kwargs["is_need_name"],
                form=form_data,
                lang_data=lang_data_dict,
                rank=rank,
                color=cls.get_create_pay_channel_color(),
                active_status=P2pPayChannelMySQL.ActiveStatus.PENDING,
                status=Status.VALID
            )
            
            db.session.add(pay_channel)
            db.session.commit()
            
            PayChannelCache.save_one(pay_channel)
            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectFiat.P2PPayChannelConfig,
                detail=pay_channel.to_dict(enum_to_name=True),
            )
            return dict(id=pay_channel.mongo_id)


@ns.route("/pay-channel/<string:pay_channel_id>")
@respond_with_code
class PayChannelConfigDetailResource(Resource):

    @classmethod
    def get(cls, pay_channel_id):
        """p2p参数配置-(地区)支付渠道配置表-获取详情数据"""
        name_field_mapper = {}
        for lang in Language:
            with force_locale(lang.value):
                name_field_mapper[lang.name] = _("姓名")
        base_dict = dict(
            name_mapper=name_field_mapper,
            languages=language_name_cn_names(),
        )
        if not pay_channel_id or pay_channel_id == str(ZERO_OBJECT_ID):
            return base_dict
        pay_channel = P2pPayChannelMySQL.query.filter(P2pPayChannelMySQL.mongo_id == pay_channel_id).first()
        if not pay_channel:
            raise RecordNotFound
        base_dict["pay_channel"] = pay_channel.to_dict(enum_to_name=True)
        return base_dict

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.Str(required=True),
        is_need_name=fields.Boolean(required=True),
        form=fields.Nested(FormSchema, many=True, required=True),
        lang_data=fields.Dict(values=fields.Nested(LangDataSchema))
    ))
    def put(cls, pay_channel_id, **kwargs):
        """p2p参数配置-(地区)支付渠道配置表-修改数据"""
        name = kwargs["name"]
        with CacheLock(LockKeys.p2p_put_pay_channel(name)):
            if P2pPayChannelMySQL.query.filter(
                P2pPayChannelMySQL.name == name,
                P2pPayChannelMySQL.mongo_id != pay_channel_id,
                P2pPayChannelMySQL.status == Status.VALID
            ).first():
                raise InvalidArgument(message=f"{name} 支付渠道已经存在")
            
            pay_channel = P2pPayChannelMySQL.query.filter(P2pPayChannelMySQL.mongo_id == pay_channel_id).first()
            if not pay_channel:
                raise RecordNotFound
            
            old_data = pay_channel.to_dict(enum_to_name=True)
            PayChannelBus.check_lock(pay_channel)
            
            if pay_channel.active_status == P2pPayChannelMySQL.ActiveStatus.INACTIVE:
                # 复制一条新的数据，并且软删除旧数据
                new_pay_channel = PayChannelBus.new_copy_data(pay_channel)
                pay_channel.status = Status.INVALID
                db.session.commit()
                pay_channel = new_pay_channel
            
            # 处理表单数据
            form_data = []
            for form_item in kwargs["form"]:
                form_data.append(FormModel(
                    filed_type=form_item["filed_type"].name,
                    key=form_item["key"],
                    name=form_item["name"],
                    field_name=form_item["field_name"],
                    status=form_item["status"].name,
                    required=form_item["required"],
                ))
            
            # 处理语言数据
            lang_data_dict = {}
            for lang_key, lang_form in kwargs["lang_data"].items():
                lang_data_dict[lang_key] = {
                    "name": lang_form["name"],
                    "name_field": lang_form["name_field"],
                    "form_map": lang_form["form_map"],
                }
            
            pay_channel.name = kwargs["name"]
            pay_channel.is_need_name = kwargs["is_need_name"]
            pay_channel.form = form_data
            pay_channel.lang_data = lang_data_dict
            
            db.session.commit()
            
            # hard delete old data
            PayChannelCache.reload()
            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectFiat.P2PPayChannelConfig,
                old_data=old_data,
                new_data=pay_channel.to_dict(enum_to_name=True),
                special_data=dict(name=name),
            )
            return dict(id=pay_channel.mongo_id)


@ns.route("/pay-channel/<string:pay_channel_id>/status")
@respond_with_code
class PayChannelConfigStatusResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(P2pPayChannelMySQL.ActiveStatus, required=True),
    ))
    def put(cls, pay_channel_id, **kwargs):
        """p2p参数配置-(地区)支付渠道配置表-修改状态"""
        pay_channel = P2pPayChannelMySQL.query.filter(P2pPayChannelMySQL.mongo_id == pay_channel_id).first()
        if not pay_channel:
            raise RecordNotFound
        
        old_data = pay_channel.to_dict(enum_to_name=True)
        PayChannelBus.check_lock(pay_channel)
        
        pay_channel.active_status = kwargs["status"]
        db.session.commit()
        
        PayChannelCache.save_one(pay_channel)
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PPayChannelConfig,
            old_data=old_data,
            new_data=pay_channel.to_dict(enum_to_name=True),
            special_data=dict(name=pay_channel.name),
        )
        
        FiatPayChannelCache.reload()
        if pay_channel.active_status == P2pPayChannelMySQL.ActiveStatus.INACTIVE:
            send_message_to_user_by_pay_channel_invalid_task.delay(pay_channel_id)
            update_advertising_by_pay_channel_invalid_task.delay(pay_channel_id)
            pay_channel_invalid_remove_dependencies.delay(pay_channel_id)


@ns.route("/pay-channel/<string:pay_channel_id>/lock")
@respond_with_code
class PayChannelConfigLockResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        lock=fields.Boolean(),
    ))
    def put(cls, pay_channel_id, **kwargs):
        """p2p参数配置-(地区)支付渠道配置表-修改锁定状态"""
        pay_channel = P2pPayChannelMySQL.query.filter(P2pPayChannelMySQL.mongo_id == pay_channel_id).first()
        if not pay_channel:
            raise RecordNotFound
        
        old_data = pay_channel.to_dict(enum_to_name=True)
        pay_channel.lock = kwargs["lock"]
        db.session.commit()
        
        PayChannelCache.save_one(pay_channel)
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PPayChannelConfig,
            old_data=old_data,
            new_data=pay_channel.to_dict(enum_to_name=True),
            special_data=dict(name=pay_channel.name),
        )


@ns.route("/pay-channel/<string:pay_channel_id>/type")
@respond_with_code
class PayChannelConfigTypeResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        type=EnumField(P2pPayChannelMySQL.ConfigType, required=True),
    ))
    def put(cls, pay_channel_id, **kwargs):
        """p2p参数配置-(地区)支付渠道配置表-转换类型"""
        pay_channel = P2pPayChannelMySQL.query.filter(P2pPayChannelMySQL.mongo_id == pay_channel_id).first()
        if not pay_channel:
            raise RecordNotFound
        
        old_data = pay_channel.to_dict(enum_to_name=True)
        PayChannelBus.check_lock(pay_channel)
        
        if kwargs["type"] == P2pPayChannelMySQL.ConfigType.COUNTRY:
            type_ = P2pPayChannelMySQL.ConfigType.NORMAL
        else:
            type_ = P2pPayChannelMySQL.ConfigType.COUNTRY
        
        pay_channel.config_type = type_
        db.session.commit()
        
        PayChannelCache.save_one(pay_channel)
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PPayChannelConfig,
            old_data=old_data,
            new_data=pay_channel.to_dict(enum_to_name=True),
            special_data=dict(name=pay_channel.name),
        )


@ns.route("/pay-channel/feed-back")
@respond_with_code
class P2pPayChannelFeedBackResource(Resource):

    @classmethod
    def get_user_email_dic(cls, user_ids):
        res = dict()
        for ids in batch_iter(user_ids, 5000):
            users = User.query.filter(
                User.id.in_(ids)
            ).all()
            res.update({user.id: user for user in users})
        return res

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer,
        name=fields.String,
        page=PageField(missing=1),
        limit=LimitField(missing=100)
    ))
    def get(cls, **kwargs):
        """p2p参数配置-(地区)支付渠道配置表-支付渠道反馈"""
        page, limit = kwargs['page'], kwargs['limit']
        query = P2pUserFeedbackChannelMySQL.query
        if user_id := kwargs.get("user_id"):
            query = query.filter(P2pUserFeedbackChannelMySQL.user_id == user_id)
        if name := kwargs.get("name"):
            query = query.filter(P2pUserFeedbackChannelMySQL.channel_name.contains(name))
        
        total = query.count()
        query = query.paginate(page, limit, error_out=False).items
        
        user_ids = [i.user_id for i in query]
        user_mapper = cls.get_user_email_dic(user_ids)
        items = []
        for item in query:
            user = user_mapper.get(item.user_id)
            if not user:
                continue
            items.append(dict(
                id=item.mongo_id,
                created_at=item.created_at,
                user_id=item.user_id,
                user_email=user.email,
                country_name=get_country(user.kyc_country).cn_name if user.kyc_country else "",
                fiat_name=item.fiat_name,
                remark=item.remark,
                channel_name=item.channel_name
            ))

        return dict(
            total=total,
            items=items
        )


def get_pay_channel_name_mapper():
    return {
        k: v['name'] for k, v in PayChannelCache.get_all().items()
    }


@ns.route("/pay-chanel/fiat")
@respond_with_code
class P2pFiatChannelResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        fiat=fields.String,
        channel_id=ObjectIdField(),
        status=EnumField(Status),
        page=PageField(missing=1),
        limit=LimitField(missing=100)
    ))
    def get(cls, **kwargs):
        """p2p参数配置-法币/支付渠道配置表-数据获取"""
        page, limit = kwargs["page"], kwargs["limit"]
        query = P2pFiatPayChannelMySQL.query
        if fiat := kwargs.get("fiat"):
            query = query.filter(P2pFiatPayChannelMySQL.fiat == fiat)
        if channel_id := kwargs.get("channel_id"):
            # 由于pay_channel_ids是JSON字段，需要特殊处理
            query = query.filter(func.json_contains(P2pFiatPayChannelMySQL.pay_channel_ids, f'"{channel_id}"'))
        if status := kwargs.get("status"):
            query = query.filter(P2pFiatPayChannelMySQL.status == status)
        
        query = query.order_by(P2pFiatPayChannelMySQL.id.desc())
        total = query.count()
        query = query.paginate(page, limit, error_out=False).items
        
        channel_id_name_mapper = get_pay_channel_name_mapper()
        items = []
        
        # 获取所有法币符号
        symbols = {i.fiat: i.fiat_symbol for i in P2pFiat.get_all_data()}
        
        for item in query:
            pay_channel_ids = [i for i in item.pay_channel_ids if i in channel_id_name_mapper]
            items.append(dict(
                id=str(item.mongo_id),
                fiat=item.fiat,
                fiat_symbol=symbols.get(item.fiat, ""),
                pay_channel_ids=pay_channel_ids,
                pay_channel_names="丶".join(
                    [channel_id_name_mapper[i] for i in pay_channel_ids if i in channel_id_name_mapper]
                ),
                status=item.status
            ))
        
        return dict(
            total=total,
            items=items,
            pay_channels=channel_id_name_mapper,
            statuses=AdminStatusEnum,
            fiats=list(symbols.keys()),
            symbols=symbols,
        )

    @classmethod
    @ns.use_kwargs(dict(
        fiat=fields.String(required=True),
        pay_channel_ids=fields.List(ObjectIdField(), required=True),
    ))
    def post(cls, **kwargs):
        """p2p参数配置-法币/支付渠道配置表-新增"""
        fiat = kwargs["fiat"]
        if P2pFiatPayChannelMySQL.query.filter(P2pFiatPayChannelMySQL.fiat == fiat).first():
            raise InvalidArgument(message=f"{fiat} 配置已经存在了")
        
        # 创建新的法币支付渠道
        fiat_pay_channel = P2pFiatPayChannelMySQL(
            fiat=fiat,
            pay_channel_ids=[str(pid) for pid in kwargs["pay_channel_ids"]],
            status=Status.VALID
        )
        
        db.session.add(fiat_pay_channel)
        db.session.commit()
        
        FiatPayChannelCache.reload()
        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PFiatPayChannel,
            detail=kwargs,
        )

    @classmethod
    @ns.use_kwargs(dict(
        id=ObjectIdField(required=True),
        fiat=fields.String(required=True),
        pay_channel_ids=fields.List(ObjectIdField(), required=True),
    ))
    def put(cls, **kwargs):
        """p2p参数配置-法币/支付渠道配置表-修改"""
        id_, fiat = kwargs["id"], kwargs["fiat"]
        
        # 检查是否存在相同fiat的其他记录
        if P2pFiatPayChannelMySQL.query.filter(
            P2pFiatPayChannelMySQL.fiat == fiat,
            P2pFiatPayChannelMySQL.mongo_id != str(id_)
        ).first():
            raise InvalidArgument(message=f"{fiat} 配置已经存在了")
        
        fiat_pay = P2pFiatPayChannelMySQL.query.filter(P2pFiatPayChannelMySQL.mongo_id == str(id_)).first()
        if not fiat_pay:
            raise RecordNotFound
        
        old_data = fiat_pay.to_dict(enum_to_name=True)
        
        # 更新字段
        fiat_pay.pay_channel_ids = [str(pid) for pid in kwargs['pay_channel_ids']]
        fiat_pay.fiat = fiat
        
        db.session.commit()
        FiatPayChannelCache.reload()
        
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PFiatPayChannel,
            old_data=old_data,
            new_data=fiat_pay.to_dict(enum_to_name=True),
            special_data=dict(fiat=fiat),
        )

    @classmethod
    @ns.use_kwargs(dict(
        id=ObjectIdField(required=True),
        status=EnumField(Status, required=True)
    ))
    def delete(cls, **kwargs):
        """p2p参数配置-法币/支付渠道配置表-删除"""
        id_, status = kwargs["id"], kwargs["status"]
        
        fiat_pay = P2pFiatPayChannelMySQL.query.filter(P2pFiatPayChannelMySQL.mongo_id == str(id_)).first()
        if not fiat_pay:
            raise RecordNotFound
        
        old_data = fiat_pay.to_dict(enum_to_name=True)
        fiat_pay.status = status
        
        db.session.commit()
        FiatPayChannelCache.reload()
        
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PFiatPayChannel,
            old_data=old_data,
            new_data=fiat_pay.to_dict(enum_to_name=True),
            special_data=dict(fiat=fiat_pay.fiat),
        )


@ns.route("/pay-chanel/country")
@respond_with_code
class P2pCountryPayChannelResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        country_code=fields.String,
        pay_channel_id=ObjectIdField(),
        status=EnumField(Status)
    ))
    def get(cls, **kwargs):
        """p2p参数配置-地区推荐支付渠道配置-数据获取"""
        query = P2pCountrySuggestPayChannelMySQL.query
        if country_code := kwargs.get("country_code"):
            query = query.filter(P2pCountrySuggestPayChannelMySQL.country_code == country_code)
        if pay_channel_id := kwargs.get("pay_channel_id"):
            query = query.filter(func.json_contains(P2pCountrySuggestPayChannelMySQL.pay_channel_ids, f'"{pay_channel_id}"'))
        if status := kwargs.get("status"):
            query = query.filter(P2pCountrySuggestPayChannelMySQL.status == status)
        
        # 注意：由于pay_channel_id是在JSON字段中，我们需要在应用层过滤
        all_items = query.order_by(P2pCountrySuggestPayChannelMySQL.id.desc()).all()
        
        channel_id_name_mapper = get_pay_channel_name_mapper()
        items = []
        
        for item in all_items:
            # 如果指定了pay_channel_id，需要过滤
            if pay_channel_id := kwargs.get("pay_channel_id"):
                pay_channel_id_str = str(pay_channel_id)
                # 检查是否在任何fiat_data的pay_channel_ids中
                has_channel = False
                for data in item.fiat_data:
                    if pay_channel_id_str in data.pay_channel_ids:
                        has_channel = True
                        break
                if not has_channel:
                    continue
            
            # 处理fiat_data
            fiat_data_list = sorted(item.fiat_data, key=lambda i: i.created_at, reverse=True)
            idx = len(fiat_data_list)
            
            for data in fiat_data_list:
                items.append({
                    "idx": idx,
                    "id": item.mongo_id,
                    "fiat": data.fiat,
                    "pay_channel_ids": data.pay_channel_ids,
                    "pay_channel_names": "丶".join(
                        [i for i in [channel_id_name_mapper.get(i) for i in data.pay_channel_ids] if i]),
                    "country_code": item.country_code,
                    "status": item.status,
                    "fiat_status": data.status,
                })
                idx = 0
        
        valid_code = set(P2pUtils.get_p2p_countries())
        return dict(
            total=len(items),
            items=items,
            pay_channels=channel_id_name_mapper,
            statuses=AdminStatusEnum,
            countries={code: get_country(code).cn_name for code in valid_code if code},
            fiats=P2pFiatCache().get_all_valid_fiats(),
        )

    @classmethod
    @ns.use_kwargs(dict(
        country_code=fields.String(required=True),
        fiat=fields.String(required=True),
        pay_channel_ids=fields.List(ObjectIdField(), required=True)
    ))
    def post(cls, **kwargs):
        """p2p参数配置-地区推荐支付渠道配置-新增数据"""
        country_code, fiat = kwargs["country_code"], kwargs['fiat']
        
        # 检查是否已存在相同配置
        existing_config = P2pCountrySuggestPayChannelMySQL.query.filter(
            P2pCountrySuggestPayChannelMySQL.country_code == country_code
        ).all()
        
        for config in existing_config:
            for fiat_data in config.fiat_data:
                if fiat_data.fiat == fiat:
                    raise InvalidArgument(message=f"{country_code} {fiat} 配置已经存在了")
        
        # 查找或创建配置
        config = P2pCountrySuggestPayChannelMySQL.query.filter(
            P2pCountrySuggestPayChannelMySQL.country_code == country_code
        ).first()
        
        if not config:
            # 创建新配置
            fiat_data_list = [
                FiatDataModel(
                    fiat=fiat,
                    pay_channel_ids=[str(pid) for pid in kwargs['pay_channel_ids']],
                    status=Status.VALID.name,
                    created_at=now()
                )
            ]
            
            config = P2pCountrySuggestPayChannelMySQL(
                country_code=country_code,
                status=Status.VALID.name,
                fiat_data=fiat_data_list
            )
            db.session.add(config)
        else:
            # 更新现有配置
            fiat_data_list = list(config.fiat_data) if config.fiat_data else []
            fiat_data_list.append(
                FiatDataModel(
                    fiat=fiat,
                    pay_channel_ids=[str(pid) for pid in kwargs['pay_channel_ids']],
                    status=Status.VALID.name,
                    created_at=now()
                )
            )
            config.fiat_data = fiat_data_list
        
        db.session.commit()
        CountrySuggestPayChannelCache.reload()
        
        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PCountryPayChannel,
            detail=kwargs,
        )

    @classmethod
    @ns.use_kwargs(dict(
        id=ObjectIdField(required=True),
        fiat=fields.String(required=True),
        pay_channel_ids=fields.List(ObjectIdField(), required=True)
    ))
    def put(cls, **kwargs):
        """P2p参数配置-地区推荐支付渠道配置-修改数据"""
        id_ = kwargs["id"]
        config = P2pCountrySuggestPayChannelMySQL.query.filter_by(mongo_id=str(id_)).first()
        if not config:
            raise RecordNotFound
            
        old_data = config.to_dict(enum_to_name=True)
        
        # 更新fiat_data
        fiat = kwargs['fiat']
        pay_channel_ids = [str(pid) for pid in kwargs['pay_channel_ids']]
        
        # 获取现有的fiat_data列表
        fiat_data_list = list(config.fiat_data) if config.fiat_data else []
        
        # 查找是否已存在该fiat的数据
        fiat_exists = False
        for i, data in enumerate(fiat_data_list):
            if data.fiat == fiat:
                # 更新现有fiat数据
                fiat_data_list[i] = FiatDataModel(
                    fiat=fiat,
                    pay_channel_ids=pay_channel_ids,
                    status=data.status,
                    created_at=data.created_at
                )
                fiat_exists = True
                break
                
        if not fiat_exists:
            # 添加新的fiat数据
            fiat_data_list.append(
                FiatDataModel(
                    fiat=fiat,
                    pay_channel_ids=pay_channel_ids,
                    status=Status.VALID.name,
                    created_at=now()
                )
            )
            
        config.fiat_data = fiat_data_list
        db.session.commit()
        
        CountrySuggestPayChannelCache.reload()
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PCountryPayChannel,
            old_data=old_data,
            new_data=config.to_dict(enum_to_name=True),
            special_data=dict(country_code=config.country_code),
        )

    @classmethod
    @ns.use_kwargs(dict(
        id=ObjectIdField(required=True),
        fiat=fields.String(required=True),
        status=EnumField(Status, required=True)
    ))
    def patch(cls, **kwargs):
        """P2p参数配置-地区推荐支付渠道配置-修改状态"""
        id_, status = kwargs["id"], kwargs["status"]
        config = P2pCountrySuggestPayChannelMySQL.query.filter_by(mongo_id=str(id_)).first()
        if not config:
            raise RecordNotFound
            
        old_data = config.to_dict(enum_to_name=True)
        
        # 更新fiat_data中指定fiat的状态
        fiat = kwargs['fiat']
        fiat_data_list = list(config.fiat_data) if config.fiat_data else []
        
        for i, data in enumerate(fiat_data_list):
            if data.fiat == fiat:
                fiat_data_list[i] = FiatDataModel(
                    fiat=data.fiat,
                    pay_channel_ids=data.pay_channel_ids,
                    status=status.name,
                    created_at=data.created_at
                )
                break
                
        config.fiat_data = fiat_data_list
        db.session.commit()
        
        CountrySuggestPayChannelCache.reload()
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PCountryPayChannel,
            old_data=old_data,
            new_data=config.to_dict(enum_to_name=True),
            special_data=dict(country_code=config.country_code),
        )


@ns.route("/country-fiat")
@respond_with_code
class P2pCountryResource(Resource):
    model = P2pCountryFiat

    @classmethod
    @ns.use_kwargs(dict(
        country_code=fields.String,
        fiat=fields.String,
    ))
    def get(cls, **kwargs):
        """P2p参数配置-地区法币对照-数据获取"""
        query = cls.model.query
        if country_code := kwargs.get("country_code"):
            query = query.filter(cls.model.country_code == country_code)
        if fiat := kwargs.get("fiat"):
            query = query.filter(cls.model.fiat == fiat)
            
        fiats = {i.fiat: i for i in P2pFiat.get_all_data()}
        
        query = query.order_by(cls.model.id.desc())
        total = query.count()
        items = [i.to_dict(enum_to_name=True) for i in query.all()]
        
        for i in items:
            fiat = i["fiat"]
            i["id"] = i["mongo_id"]
            i["precision"] = fiats[fiat].precision
            i["fiat_symbol"] = fiats[fiat].fiat_symbol
            
                
        return dict(
            total=total,
            items=items,
            countries={code: get_country(code).cn_name
                       for code in list_country_codes_3()},
            en_countries={code: get_country(code).en_name
                          for code in list_country_codes_3()},
            fiats=list(set(i["fiat"] for i in items if i.get('fiat')))
        )

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.String(required=True),
        fiat=fields.String(required=True),
    ))
    def put(cls, **kwargs):
        """P2p参数配置-地区法币对照-修改数据"""
        config = cls.model.query.filter(cls.model.mongo_id == kwargs['id']).first()
        if not config:
            raise RecordNotFound
            
        old_data = config.to_dict(enum_to_name=True)
        config.fiat = kwargs["fiat"]
        
        db.session.commit()
        CountryFiatCache.reload()
        
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PCountryFiatConfig,
            old_data=old_data,
            new_data=config.to_dict(enum_to_name=True),
            special_data=dict(country_code=config.country_code),
        )
    
    @classmethod
    @ns.use_kwargs(dict(
        id=fields.String(required=True),
    ))
    def delete(cls, **kwargs):
        """P2p参数配置-地区法币对照-删除数据"""
        config = cls.model.query.filter(cls.model.mongo_id == kwargs['id']).first()
        if not config:
            raise RecordNotFound
        config.status = Status.INVALID
        db.session.commit()
        CountryFiatCache.reload()
        
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PCountryFiatConfig,
            old_data=config.to_dict(enum_to_name=True),
            new_data=dict(status=Status.INVALID),
            special_data=dict(country_code=config.country_code),
        )


@ns.route("/fair-price")
@respond_with_code
class P2pFairPriceResource(Resource):
    model = P2pFairPrice

    @classmethod
    @ns.use_kwargs(dict(
        fiat=fields.String,
        limit=LimitField(missing=50),
        page=PageField(missing=1)
    ))
    def get(cls, **kwargs):
        """P2p参数配置-公允价格-查看"""
        query = cls.model.query
        if fiat := kwargs.get("fiat"):
            query = query.filter(cls.model.fiat == fiat)
        pagination = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        total = pagination.total
        items = []
        for row in pagination.items:
            item = row.to_dict()
            source_data = row.source_data
            item["source"] = cls.get_source(source_data)
            items.append(item)

        return dict(
            items=items,
            total=total,
            fiats=P2pFiatCache().get_all_valid_fiats(),
        )

    @classmethod
    def get_source(cls, source_data):
        coinex_name = source_data.get(cls.model.Platform.COINEX.name)
        third_name = source_data.get(cls.model.Platform.THIRD_PARTY.name)
        has_other = [i for i in source_data if i in P2pFairPrice.cex_platforms()]
        if coinex_name:
            source = cls.model.Platform.COINEX.name
        elif has_other:
            tmp_lst = sorted(source_data.items(), key=lambda x: x[1])
            length = len(tmp_lst)
            if not length:
                source = ""
            elif length % 2 == 1:  # 奇数长度，返回中间值
                source = tmp_lst[length // 2][0]
            else:  # 偶数长度，返回中间两个值
                source = f"{tmp_lst[length // 2 - 1][0]} + {tmp_lst[length // 2][0]}"
        else:
            source = third_name
        return source


class P2pTPlusNRuleMixin:
    model = P2pTPlusNRule

    @classmethod
    def format_group_condition(cls, group_condition: str, country_name_mapper: dict):
        group_condition = json.loads(group_condition) if group_condition else []
        conditions = []
        for r in group_condition:
            key = RuleKey[r['key']]
            op = Operator[r['op']]
            value = r['value']
            if key == RuleKey.KYC_COUNTRY:
                value = '、'.join([country_name_mapper[i] for i in value])
            elif key == RuleKey.REGISTRATION_TIME:
                value = f"{value} 天"
            elif key in [RuleKey.IS_HIGH_RISK_USER, RuleKey.TRADE_LIMIT, RuleKey.PAID_MARGIN_ENOUGH]:
                value = Choice[value].value
            elif key == RuleKey.USER_IDENTITY:
                value = UserIdentity[value].value
            elif key == RuleKey.TRADE_FIATS:
                value = '、'.join(value)
            conditions.append(f'{key.value} {op.value} {value}')
        return '\n'.join(conditions)

    @classmethod
    def check_group_condition(cls, group_condition: str):
        if not group_condition:
            raise InvalidArgument('请新增条件')
        group_condition = json.loads(group_condition)
        if not group_condition:
            raise InvalidArgument('请新增条件')
        keys = {r['key'] for r in group_condition}
        if len(keys) != len(group_condition):
            raise InvalidArgument('每个条件只可以出现一次')
        for r in group_condition:
            if not r['key'] or not r['op'] or not r['value']:
                raise InvalidArgument('请确保条件设置正确')


@ns.route("/t-plus-n/rule")
@respond_with_code
class P2pTPlusNRuleResource(Resource, P2pTPlusNRuleMixin):

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String,
        limit=LimitField(missing=50),
        page=PageField(missing=1)
    ))
    def get(cls, **kwargs):
        """P2p参数配置-T+N风控策略配置-列表"""
        query = cls.model.query.filter(cls.model.status != cls.model.Status.DELETED)
        if name := kwargs.get("name"):
            query = query.filter(cls.model.name == name)
        query = query.order_by(cls.model.updated_at.desc())
        pagination = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        total = pagination.total
        items = []
        country_name_mapper = get_code_to_cn_name()
        for row in pagination.items:
            item = row.to_dict(enum_to_name=True)
            item["group_condition"] = cls.format_group_condition(row.group_condition, country_name_mapper)
            items.append(item)

        return dict(
            items=items,
            total=total,
        )

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True, validate=max_length_validator(60)),
        status=EnumField(P2pTPlusNRule.Status, required=True),
        limit_days=fields.Integer(required=True, validate=lambda x: 0 <= x <= 10),
        group_condition=fields.String(required=True),
        remark=fields.String(required=False, validate=max_length_validator(200)),
    ))
    def post(cls, **kwargs):
        """P2p参数配置-T+N风控策略配置-新增配置"""
        cls.check_group_condition(kwargs['group_condition'])
        row = cls.model(
            name=kwargs['name'],
            status=kwargs['status'],
            limit_days=kwargs['limit_days'],
            group_condition=kwargs['group_condition'],
            remark=kwargs['remark'],
        )
        db.session_add_and_commit(row)
        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PTPlusNRuleConfig,
            detail=kwargs,
        )


@ns.route("/t-plus-n/rule/<int:rule_id>")
@respond_with_code
class P2pTPlusNRuleDetailResource(Resource, P2pTPlusNRuleMixin):

    @classmethod
    def get_base_data(cls):
        fiats = P2pUtils.get_all_valid_fiats()
        base_data = dict(
            countries={code: get_country(code).cn_name for code in list_country_codes_3()},
            operators=Operator,
            choices=Choice,
            user_idendities=UserIdentity,
            fiats=fiats,
            condition_key_dict={
                RuleKey.KYC_COUNTRY.name: {
                    'name': RuleKey.KYC_COUNTRY.value,
                    'operators': [Operator.EQ.name, Operator.NE.name]
                },
                RuleKey.REGISTRATION_TIME.name: {
                    'name': RuleKey.REGISTRATION_TIME.value,
                    'operators': [Operator.GE.name, Operator.LE.name]
                },
                RuleKey.IS_HIGH_RISK_USER.name: {
                    'name': RuleKey.IS_HIGH_RISK_USER.value,
                    'operators': [Operator.EQ.name]
                },
                RuleKey.APPEAL_RATE.name: {
                    'name': RuleKey.APPEAL_RATE.value,
                    'operators': [Operator.GE.name, Operator.LE.name]
                },
                RuleKey.USER_IDENTITY.name: {
                    'name': RuleKey.USER_IDENTITY.value,
                    'operators': [Operator.EQ.name, Operator.NE.name]
                },
                RuleKey.TRADE_LIMIT.name: {
                    'name': RuleKey.TRADE_LIMIT.value,
                    'operators': [Operator.EQ.name]
                },
                RuleKey.TRADE_FIATS.name: {
                    'name': RuleKey.TRADE_FIATS.value,
                    'operators': [Operator.EQ.name]
                },
                RuleKey.PAID_MARGIN_ENOUGH.name: {
                    'name': RuleKey.PAID_MARGIN_ENOUGH.value,
                    'operators': [Operator.EQ.name]
                },
                RuleKey.P2P_DEAL_COUNT.name: {
                    'name': RuleKey.P2P_DEAL_COUNT.value,
                    'operators': [Operator.GE.name, Operator.LE.name]
                },
                RuleKey.P2P_DEAL_AMOUNT.name: {
                    'name': RuleKey.P2P_DEAL_AMOUNT.value,
                    'operators': [Operator.GE.name, Operator.LE.name]
                },
                RuleKey.AVG_BALANCE_7_DAYS.name: {
                    'name': RuleKey.AVG_BALANCE_7_DAYS.value,
                    'operators': [Operator.GE.name, Operator.LE.name]
                },
            },
        )
        return base_data

    @classmethod
    def get(cls, rule_id):
        """P2p参数配置-T+N风控策略配置-配置详情"""
        base_data = cls.get_base_data()
        if rule_id == 0:
            return base_data

        row = cls.model.query.get(rule_id)
        if not row:
            raise RecordNotFound
        return dict(
            rule=row.to_dict(),
            **base_data
        )

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True, validate=max_length_validator(60)),
        status=EnumField(P2pTPlusNRule.Status, required=True),
        limit_days=fields.Integer(required=True, validate=lambda x: 0 <= x <= 10),
        group_condition=fields.String(required=True),
        remark=fields.String(required=False, validate=max_length_validator(200)),
    ))
    def put(cls, rule_id, **kwargs):
        """P2p参数配置-T+N风控策略配置-配置修改"""
        row = cls.model.query.get(rule_id)
        if not row:
            raise RecordNotFound
        cls.check_group_condition(kwargs['group_condition'])
        old_data = row.to_dict(enum_to_name=True)

        row.name = kwargs['name']
        row.status = kwargs['status']
        row.limit_days = kwargs['limit_days']
        row.group_condition = kwargs['group_condition']
        row.remark = kwargs['remark']
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PTPlusNRuleConfig,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(P2pTPlusNRule.Status, required=True),
    ))
    def patch(cls, rule_id, **kwargs):
        """P2p参数配置-T+N风控策略配置-修改状态"""
        row = cls.model.query.get(rule_id)
        if not row:
            raise RecordNotFound
        old_data = row.to_dict(enum_to_name=True)

        row.status = kwargs['status']
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PTPlusNRuleConfig,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )

    @classmethod
    def delete(cls, rule_id):
        """P2p参数配置-T+N风控策略配置-删除配置"""
        row = cls.model.query.get(rule_id)
        if not row:
            raise RecordNotFound

        row.status = cls.model.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PTPlusNRuleConfig,
            detail=dict(id=rule_id),
        )


@ns.route("/t-plus-n/rule/settings")
@respond_with_code
class P2pTPlusNRuleConfigSettingResource(Resource):

    @classmethod
    def get(cls):
        """P2p参数配置-T+N风控策略配置-获取全局T+N配置"""
        ret = dict(
            t_plus_n_days=p2p_setting.t_plus_n_days,
        )
        return ret

    @classmethod
    @ns.use_kwargs(dict(
        t_plus_n_days=fields.Integer(required=True)
    ))
    def post(cls, **kwargs):
        """p2p参数配置-T+N风控策略配置-修改全局T+N配置"""
        t_plus_n_days = kwargs["t_plus_n_days"]
        old_t_plus_n_days = p2p_setting.t_plus_n_days
        p2p_setting.t_plus_n_days = t_plus_n_days

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.P2PTPlusNRuleGlobalConfig,
            old_data={"t_plus_n_days": old_t_plus_n_days},
            new_data={"t_plus_n_days": t_plus_n_days},
        )


@ns.route("/t-plus-n/user-record")
@respond_with_code
class P2pTPlusNUserRecordResource(Resource):

    model = P2pUserTPlusNRecord

    export_headers = (
        {"field": "email", Language.ZH_HANS_CN: "用户邮箱"},
        {"field": "kyc_country", Language.ZH_HANS_CN: "KYC地区"},
        {"field": "user_identity", Language.ZH_HANS_CN: "用户身份"},
        {"field": "completion_rate", Language.ZH_HANS_CN: "完单率"},
        {"field": "registration_time", Language.ZH_HANS_CN: "注册时间"},
        {"field": "match_rules", Language.ZH_HANS_CN: "命中的策略名称"},
        {"field": "effect_rule", Language.ZH_HANS_CN: "应用的策略名称"},
        {"field": "real_days", Language.ZH_HANS_CN: "当前限制时限"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer,
        rule_name=fields.String,
        match_rule_id=fields.Integer,
        real_rule_id=fields.Integer,
        real_days=fields.Integer,
        limit=LimitField(missing=50),
        page=PageField(missing=1),
        export=fields.Boolean,
    ))
    def get(cls, **kwargs):
        """P2p参数配置-T+N风控命中-列表"""
        query = cls.model.query
        if user_id := kwargs.get("user_id"):
            query = query.filter(
                cls.model.user_id == user_id
            )
        if match_rule_id := kwargs.get("match_rule_id"):
            if match_rule_id == cls.model.GLOBAL_SETTING_RULE_ID:
                pass
            elif match_rule_id == cls.model.USER_SETTING_RULE_ID:
                query = query.filter(
                    cls.model.user_setting_days.is_not(None)
                )
            else:
                query = query.filter(
                    func.json_contains(cls.model.match_rules, str(match_rule_id)) > 0
                )
        if real_rule_id := kwargs.get("real_rule_id"):
            query = query.filter(
                cls.model.real_rule == real_rule_id
            )
        if (real_days := kwargs.get("real_days")) is not None:
            query = query.filter(
                cls.model.real_days == real_days
            )

        query = query.order_by(cls.model.user_id)
        if export := kwargs.get("export"):
            rows = query.limit(ADMIN_EXPORT_LIMIT).all()
            total = len(rows)
        else:
            pagination = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
            rows = pagination.items
            total = pagination.total

        user_ids, rule_ids = set(), set()
        for row in rows:
            user_ids.add(row.user_id)
            rule_ids.add(row.effect_rule)
            match_rule_ids = row.match_rules if row.match_rules else []
            rule_ids |= set(match_rule_ids)
        user_mapper = cls.get_user_dic(user_ids)
        kyc_country_mapper = cls.get_user_kyc_country_dict(user_ids)
        user_identity_mapper = cls.get_user_identity_dic(user_ids)
        completion_rate_mapper = cls.get_completion_rate_dic(user_ids)
        rule_name_mapper = cls.get_rule_name_dic()
        country_name_mapper = get_code_to_cn_name()
        items = []
        for row in rows:
            row: P2pUserTPlusNRecord
            user_id = row.user_id
            match_rule_ids = cls.get_real_match_rule_ids(row)
            match_rules = [rule_name_mapper[rule_id] for rule_id in match_rule_ids if rule_name_mapper.get(rule_id, '')]
            items.append({
                'user_id': user_id,
                'email': user_mapper.get(user_id, {}).get('email', ''),
                'registration_time': user_mapper.get(user_id, {}).get('created_at'),
                'kyc_country': kyc_country_mapper.get(user_id, ''),
                'user_identity': user_identity_mapper.get(user_id, ''),
                'completion_rate': completion_rate_mapper.get(user_id, Decimal()),
                'match_rules': '、'.join(match_rules),
                'effect_rule': rule_name_mapper.get(row.real_rule, ''),
                'real_days': row.real_days,
            })

        if export:
            for i in items:
                i['registration_time'] = i['registration_time'].strftime("%Y-%m-%d") if i['registration_time'] else ''
                i['kyc_country'] = country_name_mapper.get(i['kyc_country'], '')
            return export_xlsx(
                filename="t-plus-n-user-record",
                data_list=items,
                export_headers=cls.export_headers,
            )

        return dict(
            items=items,
            total=total,
            countries=country_name_mapper,
            rule_name_mapper=rule_name_mapper,
        )

    @classmethod
    def get_real_match_rule_ids(cls, row: P2pUserTPlusNRecord):
        match_rule_ids = row.match_rules if row.match_rules else []
        if row.user_setting_days:
            match_rule_ids.insert(0, P2pUserTPlusNRecord.USER_SETTING_RULE_ID)
        match_rule_ids.append(P2pUserTPlusNRecord.GLOBAL_SETTING_RULE_ID)
        return match_rule_ids

    @classmethod
    def get_user_dic(cls, user_ids):
        res = dict()
        for ids in batch_iter(user_ids, 5000):
            users = User.query.filter(
                User.id.in_(ids)
            ).with_entities(
                User.id,
                User.email,
                User.created_at
            ).all()
            res.update({user.id: {'email': user.email, 'created_at': user.created_at} for user in users})
        return res

    @classmethod
    def get_user_kyc_country_dict(cls, user_ids):
        res = dict()
        for ids in batch_iter(user_ids, 5000):
            res.update({
                u: c for u, c in
                KycVerification.query.filter(
                    KycVerification.user_id.in_(ids),
                    KycVerification.status == KycVerification.Status.PASSED
                ).with_entities(
                    KycVerification.user_id,
                    KycVerification.country
                ).all()
            })
        return res

    @classmethod
    def get_user_identity_dic(cls, user_ids):
        res = dict()
        for ids in batch_iter(user_ids, 5000):
            users = P2pUser.query.filter(
                P2pUser.user_id.in_(ids)
            ).with_entities(
                P2pUser.user_id,
                P2pUser.merchant_id,
            ).all()
            res.update({user.user_id: '商家' if user.merchant_id else '用户' for user in users})
        return res

    @classmethod
    def get_completion_rate_dic(cls, user_ids):
        res = dict()
        for ids in batch_iter(user_ids, 5000):
            rows = P2pUserTradeSummary.query.filter(
                P2pUserTradeSummary.user_id.in_(ids)
            ).all()
            res.update({row.user_id: row.completion_rate for row in rows})
        return res

    @classmethod
    def get_rule_name_dic(cls):
        res = dict()
        rules = P2pTPlusNRule.query.with_entities(
            P2pTPlusNRule.id,
            P2pTPlusNRule.name,
        ).all()
        res.update({rule.id: rule.name for rule in rules})
        res.update({
            P2pUserTPlusNRecord.GLOBAL_SETTING_RULE_ID: P2pUserTPlusNRecord.GLOBAL_SETTING_RULE_NAME,
            P2pUserTPlusNRecord.USER_SETTING_RULE_ID: P2pUserTPlusNRecord.USER_SETTING_RULE_NAME,
        })
        return res
