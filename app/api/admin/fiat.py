# -*- coding: utf-8 -*-

from flask import g
from sqlalchemy import func
from webargs import fields as mm_fields

from app.api.common import Namespace, respond_with_code, Resource
from app.api.common.fields import EnumField, PageField, LimitField, TimestampField
from app.business import CacheLock, LockKeys, WalletClient
from app.business.auth import get_admin_user_name_map
from app.business.fiat.base import SupportType, PartnerActivity
from app.common.constants import Language, ADMIN_EXPORT_LIMIT
from app.common.countries import get_country, list_country_codes_3_admin
from app.exceptions import InvalidArgument, RecordNotFound
from app.models import db
from app.models import User
from app.models.fiat import FiatOrder, FiatPartnerActivity
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectFiat
from app.utils import now, datetime_to_str
from app.utils.amount import amount_to_str
from app.utils.export import export_xlsx
from app.utils.helper import Struct
from app.business.fiat import get_fiat_currencies, get_fiat_assets, get_fiat_partners

ns = Namespace('Fiat')


@ns.route('/order-details')
@respond_with_code
class OrderDetailsResource(Resource):

    export_headers = (
        {"field": "payment_id", Language.ZH_HANS_CN: "支付ID"},
        {"field": "created_at", Language.ZH_HANS_CN: "创建时间(UTC)"},
        {"field": "email", Language.ZH_HANS_CN: "账号邮箱"},
        {"field": "asset", Language.ZH_HANS_CN: "币种"},
        {"field": "country", Language.ZH_HANS_CN: "KYC国家"},
        {"field": "coin_amount", Language.ZH_HANS_CN: "数量"},
        {"field": "fiat_currency", Language.ZH_HANS_CN: "使用法币"},
        {"field": "fiat_total_amount", Language.ZH_HANS_CN: "法币金额"},
        {"field": "unflat_amount", Language.ZH_HANS_CN: "含手续费单价"},
        {"field": "third_party", Language.ZH_HANS_CN: "服务商"},
        {"field": "status", Language.ZH_HANS_CN: "订单状态"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        order_type=EnumField(FiatOrder.OrderType, missing=FiatOrder.OrderType.BUY),
        asset=mm_fields.String(),
        fiat_currency=mm_fields.String(),
        third_party=mm_fields.String(),
        search_keyword=mm_fields.String(),
        status=EnumField(FiatOrder.StatusType, enum_by_value=True),
        country_code=mm_fields.String(),
        start_date=mm_fields.DateTime("%Y-%m-%d"),
        end_date=mm_fields.DateTime("%Y-%m-%d"),
        page=PageField(unlimited=True),
        limit=LimitField(missing=100),
        export=mm_fields.Boolean(missing=False)))
    def get(cls, **kwargs):
        """法币-快捷购币明细"""
        params = Struct(**kwargs)
        support_type = SupportType(params.order_type.value)
        query = FiatOrder.query.filter(
            FiatOrder.order_type == params.order_type
        )

        if params.asset:
            query = query.filter(FiatOrder.asset == params.asset)

        if params.fiat_currency:
            query = query.filter(FiatOrder.fiat_currency == params.fiat_currency)

        if params.third_party:
            query = query.filter(FiatOrder.third_party == params.third_party)

        if params.status:
            query = query.filter(FiatOrder.status == params.status)

        if params.country_code:
            query = query.join(User).filter(
                FiatOrder.user_id == User.id,
                User.location_code == params.country_code,
            )

        if params.start_date:
            query = query.filter(FiatOrder.created_at >= params.start_date)

        if params.end_date:
            query = query.filter(FiatOrder.created_at <= params.end_date)

        if params.search_keyword:
            search_user_ids = User.search_for_users(params.search_keyword)
            if search_user_ids:
                query = query.filter(FiatOrder.user_id.in_(search_user_ids))

        query = query.order_by(FiatOrder.id.desc()).with_entities(
            FiatOrder.id,
            FiatOrder.user_id,
            FiatOrder.created_at,
            FiatOrder.asset,
            FiatOrder.coin_amount,
            FiatOrder.fiat_currency,
            FiatOrder.third_party,
            FiatOrder.status,
            FiatOrder.fiat_total_amount,
            FiatOrder.payment_id,
            FiatOrder.order_id,
            FiatOrder.chain,
            FiatOrder.tx_id,
            FiatOrder.tx_url,
        )
        if params.export:
            data = query.limit(ADMIN_EXPORT_LIMIT).all()
        else:
            records = query.paginate(params.page, params.limit, error_out=False)
            data = records.items
        txs_url = cls._get_txs_url(rows=data)
        user_ids = {v.user_id for v in data}
        user_rows = User.query.filter(
            User.id.in_(user_ids),
        ).with_entities(
            User.id, User.email, User.location_code,
        ).all()
        user_email_map = {user.id: user.email for user in user_rows}
        user_country_map = {
            user.id: c.cn_name if (c := get_country(user.location_code)) else '其他'
            for user in user_rows
        }
        items = [
            dict(
                created_at=datetime_to_str(v.created_at),
                email=user_email_map.get(v.user_id, ''),
                asset=v.asset,
                user_id=v.user_id,
                coin_amount=v.coin_amount,
                fiat_currency=v.fiat_currency,
                fiat_total_amount=v.fiat_total_amount,
                payment_id=v.payment_id,
                order_id=v.order_id,
                third_party=v.third_party,
                status=v.status.value,
                tx_id=v.tx_id,
                tx_url=txs_url.get(v.id),
                country=user_country_map.get(v.user_id, ''),
            ) for v in data
        ]
        status_dict = {
            FiatOrder.StatusType.CREATE.value: "创建中",
            FiatOrder.StatusType.PENDING.value: "待审核",
            FiatOrder.StatusType.DECLINED.value: "已取消",
            FiatOrder.StatusType.REFUNDED.value: "已退款",
            FiatOrder.StatusType.APPROVED.value: "已成交",
        }
        if params.export:
            for item in items:
                item['status'] = status_dict[item['status']]
                item['created_at'] = item['created_at']
                if item['coin_amount']:
                    item['unflat_amount'] = amount_to_str(item['fiat_total_amount'] / item['coin_amount'], 8)
                else:
                    item['unflat_amount'] = '0'
            return export_xlsx(
                filename="fiat_orders_details",
                data_list=items,
                export_headers=cls.export_headers,
            )

        return dict(
            status_dict=status_dict,
            support_assets=get_fiat_assets(support_type),
            supported_fiat_currencies=get_fiat_currencies(support_type),
            third_parties=get_fiat_partners(),
            countries={code: get_country(code).cn_name for code in list_country_codes_3_admin()},
            total=query.count(),
            items=items
        )

    @classmethod
    def _get_txs_url(cls, rows):
        ret = {}
        txs = []
        mapping = {}
        for row in rows:
            if row.tx_url:
                ret.update({row.id: row.tx_url})
            elif row.chain and row.tx_id:
                key = (row.chain, row.tx_id)
                txs.append(key)
                mapping[key] = row.id
            else:
                pass
        if txs:
            txs_url = WalletClient().get_explorer_txs_url(txs)
            for index, k in enumerate(txs):
                tx_url = txs_url[index]
                ret.update({mapping[k]: tx_url})
        return ret


@ns.route('/activities')
@respond_with_code
class FiatActivitiesResource(Resource):
    model = FiatPartnerActivity
    STATUSES = dict(
        pending='待上架',
        online='上架',
        offline='已下架'
    )

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(list(STATUSES)),
        page=PageField(unlimited=True),
        limit=LimitField(missing=100),
    ))
    def get(cls, **kwargs):
        """法币-活动列表"""
        page = kwargs['page']
        limit = kwargs['limit']

        _now = now()
        model = cls.model
        query = model.query.filter(model.status == model.Status.VALID)
        if status := kwargs.get('status'):
            if status == 'pending':
                query = query.filter(model.begin_at > _now)
            elif status == 'online':
                query = query.filter(model.begin_at <= _now, model.end_at > _now)
            else:
                query = query.filter(model.end_at <= _now)

        records = query.order_by(model.is_top.desc(), model.sort_id.desc()).paginate(page, limit)
        user_ids = {item.updated_by for item in records.items}
        name_map = get_admin_user_name_map(user_ids)
        items = []
        item: FiatPartnerActivity
        for item in records.items:
            i_dict = item.to_dict(enum_to_name=True)
            if item.begin_at > _now:
                status = 'pending'
            elif item.end_at <= _now:
                status = 'offline'
            else:
                status = 'online'
            i_dict['status'] = status
            i_dict['updated_user_email'] = name_map.get(item.updated_by) or '-'
            items.append(i_dict)

        return dict(
            total=records.total,
            items=items,
            extra=dict(
                statuses=cls.STATUSES,
                activities=PartnerActivity,
                partners=get_fiat_partners(),
            )
        )


# noinspection PyUnresolvedReferences
@ns.route('/activity/<int:id_>')
@respond_with_code
class FiatActivityResource(Resource):
    model = FiatPartnerActivity

    @classmethod
    def get(cls, id_):
        """法币-活动详情"""
        extra = dict(
            activities=PartnerActivity,
            partners=get_fiat_partners(),
        )
        if not id_:
            return dict(
                extra=extra
            )

        row = cls._get_row(id_)
        result = row.to_dict(enum_to_name=True)
        result.update(
            extra=extra
        )
        return result

    @classmethod
    @ns.use_kwargs(dict(
        partner=mm_fields.String(required=True),
        name=EnumField(PartnerActivity, required=True),
        is_top=mm_fields.Boolean(required=True),
        begin_at=TimestampField(required=True),
        end_at=TimestampField(required=True),
        activity_value=mm_fields.String(required=False)
    ))
    def patch(cls, id_, **kwargs):
        """运营-活动编辑/新建"""
        cls.validate(kwargs, id_)
        activity_value = kwargs.get('activity_value', '')
        old_data = {}
        if id_ == 0:
            row = cls.model(
                partner=kwargs['partner'],
                name=kwargs['name'].name,
                begin_at=kwargs['begin_at'],
                end_at=kwargs['end_at'],
                is_top=kwargs['is_top'],
                updated_by=g.user.id,
                activity_value=activity_value,
            )
            max_sort_id = (cls.model.query.with_entities(
                func.max(cls.model.sort_id).label('max_sort_id')
            ).first().max_sort_id or 0) + 1
            row.sort_id = max_sort_id
            db.session.add(row)
        else:
            row = cls._get_row(id_)
            old_data = row.to_dict(enum_to_name=True)
            row.name = kwargs['name'].name
            row.partner = kwargs['partner']
            row.begin_at = kwargs['begin_at']
            row.end_at = kwargs['end_at']
            row.is_top = kwargs['is_top']
            row.updated_at = now()
            row.updated_by = g.user.id
            row.activity_value = activity_value,
        db.session.commit()

        if id_ == 0:
            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectFiat.FiatActivity,
                detail=kwargs,
            )
        else:
            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectFiat.FiatActivity,
                old_data=old_data,
                new_data=row.to_dict(enum_to_name=True),
            )
        return row

    @classmethod
    def validate(cls, params, id_):
        if params['begin_at'] > params['end_at']:
            raise InvalidArgument(message='开始时间不能大于结束时间')
        if params['partner'] not in get_fiat_partners():
            raise InvalidArgument(message='服务商不存在')
        if params["name"] in [PartnerActivity.NEW_USER, PartnerActivity.FEE_REDUCTION]:
            if not params.get('activity_value'):
                raise InvalidArgument(message='活动需要输入手续费减免比例')
        model = cls.model
        query = model.query.filter(
            model.partner == params['partner'],
            model.status == model.Status.VALID,
            model.begin_at < params['end_at'],
            model.end_at > params['begin_at'],
        )
        if id_:
            rows = query.filter(model.id != id_).all()
        else:
            rows = query.all()
        if rows:
            raise InvalidArgument(message='同一时间段只能配置一个活动')

    @classmethod
    def delete(cls, id_):
        """运营-活动删除"""
        row = cls._get_row(id_)
        row.status = cls.model.Status.DELETED
        row.updated_by = g.user.id
        db.session_add_and_commit(row)

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectFiat.FiatActivity,
            detail=row.to_dict(enum_to_name=True),
        )
        return row

    @classmethod
    def _get_row(cls, id_):
        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound
        return row


# noinspection PyUnresolvedReferences
@ns.route('/activity/<int:id_>/sort_id')
@respond_with_code
class FiatActivitySortIDResource(Resource):
    model = FiatPartnerActivity

    @classmethod
    def post(cls, id_):
        """运营-活动上移"""
        with CacheLock(LockKeys.edit_operation_sort("fiat_activity"), wait=False):
            model = cls.model
            row = model.query.get(id_)
            if row is None:
                raise RecordNotFound
            old_data = row.to_dict(enum_to_name=True)
            other = model.query.filter(
                model.status == model.Status.VALID,
                model.sort_id > row.sort_id
            ).order_by(model.sort_id.asc()).first()
            if other is not None:
                row.sort_id, other.sort_id = other.sort_id, row.sort_id
                row.updated_at = other.updated_at = now()
                row.updated_by = other.updated_by = g.user.id
                db.session.commit()

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectFiat.FiatActivity,
                old_data=old_data,
                new_data=row.to_dict(enum_to_name=True),
            )
            return {}
