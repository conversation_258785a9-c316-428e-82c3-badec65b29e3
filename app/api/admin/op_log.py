from webargs import fields
from collections import defaultdict
from bson import ObjectId
from bson.errors import InvalidId

from app.api.common import Resource
from app.api.common import Namespace
from app.api.common import respond_with_code
from app.api.common.fields import <PERSON><PERSON><PERSON>ield
from app.api.common.fields import <PERSON>it<PERSON>ield
from app.api.common.fields import Timestamp<PERSON>ield
from app.business.auth import get_admin_user_name_map
from app.models.user import User
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog
from app.models.mongo.op_log import BaseOPNamespaceObjectMeta
from app.exceptions.basic import InvalidArgument

ns = Namespace("OP Log")


@ns.route('/admin-op-log')
@respond_with_code
class AdminOperationLogResource(Resource):
    OPERATION_NAME_MAP = {
        AdminOperationLog.Operation.LOGIN.name: '登录',
        AdminOperationLog.Operation.QUERY.name: '查询',
        AdminOperationLog.Operation.ADD.name: '新增',
        AdminOperationLog.Operation.EDIT.name: '编辑',
        AdminOperationLog.Operation.DELETE.name: '删除',
        AdminOperationLog.Operation.AUDIT.name: '审核',
        AdminOperationLog.Operation.SEND.name: '发送',
        AdminOperationLog.Operation.STOP.name: '停止',
        AdminOperationLog.Operation.FLAT.name: '平仓',
    }

    @classmethod
    def get_admin_operation_log(cls, _id: str) -> AdminOperationLog:
        try:
            obj_id = ObjectId(_id)
        except InvalidId:
            raise InvalidArgument(message='invalid id')

        admin_operation_log = AdminOperationLog.query.filter(
            AdminOperationLog.mongo_id == str(obj_id)
        ).first()
        if not admin_operation_log:
            raise InvalidArgument(message='invalid id')

        return admin_operation_log

    @classmethod
    @ns.use_kwargs(dict(
        keyword=fields.String(),
        namespace=fields.String(),
        object=fields.String(),
        operation=EnumField(enum=AdminOperationLog.Operation),
        started_at=TimestampField,
        ended_at=TimestampField,
        last_id=fields.String(),
        first_id=fields.String(),
        limit=LimitField(missing=100),
    ))
    def get(cls, **kwargs):
        """
        系统-操作记录
        """
        query = AdminOperationLog.query
        if keyword_str := kwargs.get('keyword'):
            keyword_results = User.search_for_users(keyword_str)
            if len(keyword_results) == 0:
                raise InvalidArgument(message='操作人不存在')
            query = query.filter(AdminOperationLog.user_id == keyword_results[0])
        if namespace_str := kwargs.get('namespace'):
            namespace = BaseOPNamespaceObjectMeta.get_namespace_by_desc(namespace_str)
            if not namespace:
                raise InvalidArgument(message='业务类型不存在')
            query = query.filter(AdminOperationLog.namespace == namespace.name)
            if object_str := kwargs.get('object'):
                obj = BaseOPNamespaceObjectMeta.get_object_by_desc(namespace, object_str)
                if not obj:
                    raise InvalidArgument(message='操作对象不存在')
                query = query.filter(AdminOperationLog.object == obj.name)
        if operation_str := kwargs.get('operation'):
            query = query.filter(AdminOperationLog.operation == operation_str)
        started_at, ended_at = kwargs.get('started_at'), kwargs.get('ended_at')
        if started_at and ended_at:
            if started_at >= ended_at:
                raise InvalidArgument(message='开始时间必须小于结束时间')
        if started_at:
            query = query.filter(AdminOperationLog.created_at >= started_at)
        if ended_at:
            query = query.filter(AdminOperationLog.created_at <= ended_at)

        limit = kwargs['limit']
        if first_id := kwargs.get('first_id'):
            first_log = cls.get_admin_operation_log(first_id)
            query = query.filter(AdminOperationLog.created_at > first_log.created_at)
            res = query.order_by(AdminOperationLog.created_at).limit(limit).all()[::-1]
        elif last_id := kwargs.get('last_id'):
            last_log = cls.get_admin_operation_log(last_id)
            query = query.filter(AdminOperationLog.created_at < last_log.created_at)
            res = query.order_by(AdminOperationLog.created_at.desc()).limit(limit).all()
        else:
            res = query.order_by(AdminOperationLog.created_at.desc()).limit(limit).all()

        user_ids = [item.user_id for item in res if item.user_id]
        user_name_map = get_admin_user_name_map(user_ids)
        target_user_ids = [item.target_user_id for item in res if item.target_user_id]
        target_users = User.query.filter(User.id.in_(target_user_ids)).with_entities(User.id, User.email).all()
        target_user_email_map = dict(target_users)

        namespace_desc_map = {namespace.name: namespace.desc for namespace in BaseOPNamespaceObjectMeta.namespaces()}
        object_desc_map = {
            namespace.name: {
                ns_obj.object.name: ns_obj.object.desc
                for ns_obj in BaseOPNamespaceObjectMeta.objects(namespace)
            } for namespace in BaseOPNamespaceObjectMeta.namespaces()
        }

        items = [
            dict(
                id=item.id,
                created_at=item.created_at,
                namespace=namespace_desc_map.get(item.namespace, item.namespace),
                object=object_desc_map.get(item.namespace, {}).get(item.object, item.object),
                operation=cls.OPERATION_NAME_MAP.get(item.operation.name, item.operation.value),
                detail=item.show_detail(),
                user=dict(
                    user_id=item.user_id,
                    name_or_email=user_name_map.get(item.user_id, str(item.user_id)),
                ),
                target_user=dict(
                    user_id=item.target_user_id,
                    name_or_email=target_user_email_map.get(item.target_user_id, str(item.target_user_id)),
                ),
            ) for item in res
        ]

        first_id, last_id = None, None
        if len(items) > 0:
            first_id = items[0]['id']
            last_id = items[-1]['id']

        namespace_object_map = defaultdict(list)
        for namespace in BaseOPNamespaceObjectMeta.namespaces():
            for ns_obj in BaseOPNamespaceObjectMeta.objects(namespace):
                namespace_object_map[namespace.desc].append(ns_obj.object.desc)

        return dict(
            namespace_object_map=namespace_object_map,
            operation_name_map=cls.OPERATION_NAME_MAP,
            items=items,
            first_id=first_id,
            last_id=last_id,
        )
