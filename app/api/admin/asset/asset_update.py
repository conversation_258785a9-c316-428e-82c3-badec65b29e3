# -*- coding: utf-8 -*-
import json
from typing import List
from decimal import Decimal
from datetime import timedelta

from flask import request, g
from flask_restx import fields as fx_fields, marshal
from webargs import fields as wa_fields

from app.business.auth import get_admin_user_name_map

from ...common import (Resource, Namespace, respond_with_code)
from ...common.decorators import require_admin_webauth_token
from ...common.fields import (EnumField, TimestampMarshalField, PageField, AssetField,
                              LimitField, AmountField, EnumMarshalField, TimestampField)
from ....assets import list_all_assets
from ....business import UpdateAssetHelper, ServerClient, \
    is_user_has_audit_permission, PriceManager
from ....common import Language, BalanceBusiness, MessageTitle, MessageContent, MessageWebLink, PrecisionEnum, \
    ADMIN_EXPORT_LIMIT
from ....exceptions import InvalidArgument
from ....models import UpdateAssetBalance, db, BalanceUpdateBusiness, Message, User, CleanedBalanceTransferHistory
from ....models.wallet import CleanedAssetResume
from ....models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectWallet
from ....utils import export_xlsx, query_to_page, timestamp_to_datetime, amount_to_str, now, quantize_amount
from ....utils.importer import get_table_rows

ns = Namespace('Asset - Asset Update')
url_prefix = '/asset-update'


@ns.route('/template')
@respond_with_code
class AssetUpdateTemplateResource(Resource):

    export_headers = (
        {'field': 'asset', Language.ZH_HANS_CN: '币种', Language.EN_US: 'Asset'},
        {'field': 'email_userid', Language.ZH_HANS_CN: '邮箱/ID',
         Language.EN_US: 'Email/ID'},
        {'field': 'amount', Language.ZH_HANS_CN: '操作数量',
         Language.EN_US: 'Amount'},
        {'field': 'remark', Language.ZH_HANS_CN: '备注',
         Language.EN_US: 'Remark'},
        {'field': 'activity', Language.ZH_HANS_CN: '活动名', Language.EN_US: 'Activity'},
    )

    @classmethod
    def get(cls):
        """钱包-资产变更-模板"""
        return export_xlsx(
            filename='asset-update-template',
            data_list=[],
            export_headers=cls.export_headers
        )


@ns.route('/')
@respond_with_code
class AssetUpdateListResource(Resource):

    marshal_fields = {
        'id': fx_fields.Integer,
        'user_id': fx_fields.Integer,
        'create_time': TimestampMarshalField(attribute='created_at'),
        'update_time': TimestampMarshalField(attribute='updated_at'),
        'asset': fx_fields.String,
        'amount': AmountField,
        'created_by': fx_fields.Integer,
        'status': EnumMarshalField(
            UpdateAssetBalance.Status, output_field_lower=False),
        'type': EnumMarshalField(
            UpdateAssetBalance.Type, output_field_lower=False),
        'audit_id': fx_fields.Integer,
        'check_id': fx_fields.Integer,
        'remark': fx_fields.String,
        'activity': fx_fields.String,
    }

    EXPORT_HEADERS = (
        {'field': 'id', Language.ZH_HANS_CN: 'ID'},
        {'field': 'create_time', Language.ZH_HANS_CN: '上传时间'},
        {'field': 'update_time', Language.ZH_HANS_CN: '更新时间'},
        {'field': 'asset', Language.ZH_HANS_CN: '币种'},
        {'field': 'amount', Language.ZH_HANS_CN: '数量'},
        {'field': 'user_id', Language.ZH_HANS_CN: '用户ID'},
        {'field': 'created_by', Language.ZH_HANS_CN: '上传用户'},
        {'field': 'audit_id', Language.ZH_HANS_CN: '初审人'},
        {'field': 'check_id', Language.ZH_HANS_CN: '复审人'},
        {'field': 'status', Language.ZH_HANS_CN: '状态'},
        {'field': 'type', Language.ZH_HANS_CN: '变更类型'},
        {'field': 'remark', Language.ZH_HANS_CN: '备注'},
        {'field': 'activity', Language.ZH_HANS_CN: '活动名'},
    )

    @classmethod
    @ns.use_kwargs(dict(
        asset=wa_fields.String,
        status=EnumField(UpdateAssetBalance.Status),
        type=EnumField(UpdateAssetBalance.Type),
        remark=wa_fields.String,
        user_id=wa_fields.Integer,
        export=wa_fields.Boolean(missing=False),
        start_time=TimestampField(is_ms=True),
        end_time=TimestampField(is_ms=True),
        page=PageField(unlimited=True),
        limit=LimitField(max_limit=10000)
    ))
    def get(cls, **kwargs):
        """钱包-资产变更-列表"""
        query = UpdateAssetBalance.query
        if asset := kwargs.get('asset'):
            query = query.filter(UpdateAssetBalance.asset == asset)
        if status := kwargs.get('status'):
            query = query.filter(UpdateAssetBalance.status == status)
        if type := kwargs.get('type'):
            query = query.filter(UpdateAssetBalance.type == type)
        if user_id := kwargs.get('user_id'):
            query = query.filter(UpdateAssetBalance.user_id == user_id)
        if remark := kwargs.get('remark'):
            query = query.filter(UpdateAssetBalance.remark == remark)
        if start_time := kwargs.get('start_time'):
            query = query.filter(UpdateAssetBalance.updated_at >= start_time)
        if end_time := kwargs.get('end_time'):
            query = query.filter(UpdateAssetBalance.updated_at < end_time)
        query = query.filter(
            UpdateAssetBalance.status != UpdateAssetBalance.Status.DELETED
        ).order_by(UpdateAssetBalance.id.desc())
        if kwargs['export']:
            export_data = marshal(query.limit(ADMIN_EXPORT_LIMIT).all(), cls.marshal_fields)
            user_ids = set()
            user_ids |= {i['created_by'] for i in export_data}
            user_ids |= {i['check_id'] for i in export_data}
            user_ids |= {i['audit_id'] for i in export_data}
            name_map = get_admin_user_name_map(user_ids)

            type_map = {item.name: item.value for item in UpdateAssetBalance.Type}
            status_map = {item.name: item.value for item in UpdateAssetBalance.Status}
            for data in export_data:
                data['create_time'] = timestamp_to_datetime(data['create_time']).replace(tzinfo=None)
                data['update_time'] = timestamp_to_datetime(data['update_time']).replace(tzinfo=None)
                data['created_by'] = name_map.get(data['created_by']) or '--'
                data['audit_id'] = name_map.get(data['audit_id']) or '--'
                data['check_id'] = name_map.get(data['check_id']) or '--'
                if data['type']:
                    data['type'] = type_map[data['type']]
                data['status'] = status_map[data['status']]
            return export_xlsx('asset-update', export_data, cls.EXPORT_HEADERS)
        items = query_to_page(
            query, kwargs['page'], kwargs['limit'], cls.marshal_fields)
        user_ids = set()
        user_ids |= {i['created_by'] for i in items['data']}
        user_ids |= {i['check_id'] for i in items['data']}
        user_ids |= {i['audit_id'] for i in items['data']}
        name_map = get_admin_user_name_map(user_ids)
        for item in items['data']:
            item['created_user_name'] = name_map.get(item['created_by'])
            item['audit_user_name'] = name_map.get(item['audit_id'])
            item['check_user_name'] = name_map.get(item['check_id'])
        return dict(
            items=items,
            types=UpdateAssetBalance.Type
        )

    @classmethod
    @require_admin_webauth_token
    def post(cls):
        """钱包-资产变更-批量上传"""
        if not (type_ := request.form.get('type')):
            raise InvalidArgument
        if not (type_ := getattr(UpdateAssetBalance.Type, type_, None)):
            raise InvalidArgument
        file_ = request.files.get('batch-upload')
        file_columns = ["asset", "email_userid", "amount", "remark", "activity"]
        try:
            rows = get_table_rows(file_, file_columns)
        except Exception as e:
            msg = getattr(e, 'message', '文件解析失败，请重新生成并上传')
            raise InvalidArgument(message=msg)
        for row in rows:
            if type_ is UpdateAssetBalance.Type.MARKET_EXPENSE:
                if not row['activity']:
                    raise InvalidArgument(message=f'活动名未填写')
            row['type'] = type_
        UpdateAssetHelper(rows).insert_rows()
        return dict(count=len(rows))


@ns.route('/batch-audit')
@respond_with_code
class AssetUpdateBatchAuditResource(Resource):

    @classmethod
    @require_admin_webauth_token
    @ns.use_kwargs(dict(
        ids=wa_fields.List(wa_fields.Integer, required=True)
    ))
    def post(cls, **kwargs):
        """钱包-资产变更-批量初审"""
        user_id = g.user.id
        if not is_user_has_audit_permission(user_id, 'ASSET_UPDATE_AUDIT_USERS'):
            raise InvalidArgument(message='没有审核权限')

        data_list = []
        for update_config in UpdateAssetBalance.query.filter(
            UpdateAssetBalance.id.in_(kwargs['ids']),
        ).all():
            if update_config.status != UpdateAssetBalance.Status.CREATED:
                raise InvalidArgument(message=f'{update_config.id} 状态异常')
            data_list.append(update_config.to_dict(enum_to_name=True))

        UpdateAssetBalance.query.filter(
            UpdateAssetBalance.id.in_(kwargs['ids']),
            UpdateAssetBalance.status == UpdateAssetBalance.Status.CREATED,
        ).update(
            {'audit_id': user_id, 'status': UpdateAssetBalance.Status.AUDITED},
            synchronize_session=False
        )
        db.session.commit()

        for data in data_list:
            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectWallet.AssetUpdate,
                old_data=dict(status=UpdateAssetBalance.Status.CREATED.name),
                new_data=dict(status=UpdateAssetBalance.Status.AUDITED.name),
                special_data=dict(id=data['id'], asset=data['asset'], amount=data['amount']),
                target_user_id=data['user_id'],
            )


@ns.route('/batch-check')
@respond_with_code
class AssetUpdateBatchCheckResource(Resource):

    @classmethod
    @require_admin_webauth_token
    @ns.use_kwargs(dict(
        ids=wa_fields.List(wa_fields.Integer, required=True)
    ))
    def post(cls, **kwargs):
        """钱包-资产变更-批量复审"""
        check_user_id = g.user.id
        if not is_user_has_audit_permission(check_user_id, 'ASSET_UPDATE_CHECK_USERS'):
            raise InvalidArgument(message='没有审核权限')
        records: List[UpdateAssetBalance] = UpdateAssetBalance.query.filter(
            UpdateAssetBalance.id.in_(kwargs['ids']),
        ).all()
        for update_config in records:
            if update_config.status != UpdateAssetBalance.Status.AUDITED:
                raise InvalidArgument(message=f'{update_config.id} 状态异常')
            if update_config.audit_id == check_user_id:
                raise InvalidArgument(
                    message=f'{update_config.id} 初审和复审不能为同一人')

        client = ServerClient()
        for update_config in records:
            old_data = update_config.to_dict(enum_to_name=True)
            user_id = update_config.user_id
            asset = update_config.asset
            amount = update_config.amount
            business_id = BalanceUpdateBusiness.new_id(user_id, asset, amount)
            UpdateAssetHelper.check_valid_amount_for_balance_update(
                user_id, asset, amount)
            # noinspection PyBroadException
            try:
                client.add_user_balance(
                    user_id, asset, amount, BalanceBusiness.SYSTEM, business_id)
            except Exception:
                update_config.status = UpdateAssetBalance.Status.FAILED
                db.session.commit()
                continue
            update_config.check_id = check_user_id
            update_config.status = UpdateAssetBalance.Status.FINISHED
            db.session.commit()
            cls._add_message(row=update_config)

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectWallet.AssetUpdate,
                old_data=old_data,
                new_data=update_config.to_dict(enum_to_name=True),
                target_user_id=update_config.user_id,
            )

    @classmethod
    def _add_message(cls, row):
        if row.type is not UpdateAssetBalance.Type.MARKET_EXPENSE:
            return
        if row.amount <= 0:
            return
        db.session.add(Message(
            user_id=row.user_id,
            title=MessageTitle.ACTIVITY_REWARD_RECEIPT.name,
            content=MessageContent.ACTIVITY_REWARD_RECEIPT.name,
            params=json.dumps({
                'activity': row.activity,
                'reward': f'{amount_to_str(row.amount, PrecisionEnum.COIN_PLACES)}{row.asset}',
            }),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.SPOT_ASSET_HISTORY_PAGE.value,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.POPUP_WINDOW,
            channel=Message.Channel.ACTIVITY,
        ))
        db.session.commit()


@ns.route('/batch-delete')
@respond_with_code
class AssetUpdateBatchDeleteResource(Resource):

    @classmethod
    @require_admin_webauth_token
    @ns.use_kwargs(dict(
        ids=wa_fields.List(wa_fields.Integer, required=True)
    ))
    def post(cls, **kwargs):
        """钱包-资产变更-批量删除"""
        data_list = []
        for update_config in UpdateAssetBalance.query.filter(
                UpdateAssetBalance.id.in_(kwargs['ids']),
        ).all():
            if update_config.status == UpdateAssetBalance.Status.FINISHED:
                raise InvalidArgument(message=f'{update_config.id} 状态异常')
            data_list.append(update_config.to_dict(enum_to_name=True))

        UpdateAssetBalance.query.filter(
            UpdateAssetBalance.id.in_(kwargs['ids']),
        ).update(
            {'status': UpdateAssetBalance.Status.DELETED},
            synchronize_session=False
        )
        db.session.commit()

        for data in data_list:
            data['status'] = UpdateAssetBalance.Status.DELETED.name
            AdminOperationLog.new_delete(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectWallet.AssetUpdate,
                detail=data,
                target_user_id=data['user_id'],
            )


@ns.route('/cleaned-resume')
@respond_with_code
class CleanedAssetResumeResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        user_id=wa_fields.Integer,
        status=EnumField(CleanedAssetResume.Status),
        page=PageField,
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """钱包-小额资产恢复-列表"""
        query = CleanedAssetResume.query
        if user_id := kwargs.get('user_id'):
            query = query.filter(CleanedAssetResume.user_id == user_id)
        if status := kwargs.get('status'):
            query = query.filter(CleanedAssetResume.status == status)
        else:
            query = query.filter(CleanedAssetResume.status != CleanedAssetResume.Status.DELETED)
        query = query.order_by(CleanedAssetResume.id.desc())
        records = query.paginate(kwargs['page'], kwargs['limit'])

        user_ids = {x.user_id for x in records.items}
        user_emails = User.query.filter(User.id.in_(user_ids)).with_entities(User.id, User.email).all()
        user_emails = dict(user_emails)
        admin_ids = {x.created_by for x in records.items} | {x.audit_by for x in records.items if x.audit_by}
        admin_names = get_admin_user_name_map(admin_ids)

        prices = PriceManager.assets_to_usd()

        return dict(
            items=[dict(
                id=x.id,
                created_at=x.created_at,
                updated_at=x.updated_at,
                user_id=x.user_id,
                email=user_emails[x.user_id] or '',
                asset=x.asset,
                amount=x.amount,
                usd=quantize_amount(prices.get(x.asset, 0) * x.amount, 4),
                type=x.type.name,
                status=x.status.name,
                created_by=x.created_by,
                created_name=admin_names[x.created_by],
                audit_by=x.audit_by,
                audit_name=admin_names[x.audit_by] if x.audit_by else '',
            ) for x in records.items],
            total=records.total,
            statuses={x.name: x.value for x in CleanedAssetResume.Status if x != CleanedAssetResume.Status.DELETED},
            types={x.name: x.value for x in CleanedAssetResume.Type},
            assets=list_all_assets()
        )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=wa_fields.Integer(required=True),
        asset=AssetField(required=True),
        amount=wa_fields.Decimal(required=True),
    ))
    @require_admin_webauth_token
    def post(cls, **kwargs):
        """钱包-小额资产恢复-添加"""
        user_id = kwargs['user_id']
        asset = kwargs['asset']
        amount = kwargs['amount']
        if amount <= 0:
            raise InvalidArgument
        # see app/schedules/balance_clean.py
        # 不活跃账户资产清理，记录CleanedBalanceTransferHistory并走收支结算;
        # 现货极小额清理，直接扣掉资产。两种清理都不生成资产流水
        history = CleanedBalanceTransferHistory.query.filter(
                  CleanedBalanceTransferHistory.user_id == user_id,
                  CleanedBalanceTransferHistory.asset == asset,
                  CleanedBalanceTransferHistory.amount == amount,
                  CleanedBalanceTransferHistory.status == CleanedBalanceTransferHistory.Status.FINISHED).first()
        if history:
            typ = CleanedAssetResume.Type.INACTIVE_ACCOUNT
            amount_limit = Decimal(1)
        else:
            typ = CleanedAssetResume.Type.TINY_SPOT_ASSET
            amount_limit = Decimal('0.01')

        price = PriceManager.asset_to_usd(asset)
        if price * amount > amount_limit * 5:  # 考虑币价波动
            raise InvalidArgument(message='金额过大')

        if typ == CleanedAssetResume.Type.TINY_SPOT_ASSET:
            # 找到该金额的流水，如果被清理了，那么下一条流水的变更数量应该等于该流水变更后的余额
            # 如果被清理的是最后一条流水，那么当前资产的数量应该为0
            client = ServerClient()
            data = client.get_user_balance_history(user_id, asset)
            pre = None
            for item in data:
                if quantize_amount(item['balance'], 8) == amount:
                    if not pre:
                        balance = client.get_user_balances(user_id, asset=asset)
                        if balance[asset]['available'] == 0:
                            break
                    else:
                        if Decimal(pre['balance']) == Decimal(pre['change']):
                            break
                pre = item
            else:
                raise InvalidArgument(message='无法确认该笔资产清理流水')

        row = CleanedAssetResume.query.filter(
            CleanedAssetResume.user_id == user_id,
            CleanedAssetResume.asset == asset,
            CleanedAssetResume.amount == amount,
            CleanedAssetResume.status.in_((CleanedAssetResume.Status.CREATED, CleanedAssetResume.Status.FINISHED))
        ).first()
        if row:
            raise InvalidArgument(message='该笔资产清理流水已提交过')
        db.session_add_and_commit(CleanedAssetResume(
            user_id=user_id,
            asset=asset,
            amount=amount,
            type=typ,
            status=CleanedAssetResume.Status.CREATED,
            created_by=g.user.id
        ))

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectWallet.CleanedAssetResume,
            detail=kwargs,
            target_user_id=user_id,
        )
        return {}

    @classmethod
    @ns.use_kwargs(dict(
        ids=wa_fields.List(wa_fields.Integer, required=True),
    ))
    @require_admin_webauth_token
    def patch(cls, **kwargs):
        """钱包-小额资产恢复-审核"""
        if not is_user_has_audit_permission(g.user.id, 'ASSET_UPDATE_CHECK_USERS'):
            raise InvalidArgument(message='没有审核权限')
        rows = CleanedAssetResume.query.filter(
            CleanedAssetResume.id.in_(kwargs['ids'])
        ).all()
        for row in rows:
            if row.status != CleanedAssetResume.Status.CREATED:
                raise InvalidArgument
        for row in rows:
            cls.audit_one(row)

            AdminOperationLog.new_audit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectWallet.CleanedAssetResume,
                detail=dict(asset=row.asset, amount=row.amount, status=CleanedAssetResume.Status.FINISHED.name),
                target_user_id=row.user_id,
            )
        return {}

    @classmethod
    def audit_one(cls, row: CleanedAssetResume):
        client = ServerClient()
        # 重入判断
        if not row.business_id:
            row.business_id = BalanceUpdateBusiness.new_id(row.user_id, row.asset, row.amount)
            db.session.commit()
        else:
            if row.created_at < now() - timedelta(days=1):
                raise InvalidArgument(message='无法直接审核，需确认是否重入。')
            r = client.asset_query_business(row.user_id, row.asset, BalanceBusiness.SYSTEM, row.business_id)
            if r:
                row.audit_by = g.user.id
                row.status = CleanedAssetResume.Status.FINISHED
                db.session.commit()
                return

        client.add_user_balance(
            user_id=row.user_id,
            asset=row.asset,
            amount=row.amount,
            business=BalanceBusiness.SYSTEM,
            business_id=row.business_id,
        )
        row.audit_by = g.user.id
        row.status = CleanedAssetResume.Status.FINISHED
        db.session.commit()

    @classmethod
    @ns.use_kwargs(dict(
        ids=wa_fields.String(required=True),
    ))
    @require_admin_webauth_token
    def delete(cls, **kwargs):
        """钱包-小额资产恢复-删除"""
        ids = {int(x) for x in kwargs['ids'].split(',')}
        rows = CleanedAssetResume.query.filter(
            CleanedAssetResume.id.in_(ids)
        ).all()
        for row in rows:
            if row.status != CleanedAssetResume.Status.CREATED:
                raise InvalidArgument(message="只能删除待审核的记录")
            row.status = CleanedAssetResume.Status.DELETED
        db.session.commit()

        for row in rows:
            AdminOperationLog.new_delete(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectWallet.CleanedAssetResume,
                detail=row.to_dict(enum_to_name=True),
                target_user_id=row.user_id,
            )
        return {}
