# -*- coding: utf-8 -*-
import json
import math
from collections import defaultdict
from datetime import date, timedelta
from decimal import Decimal
from itertools import chain
from typing import List

from dateutil.relativedelta import relativedelta
from flask import request, g
from marshmallow import fields as mm_fields
from sqlalchemy import func
from jose import jwt

from app.api.common.decorators import require_login
from app.business import cached
from app.config import config
from ..common import (Resource, Namespace, respond_with_code,
                      json_string_success)
from ..common.fields import LimitField, PageField, EnumField
from ...caches.operation import AnnouncementCache, AnnouncementCategoryCache, \
    HelpCenterCategoryCache
from ...caches.spot import CetCirculationCache
from ...common import PrecisionEnum, Language
from ...exceptions import InvalidArgument
from ...models import DailyCetBuyBackReport, CetDestroyReport
from ...models.mongo.announcement import AnnouncementArticleMySQL as AnnouncementArticle, HelpCenterArticleMySQL as HelpCenterArticle
from ...utils import now, RESTClient, amount_to_str, quantize_amount, today, str_to_datetime
from ...utils.date_ import current_timestamp, date_to_datetime, datetime_to_str
from ...utils.helper import Struct

ns = Namespace('Support')

_coin919_client = RESTClient('http://www.coin919.pro:8095/api/v2/')


@ns.route('/zendesk/articles')
@respond_with_code
class ArticlesResource(Resource):

    @classmethod
    def format_new_url(cls, article_id) -> str:
        # 新版公告中心文章url
        url = f'{config["SITE_URL"]}/announcements/detail/{article_id}'
        return url

    @classmethod
    def get_request_lang(cls) -> str:
        # support langs than are not in Language enum.
        for lang in chain(request.accept_languages.values(), (g.lang,)):
            if lang in AnnouncementArticle.LANG_LOCALE_DICT:
                break
        else:
            lang = Language.DEFAULT.value
        return lang

    @classmethod
    @ns.use_kwargs(dict(
        limit=LimitField(missing=8)
    ))
    def get(cls, **kwargs):
        lang = cls.get_request_lang()
        limit = kwargs['limit']
        articles = AnnouncementCache(lang).get_top_announcements(limit)
        for article in articles:
            article["url"] = cls.format_new_url(article["id"])
        return articles


@ns.route('/zendesk/categories')
@respond_with_code
class ZendeskCategoriesResource(Resource):

    @classmethod
    def get(cls):
        """ zendesk公告-类别信息 """
        lang = ArticlesResource.get_request_lang()
        cache = AnnouncementCategoryCache(lang)
        if data := cache.read():
            return json_string_success(data)
        return []


@ns.route('/zendesk/sections/<int:section_id>/articles')
@respond_with_code
class ZendeskSectionArticlesResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, section_id, **kwargs):
        """ zendesk公告-二级分类下的文章（APP），只按时间排序 """
        page, limit = kwargs["page"], kwargs["limit"]
        lang = Language(ArticlesResource.get_request_lang())
        total, articles = AnnouncementArticle.pagination(
            lang=Language(lang),
            section_id=section_id,
            page=page,
            limit=limit,
            only=['article_id', 'title', 'created_at'],
            order_by='-created_at',
        )
        items = []
        for r in articles:
            items.append(
                {
                    "id": r.article_id,
                    "title": r.title,
                    "url": ArticlesResource.format_new_url(r.article_id),
                    "created_at": datetime_to_str(r.created_at, fmt="%Y-%m-%dT%H:%M:%SZ"),
                }
            )
        return dict(
            has_next=total > page * limit,
            curr_page=page,
            count=len(items),
            data=items,
            total=total,
            total_page=math.ceil(total / limit),
        )


@ns.route('/zendesk/articles/new')
@respond_with_code
class ZendeskNewArticlesResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField,
        section_id=mm_fields.Integer,
        category_id=mm_fields.Integer,
        order_by=EnumField(['is_top', 'created_at'], required=True)
    ))
    def get(cls, **kwargs):
        """ 最新公告列表/分类公告列表（Web），按置顶和时间排序 """
        page = kwargs['page']
        limit = kwargs['limit']
        section_id = kwargs.get("section_id")
        category_id = kwargs.get("category_id")
        order_by = None if kwargs['order_by'] == 'is_top' else '-created_at'
        result_show_is_top = True if kwargs['order_by'] == 'is_top' else False
        lang = Language(ArticlesResource.get_request_lang())

        total, articles = AnnouncementArticle.pagination(
            lang=lang,
            category_id=category_id,
            section_id=section_id,
            page=page,
            limit=limit,
            only=['article_id', 'section_id', 'title', 'body_html', 'is_top', 'created_at', 'updated_at'],
            order_by=order_by,
        )
        items = []
        for r in articles:
            items.append(
                {
                    "id": r.article_id,
                    "section_id": r.section_id,
                    "title": r.title,
                    "body": r.body_html,
                    "is_top": r.is_top if result_show_is_top else False,
                    "created_at": int(r.created_at.timestamp()),
                    "updated_at": int(r.updated_at.timestamp()),
                }
            )
        return dict(
            has_next=total > page * limit,
            curr_page=page,
            count=len(items),
            data=items,
            total=total,
            total_page=math.ceil(total / limit),
        )


@ns.route('/zendesk/articles/recommend')
@respond_with_code
class ZendeskRecommendArticlesResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        """ 推荐公告列表（Web） """
        page = kwargs['page']
        limit = kwargs['limit']
        lang = Language(ArticlesResource.get_request_lang())
        total, articles = AnnouncementArticle.top_article_pagination(
            lang=lang,
            page=page,
            limit=limit,
            only=['article_id', 'section_id', 'title', 'body_html', 'created_at', 'updated_at'],
        )
        items = []
        for r in articles:
            items.append(
                {
                    "id": r.article_id,
                    "section_id": r.section_id,
                    "title": r.title,
                    "body": r.body_html,
                    "created_at": int(r.created_at.timestamp()),
                    "updated_at": int(r.updated_at.timestamp()),
                }
            )
        return dict(
            has_next=total > page * limit,
            curr_page=page,
            count=len(items),
            data=items,
            total=total,
            total_page=math.ceil(total / limit),
        )


@ns.route('/zendesk/articles/<int:article_id>')
@respond_with_code
class ZendeskArticleDetailResource(Resource):
    @classmethod
    @cached(300)
    def get_section_category_name(cls, lang: str, section_id: int) -> List[str]:
        data = AnnouncementCategoryCache(lang).read()
        infos = json.loads(data) if data else []
        for info in infos:
            for sec in info["sections"]:
                if int(sec["id"]) == section_id:
                    return [sec["name"], info["name"]]
        return ["", ""]

    @classmethod
    def get(cls, article_id: int):
        """ 公告详情（Web）"""
        lang = Language(ArticlesResource.get_request_lang())
        row = AnnouncementArticle.get_by_article_id(
            article_id=article_id,
            lang=lang,
            only=['article_id', 'section_id', 'category_id', 'title', 'body_html', 'created_at', 'updated_at'],
        )
        if not row:
            raise InvalidArgument

        section_name, category_name = cls.get_section_category_name(lang.value, row.section_id)
        res = {
            "id": row.article_id,
            "section_id": row.section_id,
            "section_name": section_name,
            "category_id": row.category_id,
            "category_name": category_name,
            "title": row.title,
            "body": row.body_html,
            "created_at": int(row.created_at.timestamp()),
            "updated_at": int(row.updated_at.timestamp()),
        }
        return res


@ns.route('/zendesk/articles/search')
@respond_with_code
class ZendeskArticlesSearchResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            keyword=mm_fields.String(required=True),
            category_id=mm_fields.String,
            sort_by=EnumField(
                enum=['position', 'created_at', 'updated_at'], missing='position'),
            page=PageField,
            limit=LimitField(max_limit=100),
        )
    )
    def get(cls, **kwargs):
        """ 公告搜索（Web） """
        page = kwargs["page"]
        limit = kwargs["limit"]
        sort_by = kwargs['sort_by']
        lang = ArticlesResource.get_request_lang()
        locale = AnnouncementArticle.LANG_LOCALE_DICT[lang]
        keyword = kwargs["keyword"].strip()
        category_id = kwargs.get("category_id", "").strip()
        params = {
            "query": keyword,
            "locale": locale,
            "category": category_id,
            "page": page,
            "per_page": limit,
            "sort_by": sort_by,
        }
        client = RESTClient(config['ZENDESK_CONFIG']['article_url'], timeout=10)
        resp = client.get('articles/search', **params)
        items = []
        for r in resp["results"]:
            if r['draft']:
                continue
            items.append(
                {
                    "id": r["id"],
                    "created_at": int(str_to_datetime(r["created_at"]).timestamp()),
                    "updated_at": int(str_to_datetime(r["edited_at"]).timestamp()),
                    "is_top": r['promoted'],
                    "title": r["title"],
                    "snippet": r["snippet"],
                }
            )

        total = resp['count']
        return dict(
            has_next=total > page * limit,
            curr_page=page,
            count=len(items),
            data=items,
            total=total,
            total_page=math.ceil(total / limit),
        )


@ns.route('/zendesk/help_center/categories')
@respond_with_code
class ZendeskHelpCenterCategoriesResource(Resource):

    @classmethod
    def get(cls):
        """ 帮助中心公告-类别信息 """
        lang = ArticlesResource.get_request_lang()
        cache = HelpCenterCategoryCache(lang)
        if data := cache.read():
            return json_string_success(data)
        return []


@ns.route('/zendesk/help_center/articles')
@respond_with_code
class ZendeskHelpCenterArticlesResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField,
        section_id=mm_fields.Integer,
        category_id=mm_fields.Integer,
    ))
    def get(cls, **kwargs):
        """ 帮助中心列表 """
        page = kwargs['page']
        limit = kwargs['limit']
        section_id = kwargs.get("section_id")
        category_id = kwargs.get("category_id")
        lang = Language(ArticlesResource.get_request_lang())
        total, articles = HelpCenterArticle.pagination(
            lang=lang,
            category_id=category_id,
            section_id=section_id,
            page=page,
            limit=limit,
            only=['article_id', 'section_id', 'title', 'body_html',
                  'created_at', 'updated_at', 'position'],
            order_by='-created_at',
        )
        items = []
        for r in articles:
            items.append(
                {
                    "id": r.article_id,
                    "section_id": r.section_id,
                    "title": r.title,
                    "position": r.position,
                    "body": r.body_html,
                    "created_at": int(r.created_at.timestamp()),
                    "updated_at": int(r.updated_at.timestamp()),
                }
            )
        return dict(
            has_next=total > page * limit,
            curr_page=page,
            count=len(items),
            data=items,
            total=total,
            total_page=math.ceil(total / limit),
        )


@ns.route('/zendesk/help_center/articles/<int:article_id>')
@respond_with_code
class ZendeskHelpCenterArticleDetailResource(Resource):

    @classmethod
    @cached(300)
    def get_section_path(cls, lang: str, section_id: int, category_id: int) -> List[str]:
        data = HelpCenterCategoryCache(lang).read()
        infos = json.loads(data) if data else []

        def find_section_path(data, section_id):
            # 遍历所有类别
            for category in data:
                # 遍历每个类别中的 sections
                for section in category['sections']:
                    # 如果找到了对应的 section_id
                    if section['id'] == section_id:
                        # 构建链路，首先将当前 section 加入链路
                        path = [
                            {'id': section['id'], 'name': section['name']}]

                        # 递归查找 parent_section_id
                        if section['parent_section_id']:
                            parent_path = find_section_path(data, section['parent_section_id'])
                            path = parent_path + path
                        return path
            return []

        section_path = find_section_path(infos, section_id)
        for info in infos:
            if info['id'] == category_id:
                section_path = [{'id': info['id'], 'name': info['name']}] + section_path
        return section_path

    @classmethod
    def get(cls, article_id: int):
        """ 帮助中心文章详情"""
        lang = Language(ArticlesResource.get_request_lang())
        row = HelpCenterArticle.get_by_article_id(
            article_id=article_id,
            lang=lang,
            only=['article_id', 'section_id', 'category_id', 'title', 'body_html', 'created_at', 'updated_at'],
        )
        if not row:
            raise InvalidArgument

        section_path = cls.get_section_path(lang.value, row.section_id, row.category_id)
        res = {
            "id": row.article_id,
            "section_id": row.section_id,
            "category_id": row.category_id,
            "section_path": section_path,
            "title": row.title,
            "body": row.body_html,
            "created_at": int(row.created_at.timestamp()),
            "updated_at": int(row.updated_at.timestamp()),
        }
        return res


@ns.route('/zendesk/help_center/articles/search')
@respond_with_code
class ZendeskHelpCenterArticlesSearchResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            keyword=mm_fields.String(required=True),
            category_id=mm_fields.String,
            sort_by=EnumField(
                enum=['position', 'created_at', 'updated_at'], missing='position'),
            page=PageField,
            limit=LimitField(max_limit=100),
        )
    )
    def get(cls, **kwargs):
        """ 帮助中心搜索（zendesk api） """
        page = kwargs["page"]
        limit = kwargs["limit"]
        sort_by = kwargs['sort_by']
        lang = ArticlesResource.get_request_lang()
        locale = HelpCenterArticle.LANG_LOCALE_DICT[lang]
        keyword = kwargs["keyword"].strip()
        category_id = kwargs.get("category_id", "").strip()
        params = {
            "query": keyword,
            "locale": locale,
            "category": category_id,
            "page": page,
            "per_page": limit,
            "sort_by": sort_by,
        }
        client = RESTClient(config['ZENDESK_CONFIG']['support_article_url'], timeout=10)
        resp = client.get('articles/search', **params)
        items = []
        for r in resp["results"]:
            if r['draft']:
                continue
            items.append(
                {
                    "id": r["id"],
                    "created_at": int(str_to_datetime(r["created_at"]).timestamp()),
                    "updated_at": int(str_to_datetime(r["updated_at"]).timestamp()),
                    "title": r["title"],
                    "snippet": r["snippet"],
                }
            )

        total = resp['count']
        return dict(
            has_next=total > page * limit,
            curr_page=page,
            count=len(items),
            data=items,
            total=total,
            total_page=math.ceil(total / limit),
        )


@ns.route('/zendesk/user-token')
@respond_with_code
class ZendeskJwtTokenResource(Resource):

    @classmethod
    @require_login
    def get(cls, **kwargs):
        user = g.user
        payload = {
        'name': user.name_displayed,
        'email': user.email,
        'email_verified': True,
        'ext': str(current_timestamp(to_int=True)), 
        'external_id': config['ZENDESK_BOT_CONFIG']['id_prefix'] + str(user.id),
        'scope': "user"
        }
        token = jwt.encode(payload, 
                           config['ZENDESK_BOT_CONFIG']['shared_secret'],
                           headers={'alg': "HS256", 
                                    'typ': "JWT", 
                                    'kid': config['ZENDESK_BOT_CONFIG']['secret_id']})
        return dict(
            token=token
        )


@ns.route('/coin919')
@respond_with_code
class TicketsResource(Resource):

    @classmethod
    def get(cls):
        return _coin919_client.get('DataCenter/GetCoinExGridRankingList')


@ns.route('/cet/remain')
@respond_with_code
class CetInfoResource(Resource):
    """ CET流通、销毁 统计信息 """

    @classmethod
    def get(cls):
        value = CetCirculationCache().read_aside()
        result = dict()
        result['total'] = quantize_amount(value['total'], PrecisionEnum.CASH_PLACES)
        result["total_supply"] = quantize_amount(value["total_supply"], PrecisionEnum.CASH_PLACES)
        result["ready_destroy"] = quantize_amount(value.get("ready_destroy", Decimal()), PrecisionEnum.CASH_PLACES)
        # 缓存中是8位精度，这里是2位精度，重新计算下remain
        result["remain"] = quantize_amount(result["total_supply"] - result["ready_destroy"], PrecisionEnum.CASH_PLACES)
        result["already_destroy"] = quantize_amount(value.get("already_destroy", Decimal()), PrecisionEnum.CASH_PLACES)
        return result


@ns.route('/buy_back/info')
@respond_with_code
class CetBuyBackInfoResource(Resource):
    """
    cet回购信息
    """

    @classmethod
    def get(cls):
        quarter_before = now() - timedelta(days=100)
        query = DailyCetBuyBackReport.query.filter(
            DailyCetBuyBackReport.asset == 'CET',
            DailyCetBuyBackReport.report_type.in_(
                [
                    DailyCetBuyBackReport.ReportType.BUY_BACK,
                    DailyCetBuyBackReport.ReportType.TRANSFER
                ]
            ),
            DailyCetBuyBackReport.report_date >= quarter_before
        ).with_entities(
            DailyCetBuyBackReport.report_date,
            func.sum(DailyCetBuyBackReport.amount).label('total_amount'),
            func.sum(DailyCetBuyBackReport.usd).label('total_usd'),
        ).order_by(DailyCetBuyBackReport.report_date.desc()).group_by(
            DailyCetBuyBackReport.report_date
        ).all()

        day_info = dict(amount=0, value=0)
        week_info = dict(amount=0, value=0)
        monthly_info = dict(amount=0, value=0)
        quarter_info = dict(amount=0, value=0)

        condition_mapping = [
            (day_info, 3),
            (week_info, 7),
            (monthly_info, 30),
            (quarter_info, 90),
        ]
        index = 0
        for v in query:
            index += 1
            for data, delta_days in condition_mapping:
                if index <= delta_days:
                    data['amount'] += v.total_amount
                    data['value'] += v.total_usd

        return dict(
            day_info=day_info,
            week_info=week_info,
            monthly_info=monthly_info,
            quarter_info=quarter_info
        )


@ns.route('/buy_back/amount/chart')
@respond_with_code
class GetBuyBackAmountChartResource(Resource):
    """
    cet回购市值图表
    """
    @classmethod
    @ns.use_kwargs(
        dict(
            time_type=EnumField(
                enum=['day', 'month'], required=True)
        ))
    def get(cls, **kwargs):
        time_type = kwargs["time_type"]
        now_date = today()
        if time_type == "day":
            start_date = now_date - timedelta(days=90)
            end_date = now_date
            group_by = (DailyCetBuyBackReport.report_date, )
            entities = (
                DailyCetBuyBackReport.report_date,
                func.sum(DailyCetBuyBackReport.usd).label('amount')
            )
            date_format = lambda x: int(date_to_datetime(x.report_date + timedelta(days=1)).timestamp())
        elif time_type == "month":
            last_year_date = now_date.replace(day=1) - relativedelta(years=1)
            end_date = now_date - timedelta(days=now_date.day)
            start_date = max(last_year_date, date(2020, 6, 1))
            group_by = (
                func.year(DailyCetBuyBackReport.report_date).label('year'),
                func.month(DailyCetBuyBackReport.report_date).label('month')
            )
            entities = (
                func.year(DailyCetBuyBackReport.report_date).label('year'),
                func.month(DailyCetBuyBackReport.report_date).label('month'),
                func.sum(DailyCetBuyBackReport.usd).label('amount')
            )
            date_format = lambda x: int(str_to_datetime(f"{x.year}-{x.month}-01").timestamp())
        else:
            return

        query = DailyCetBuyBackReport.query.filter(
            DailyCetBuyBackReport.report_date >= start_date,
            DailyCetBuyBackReport.report_date <= end_date,
            DailyCetBuyBackReport.report_type.in_(
                [
                    DailyCetBuyBackReport.ReportType.BUY_BACK,
                    DailyCetBuyBackReport.ReportType.TRANSFER
                ]
            ),
            DailyCetBuyBackReport.asset == 'CET'
        ).group_by(
            *group_by
        ).order_by(
            DailyCetBuyBackReport.report_date.asc()
        ).with_entities(
            *entities
        ).all()

        return [[date_format(report),
                 quantize_amount(Decimal(report.amount), PrecisionEnum.COIN_PLACES)] for report in query]


@ns.route('/buy_back/details')
@respond_with_code
class CetBuyBackDetailsResource(Resource):
    """
    cet回购数据明细
    :return:
    """

    @classmethod
    @ns.use_kwargs(
        dict(page=PageField(),
             limit=LimitField(missing=7),
             )
    )
    def get(cls, **kwargs):
        params = Struct(**kwargs)
        first_date = date(2020, 4, 12)
        records = DailyCetBuyBackReport.query.filter(
            DailyCetBuyBackReport.asset == 'CET',
            DailyCetBuyBackReport.report_type ==
            DailyCetBuyBackReport.ReportType.BUY_BACK,
            DailyCetBuyBackReport.report_date > first_date).order_by(
            DailyCetBuyBackReport.report_date.desc())
        result = records.paginate(params.page, params.limit, error_out=False)
        report_dates = [v.report_date for v in result.items]
        transfer_records = DailyCetBuyBackReport.query.filter(
            DailyCetBuyBackReport.asset == 'CET',
            DailyCetBuyBackReport.report_type ==
            DailyCetBuyBackReport.ReportType.TRANSFER,
            DailyCetBuyBackReport.report_date.in_(report_dates))
        transfer_data = defaultdict(lambda:
                                    dict(amount=Decimal(), usd=Decimal()))
        for v in transfer_records:
            transfer_data[v.report_date] = {'amount': v.amount, 'usd': v.usd}
        f_amount = lambda x: quantize_amount(Decimal(x),
                                             PrecisionEnum.COIN_PLACES)
        data = [
            {
                "report_date": (v.report_date + timedelta(days=1)).strftime("%Y-%m-%d"),
                "buy_back_amount": f_amount(v.amount +
                                            transfer_data[v.report_date]['amount']),
                "price": f_amount(
                    (v.usd + transfer_data[v.report_date]['usd']) /
                    (v.amount + transfer_data[v.report_date]['amount'])
                ) if v.amount + transfer_data[v.report_date]['amount'] > Decimal()
                else Decimal(),
                "value": f_amount(v.usd + transfer_data[v.report_date]['usd'])
            } for v in result.items]

        return {
            "page": params.page,
            "limit": params.limit,
            "total": result.total,
            "data": data
        }


@ns.route('/buy_back/destroy/details')
@respond_with_code
class CetDestroyDetailsResource(Resource):
    """
    cet销毁明细
    """

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField(),
        limit=LimitField(missing=7)
    ))
    def get(cls, **kwargs):
        params = Struct(**kwargs)
        records = CetDestroyReport.query.order_by(
            CetDestroyReport.report_date.desc())
        result = records.paginate(params.page, params.limit,
                                  error_out=False)
        data = [{
            "report_date": v.report_date.strftime("%Y-%m-%d"),
            "content": v.content,
            "current_destroy_amount": amount_to_str(
                v.current_destroy_amount,
                8),
            "destroy_value": amount_to_str(v.destroy_value, 8),
        } for v in result.items]

        return {
            "page": params.page,
            "limit": params.limit,
            "total": result.total,
            "data": data
        }
