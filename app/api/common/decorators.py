# -*- coding: utf-8 -*-
import json
import time
import hmac
from base64 import b64encode
from functools import wraps
from hashlib import sha256
from inspect import isclass
from json import loads as json_loads
from types import FunctionType
from typing import Callable, Union, List, Optional
from urllib.parse import urlparse, Pa<PERSON><PERSON><PERSON><PERSON>

from flask import g, request, Response, current_app
from flask.views import http_method_funcs as _http_method_funcs
from flask_api.exceptions import AuthenticationFailed

from .auth import verify_signature, AlgorithmType
from .request import (
    get_request_ip, try_verify_request_mobile_code, verify_request_totp_code,
    try_verify_request_totp_code, verify_request_operation_token,
    verify_request_email_token, get_request_platform, verify_admin_request_operation_token,
    verify_admin_webauthn_operation_token, get_request_host_url, try_verify_request_webauthn_credential,
    get_request_language, get_request_info, get_request_user_agent_parsed,
)
from .responses import success
from ...business import (
    UserSettings, UserPreferences, SiteSettings, BusinessSettings,
    CacheLock, LockK<PERSON>s, Locked,
    is_super_user, set_user_active,
    SPOT_ACCOUNT_ID,
)
from ...business.p2p.user import P2pUserManger
from ...business.user import delete_user_login_token
from ...business.copy_trading.trader import get_copy_trader_sub_user
from ...caches import (UserLoginTokenCache, FrequencyCache, SecurityOperationCache, GeetestCache,
                       UserLoginOperationTokenCache, ApiAuthCache, SubAccountInfoCache,
                       UserLoginTokenAccessCache)
from ...caches.admin import AdminUserLoginTokenCache
from ...caches.user import UserVisitPermissionCache, UserStateUpdateCache
from ...common import (CAPTCHA_VALIDATION_LIMIT, SubAccountPermission, TradeBusinessType)
from ...common import (LOGIN_TOKEN_SIZE, Language, MobileCodeType)
from ...config import config
from ...exceptions import (
    CaptchaRequired, FrequencyExceeded,
    TwoFactorAuthenticationFailed, SuperAdminRequire,
    ForbidTrading, AccessIdDoesNotExist, OperationNotAllowed,
    AccessIdExpired, IpNotAllowed, InvalidSignature, PerpetualTradingLimited,
    EmailCodeVerificationFailed, ApiPermissionNotAllowed, LoginTimeOutFailed,
    TimestampCheckError, InvalidTotpVerificationCode,
    InvalidSecurityResetToken, SubAccountNotAllowed, SubAccountPermissionRequire,
    NoKycQualifications
)
from ...exceptions.p2p import P2pExceptionMap, P2pExceptionCode
from ...models import User, UserLoginState
from ...utils import Geetest3, Geetest4, func_to_str, now, ConfigMode, WhyNot


def _decorate_class(dec, cls):
    cls_vars = vars(cls)
    for name in _http_method_funcs:
        func = cls_vars.get(name)
        if func is None:
            continue
        if isinstance(func, (classmethod, staticmethod)):
            func = type(func)(dec(func.__func__))
        else:
            func = dec(func)
        setattr(cls, name, func)
    return cls


def respond_with_code(func_or_class):
    if isclass(func_or_class):
        return _decorate_class(respond_with_code, func_or_class)

    @wraps(func_or_class)
    def wrapper(*args, **kwargs):
        resp = func_or_class(*args, **kwargs)
        if isinstance(resp, Response):
            return resp
        return success(resp)

    return wrapper


def response_replace_host_url(func_or_class):
    if isclass(func_or_class):
        return _decorate_class(respond_with_code, func_or_class)

    @wraps(func_or_class)
    def wrapper(*args, **kwargs):
        resp = func_or_class(*args, **kwargs)
        site_url = config['SITE_URL']
        request_host_url = get_request_host_url()
        need_replace = site_url != request_host_url
        if not need_replace:
            return resp

        if isinstance(resp, Response):
            resp_json = resp.get_json()
            _replace_host_url(resp_json, site_url, request_host_url)
            resp.data = json.dumps(resp_json)
        else:
            _replace_host_url(resp, site_url, request_host_url)
        return resp

    return wrapper


def _replace_host_url(res, from_url, new_host):

    def deal_dict(dict_):
        for key, value in dict_.items():
            if isinstance(value, list):
                deal_list(value)
            elif isinstance(value, dict):
                deal_dict(value)
            elif isinstance(value, str) and value.startswith(from_url):
                dict_[key] = _host_replace(value, new_host)

    def deal_list(list_):
        for i, value in enumerate(list_):
            if isinstance(value, list):
                deal_list(value)
            elif isinstance(value, dict):
                deal_dict(value)
            elif isinstance(value, str) and value.startswith(from_url):
                list_[i] = _host_replace(value, new_host)

    if isinstance(res, list):
        deal_list(res)
    elif isinstance(res, dict):
        deal_dict(res)


def _host_replace(value, new_host):
    val = urlparse(value)
    to = urlparse(new_host)
    new = ParseResult(to.scheme, to.netloc, val.path, val.params, val.query, val.fragment)
    return new.geturl()


def respond_with_code_new_message(func_or_class):
    if isclass(func_or_class):
        return _decorate_class(respond_with_code_new_message, func_or_class)

    @wraps(func_or_class)
    def wrapper(*args, **kwargs):
        resp = func_or_class(*args, **kwargs)
        if isinstance(resp, Response):
            return resp
        return success(resp, message="OK")

    return wrapper


def check_login_token(token: str):
    if not token or len(token) != LOGIN_TOKEN_SIZE:
        return False
    return True


def verify_user_permission(user_id):
    if not UserSettings(user_id, ConfigMode.REAL_TIME).login_enabled:
        raise AuthenticationFailed
    if UserVisitPermissionCache().check_user_permission(
            user_id,
            [UserVisitPermissionCache.FORBIDDEN_VALUE]):
        raise AuthenticationFailed


def verify_user_login_safe_settings(user_id: int, login_token: str):

    def _ip_is_different(_ip1: str, _ip2: str) -> bool:
        if _ip1 and _ip2 and _ip1 != _ip2:
            whitelist_ips = BusinessSettings.login_ip_locking_whitelist_ips
            if _ip1 not in whitelist_ips and _ip2 not in whitelist_ips:
                return True
        return False

    platform = get_request_platform()
    ua = get_request_user_agent_parsed()
    if platform.is_web() and not ua.is_mobile:
        pref = UserPreferences(user_id, ConfigMode.REAL_TIME)
        # APP的H5页面（'platform': 'WEB'），额外判断下ua
        if pref.opening_web_login_ip_locking:
            login_state: UserLoginState = UserLoginState.query.filter(
                UserLoginState.user_id == user_id,
                UserLoginState.token == login_token,
            ).first()
            req_ip = get_request_ip()
            if login_state and _ip_is_different(login_state.ip, req_ip):
                current_app.logger.warning(f"verify_user_login_safe find {user_id} {(login_state.id, login_state.ip)} {get_request_info()}")
                delete_user_login_token(login_state)
                raise AuthenticationFailed
        if pref.opening_web_security_login_duration:
            if not UserLoginTokenAccessCache(login_token).exists():
                delete_user_login_token(login_token)
                raise AuthenticationFailed
            UserLoginTokenAccessCache(login_token).access()


def require_login(func_or_class=None, *, allow_sub_account: bool = True):
    """
    Please put this decorator above any `ns.expect()`.
    """
    def dec(_func):

        @wraps(_func)
        def wrapper(*args, **kwargs):
            if g.get('auth_user') is None:
                token = request.headers.get('AUTHORIZATION', '')
                if not check_login_token(token):
                    raise AuthenticationFailed

                token_cache = UserLoginTokenCache.from_token(token)
                if token_cache is None:
                    raise AuthenticationFailed
                user_id = token_cache.user_id

                user = User.query.filter_by(id=user_id).first()
                if not user:
                    raise AuthenticationFailed
                if user.is_sub_account:
                    raise AuthenticationFailed
                verify_user_permission(user_id)
                verify_user_login_safe_settings(user_id, token)

                switch_user_id = request.headers.get('USERID', '')
                if not switch_user_id:
                    # 主账户登入
                    switch_user = user
                else:
                    # 主账户登入后 切换到子账号身份
                    try:
                        switch_user_id = int(switch_user_id)
                    except ValueError:
                        raise AuthenticationFailed
                    if user_id == switch_user_id:
                        raise AuthenticationFailed

                    if not allow_sub_account:
                        raise SubAccountNotAllowed
                    if not UserSettings(switch_user_id, ConfigMode.REAL_TIME).login_enabled:
                        raise AuthenticationFailed

                    sub_acc_info = SubAccountInfoCache(switch_user_id).info()
                    if not sub_acc_info:
                        raise AuthenticationFailed
                    if user_id != sub_acc_info["main_user_id"] and user_id not in sub_acc_info["manage_user_ids"]:
                        raise AuthenticationFailed
                    if user_id in sub_acc_info["manage_user_ids"]:
                        verify_user_permission(sub_acc_info["main_user_id"])

                    switch_user = User.query.filter_by(id=switch_user_id).first()
                    if not switch_user:
                        raise AuthenticationFailed

                if expires_at := token_cache.renew(token):
                    token_dict = {token: expires_at}
                    _refresh_user_state(user_id, token_dict)

                g.auth_user = user  # 登入用户，用于鉴权
                g.user = switch_user  # 切换的用户，用于业务
                g.login_token = token

                platform_name = get_request_platform().name
                set_user_active(user_id, platform_name)
                if switch_user_id:
                    set_user_active(switch_user_id, platform_name)

            return _func(*args, **kwargs)

        return wrapper

    if func_or_class is not None:
        return dec(func_or_class)

    return dec


def _refresh_user_state(user_id: int, token_dict: dict):
    from traceback import format_exc
    from .request import get_request_info

    pref = UserPreferences(user_id, ConfigMode.REAL_TIME)
    pref_dict = {}
    req_lang = get_request_language(use_default_val=False)
    if not req_lang:
        req = get_request_info()
        msg = ' '.join([f'{k}: {v}' for k, v in req.items()])
        current_app.logger.warning(f"require_login lack of language: {msg} {format_exc()}",
                                   extra={'method': req['method'], 'path': req['path']})
    else:
        if (lang := g.get('user_lang')) is not None:
            pref_dict["language"] = Language(lang)
        if (web_lang := g.get('user_lang_web')) is not None:
            pref_dict["web_language"] = Language(web_lang)
        if (app_lang := g.get('user_lang_app')) is not None:
            pref_dict["app_language"] = Language(app_lang)

    if (tz_offset := g.get('user_tz_offset')) is not None:
        pref_dict["timezone_offset"] = tz_offset
    request_host_url = get_request_host_url()
    if request_host_url.endswith('/'):
        request_host_url = request_host_url[:-1]
    if request_host_url != pref.host_url:
        pref_dict["host_url"] = request_host_url

    UserStateUpdateCache().add(user_id, token_dict, pref_dict)


def require_admin_login(func_or_class=None):
    """
    admin login
    """
    def dec(_func):
        @wraps(_func)
        def wrapper(*args, **kwargs):
            if g.get('user') is None:
                token = request.headers.get('AUTHORIZATION', '')
                if not check_login_token(token):
                    raise AuthenticationFailed

                token_cache = AdminUserLoginTokenCache.from_token(token)
                if token_cache is None:
                    raise AuthenticationFailed
                user_id = token_cache.user_id

                user = User.query.filter_by(id=user_id).first()
                if not user:
                    raise AuthenticationFailed
                if user.is_sub_account:
                    raise AuthenticationFailed

                g.user = g.auth_user = user  # 登入用户，用于鉴权
                g.admin_token = token

            return _func(*args, **kwargs)

        return wrapper

    if func_or_class is not None:
        return dec(func_or_class)

    return dec


def require_super_admin(func=None):
    def dec(_func):

        @wraps(_func)
        def wrapper(*args, **kwargs):
            if not is_super_user(g.user.id):
                raise SuperAdminRequire
            return _func(*args, **kwargs)
        return require_login()(wrapper)

    if func is not None:
        return dec(func)

    return dec


def require_geetest(func=None):
    def dec(_func):

        @wraps(_func)
        def wrapper(*args, **kwargs):
            geetest = request.headers.get('geetest')
            if not geetest:
                raise CaptchaRequired

            try:
                data = json_loads(geetest)
            except Exception:
                raise CaptchaRequired

            limit_ip_frequency(*CAPTCHA_VALIDATION_LIMIT)(lambda : True)()

            if Geetest3.FN_CHALLENGE in data:  # old app geetest
                try:
                    challenge = data[Geetest3.FN_CHALLENGE][:Geetest3.CHALLENGE_SIZE]
                    gt_cache = GeetestCache(challenge)
                    user_id, status = gt_cache.get_data()
                except Exception:
                    raise CaptchaRequired

                if not Geetest3().validate(data, user_id, status):
                    raise CaptchaRequired

                gt_cache.delete()
            else:
                try:
                    captcha_id = data['captcha_id']
                    lot_number = data['lot_number']
                    captcha_output = data['captcha_output']
                    pass_token = data['pass_token']
                    gen_time = data['gen_time']
                    captcha_key = config['GEETEST4'][captcha_id]
                except Exception:
                    raise CaptchaRequired

                if not Geetest4(captcha_id, captcha_key).validate(lot_number, captcha_output, pass_token, gen_time):
                    raise CaptchaRequired

            return _func(*args, **kwargs)

        return wrapper

    if func is not None:
        return dec(func)

    return dec


def require_login_operation_token(func):

    @wraps(func)
    def wrapper(*args, **kwargs):
        headers = request.headers
        token = (headers.get('OPERATION_TOKEN')
                 or headers.get('OPERATE_TOKEN')
                 or headers.get('Operate-Token'))
        if not token:
            raise TwoFactorAuthenticationFailed

        cache = UserLoginOperationTokenCache(token)
        user_id = cache.get_user()
        if not user_id:
            raise LoginTimeOutFailed

        user = User.query.get(user_id)
        if user is None:
            raise TwoFactorAuthenticationFailed
        if user.is_sub_account:
            raise SubAccountNotAllowed

        g.auth_user = g.user = user
        return func(*args, **kwargs)

    return wrapper


def require_security_reset_token(func):

    @wraps(func)
    def wrapper(*args, **kwargs):
        if request.method in ('GET', 'DELETE'):
            token = request.args.get('operate_token')
        else:
            token = (value := request.get_json(silent=True)) and value.get('operate_token')
        if not token:
            token = request.headers.get('operate_token') or request.headers.get('operate-token')
        if not token or not isinstance(token, str):
            raise InvalidSecurityResetToken
        if not (user_info := SecurityOperationCache(token).read()):
            raise InvalidSecurityResetToken
        user_id = int(user_info['user_id'])
        user = User.query.get(user_id)
        if user.is_sub_account:
            raise SubAccountNotAllowed
        g.operate_token = token
        g.security_reset_info = user_info
        g.auth_user = g.user = user

        return func(*args, **kwargs)

    return wrapper


def require_totp_code(func=None, *, allow_sub_account: bool = False):
    def dec(_func):

        @wraps(_func)
        def wrapper(*args, **kwargs):
            _user: User = g.auth_user
            if not _user.totp_auth_key:
                raise InvalidTotpVerificationCode
            _callback = verify_request_totp_code(_user)
            _ret = _func(*args, **kwargs)
            _callback()
            return _ret

        return require_login(allow_sub_account=allow_sub_account)(wrapper)

    if func is not None:
        return dec(func)

    return dec


def require_2fa(code_type: MobileCodeType, *,
                allow_sub_account: bool = True):
    def dec(_func):

        @wraps(_func)
        def wrapper(*args, **kwargs):
            _user = g.auth_user
            with CacheLock(LockKeys.two_fa(_user.id), wait=False):
                callback = try_verify_request_mobile_code(_user, code_type)
                if not callback:
                    callback = try_verify_request_totp_code(_user)
                if not callback:
                    callback = try_verify_request_webauthn_credential(_user)
                if not callback:
                    callback = verify_request_operation_token(_user)
                _ret = _func(*args, **kwargs)
                callback()
            return _ret

        return require_login(allow_sub_account=allow_sub_account)(wrapper)

    return dec


def require_email_code(func=None, *, allow_sub_account: bool = True):
    def dec(_func):

        @wraps(_func)
        def wrapper(*args, **kwargs):
            _user: User = g.auth_user
            if not _user.main_user_email:
                raise EmailCodeVerificationFailed
            _callback = verify_request_email_token(_user)
            _ret = _func(*args, **kwargs)
            _callback()
            return _ret

        return require_login(allow_sub_account=allow_sub_account)(wrapper)

    if func is not None:
        return dec(func)

    return dec


def require_kyc(func=None, *, allow_sub_account: bool = True):
    def dec(_func):

        @wraps(_func)
        def wrapper(*args, **kwargs):
            _user: User = g.auth_user
            if _user.kyc_status != User.KYCStatus.PASSED:
                raise NoKycQualifications
            _ret = _func(*args, **kwargs)
            return _ret

        return require_login(allow_sub_account=allow_sub_account)(wrapper)

    if func is not None:
        return dec(func)

    return dec

def require_trade_password(func=None):
    def deco(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            from app.business.security import check_trade_password
            user_id = g.user.main_user_id
            if UserPreferences(user_id).opening_trade_password:
                headers = request.headers
                trade_password = headers.get('Trade-Password')
                if not trade_password:
                    if (request_json := request.get_json(silent=True)) \
                        and (value := request_json.get('trade_password')):
                        trade_password = value
                check_trade_password(g.user, trade_password)
            return func(*args, **kwargs)
        return wrapper
    if func is not None:
        return deco(func)

    return deco


def limit_frequency(count: int, interval: int,
                    func_name: str = None,
                    arg_key: Union[str, Callable[..., str]] = None):
    def dec(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            nonlocal func_name, arg_key

            _func_name = func_name or func_to_str(func)
            _arg_key = arg_key(*args) if callable(arg_key) else arg_key
            full_key = f'{_func_name}-{_arg_key}' if _arg_key else func_name

            cache = FrequencyCache(full_key, interval)
            if not cache.add_value_if_fewer_than(count):
                raise FrequencyExceeded
            return func(*args, **kwargs)

        return wrapper

    return dec


def limit_ip_frequency(count: int, interval: int):
    return limit_frequency(count, interval,
                           arg_key=lambda *args: get_request_ip())


def limit_user_frequency(count: int, interval: int):
    return limit_frequency(count, interval, arg_key=lambda *args: g.user.id)


def lock_request(key: Union[str, FunctionType] = None,
                 *, with_user: bool = False):
    """
    Put this decorator under `require_login` if `with_user` is True.
    """
    def dec(func):
        nonlocal key
        key = key or LockKeys.api_lock(func_to_str(func))

        @wraps(func)
        def wrapper(*args, **kwargs):
            _key = key
            if with_user:
                if (user := g.get('user')) is None:
                    raise AuthenticationFailed
                _key = f'{_key}:user={user.id}'
            try:
                with CacheLock(_key, wait=False):
                    return func(*args, **kwargs)
            except Locked:
                raise FrequencyExceeded

        return wrapper

    if isinstance(key, FunctionType):
        return dec(key)

    return dec


def trade_permission_validate(*, is_spot: bool,
                              account_id: Optional[int] = None,
                              is_closing_or_reducing_position: bool = False):
    """
    account_id：现货交易，杠杆账户id，默认从api的kwargs参数中获取。
    is_closing_or_reducing_position：合约交易，是否正在进行平(减)仓操作
    """

    def deco(func):

        @wraps(func)
        def wrapper(*args, **kwargs):
            user_setting = UserSettings(g.user.id)
            condition = [
                SiteSettings.trading_enabled,
            ]
            check_account_id = account_id
            if is_spot:
                if check_account_id is None:
                    check_account_id = kwargs.get("account_id")
                if check_account_id is None:
                    raise ValueError("account_id is required")
                check_account_id = int(check_account_id)
                if check_account_id == SPOT_ACCOUNT_ID:
                    condition.append(user_setting.spot_trading_enabled)
                else:
                    condition.append(user_setting.margin_trading_enabled)
                    condition.append(user_setting.can_margin_account_trade(check_account_id))
                    # 杠杆下单 检查子账号的权限
                    require_user_permission(sub_account_permissions=[SubAccountPermission.MARGIN])(lambda: None)()
                condition.append(SiteSettings.spot_trading_enabled)
            else:
                condition.append(SiteSettings.perpetual_trading_enable)
                condition.append(user_setting.perpetual_trading_enabled)
                # 限制合约交易，但可以平(减)仓
                if not is_closing_or_reducing_position:
                    condition.append(WhyNot(not user_setting.perpetual_limited, PerpetualTradingLimited))

            for c in condition:
                if not c:
                    err = c.reason if isinstance(c, WhyNot) else ForbidTrading
                    raise err

            return func(*args, **kwargs)
        return wrapper

    return deco


def copy_trading_sub_user_setter(*, require_running: bool,
                                 check_args: list[str] = None,
                                 check_count: bool = False):
    """
    用于跟单交易相关的合约api，获取带单人的带单子帐号sub_user，并替换g.user = sub_user
    需要与require_login装饰器一起使用
    """
    def deco(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            is_copy_trader_sub = request.headers.get('IS-COPY-TRADER-SUB', '') == "1"
            if is_copy_trader_sub:
                req_user = g.user
                if req_user.is_sub_account:
                    raise AuthenticationFailed

                check_data = {k: kwargs.get(k) for k in check_args} if check_args else None
                copy_trader_sub_user = get_copy_trader_sub_user(
                    main_user_id=req_user.id,
                    require_running=require_running,
                    check_pos_count=check_count,
                    check_data=check_data,
                )
                g.user = copy_trader_sub_user
                g.is_copy_trader_sub = True
            return func(*args, **kwargs)
        return wrapper

    return deco


def require_api_auth(func=None, *, allow_sub_account: bool = True,
                     require_withdrawal_permission: bool = False,
                     require_trading_permission: bool = False,
                     business_type: TradeBusinessType = TradeBusinessType.SPOT):
    def dec(_func):
        @wraps(_func)
        def wrapper(*args, **kwargs):
            access_id = (request.args.get('access_id')
                         or (json.get('access_id') if (json := request.get_json(silent=True))
                             else None)
                         or request.headers.get('access_id'))
            if not access_id:
                raise AccessIdDoesNotExist

            auth_cache = ApiAuthCache(access_id)
            try:
                auth = auth_cache.dict
            except Exception:
                raise AccessIdDoesNotExist
            if auth['status'] is not ApiAuthCache.model.Status.VALID:
                raise AccessIdDoesNotExist
            if (expired_at := auth['expired_at']) and now() >= expired_at:
                raise AccessIdExpired

            if allowed_ips := auth['allowed_ips']:
                ip = get_request_ip()
                if ip not in set(allowed_ips.split('\n')):
                    raise IpNotAllowed

            body = dict(request.args or request.get_json(silent=True) or ())
            time_check_field = 'tonce'
            if business_type == TradeBusinessType.PERPETUAL:
                time_check_field = 'timestamp'
            if not (tonce := body.get(time_check_field)):
                raise TimestampCheckError
            try:
                tonce = int(tonce)
            except Exception:
                raise TimestampCheckError
            if abs(int(time.time() * 1000) - tonce) > 60000:
                raise TimestampCheckError

            body['access_id'] = access_id
            if business_type == TradeBusinessType.PERPETUAL:
                body = {
                    "access_id": access_id,
                    time_check_field: tonce
                }
                algorithm_type = AlgorithmType.SHA256
            else:
                algorithm_type = AlgorithmType.MD5

            if not verify_signature(body,
                                    auth['secret_key'],
                                    request.headers.get('AUTHORIZATION', ''),
                                    algorithm_type):
                raise InvalidSignature

            user = User.query.get(auth['user_id'])
            if (not allow_sub_account
                    and user.user_type is User.UserType.SUB_ACCOUNT):
                raise SubAccountNotAllowed

            withdrawals_enabled = auth['withdrawals_enabled']
            trading_enabled = auth['trading_enabled']
            if require_withdrawal_permission and not withdrawals_enabled:
                raise ApiPermissionNotAllowed
            if require_trading_permission and not trading_enabled:
                raise ApiPermissionNotAllowed

            if not UserSettings(user.id).login_enabled:
                raise OperationNotAllowed

            g.user = user
            g.api_auth = auth_cache.shadow
            g.withdrawals_enabled = withdrawals_enabled
            g.trading_enabled = trading_enabled
            return _func(*args, **kwargs)

        return wrapper

    if func is not None:
        return dec(func)

    return dec


def check_api_v2_sign(message: str, secret_key: str, user_signature: str) -> bool:
    sha256_sign = sha256(f"{message}{secret_key}".encode()).hexdigest().upper()
    upper_sign = user_signature.upper()
    if upper_sign == sha256_sign:
        return True

    hmac_sign = hmac.new(
        secret_key.encode(),
        msg=message.encode(),
        digestmod=sha256
    ).hexdigest().upper()
    if upper_sign == hmac_sign:
        return True

    return False


def require_api_v2_auth(func=None, *, allow_sub_account: bool = True,
                        require_withdrawal_permission: bool = False,
                        require_trading_permission: bool = False):

    def dec(_func):
        @wraps(_func)
        def wrapper(*args, **kwargs):

            DEFAULT_WINDOW_TIME_MS = 5000
            window_time_ms = request.headers.get('X-COINEX-WINDOWTIME') or DEFAULT_WINDOW_TIME_MS
            access_id = request.headers.get('X-COINEX-KEY') or ''
            signature = request.headers.get('X-COINEX-SIGN') or ''
            tonce = request.headers.get('X-COINEX-TIMESTAMP')

            if not access_id:
                raise AccessIdDoesNotExist

            auth_cache = ApiAuthCache(access_id)
            try:
                auth = auth_cache.dict
            except Exception:
                raise AccessIdDoesNotExist
            if auth['status'] is not ApiAuthCache.model.Status.VALID:
                raise AccessIdDoesNotExist
            if (expired_at := auth['expired_at']) and now() >= expired_at:
                raise AccessIdExpired

            if allowed_ips := auth['allowed_ips']:
                ip = get_request_ip()
                if ip not in set(allowed_ips.split('\n')):
                    raise IpNotAllowed

            try:
                tonce = int(tonce)
                window_time_ms = int(window_time_ms)
            except Exception:
                raise TimestampCheckError
            if abs(int(time.time() * 1000) - tonce) > window_time_ms:
                raise TimestampCheckError

            user = User.query.get(auth['user_id'])
            if (not allow_sub_account
                    and user.user_type is User.UserType.SUB_ACCOUNT):
                raise SubAccountNotAllowed
            full_path = request.full_path if request.query_string else request.path
            build_str = f"{request.method}{full_path}{request.get_data(as_text=True)}{tonce}"
            if not check_api_v2_sign(build_str, auth["secret_key"], signature):
                raise InvalidSignature

            withdrawals_enabled = auth['withdrawals_enabled']
            trading_enabled = auth['trading_enabled']
            if require_withdrawal_permission and not withdrawals_enabled:
                raise ApiPermissionNotAllowed
            if require_trading_permission and not trading_enabled:
                raise ApiPermissionNotAllowed

            if not UserSettings(user.id).login_enabled:
                raise OperationNotAllowed

            g.user = user
            g.api_auth = auth_cache.shadow
            g.withdrawals_enabled = withdrawals_enabled
            g.trading_enabled = trading_enabled
            return _func(*args, **kwargs)

        return wrapper

    if func is not None:
        return dec(func)

    return dec


def require_api_v2_ws_auth(func=None, *, allow_sub_account: bool = True):

    def dec(_func):
        @wraps(_func)
        def wrapper(*args, **kwargs):

            DEFAULT_WINDOW_TIME_MS = 5000
            window_time_ms = request.headers.get('X-COINEX-WINDOWTIME') or DEFAULT_WINDOW_TIME_MS
            access_id = request.headers.get('X-COINEX-KEY') or ''
            signature = request.headers.get('X-COINEX-SIGN') or ''
            tonce = request.headers.get('X-COINEX-TIMESTAMP')

            if not access_id:
                raise AccessIdDoesNotExist

            auth_cache = ApiAuthCache(access_id)
            try:
                auth = auth_cache.dict
            except Exception:
                raise AccessIdDoesNotExist
            if auth['status'] is not ApiAuthCache.model.Status.VALID:
                raise AccessIdDoesNotExist
            if (expired_at := auth['expired_at']) and now() >= expired_at:
                raise AccessIdExpired

            if allowed_ips := auth['allowed_ips']:
                ip = get_request_ip()
                if ip not in set(allowed_ips.split('\n')):
                    raise IpNotAllowed

            try:
                tonce = int(tonce)
                window_time_ms = int(window_time_ms)
            except Exception:
                raise TimestampCheckError
            if abs(int(time.time() * 1000) - tonce) > window_time_ms:
                raise TimestampCheckError

            user = User.query.get(auth['user_id'])
            if (not allow_sub_account
                    and user.user_type is User.UserType.SUB_ACCOUNT):
                raise SubAccountNotAllowed
            build_str = f"{tonce}"

            if not check_api_v2_sign(build_str, auth["secret_key"], signature):
                raise InvalidSignature

            if not UserSettings(user.id).login_enabled:
                raise OperationNotAllowed

            g.user = user
            return _func(*args, **kwargs)

        return wrapper

    if func is not None:
        return dec(func)

    return dec


def api_permission_deco(check_trade=False, check_withdraw=False):
    """
    should put below require_api_auth
    """
    def deco(func):
        @wraps(func)
        def _wrapped(*args, **kwargs):
            if check_trade:
                if not g.trading_enabled:
                    raise ApiPermissionNotAllowed
            if check_withdraw:
                if not g.withdrawals_enabled:
                    raise ApiPermissionNotAllowed
            return func(*args, **kwargs)
        return _wrapped
    return deco


def require_verify_sign(func_):
    @wraps(func_)
    def wrap(*args, **kwargs):
        sign = request.headers.get('X-DATA-SIGN')
        if not sign:
            raise InvalidSignature
        body = request.data
        b64_body = b64encode(body)
        sha256_body = sha256(b64_body + b"." + config.get('EVENT_MONITOR_SIGN_SECRET').encode()).digest()
        content = b64encode(sha256_body).decode()
        if sign != content:
            raise InvalidSignature
        return func_(*args, **kwargs)
    return wrap


def trace_require_verify_sign(func_):
    @wraps(func_)
    def wrap(*args, **kwargs):
        sign = request.headers.get('X-DATA-SIGN')
        if not sign:
            raise InvalidSignature
        body = request.data
        b64_body = b64encode(body)
        sha256_body = sha256(b64_body + b"." + config.get('EVENT_TRACE_SIGN_SECRET').encode()).digest()
        content = b64encode(sha256_body).decode()
        if sign != content:
            raise InvalidSignature
        return func_(*args, **kwargs)
    return wrap


def require_user_permission(sub_account_permissions: List[SubAccountPermission]):
    def deco(func):

        @wraps(func)
        def wrapper(*args, **kwargs):
            user: User = g.user
            if user.is_sub_account:
                # 目前只有切换到子账号时 要检查权限
                sub_acc_info = SubAccountInfoCache(user.id).info()
                sub_permissions = sub_acc_info.get("permissions", [])
                for need_permission in sub_account_permissions:
                    if need_permission.name not in sub_permissions:
                        raise SubAccountPermissionRequire.from_name(name=need_permission.name)

            return func(*args, **kwargs)

        return wrapper

    return deco


def require_admin_email_code(func=None, *, delete_cache: bool = False):
    def dec(_func):

        @wraps(_func)
        def wrapper(*args, **kwargs):
            _user = g.operation_user
            _callback = verify_request_email_token(_user)
            if delete_cache:
                _callback()
            _ret = _func(*args, **kwargs)
            return _ret

        return wrapper

    if func is not None:
        return dec(func)

    return dec


def require_admin_operation_token(func=None, *, delete_cache=False):
    def dec(_func):
        @wraps(_func)
        def wrapper(*args, **kwargs):
            _cb = verify_admin_request_operation_token()
            if delete_cache:
                _cb()
            return _func(*args, **kwargs)
        return wrapper

    if func is not None:
        return dec(func)

    return dec


def require_admin_webauth_token(func=None):
    def dec(_func):
        @wraps(_func)
        def wrapper(*args, **kwargs):
            _user: User = g.user
            _cb = verify_admin_webauthn_operation_token(_user)
            _cb()
            return _func(*args, **kwargs)
        return wrapper

    if func is not None:
        return dec(func)

    return dec


def require_p2p_permission(
        func_or_class=None,
        *,
        is_merchant: bool = False,
        is_trading: bool = False,
        is_kyc: bool = False,
        is_valid_mer: bool = False,
        auth_method: str = 'WEB',
):
    def dec(_func):

        @wraps(_func)
        def wrapper(*args, **kwargs):
            user = g.user
            if is_kyc:
                if (user.kyc_status != User.KYCStatus.PASSED and
                        user.kyc_pro_status != User.KycProStatus.PASSED):
                    raise NoKycQualifications

            pref = UserPreferences(user.id)
            # 未开通 p2p 权限
            if not pref.opening_p2p_function:
                raise P2pExceptionMap[P2pExceptionCode.NOT_OPEN_P2P_FUNCTION]
            # 未开通 商家权限
            if is_merchant:
                check_p2p_merchant_function(pref)
            # 交易权限，检查全站p2p交易是否禁止
            if is_trading:
                check_p2p_site_setting()
            # 检查商家保证金
            if is_valid_mer:
                check_p2p_merchant_function(pref)
                P2pUserManger(user.id).check_merchant_valid_status()

            return _func(*args, **kwargs)

        if auth_method == 'API':
            return require_api_v2_auth(allow_sub_account=False)(wrapper)
        elif auth_method == 'WEB':
            return require_login(allow_sub_account=False)(wrapper)
        else:
            raise ValueError(f"Invalid auth_method: {auth_method}. Must be 'WEB' or 'API'")

    if func_or_class is not None:
        return dec(func_or_class)

    return dec


def require_p2p_site_setting(func):
    @wraps(func)
    def wrap(*args, **kwargs):
        check_p2p_site_setting()
    return wrap


def check_p2p_merchant_function(pref):
    if not pref.opening_p2p_merchant_function:
        raise P2pExceptionMap[P2pExceptionCode.NOT_OPEN_P2P_FUNCTION]


def check_p2p_site_setting():
    if not SiteSettings.p2p_trading_enabled or not SiteSettings.p2p_trading_enabled_manual:
        raise P2pExceptionMap[P2pExceptionCode.FORBID_P2P_TRADING]


def require_p2p_permission_except_mobile(
        func_or_class=None,
        *,
        is_merchant: bool = False,
        is_trading: bool = False,
        is_kyc: bool = False,
):
    def dec(_func):
        @wraps(_func)
        def wrapper(*args, **kwargs):
            if not get_request_platform().is_mobile():
                require_p2p_permission(
                    lambda: g.user,
                    is_merchant=is_merchant,
                    is_trading=is_trading,
                    is_kyc=is_kyc
                )()
            return _func(*args, **kwargs)
        return wrapper

    if func_or_class is not None:
        return dec(func_or_class)

    return dec


def read_only(func):
    func.read_only = True
    return func