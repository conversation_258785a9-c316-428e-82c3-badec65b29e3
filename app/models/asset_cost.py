# -*- coding: utf-8 -*-
from __future__ import annotations

from app.common.constants import AccountBalanceType

from .base import ModelBase, db

class AssetCost(ModelBase):
    """
    用户持仓成本记录
    """

    __table_args__ = (
        db.UniqueConstraint('user_id', 'account_type', 'asset', name="user_id_account_type_asset_uniq"),
    )

    user_id = db.Column(db.Integer, nullable=False)  # no foreign key here
    account_type = db.Column(db.StringEnum(AccountBalanceType), nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    price = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0) # 单位USD

    # 持仓量，基于此持仓量更新均价
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  


class AssetCostUpdateHistory(ModelBase):
    """
    用户手动修改持仓成本记录
    """

    __table_args__ = (
        db.Index("idx_user_id_asset", "user_id", "asset"),
    )
    
    user_id = db.Column(db.Integer, nullable=False)  # no foreign key here
    asset = db.Column(db.String(32), nullable=False)
    price = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0) # 单位USD