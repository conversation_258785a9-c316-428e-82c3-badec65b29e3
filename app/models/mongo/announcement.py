# -*- coding: utf-8 -*-
from typing import List, Optional, Tuple
from enum import Enum

from sqlalchemy import desc, asc

from app.models import db
from app.models.base import M2MModelBase
from app.common.constants import Language


class AnnouncementArticleMySQL(M2MModelBase):
    """MySQL 版本的公告文章模型"""
    __tablename__ = 'announcement_articles'

    LANG_LOCALE_DICT = {
        Language.EN_US.value: "en-us",
        Language.ZH_HANS_CN.value: "zh-cn",
        Language.ZH_HANT_HK.value: "zh-tw",
        Language.ES_ES.value: "es",
        Language.RU_KZ.value: "ru",
        Language.AR_AE.value: "ar",
        Language.FA_IR.value: "fa",
        Language.TR_TR.value: "tr",
        Language.JA_JP.value: "ja",
        Language.KO_KP.value: "ko-kr",
        Language.VI_VN.value: "vi-vn",
        Language.ID_ID.value: "id-id",
        Language.TH_TH.value: "th",
        Language.PT_PT.value: "pt",
        Language.DE_DE.value: "de",
        Language.FR_FR.value: "fr",
        Language.IT_IT.value: "it",
        Language.PL_PL.value: "pl",
    }

    LOCALE_LANG_DICT = {v: k for k, v in LANG_LOCALE_DICT.items()}

    class Status(Enum):
        DRAFT = "draft"
        VALID = "valid"
        DELETED = "deleted"

    id = db.Column(db.Integer, primary_key=True)
    article_id = db.Column(db.BigInteger, nullable=False)
    section_id = db.Column(db.BigInteger, nullable=False)
    category_id = db.Column(db.BigInteger, nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    title = db.Column(db.String(255), nullable=False)
    body_html = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False)
    html_url = db.Column(db.String(512))
    position = db.Column(db.Integer, nullable=False)
    is_top = db.Column(db.Boolean, nullable=False, default=False)
    status = db.Column(db.StringEnum(Status), nullable=False)

    __table_args__ = (
        db.UniqueConstraint('article_id', 'lang', name='article_id_lang_unique'),
        db.Index('idx_lang_section_is_top_created_at', 'lang', 'section_id', 'is_top', 'created_at'),
        db.Index('idx_lang_is_top_created_at', 'lang', 'is_top', 'created_at'),
        db.Index('idx_lang_created_at', 'lang', 'created_at'),
    )

    @classmethod
    def latest_update(cls, lang: Language, only: List[str] = None) -> Optional["AnnouncementArticleMySQL"]:
        query = cls.query.filter(cls.lang == lang).order_by(desc(cls.updated_at))
        return query.first()

    @classmethod
    def create_or_update(cls, article: dict):
        instance = cls.query.filter(
            cls.article_id == article["article_id"],
            cls.lang == article["lang"]
        ).first()
        
        if instance:
            for key, value in article.items():
                setattr(instance, key, value)
        else:
            instance = cls(**article)
            db.session.add(instance)
        
        db.session.commit()

    @classmethod
    def delete_by_article_ids(cls, article_ids: List[int]):
        cls.query.filter(
            cls.article_id.in_(article_ids)
        ).update({cls.status: cls.Status.DELETED}, synchronize_session=False)
        db.session.commit()

    @classmethod
    def pagination(
        cls,
        lang: Language,
        category_id: int = None,
        section_id: int = None,
        page: int = 1,
        limit: int = 10,
        only: List[str] = None,
        order_by: str = None,
    ) -> Tuple[int, List[Optional["AnnouncementArticleMySQL"]]]:
        query = cls.query.filter(
            cls.lang == lang,
            cls.status == cls.Status.VALID
        )
        
        if category_id:
            query = query.filter(cls.category_id == category_id)
        if section_id:
            query = query.filter(cls.section_id == section_id)
            
        if not order_by:
            query = query.order_by(desc(cls.is_top), desc(cls.created_at))
        else:
            is_desc = True if order_by.startswith('-') else False
            order_by = order_by[1:] if is_desc else order_by
            if is_desc:
                query = query.order_by(desc(getattr(cls, order_by)))
            else:
                query = query.order_by(asc(getattr(cls, order_by)))

        total = query.count()
        items = query.offset((page - 1) * limit).limit(limit).all()
        return total, items

    @classmethod
    def top_article_pagination(
        cls,
        lang: Language,
        page: int = 1,
        limit: int = 10,
        only: List[str] = None,
    ) -> Tuple[int, List[Optional["AnnouncementArticleMySQL"]]]:
        query = cls.query.filter(
            cls.lang == lang,
            cls.is_top == True,  # noqa: E712
            cls.status == cls.Status.VALID
        ).order_by(desc(cls.created_at))
        
        total = query.count()
        items = query.offset((page - 1) * limit).limit(limit).all()
        return total, items

    @classmethod
    def get_by_article_id(
            cls, article_id: int, lang: Language, only: List[str] = None
    ) -> Optional['AnnouncementArticleMySQL']:
        return cls.query.filter(
            cls.article_id == article_id,
            cls.lang == lang,
            cls.status == cls.Status.VALID
        ).first()

    @classmethod
    def latest_n(cls, lang: Language, num: int, only: List[str] = None) -> List['AnnouncementArticleMySQL']:
        return cls.query.filter(
            cls.lang == lang,
            cls.status == cls.Status.VALID
        ).order_by(desc(cls.created_at)).limit(num).all()


class HelpCenterArticleMySQL(M2MModelBase):
    """MySQL 版本的帮助中心文章模型"""
    __tablename__ = 'help_center_articles'

    LANG_LOCALE_DICT = {
        Language.EN_US.value: "en-us",
        Language.ZH_HANS_CN.value: "zh-cn",
        Language.ZH_HANT_HK.value: "zh-tw",
        Language.ES_ES.value: "es",
        Language.RU_KZ.value: "ru",
        Language.AR_AE.value: "ar",
        Language.FA_IR.value: "fa",
        Language.TR_TR.value: "tr",
        Language.JA_JP.value: "ja",
        Language.KO_KP.value: "ko-kr",
        Language.VI_VN.value: "vi-vn",
        Language.ID_ID.value: "id-id",
        Language.TH_TH.value: "th",
        Language.PT_PT.value: "pt",
        Language.DE_DE.value: "de",
        Language.FR_FR.value: "fr",
        Language.IT_IT.value: "it",
        Language.PL_PL.value: "pl",
    }

    LOCALE_LANG_DICT = {v: k for k, v in LANG_LOCALE_DICT.items()}

    class Status(Enum):
        DRAFT = "draft"
        VALID = "valid"
        DELETED = "deleted"

    id = db.Column(db.Integer, primary_key=True)
    article_id = db.Column(db.BigInteger, nullable=False)
    section_id = db.Column(db.BigInteger, nullable=False)
    category_id = db.Column(db.BigInteger, nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    title = db.Column(db.String(255), nullable=False)
    body_html = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False)
    html_url = db.Column(db.String(255))
    position = db.Column(db.Integer, nullable=False)
    is_top = db.Column(db.Boolean, nullable=False, default=False)
    status = db.Column(db.StringEnum(Status), nullable=False)

    __table_args__ = (
        db.UniqueConstraint('article_id', 'lang', name='article_id_lang_unique'),
        db.Index('idx_lang_section_is_top_created_at', 'lang', 'section_id', 'is_top', 'created_at'),
        db.Index('idx_lang_is_top_created_at', 'lang', 'is_top', 'created_at'),
        db.Index('idx_lang_created_at', 'lang', 'created_at'),
    )

    @classmethod
    def latest_update(cls, lang: Language, only: List[str] = None) -> Optional["HelpCenterArticleMySQL"]:
        query = cls.query.filter(cls.lang == lang).order_by(desc(cls.updated_at))
        return query.first()

    @classmethod
    def create_or_update(cls, article: dict):
        instance = cls.query.filter(
            cls.article_id == article["article_id"],
            cls.lang == article["lang"]
        ).first()
        
        if instance:
            for key, value in article.items():
                setattr(instance, key, value)
        else:
            instance = cls(**article)
            db.session.add(instance)
        
        db.session.commit()

    @classmethod
    def delete_by_article_ids(cls, article_ids: List[int]):
        cls.query.filter(
            cls.article_id.in_(article_ids)
        ).update({cls.status: cls.Status.DELETED}, synchronize_session=False)
        db.session.commit()

    @classmethod
    def pagination(
        cls,
        lang: Language,
        category_id: int = None,
        section_id: int = None,
        page: int = 1,
        limit: int = 10,
        only: List[str] = None,
        order_by: str = None,
    ) -> Tuple[int, List[Optional["HelpCenterArticleMySQL"]]]:
        query = cls.query.filter(
            cls.lang == lang,
            cls.status == cls.Status.VALID
        )
        
        if category_id:
            query = query.filter(cls.category_id == category_id)
        if section_id:
            query = query.filter(cls.section_id == section_id)
            
        if not order_by:
            query = query.order_by(desc(cls.is_top), desc(cls.created_at))
        else:
            is_desc = True if order_by.startswith('-') else False
            order_by = order_by[1:] if is_desc else order_by
            if is_desc:
                query = query.order_by(desc(getattr(cls, order_by)))
            else:
                query = query.order_by(asc(getattr(cls, order_by)))
            
        total = query.count()
        items = query.offset((page - 1) * limit).limit(limit).all()
        return total, items

    @classmethod
    def get_by_article_id(
            cls, article_id: int, lang: Language, only: List[str] = None
    ) -> Optional['HelpCenterArticleMySQL']:
        return cls.query.filter(
            cls.article_id == article_id,
            cls.lang == lang,
            cls.status == cls.Status.VALID
        ).first()

    @classmethod
    def latest_n(cls, lang: Language, num: int, only: List[str] = None) -> List['HelpCenterArticleMySQL']:
        return cls.query.filter(
            cls.lang == lang,
            cls.status == cls.Status.VALID
        ).order_by(desc(cls.created_at)).limit(num).all()