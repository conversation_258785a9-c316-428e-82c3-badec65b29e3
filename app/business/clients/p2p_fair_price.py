import json
from decimal import Decimal

import numpy as np
from flask import current_app

from app import config
from app.business import send_alert_notice
from app.business.clients.server import ServerClient
from app.caches import FiatUSDPricesCache
from app.caches.p2p import P2pFiatCache, P2pSiteFairPriceCache, P2pFiatMarketCache
from app.common import P2pBusinessType
from app.exceptions import InvalidArgument
from app.models import db, P2pFairPrice, P2pFairPriceSnapshot, P2pMerActFairPrice, P2pMerActFairPriceSnapshot
from app.models.mongo import Status
from app.models.mongo.p2p.config import P2pFiatMarket
from app.utils import RESTClient, current_timestamp, quantize_amount, now
from app.utils.parser import JsonEncoder


class BaseFairPriceCrawl:
    platform: P2pFairPrice.Platform
    url: str
    headers = {
        'accept': 'application/json',
        'content-type': 'application/json;charset=UTF-8',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) '
                      'AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
    }

    def __init__(self, fiat: str, asset: str, side: P2pBusinessType):
        self.fiat = fiat
        self.side = side
        self.asset = asset
        self.client = RESTClient("https://", headers=self.headers)

    def request_data(self, fiat, asset, side: P2pBusinessType) -> dict:
        raise NotImplementedError

    def handle_resp_data(self, data):
        raise NotImplementedError

    def extract_price_list(self, data):
        price_list = [Decimal(i['price']) for i in data]
        return price_list

    def calc_fair_price(self, price_list) -> Decimal:
        if not price_list:
            return Decimal(0)
        mean = Decimal(np.mean(price_list))
        std = Decimal(np.std(price_list))
        # 平均值±2.5倍标准差为有效区间
        offset = Decimal('2.5') * std
        valid_prices = [i for i in price_list if mean - offset <= i <= mean + offset]
        if valid_prices:
            return self.get_price_by_side(valid_prices, self.side)
        else:
            # current_app.logger.warning(f"{self.platform} valid prices is empty: {price_list}")
            return Decimal(0)

    @staticmethod
    def get_price_by_side(prices, side):
        # 买币区取最小值为该平台买一价，买币区取最大值为该平台卖一价
        if side == P2pBusinessType.BUY:
            return min(prices)
        elif side == P2pBusinessType.SELL:
            return max(prices)

    def get_fair_price(self):
        try:
            resp_data = self.request_data(self.fiat, self.asset, self.side)
            data = self.handle_resp_data(resp_data)
            price_list = self.extract_price_list(data)
        except Exception:
            # current_app.logger.warning(f"{self.platform} request p2p price error: {e}")
            return Decimal(0)
        price = self.calc_fair_price(price_list)
        return price


class BinanceFairPriceCrawl(BaseFairPriceCrawl):
    platform = P2pFairPrice.Platform.BINANCE
    url = "p2p.binance.com/bapi/c2c/v2/friendly/c2c/adv/search"

    @RESTClient.retry(3, timeout=5)
    def request_data(self, fiat, asset, side: P2pBusinessType) -> dict:
        side_map = {
            P2pBusinessType.BUY: "BUY",
            P2pBusinessType.SELL: "SELL",
        }
        params = {
            "fiat": fiat,
            "page": 1,
            "rows": 20,
            "tradeType": side_map[side],
            "asset": asset,
            "countries": [],
            "proMerchantAds": False,
            "shieldMerchantAds": False,  # 是否仅显示神盾广告方
            "filterType": "all",  # 是否限可交易广告
            "periods": [],
            "additionalKycVerifyFilter": 1,  # 是否无需认证的广告商
            "publisherType": None,
            "payTypes": [],
            "classifies": [
                "mass",
                "profession",
                "fiat_trade"
            ]
        }
        resp_data = self.client.post(self.url, json=params)
        return resp_data

    def handle_resp_data(self, data):
        if data['code'] != "000000":
            # current_app.logger.warning(f"{self} request p2p price error: {data}")
            return []
        return data["data"]

    def extract_price_list(self, data):
        price_list = [Decimal(i['adv']['price']) for i in data]
        return price_list


class BybitFairPriceCrawl(BaseFairPriceCrawl):
    platform = P2pFairPrice.Platform.BYBIT
    url = "api2.bybit.com/fiat/otc/item/online"

    @RESTClient.retry(3, timeout=10)
    def request_data(self, fiat, asset, side: P2pBusinessType) -> dict:
        side_map = {
            P2pBusinessType.BUY: "1",
            P2pBusinessType.SELL: "0",
        }
        params = {
            "userId": "",
            "tokenId": asset,
            "currencyId": fiat,
            "payment": [],
            "paymentPeriod": [],
            "side": side_map[side],
            "size": "20",
            "page": "1",
            "amount": "",
            "authMaker": False,
            "canTrade": False,
            "itemRegion": 2,
            "sortType": "TRADE_PRICE",
        }
        resp_data = self.client.post(self.url, json=params)
        return resp_data

    def handle_resp_data(self, data):
        if data['ret_code'] != 0:
            # current_app.logger.warning(f"{self.platform} request p2p price error: {data}")
            return []
        return data["result"]["items"]


class OKXFairPriceCrawl(BaseFairPriceCrawl):
    platform = P2pFairPrice.Platform.OKX
    url = "www.okx.com/v3/c2c/tradingOrders/getMarketplaceAdsPrelogin"
    side_map = {
        P2pBusinessType.BUY: "buy",
        P2pBusinessType.SELL: "sell",
    }

    @RESTClient.retry(3, timeout=5)
    def request_data(self, fiat, asset, side: P2pBusinessType) -> dict:
        sort_map = {
            P2pBusinessType.BUY: "price_desc",
            P2pBusinessType.SELL: "price_asc",
        }
        params = {
            'side': self.side_map[side],
            'paymentMethod': 'all',
            'userType': 'all',
            'hideOverseasVerificationAds': 'false',
            'sortType': sort_map[side],
            'limit': '100',
            'cryptoCurrency': asset,
            'fiatCurrency': fiat,
            'currentPage': '1',
            'numberPerPage': '20',
            't': int(current_timestamp() * 1000),
        }
        resp_data = self.client.get(self.url, **params)
        return resp_data

    def handle_resp_data(self, data):
        if data['code'] != 0:
            current_app.logger.warning(f"{self.platform} request p2p price error: {data}")
            return []
        return data["data"][self.side_map[self.side]]


class BitgetFairPriceCrawl(BaseFairPriceCrawl):
    # 产品要求暂时不使用 Bitget 价格
    # platform = P2pFairPrice.Platform.BITGET
    url = "www.bitget.com/v1/p2p/pub/adv/queryAdvList"

    @RESTClient.retry(3, timeout=5)
    def request_data(self, fiat, asset, side: P2pBusinessType) -> dict:
        side_map = {
            P2pBusinessType.BUY: "1",
            P2pBusinessType.SELL: "2",
        }
        params = {
            "side": side_map[side],
            "pageNo": 1,
            "pageSize": 20,
            "coinCode": asset,
            "fiatCode": fiat,
            "languageType": 5
        }
        resp_data = self.client.post(self.url, json=params)
        return resp_data

    def handle_resp_data(self, data):
        if data['code'] != "00000":
            # current_app.logger.warning(f"{self.platform} request p2p price error: {data}")
            return []
        return data["data"]["dataList"]

    def extract_price_list(self, data):
        price_list = [Decimal(i['priceValue']) for i in data]
        return price_list


class CoinexFairPriceCrawl(BaseFairPriceCrawl):
    platform = P2pFairPrice.Platform.COINEX

    def get_fair_price(self):
        price = P2pSiteFairPriceCache().read_fiat_price(self.fiat)
        return price


class ThirdPartyFairPriceCrawl(BaseFairPriceCrawl):
    platform = P2pFairPrice.Platform.THIRD_PARTY

    def get_fair_price(self):
        price = FiatUSDPricesCache().get_price(self.fiat)
        f_model = P2pFiatMarket
        fair_config = f_model.query.filter(
            f_model.fiat == self.fiat,
            f_model.asset == self.asset
        ).first()

        # p2p 场景下使用 u 本位价格
        usdt_price = 1
        new_price = quantize_amount(usdt_price / price, fair_config.precision) if price else Decimal()
        return new_price


class FairPriceBiz:

    def __init__(self, fiat, asset="USDT"):
        self.fiat = fiat
        self.asset = asset

    def get_fair_price_map(self):
        fair_price_map = {}
        platform_name = []
        for crawl in BaseFairPriceCrawl.__subclasses__():
            # 跳过关闭的渠道
            if not getattr(crawl, "platform", None):
                continue
            platform_name.append(crawl.platform.name)
            buy_price = crawl(self.fiat, self.asset, P2pBusinessType.BUY).get_fair_price()
            sell_price = crawl(self.fiat, self.asset, P2pBusinessType.SELL).get_fair_price()

            if buy_price and sell_price:
                fair_price = (buy_price + sell_price) / 2
            elif buy_price:
                fair_price = buy_price
            else:
                fair_price = sell_price

            if fair_price:
                fair_price_map[crawl.platform] = fair_price

        if not fair_price_map:
            # 如果没有获取到任何价格，发送告警
            self.alter_zero_price(platform_name, self.fiat)
        return fair_price_map

    @staticmethod
    def calc_fair_price(price_map):
        # 优先级  coinex > 其他交易平台 > 第三方价格
        coinex_price = price_map.get(P2pFairPrice.Platform.COINEX)
        third_price = price_map.get(P2pFairPrice.Platform.THIRD_PARTY)
        has_other = [i for i in price_map if i in P2pFairPrice.cex_platforms()]
        if coinex_price:
            price = coinex_price
        elif has_other:
            sort_prices = sorted(price_map.values())
            # 取中位数
            price = np.median(sort_prices)
        else:
            price = third_price
        return price

    @classmethod
    def alter_zero_price(cls, platforms, fiat):
        if not platforms:
            return
        plat_str = '，'.join(platforms)
        err_msg = f"P2P公允价格爬虫无法获取 {plat_str} 的 {fiat} 价格数据"
        send_alert_notice(
            err_msg,
            config["ADMIN_CONTACTS"].get("p2p_mer_act"),
            expired_seconds=86400,
            at=config["ADMIN_CONTACTS"]['slack_at'].get("p2p_mer_act")
        )
        current_app.logger.warning(f"{plat_str} get {fiat} price failed")

    def handle_fair_price(self):
        price_map = self.get_fair_price_map()
        if price_map:
            price = self.calc_fair_price(price_map)
        else:
            price = Decimal()
        model = P2pFairPrice
        obj = model.query.filter(model.fiat == self.fiat).first()
        source_data = {k.name: str(v) for k, v in price_map.items()}
        if obj:
            if obj.price == price:
                return
            else:
                obj.price = price
                obj.source_data = source_data
        else:
            obj = P2pFairPrice(
                fiat=self.fiat,
                price=price,
                source_data=source_data,
            )
            db.session_add_and_flush(obj)
        self.save_fair_snapshot(obj)
        db.session.commit()
        return obj

    @staticmethod
    def save_fair_snapshot(obj):
        snap = json.loads(json.dumps(obj.to_dict(), cls=JsonEncoder))
        row = P2pFairPriceSnapshot(
            snap_at=now(),
            snap=snap,
            source_id=obj.id,
            fiat=obj.fiat
        )
        db.session.add(row)

    @classmethod
    def refresh_all_fair_price(cls):
        fiats = P2pFiatCache().get_all_valid_fiats()
        for fiat in fiats:
            try:
                FairPriceBiz(fiat).handle_fair_price()
            except Exception as e:
                current_app.logger.error(f"Update fiat fair price failed: {fiat}")
                current_app.logger.exception(e)


class MerActFairPriceBiz:
    model = P2pMerActFairPrice
    market_model = P2pFiatMarket
    
    @classmethod
    def refresh_one_fiat_price(cls, fiat, asset):
        market_row = cls.query_one_fiat_asset_price(fiat, asset)
        if not market_row:
            raise ValueError(f"Fiat config not found: {fiat} {asset}")
        
        row = cls.model.get_or_create(fiat=fiat, asset=asset)
        if row.price != market_row.auto_price:
            row.price = market_row.auto_price
            db.session_add_and_commit(row)
            cls.save_fair_snapshot(row)
        return row

    @classmethod
    def refresh_all_config_price(cls):
        # 查询法币配置的价格，并同步到 商家活动法币价格
        old_rows = cls.model.query.all()
        cfg_map = cls.query_all_fair_price_map()
        updated_rows = []
        for row in old_rows:
            _key = (row.fiat, row.asset)
            if _key not in cfg_map:
                continue
            cfg_row = cfg_map[_key]
            price = quantize_amount(cfg_row.auto_price, cfg_row.precision)
            if price and row.price != price:
                row.price = price
                updated_rows.append(row)
        db.session.commit()
        for row in updated_rows:
            cls.save_fair_snapshot(row)
        return updated_rows

    @classmethod
    def query_one_fiat_asset_price(cls, fiat, asset):
        row = cls.market_model.query.filter(
            cls.market_model.fiat == fiat,
            cls.market_model.asset == asset,
            cls.market_model.status == Status.VALID
        ).first()
        if not row:
            raise InvalidArgument(message=f"该p2p市场配置 {fiat}/{asset} 未上架")
        return row

    @classmethod
    def query_all_fair_price_map(cls):
        rows = cls.market_model.query.all()
        return {(i.fiat, i.asset): i for i in rows}

    @staticmethod
    def save_fair_snapshot(obj):
        snap = json.loads(json.dumps(obj.to_dict(), cls=JsonEncoder))
        row = P2pMerActFairPriceSnapshot(
            snap_at=now(),
            snap=snap,
            source_id=obj.id,
            fiat=obj.fiat
        )
        db.session.add(row)


class P2pFiatConfigPriceBiz:

    @classmethod
    def refresh_one_fiat_price(cls, fiat: str, asset: str):
        f_model = P2pFiatMarket
        
        row = f_model.query.filter(
            f_model.fiat == fiat,
            f_model.asset == asset
        ).first()
        usdt_fair_price = P2pFairPrice.query.filter(P2pFairPrice.fiat == fiat).first().price
        
        market_price = ServerClient().market_last(f"{asset}USDT")
        new_price = quantize_amount(usdt_fair_price * market_price, row.precision)
        if new_price and row.auto_price != new_price:
            row.auto_price = new_price
            row.source = f_model.Sources.FAIR_PRICE
        db.session.commit()
        P2pFiatMarketCache(asset).refresh_one_fiat(fiat)
        return row

    @classmethod
    def refresh_all_config_price(cls):
        f_model = P2pFiatMarket
        market_rows = f_model.get_all_valid_market_data()
        assets = list(set([i.asset for i in market_rows]))
        
        # 所有 usdt 的公允价格
        usdt_price_map = {i.fiat: i.price for i in P2pFairPrice.query.all()}
        client = ServerClient()
        
        # 市场指数价格映射
        market_price_map = {}
        for asset in assets:
            market = f"{asset}USDT"
            market_price_map[asset] = client.market_last(market)
                
        usdt_rows = [i for i in market_rows if i.fiat == f_model.DEFAULT_FIAT]
        other_rows = market_rows - usdt_rows

        # 先更新 usdt 的配置价格
        for row in usdt_rows:
            fiat_price = quantize_amount(usdt_price_map.get(row.fiat), row.precision)
            if fiat_price and row.auto_price != fiat_price:
                row.auto_price = fiat_price
                row.source = f_model.Sources.FAIR_PRICE
        db.session.commit()

        # 其他币种基于 usdt 价格计算
        for row in other_rows:
            asset_price = market_price_map.get(row.asset)
            if not asset_price:
                current_app.logger.warning(f"{row.asset}/USDT market index price not found")
                continue
            
            fiat_price = quantize_amount(usdt_price_map.get(row.fiat) * asset_price, row.precision)
            if fiat_price and row.auto_price != fiat_price:
                row.auto_price = fiat_price
        db.session.commit()

        P2pFiatMarketCache.refresh_all()