# -*- coding: utf-8 -*-

import json
import math
from collections import defaultdict
from decimal import Decimal
from enum import Enum
from typing import Dict, Tu<PERSON>, Union, List

from flask import current_app
from werkzeug.utils import cached_property

from ..business import CacheLock, LockKeys, SiteSettings, lock_call
from ..business.alert import send_alert_notice
from ..caches import (
    LiquidityPoolAmountCache, LiquidityPoolCache,
    LiquidityRankCache, MarketCache, AmmMarketCache, LiquidityFrequencyCache,
)
from ..common import BalanceBusiness, CeleryQueues, PrecisionEnum, MarketStatusType
from ..config import config
from ..exceptions import (
    InsufficientBalance, LessThanMinLiquidity,
    LiquidityFluctuate, LiquidityDisabled, FrequencyExceeded, AMMWithdrawalForbidden,
)
from ..models import (
    AmmMarket, DailyAmmMarketReport, LiquidityHistory, LiquidityPool,
    UserLiquidity, SubAccount, LiquiditySlice,
    UserLiquiditySlice, db, Market,
)
from ..utils import (
    RESTClient, amount_to_str, celery_task, quantize_amount, route_module_to_celery_queue, batch_iter, group_by,
    now,
)
from ..utils.parser import JsonEncoder
from .clients import AmmClient, ServerClient
from .prices import PriceManager

route_module_to_celery_queue(__name__, CeleryQueues.REAL_TIME)


class BiddingStage(Enum):
    FIRST = 'first'
    SECOND = 'second'


class LiquidityService:
    """
    see https://github.com/oneswap/oneswap_contract_ethereum/blob/master/contracts/OneSwapRouter.sol
    let s: stock amount, m: money amount, l: liquidity, d: delta.
    if l == 0:
        dl = sqrt(ds * dm)
    else:
        ds / dm = s / m
        dl = ds / s * l
    stable amm see https://viabtc.yuque.com/r.d/akdy2o/vc0a9y
    """

    # 最低流动性 100USD, 双边200
    MIN_LIQUIDITY_USD = Decimal('100')

    def __init__(self, market: str) -> None:
        self.market_name = market
    
    @cached_property
    def pool(self) -> LiquidityPool:
        return LiquidityPool.query.filter(LiquidityPool.market == self.market_name).first()
    
    @cached_property
    def market(self) -> Dict:
        return MarketCache(self.market_name).dict
    
    @cached_property
    def amm_market(self) -> AmmMarket:
        return AmmMarket.query.filter(AmmMarket.name == self.market_name).first()

    @classmethod
    def list_amm_system_user_ids(cls) -> List[int]:
        main_user_id = config['AMM_DERIVE_USER_ID']
        rows = SubAccount.query.filter(SubAccount.main_user_id == main_user_id).all()
        users = [main_user_id]
        users.extend([x.user_id for x in rows])
        return users

    def validate_market(self) -> bool:
        if self.market['name'] in SiteSettings.forbidden_amm_markets:
            raise LiquidityDisabled
        match self.market['status']:
            case Market.Status.ONLINE:
                return True
            case Market.Status.BIDDING:
                if not self.is_allow_in_bidding():
                    raise LiquidityDisabled
                if not self.in_bidding():
                    raise LiquidityDisabled
            case _:
                raise LiquidityDisabled
        return True

    def get_bidding_market_stage(self) -> BiddingStage | None:
        # 此函数需要放在validate_market之后
        if self.market['status'] != Market.Status.BIDDING:
            return
        if now() < self.market['bidding_matching_started_at']:
            return BiddingStage.FIRST
        else:
            return BiddingStage.SECOND

    def add_limit_cache(self, user_id: int):
        stage = self.get_bidding_market_stage()
        match stage:
            case None:
                LiquidityFrequencyCache(user_id, self.market_name).add()
            case BiddingStage.FIRST | BiddingStage.SECOND:
                LiquidityFrequencyCache(user_id, self.market_name, True).add()
                delta = int((self.market['bidding_ended_at'] - now()).total_seconds())
                ttl = LiquidityFrequencyCache.ttl + delta
                LiquidityFrequencyCache(user_id, self.market_name).add_with_ttl(ttl)

    def check_for_remove_liquidity(self, user_id: int):
        stage = self.get_bidding_market_stage()
        match stage:
            case None:
                if (ttl := LiquidityFrequencyCache(user_id, self.market_name).test()) > 0:
                    raise FrequencyExceeded(data={"ttl": ttl})
            case BiddingStage.FIRST:
                if (ttl := LiquidityFrequencyCache(user_id, self.market_name, True).test()) > 0:
                    raise FrequencyExceeded(data={"ttl": ttl})
            case BiddingStage.SECOND:
                if (ttl := LiquidityFrequencyCache(user_id, self.market_name).test()) > 0:
                    raise FrequencyExceeded(data={"ttl": ttl})
                if (ttl := LiquidityFrequencyCache(user_id, self.market_name, True).test()) > 0:
                    raise FrequencyExceeded(data={"ttl": ttl})
                raise AMMWithdrawalForbidden

    def in_bidding(self) -> bool:
        if self.market['status'] != Market.Status.BIDDING:
            return False
        if now() >= self.market['bidding_ended_at']:
            return False
        return True

    def in_bidding_not_allow_cancel(self) -> bool:
        if self.market['status'] != Market.Status.BIDDING:
            return False
        if now() < self.market['bidding_matching_started_at']:
            return False
        return True

    def is_allow_in_bidding(self) -> bool:
        return AmmMarketCache.allow_bidding_markets_has(self.market_name)

    @classmethod
    def replace_orders(cls, market: str):
        client = AmmClient()
        with CacheLock(LockKeys.amm_liquidity(market)):
            client.cancel_orders(market)
            client.place_orders(market)

    @property
    def fee_refund_rate(self):
        """手续费注入资金池的比例，如果资金池没有流动性，实际注入时比例应为0"""
        return self.amm_market.fee_refund_rate


    @classmethod
    def _finit_liq_2_virtual_liq(cls, x, y, pa, pb):
        """有限区间流动性转虚拟流动性"""
        sqrt = lambda x: Decimal(math.sqrt(x))

        def slove(a, b, c):
            """一元二次方程求根公式"""
            d = b**2 - 4 * a * c
            assert d >= 0
            x1 = (-b + sqrt(d)) / (2 * a)
            return x1

        sp = sqrt(pa * pb)
        a = pb - sp
        b = -(sp * x + y)
        c = -(x * y)
        xb = slove(a, b, c)
        ya = xb * sp
        return xb + x, ya + y

    @classmethod
    def _virtual_price_2_finit_price(cls, p, pa, pb):
        """无限区间价格转有限区间价格"""
        sqrt = lambda x: Decimal(math.sqrt(x))
        return (sqrt(pa * p) -  pa) / (sqrt(pa / p) - sqrt(pa / pb))

    def get_pool_price(self, base_amout: Decimal, quote_amount: Decimal) -> Decimal:
        """获取资金池价格"""
        if self.pool.liquidity == 0:
            return self.get_pool_initial_price()
        if self.amm_market.amm_type == AmmMarket.AmmType.FINITE:
            base_amout, quote_amount = self._finit_liq_2_virtual_liq(base_amout, quote_amount, 
                                       self.amm_market.min_price, self.amm_market.max_price)
        return quantize_amount(quote_amount / base_amout, self.market['quote_asset_precision'])

    def is_allow_and_in_bidding(self):
        if self.in_bidding() and self.is_allow_in_bidding():
            return True
        return False

    def get_pool_initial_price(self) -> Decimal:
        """获取资金池初始价格"""
        if self.amm_market.amm_type == AmmMarket.AmmType.FINITE:
            return self._virtual_price_2_finit_price(self.amm_market.initial_price,
                                                     self.amm_market.min_price,
                                                     self.amm_market.max_price)
        if self.is_allow_and_in_bidding():
            # 竞价模式资金池价格为设置的开盘价
            return PriceManager.convert_price(
                self.market['opening_price_asset'],
                self.market['opening_price'],
                self.market['quote_asset'])
        else:
            return ServerClient().market_last(self.market_name)

    def get_assets_amount(self, use_cache=True, require_no_frozen=False) -> Tuple[Decimal, Decimal]:
        """
        get base and quote amounts in pool.
        use_cache: use cached balance amounts.
        require_no_frozen: used when not use_cahce. raise error if there's any frozen balance.
        """
        cache = LiquidityPoolAmountCache(self.market_name)
        if use_cache:
            if r := cache.read():
                return Decimal(r[self.market['base_asset']]), Decimal(r[self.market['quote_asset']])

        if not use_cache:
            base, quote = self._get_assets_amount(self.pool.system_user_id, require_no_frozen=require_no_frozen)
        else:
            # 以下逻辑使用自旋锁保证全局只有一个协程可以去请求server，从而将并发请求转化为串行请求，有以下两种情况:
            # 1. 当无缓存的时候拿到了锁，则直接去查询server,并返回。
            # 2. 当在1情况中返回了 cache，说明之前有协程已经执行了查询server，返回cache中的数据。
            with CacheLock(LockKeys.amm_system_user_balance(self.pool.system_user_id), wait=True, ttl=5):
                if r := cache.read():
                    base, quote = Decimal(r[self.market['base_asset']]), Decimal(r[self.market['quote_asset']])
                else:
                    base, quote = self._get_assets_amount(self.pool.system_user_id, require_no_frozen=require_no_frozen)

        cache.hmset({
            self.market['base_asset']: amount_to_str(base),
            self.market['quote_asset']: amount_to_str(quote)
        })
        cache.expire(cache.ttl)
        return base, quote

    def _get_assets_amount(self, user_id, *, require_no_frozen: bool):
        r = ServerClient().get_user_balances(user_id)
        if require_no_frozen:
            if (balance := r.get(self.market['base_asset'])) and balance.get('frozen'):
                raise RuntimeError("there's frozen balance in amm pool")
            if (balance := r.get(self.market['quote_asset'])) and balance.get('frozen'):
                raise RuntimeError("there's frozen balance in amm pool")

        base = quote = Decimal()
        if balance := r.get(self.market['base_asset']):
            base = sum(balance.values())
        if balance := r.get(self.market['quote_asset']):
            quote = sum(balance.values())
        return base, quote


    def asset_amounts_to_usd(self, base_amount: Decimal, quote_amount: Decimal) -> Decimal:
        prices = PriceManager.assets_to_usd((self.market['base_asset'], self.market['quote_asset']))
        usd = base_amount * prices.get(self.market['base_asset'], 0) \
            + quote_amount * prices.get(self.market['quote_asset'], 0)
        return quantize_amount(usd, 8)

    def liquidity_to_asset_amounts(self, liquidity: Decimal,
                                   pool_base_amount: Decimal = None,
                                   pool_quote_amount: Decimal = None) -> Tuple[Decimal, Decimal]:
        """get base and quote amounts of liquidity. if pool_*_amount is None, use latest."""
        if liquidity == 0 and self.pool.liquidity == 0:
            return Decimal(), Decimal()
        if pool_base_amount is None or pool_quote_amount is None:
            pool_base_amount, pool_quote_amount = self.get_assets_amount()
        rate = liquidity / self.pool.liquidity
        return quantize_amount(pool_base_amount * rate, 8), \
               quantize_amount(pool_quote_amount * rate, 8)

    def liquidity_to_asset_amounts_and_rate(self, liquidity: Decimal) -> (
            Tuple)[Decimal, Decimal, Decimal]:
        """get base and quote amounts of liquidity and liquidity rate"""
        if liquidity == 0 and self.pool.liquidity == 0:
            return Decimal(), Decimal(), Decimal()
        pool_base_amount, pool_quote_amount = self.get_assets_amount()
        rate = liquidity / self.pool.liquidity
        return quantize_amount(pool_base_amount * rate, PrecisionEnum.COIN_PLACES), \
               quantize_amount(pool_quote_amount * rate, PrecisionEnum.COIN_PLACES), \
               quantize_amount(rate, PrecisionEnum.RATE_PLACES)


    def get_recent_deal_sum(self, days) -> Dict[str, Decimal]:
        rows = DailyAmmMarketReport.query.filter(
            DailyAmmMarketReport.market == self.market_name,
            DailyAmmMarketReport.fee_refund_rate.isnot(None),
        ).order_by(
            DailyAmmMarketReport.report_date.desc()
        ).limit(days).all()
        zero = Decimal()
        return dict(
            days=len(rows),
            deal_usd=sum((x.deal_usd for x in rows), zero),
            fee_usd=sum((x.fee_usd for x in rows), zero),
            fee_base_amount=sum((x.fee_base_amount for x in rows), zero),
            fee_quote_amount=sum((x.fee_quote_amount for x in rows), zero),
            refund_fee_usd=sum((x.refund_fee_usd for x in rows), zero),
            refund_fee_base_amount=sum((x.refund_fee_base_amount for x in rows), zero),
            refund_fee_quote_amount=sum((x.refund_fee_quote_amount for x in rows), zero),
        )

    def calc_profit_rate(self,
                         fee_base_amount: Decimal,
                         fee_quote_amount: Decimal,
                         pool_base_amount: Decimal,
                         pool_quote_amount: Decimal):
        """calc avg profit rate by daily fee"""
        fee_usd = self.asset_amounts_to_usd(fee_base_amount, fee_quote_amount)
        liq_usd = self.asset_amounts_to_usd(pool_base_amount, pool_quote_amount)
        if liq_usd == 0:
            return Decimal()
        # 计算年化收益率无需使用实际注入比例
        return quantize_amount(fee_usd / liq_usd * self.fee_refund_rate * 365, 4)

    def calc_real_liquidity_amounts(self,
                                   base_amount: Decimal, 
                                   quote_amount: Decimal,
                                   pool_base_amount: Decimal,
                                   pool_quote_amount: Decimal,
                                   balance_base_amount: Decimal,
                                   balance_quote_amount: Decimal):
        """calc needed amounts of base and quote asset when adding liq"""
        if self.pool.liquidity == 0:
            price = self.get_pool_initial_price()
        elif pool_base_amount == 0:
            return Decimal(), quote_amount
        elif pool_quote_amount == 0:
            return base_amount, Decimal()
        else:   # 有限区间的恒定乘积做市也是按y/x比例添加/提取资产
            assert pool_base_amount > 0 and pool_quote_amount > 0
            price = pool_quote_amount / pool_base_amount
        # pool price
        price = quantize_amount(price, self.market['quote_asset_precision'])
        if price == 0:  # 因误差问题，可能为0，说明quote_asset为0
            return base_amount, Decimal()
        # 如果用户意图是耗尽一种资产，允许另一种资产大于实际提交上来的数量
        if base_amount == balance_base_amount:
            want_quote_amount = quantize_amount(base_amount * price, 8)
            if want_quote_amount <= balance_quote_amount:
                return base_amount, want_quote_amount

        if quote_amount == balance_quote_amount:
            want_base_amount = quantize_amount(quote_amount / price, 8)
            if want_base_amount <= balance_base_amount:
                return want_base_amount, quote_amount

        want_base_amount = quantize_amount(quote_amount / price, 8)
        if want_base_amount <= base_amount:
            return want_base_amount, quote_amount

        want_quote_amount = quantize_amount(base_amount * price, 8)
        assert want_quote_amount <= quote_amount

        return base_amount, want_quote_amount

    def test_min_liquidity(self, base_amount: Decimal, quote_amount: Decimal) -> bool:
        """test if assets meet min liq reuirement."""
        liq_usd = self.asset_amounts_to_usd(base_amount, quote_amount)
        # ignore price fluctuation
        return liq_usd / 2 >= self.MIN_LIQUIDITY_USD * Decimal('0.95')


    def test_price_fluctuation(self, src_base_amount: Decimal, src_quote_amount: Decimal,
                               real_base_amount: Decimal, real_quote_amount: Decimal) -> bool:
        """test price fluctuation when adding liq"""
        if src_base_amount == 0 or src_quote_amount == 0:
            return True
        src_price = src_quote_amount / src_base_amount
        delta_price = abs(src_price - real_quote_amount / real_base_amount)
        return delta_price / src_price < Decimal('0.05')


    def _add_liquidity(self, user_id: int, base_amount: Decimal, quote_amount: Decimal, liq: Decimal):
        history = LiquidityHistory(
            market=self.market_name,
            user_id=user_id,
            business=LiquidityHistory.Business.ADD,
            base_amount=base_amount,
            quote_amount=quote_amount,
            liquidity=liq,
            liquidity_usd=self.asset_amounts_to_usd(base_amount, quote_amount)
        )
        db.session.add(history)
        db.session.commit()

        client = ServerClient()
        try:
            client.add_user_balance(
                user_id=user_id,
                asset=self.market['base_asset'],
                amount=-base_amount,
                business=BalanceBusiness.ADD_LIQUIDITY,
                business_id=history.id
            )
            client.add_user_balance(
                user_id=user_id,
                asset=self.market['quote_asset'],
                amount=-quote_amount,
                business=BalanceBusiness.ADD_LIQUIDITY,
                business_id=history.id
            )

            client.batch_add_user_balance([
                dict(
                    user_id=self.pool.system_user_id,
                    asset=self.market['base_asset'],
                    amount=base_amount,
                    business=BalanceBusiness.ADD_LIQUIDITY,
                    business_id=history.id
                ),
                dict(
                    user_id=self.pool.system_user_id,
                    asset=self.market['quote_asset'],
                    amount=quote_amount,
                    business=BalanceBusiness.ADD_LIQUIDITY,
                    business_id=history.id
                )
            ])
        except Exception as e:
            current_app.logger.error("add liquidity failed, market %s, user %s: %s", 
                                     self.market_name, user_id, e)
            history.status = LiquidityHistory.Status.FAILED
            db.session.commit()
            raise

        history.status = LiquidityHistory.Status.FINISHED
        self.pool.liquidity += liq

        record = UserLiquidity.get_or_create(market=self.market_name, user_id=user_id)
        if record.liquidity is None:
            record.liquidity = liq
        else:
            record.liquidity += liq
        db.session.add(record)

        db.session.commit()
        return history

    def _remove_liquidity(self, user_id: int, base_amount: Decimal, quote_amount: Decimal, liq: Decimal):
        history = LiquidityHistory(
            market=self.market_name,
            user_id=user_id,
            business=LiquidityHistory.Business.REMOVE,
            base_amount=base_amount,
            quote_amount=quote_amount,
            liquidity=liq,
            liquidity_usd=self.asset_amounts_to_usd(base_amount, quote_amount)
        )
        db.session.add(history)
        db.session.commit()

        client = ServerClient()
        pool_balance_changed = False
        try:
            client.add_user_balance(
                user_id=self.pool.system_user_id,
                asset=self.market['base_asset'],
                amount=-base_amount,
                business=BalanceBusiness.REMOVE_LIQUIDITY,
                business_id=history.id
            )
            pool_balance_changed = True
            client.add_user_balance(
                user_id=self.pool.system_user_id,
                asset=self.market['quote_asset'],
                amount=-quote_amount,
                business=BalanceBusiness.REMOVE_LIQUIDITY,
                business_id=history.id
            )

            client.batch_add_user_balance([
                dict(
                    user_id=user_id,
                    asset=self.market['base_asset'],
                    amount=base_amount,
                    business=BalanceBusiness.REMOVE_LIQUIDITY,
                    business_id=history.id
                ),
                dict(
                    user_id=user_id,
                    asset=self.market['quote_asset'],
                    amount=quote_amount,
                    business=BalanceBusiness.REMOVE_LIQUIDITY,
                    business_id=history.id
                )
            ])
        except Exception as e:
            current_app.logger.error("remove liquidity failed, market %s, user %s: %s", 
                                     self.market_name, user_id, e)
            history.status = LiquidityHistory.Status.FAILED
            db.session.commit()
            # pool balance changed, but pool liquidity not changed. must forbid adding or removing liquidity.
            if pool_balance_changed:
                SiteSettings.forbidden_amm_markets.add(self.market_name)
                send_alert_notice(
                    f"AMM市场{self.market_name}异常，禁止增加/提取流动性",
                    config["ADMIN_CONTACTS"]["web_notice"],
                )
            raise

        history.status = LiquidityHistory.Status.FINISHED
        self.pool.liquidity -= liq

        record = UserLiquidity.query.filter(
            UserLiquidity.market == self.market_name,
            UserLiquidity.user_id == user_id
        ).first()
        record.liquidity -= liq

        db.session.commit()
        return history


def add_liquidity(market: str, user_id: int,
                  src_base_amount: Decimal, src_quote_amount: Decimal) -> LiquidityHistory:
    with CacheLock(LockKeys.amm_liquidity(market)):
        # important: start new tx
        db.session.rollback()

        amm_client = AmmClient()
        amm_client.cancel_orders(market)
        srv = LiquidityService(market)
        try:
            pool_base, pool_quote = srv.get_assets_amount(use_cache=False, require_no_frozen=True)

            result = ServerClient().get_user_balances(user_id)
            balance_base = balance_quote = Decimal()
            if (balance := result.get(srv.market['base_asset'])):
                balance_base = balance.get('available', Decimal())
            if (balance := result.get(srv.market['quote_asset'])):
                balance_quote = balance.get('available', Decimal())

            pool_base, pool_quote = srv.get_assets_amount(use_cache=False, require_no_frozen=True)
            base_amount, quote_amount = srv.calc_real_liquidity_amounts(
                                        src_base_amount, src_quote_amount,
                                        pool_base, pool_quote,
                                        balance_base, balance_quote)

            if base_amount > balance_base or quote_amount > balance_quote:
                raise InsufficientBalance
            if not srv.test_min_liquidity(base_amount, quote_amount):
                # 这里提示的是双边市值
                raise LessThanMinLiquidity(amount=srv.MIN_LIQUIDITY_USD*2)
            if not srv.test_price_fluctuation(src_base_amount, src_quote_amount,
                                              base_amount, quote_amount):
                raise LiquidityFluctuate

            if srv.pool.liquidity == 0:
                liq = math.sqrt(base_amount * quote_amount)
            else:
                if pool_base > 0:
                    rate = base_amount / pool_base
                else:
                    rate = quote_amount / pool_quote
                liq = rate * srv.pool.liquidity
            liq = quantize_amount(liq, 8)
            assert liq > 0  # ensure not too small
            return srv._add_liquidity(user_id, base_amount, quote_amount, liq)
        finally:
            amm_client.place_orders(market)


def remove_liquidity(market: str, user_id: int) -> LiquidityHistory:
    with CacheLock(LockKeys.amm_liquidity(market)):
        # important: start new tx
        db.session.rollback()

        row = UserLiquidity.query.filter(
            UserLiquidity.market == market,
            UserLiquidity.user_id == user_id
        ).first()
        if not row or row.liquidity <= 0:
            raise RuntimeError(f"user {user_id} has no liquidity at {market}")
        market_status = ServerClient().get_market_maintain_status(market)['status']
        amm_client = AmmClient()
        amm_client.cancel_orders(market)

        srv = LiquidityService(market)
        try:
            liq = row.liquidity
            pool_base, pool_quote = srv.get_assets_amount(use_cache=False, require_no_frozen=True)
            share = liq / srv.pool.liquidity
            assert share <= 1

            base_amount = quantize_amount(pool_base * share, 8)
            quote_amount = quantize_amount(pool_quote * share, 8)
            return srv._remove_liquidity(user_id, base_amount, quote_amount, liq)
        finally:
            if market_status == MarketStatusType.MARKET_STATUS_START:
                amm_client.place_orders(market)


def close_liqudity_pool(market: str, force=False):
    with CacheLock(LockKeys.amm_liquidity(market), wait=True):
        # important: start new tx
        db.session.rollback()

        amm_client = AmmClient()
        try:
            amm_client.cancel_orders(market)
        except RESTClient.BadResponse:
            if not force:
                raise
            current_app.logger.warning('cancel orders failed, force close liquidity pool %s', market)

        rows = UserLiquidity.query.filter(UserLiquidity.market == market).all()
        for row in rows:
            liq = row.liquidity
            if liq <= 0:
                continue
            srv = LiquidityService(market)
            pool_base, pool_quote = srv.get_assets_amount(use_cache=False, require_no_frozen=True)
            share = liq / srv.pool.liquidity
            assert share <= 1

            base_amount = quantize_amount(pool_base * share, 8)
            quote_amount = quantize_amount(pool_quote * share, 8)
            srv._remove_liquidity(row.user_id, base_amount, quote_amount, liq)
    update_liquidity_rank_task.delay(market)


@celery_task
def update_liquidity_rank_task(market: str):
    rows = UserLiquidity.query.filter(
        UserLiquidity.market == market
    ).order_by(
        UserLiquidity.liquidity.desc()
    ).limit(10).all()
    srv = LiquidityService(market)
    total_liq = srv.pool.liquidity
    if total_liq <= 0:
        LiquidityRankCache().hdel(market)
        return
    data = [dict(
        rank=i,
        liquidity=amount_to_str(x.liquidity),
        share=amount_to_str(x.liquidity / total_liq, PrecisionEnum.RATE_PLACES)
    ) for i, x in enumerate(rows, 1) if x.liquidity]
    LiquidityRankCache().hset(market, json.dumps(data))


@celery_task
@lock_call(wait=True)
def update_liquidity_pool_cache_task(market: str = None):

    def update_one(market_name):
        srv = LiquidityService(market_name)
        pool_base, pool_quote = srv.get_assets_amount()
        deal_info = srv.get_recent_deal_sum(days=7)
        if (days := deal_info['days']) == 0:
            profit_rate = Decimal()
        else:
            profit_rate = srv.calc_profit_rate(deal_info['fee_base_amount'] / days,
                                               deal_info['fee_quote_amount'] / days,
                                               pool_base, pool_quote)

        deal_info_last = srv.get_recent_deal_sum(days=1)
        if (deal_info_last['days']) == 0:
            profit_rate_last = Decimal()
        else:
            profit_rate_last = srv.calc_profit_rate(deal_info_last['fee_base_amount'],
                                                    deal_info_last['fee_quote_amount'],
                                                    pool_base, pool_quote)
        return dict(
            market=market_name,
            amm_type=srv.amm_market.amm_type,
            base_asset=srv.market['base_asset'],
            quote_asset=srv.market['quote_asset'],
            base_amount=pool_base,
            quote_amount=pool_quote,
            profit_rate=profit_rate,
            profit_rate_last=profit_rate_last,
            liquidity=srv.pool.liquidity,
            liquidity_usd=srv.asset_amounts_to_usd(pool_base, pool_quote),
            deal_usd=deal_info['deal_usd'],
            fee_usd=deal_info['fee_usd'],
            refund_fee_usd=deal_info['refund_fee_usd'],
            refund_fee_base_amount=deal_info['refund_fee_base_amount'],
            refund_fee_quote_amount=deal_info['refund_fee_quote_amount'],
            fee_base_amount=deal_info['fee_base_amount'],
            fee_quote_amount=deal_info['fee_quote_amount'],
        )

    cache = LiquidityPoolCache()
    if market and (v := cache.read()):
        data = json.loads(v)
        item = update_one(market)
        for i in range(len(data)):
            if data[i]['market'] == market:
                data[i] = item
                break
        else:
            data.append(item)
    else:
        data = [update_one(m) for m in AmmMarketCache.list_amm_markets()]

    cache.set(json.dumps(data, cls=JsonEncoder))


def sum_amm_transfer_fee(report_date) -> Dict[str, Decimal]:
    """汇总当天注入资金池的手续费"""
    rows = DailyAmmMarketReport.query.filter(
        DailyAmmMarketReport.report_date == report_date
    ).all()
    if not rows or not all(x.fee_refunded for x in rows):
        raise RuntimeError("amm fee transfer not finished")
    result = defaultdict(Decimal)
    for row in rows:
        market = MarketCache(row.market).dict
        result[market['base_asset']] += row.refund_fee_base_amount
        result[market['quote_asset']] += row.refund_fee_quote_amount
    return result


def get_all_user_amm_assets() -> Dict[int, Dict[str, Decimal]]:
    result = defaultdict(lambda : defaultdict(Decimal))
    for market in AmmMarketCache.list_amm_markets():
        srv = LiquidityService(market)
        pool_base, pool_qoute = srv.get_assets_amount()
        rows = UserLiquidity.query.filter(
            UserLiquidity.market == market,
            UserLiquidity.liquidity > 0
        ).all()
        for row in rows:
            base, quote = srv.liquidity_to_asset_amounts(row.liquidity, pool_base, pool_qoute)
            result[row.user_id][srv.market['base_asset']] += base
            result[row.user_id][srv.market['quote_asset']] += quote
    return result


def get_user_amm_assets(user_id: int, asset: str = None) -> Union[Decimal, Dict[str, Decimal]]:
    result = defaultdict(Decimal)
    rows = UserLiquidity.query.filter(
        UserLiquidity.user_id == user_id,
        UserLiquidity.liquidity > 0
    ).all()
    for row in rows:
        srv = LiquidityService(row.market)
        if asset and asset != srv.market['base_asset'] and asset != srv.market['quote_asset']:
            continue
        base, quote = srv.liquidity_to_asset_amounts(row.liquidity)
        result[srv.market['base_asset']] += base
        result[srv.market['quote_asset']] += quote
    return result[asset] if asset else result


def batch_get_user_amm_assets(user_ids: List[int], asset: str = None) -> Dict[int, Dict[str, Decimal]]:
    """ get_user_amm_assets 的批量版本 """
    rows = []
    for chunk_user_ids in batch_iter(user_ids, 1000):
        chunk_rows = UserLiquidity.query.filter(
            UserLiquidity.user_id.in_(chunk_user_ids),
            UserLiquidity.liquidity > 0,
        ).all()
        rows.extend(chunk_rows)

    result = defaultdict(lambda: defaultdict(Decimal))
    for market, market_rows in group_by(lambda x: x.market, rows).items():
        srv = LiquidityService(market)
        for row in market_rows:
            if asset and asset != srv.market['base_asset'] and asset != srv.market['quote_asset']:
                continue
            base, quote = srv.liquidity_to_asset_amounts(row.liquidity)
            result[row.user_id][srv.market['base_asset']] += base
            result[row.user_id][srv.market['quote_asset']] += quote
    return result


def get_amm_assets_slice(date) -> Dict[int, Dict[str, Decimal]]:
    pool_slices = LiquiditySlice.query.filter(LiquiditySlice.date == date).all()
    user_slices = UserLiquiditySlice.query.filter(UserLiquiditySlice.date == date).all()
    pool_slices = {x.market: x for x in pool_slices}

    result = defaultdict(lambda: defaultdict(Decimal))
    markets = {}
    for user_slice in user_slices:
        market_name = user_slice.market
        pool_slice = pool_slices[market_name]
        if pool_slice.liquidity <= 0:
            continue
        rate = user_slice.liquidity / pool_slice.liquidity
        base_amount = quantize_amount(rate * pool_slice.base_amount, 8)
        quote_amount = quantize_amount(rate * pool_slice.quote_amount, 8)

        m = markets.get(market_name)
        if m is None:
            markets[market_name] = m = MarketCache(market_name).dict
        result[user_slice.user_id][m['base_asset']] += base_amount
        result[user_slice.user_id][m['quote_asset']] += quote_amount
    return result
