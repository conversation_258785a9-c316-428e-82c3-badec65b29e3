# -*- coding: utf-8 -*-
from datetime import timedelta, date

from app.caches.broker import BrokerReportLastUpdateTimeCache
from app.caches.report import DailyIncomeProcessResultCache
from app.utils import scheduled

from decimal import Decimal
from pyroaring import BitMap
from app.business import (
    AssetPrice,
    crontab,
    lock_call,
    defaultdict,
    TradeSummaryDB,
    PerpetualSummaryDB, batch_iter,
)
from app.common import (
    CeleryQueues,
)
from app.models import db, IncomeType, SubAccount
from app.models.broker import (
    DailyBrokerUserAssetReport,
    DailyBrokerAssetReport,
    MonthlyBrokerAssetReport,
    BrokerReferralAssetDetail,
    Broker, MonthlyBrokerUserAssetReport,
)
from app.utils import (
    route_module_to_celery_queue,
)
from app.utils.date_ import next_month, today, current_timestamp

route_module_to_celery_queue(__name__, CeleryQueues.REAL_TIME)


def get_broker_id_by_client_id(client_id):
    broker_id = ''
    split_result = client_id.split('-')  # 'x-**********-zlczpf54lgjsba'
    if len(split_result) >= 2 and split_result[0].lower() == 'x' and split_result[1].isdigit():
        broker_id = split_result[1]
    return broker_id


def update_daily_broker_user_asset_detail(start_date) -> bool:

    broker_refer_records = BrokerReferralAssetDetail.query.filter(
        BrokerReferralAssetDetail.date == start_date
    ).all()

    if not broker_refer_records:
        return

    prices = AssetPrice.get_close_price_map(start_date)
    prices["USD"] = 1
    client_query = Broker.query.filter(Broker.status == Broker.Status.VALID).all()
    broker_id_map = {i.broker_id: i.user_id for i in client_query}
    spot_trade_records = TradeSummaryDB.get_client_trade_summary_by_date(start_date)
    perpetual_trade_records = PerpetualSummaryDB.get_client_trade_summary_by_date(
        start_date
    )

    refer_asset = BrokerReferralAssetDetail.REFERRAL_ASSET
    broker_user_map = defaultdict(
        lambda: defaultdict(
            lambda: {
                "total_amount": Decimal(),
                "total_spot_trade_usd": Decimal(),
                "total_perpetual_trade_usd": Decimal(),
                "total_spot_fee_usd": Decimal(),
                "total_perpetual_fee_usd": Decimal(),
            }
        )
    )

    sub_users = SubAccount.query.with_entities(SubAccount.user_id, SubAccount.main_user_id).all()
    sub_users = dict(sub_users)

    spot_broker_trade_map = defaultdict(lambda: defaultdict(Decimal))
    perpetual_broker_trade_map = defaultdict(lambda: defaultdict(Decimal))
    # 计算交易额
    for row in spot_trade_records:
        broker_id = get_broker_id_by_client_id(row["client_id"])
        if broker_id not in broker_id_map:
            continue
        broker_user_id = broker_id_map[broker_id]
        main_user_id = sub_users.get(row['user_id'], row['user_id'])
        spot_broker_trade_map[broker_user_id][main_user_id] += row["deal_amount"] * prices.get(
            row["asset"], 0
        )

    for row in perpetual_trade_records:
        broker_id = get_broker_id_by_client_id(row["client_id"])
        if broker_id not in broker_id_map:
            continue
        broker_user_id = broker_id_map[broker_id]
        main_user_id = sub_users.get(row['user_id'], row['user_id'])
        perpetual_broker_trade_map[broker_user_id][main_user_id] += row["deal_amount"] * prices.get(
            row["asset"], 0
        )

    for row in broker_refer_records:
        broker_user_id = row.broker_user_id
        broker_data = broker_user_map[broker_user_id][row.user_id]
        broker_data["total_amount"] = row.amount
        broker_data["total_spot_fee_usd"] = row.spot_fee_usd
        broker_data["total_perpetual_fee_usd"] = row.perpetual_fee_usd
        broker_data["total_spot_trade_usd"] = spot_broker_trade_map[broker_user_id][row.user_id]
        broker_data["total_perpetual_trade_usd"] = perpetual_broker_trade_map[broker_user_id][row.user_id]

    broker_user_asset_rows = []

    for broker_user_id, broker_trade_data in broker_user_map.items():
        for user_id, broker_data in broker_trade_data.items():
            row = DailyBrokerUserAssetReport(
                date=start_date,
                broker_user_id=broker_user_id,
                user_id=user_id,
                asset=refer_asset,
                amount=broker_data["total_amount"],
                spot_trade_usd=broker_data["total_spot_trade_usd"],
                perpetual_trade_usd=broker_data["total_perpetual_trade_usd"],
                spot_fee_usd=broker_data["total_spot_fee_usd"],
                perpetual_fee_usd=broker_data["total_perpetual_fee_usd"],
            )
            broker_user_asset_rows.append(row)

    for rows in batch_iter(broker_user_asset_rows, 1000):
        db.session.bulk_save_objects(rows)
    db.session.commit()

    return True


def update_daily_broker_user_report(start_date):
    is_finish = DailyIncomeProcessResultCache(start_date).get_result(
        IncomeType.BROKER_PAY)
    if not is_finish:
        return False

    rows = DailyBrokerUserAssetReport.query.filter(
        DailyBrokerUserAssetReport.date == start_date
    ).all()
    refer_asset = BrokerReferralAssetDetail.REFERRAL_ASSET
    broker_user_map = defaultdict(
        lambda: {
            "total_amount": Decimal(),
            "total_refer_usd": Decimal(),
            "total_spot_trade_usd": Decimal(),
            "total_perpetual_trade_usd": Decimal(),
            "total_spot_fee_usd": Decimal(),
            "total_perpetual_fee_usd": Decimal(),
            "user_ids": set(),
        }
    )

    for row in rows:
        broker_data = broker_user_map[row.broker_user_id]
        broker_data["total_amount"] += row.amount
        broker_data["total_spot_trade_usd"] += row.spot_trade_usd
        broker_data["total_perpetual_trade_usd"] += row.perpetual_trade_usd
        broker_data["total_spot_fee_usd"] += row.spot_fee_usd
        broker_data["total_perpetual_fee_usd"] += row.perpetual_fee_usd
        broker_data["user_ids"].add(row.user_id)

    broker_asset_rows = []
    for broker_user_id, broker_data in broker_user_map.items():
        broker_asset_rows.append(DailyBrokerAssetReport(
            date=start_date,
            user_id=broker_user_id,
            asset=refer_asset,
            amount=broker_data["total_amount"],
            deal_user_count=len(broker_data["user_ids"]),
            deal_user_bit_map=BitMap(broker_data["user_ids"]).serialize(),
            spot_trade_usd=broker_data["total_spot_trade_usd"],
            perpetual_trade_usd=broker_data["total_perpetual_trade_usd"],
            spot_fee_usd=broker_data["total_spot_fee_usd"],
            perpetual_fee_usd=broker_data["total_perpetual_fee_usd"],
        ))

    for rows in batch_iter(broker_asset_rows, 1000):
        db.session.bulk_save_objects(rows)
    db.session.commit()

    BrokerReportLastUpdateTimeCache().set(
        str(current_timestamp(to_int=True)), ex=86400 * 3
    )

    return True


def update_monthly_broker_user_report(start_date, end_date):
    rows = DailyBrokerAssetReport.query.filter(
        DailyBrokerAssetReport.date >= start_date,
        DailyBrokerAssetReport.date < end_date,
    ).all()
    refer_asset = BrokerReferralAssetDetail.REFERRAL_ASSET
    broker_user_map = defaultdict(
        lambda: {
            "total_amount": Decimal(),
            "total_refer_usd": Decimal(),
            "total_spot_trade_usd": Decimal(),
            "total_perpetual_trade_usd": Decimal(),
            "total_spot_fee_usd": Decimal(),
            "total_perpetual_fee_usd": Decimal(),
            "user_ids": set(),
        }
    )

    for row in rows:
        broker_data = broker_user_map[row.user_id]
        broker_data["total_amount"] += row.amount
        broker_data["total_spot_trade_usd"] += row.spot_trade_usd
        broker_data["total_perpetual_trade_usd"] += row.perpetual_trade_usd
        broker_data["total_spot_fee_usd"] += row.spot_fee_usd
        broker_data["total_perpetual_fee_usd"] += row.perpetual_fee_usd
        broker_data["user_ids"].update(row.get_deal_user_ids())

    for broker_user_id, broker_data in broker_user_map.items():
        row = MonthlyBrokerAssetReport.get_or_create(
            date=start_date, user_id=broker_user_id
        )

        row.asset = refer_asset
        row.amount = broker_data["total_amount"]
        row.deal_user_count = len(broker_data["user_ids"])
        row.spot_trade_usd = broker_data["total_spot_trade_usd"]
        row.perpetual_trade_usd = broker_data["total_perpetual_trade_usd"]
        row.spot_fee_usd = broker_data["total_spot_fee_usd"]
        row.perpetual_fee_usd = broker_data["total_perpetual_fee_usd"]
        db.session.add(row)
    db.session.commit()


@scheduled(crontab(minute='30', hour="1-3"))
def update_daily_broker_user_asset_detail_schedule():
    """汇总BrokerReferralAssetDetail完成基础的数据统计"""
    _today = today()
    last = DailyBrokerUserAssetReport.query.order_by(
        DailyBrokerUserAssetReport.date.desc()
    ).first()
    if not last:
        report_date = _today - timedelta(days=1)
    else:
        report_date = last.date + timedelta(days=1)
    while report_date < _today:
        update_daily_broker_user_asset_detail(report_date)
        report_date += timedelta(days=1)


@scheduled(crontab(minute='20', hour="5-6"))
@lock_call()
def update_monthly_broker_user_asset_detail_schedule():
    """DailyBrokerUserAssetReport 汇总月报"""
    last = MonthlyBrokerUserAssetReport.query.order_by(
        MonthlyBrokerUserAssetReport.date.desc()
    ).first()
    if last:
        start_month = next_month(last.date.year, last.date.month)
    else:
        first_ref_date = (
            DailyBrokerUserAssetReport.query.order_by(DailyBrokerUserAssetReport.date.asc())
            .first()
            .date
        )
        start_month = date(first_ref_date.year, first_ref_date.month, 1)

    _today = today()
    cur_month = date(_today.year, _today.month, 1)
    if start_month > cur_month:
        # 重复更新当月的数据
        start_month = cur_month

    while start_month <= cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_broker_user_asset_detail_report(start_month, end_month)
        start_month = end_month


def update_monthly_broker_user_asset_detail_report(start_date, end_date):
    model = DailyBrokerUserAssetReport
    rows = model.query.filter(
        model.date >= start_date,
        model.date < end_date,
    ).all()
    refer_asset = BrokerReferralAssetDetail.REFERRAL_ASSET
    broker_user_map = defaultdict(
        lambda: defaultdict(
            lambda: {
                "total_amount": Decimal(),
                "total_spot_trade_usd": Decimal(),
                "total_perpetual_trade_usd": Decimal(),
                "total_spot_fee_usd": Decimal(),
                "total_perpetual_fee_usd": Decimal(),
            }
        )
    )

    for row in rows:
        broker_data = broker_user_map[row.broker_user_id][row.user_id]
        broker_data["total_amount"] += row.amount
        broker_data["total_spot_trade_usd"] += row.spot_trade_usd
        broker_data["total_perpetual_trade_usd"] += row.perpetual_trade_usd
        broker_data["total_spot_fee_usd"] += row.spot_fee_usd
        broker_data["total_perpetual_fee_usd"] += row.perpetual_fee_usd

    for broker_user_id, broker_trade_data in broker_user_map.items():
        for user_id, broker_data in broker_trade_data.items():
            row = MonthlyBrokerUserAssetReport.get_or_create(
                date=start_date, broker_user_id=broker_user_id, user_id=user_id
            )
            row.asset = refer_asset
            row.amount = broker_data["total_amount"]
            row.spot_trade_usd = broker_data["total_spot_trade_usd"]
            row.perpetual_trade_usd = broker_data["total_perpetual_trade_usd"]
            row.spot_fee_usd = broker_data["total_spot_fee_usd"]
            row.perpetual_fee_usd = broker_data["total_perpetual_fee_usd"]
            db.session.add(row)
        db.session.commit()


@scheduled(crontab(minute='40', hour="1-3"))
def update_daily_broker_user_report_schedule():
    """生成用户的经纪商日报"""
    _today = today()
    last = DailyBrokerAssetReport.query.order_by(
        DailyBrokerAssetReport.date.desc()
    ).first()
    if not last:
        report_date = _today - timedelta(days=1)
    else:
        report_date = last.date + timedelta(days=1)
    while report_date < _today:
        update_daily_broker_user_report(report_date)
        report_date += timedelta(days=1)


@scheduled(crontab(minute='20', hour="5-6"))
@lock_call()
def update_monthly_broker_schedule():
    """生成用户的经纪商月报"""
    last = MonthlyBrokerAssetReport.query.order_by(
        MonthlyBrokerAssetReport.date.desc()
    ).first()
    if last:
        start_month = next_month(last.date.year, last.date.month)
    else:
        first_ref_date = (
            DailyBrokerAssetReport.query.order_by(DailyBrokerAssetReport.date.asc())
            .first()
            .date
        )
        start_month = date(first_ref_date.year, first_ref_date.month, 1)

    _today = today()
    cur_month = date(_today.year, _today.month, 1)
    if start_month > cur_month:
        # 重复更新当月的数据
        start_month = cur_month

    while start_month <= cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_broker_user_report(start_month, end_month)
        start_month = end_month