# -*- coding: utf-8 -*-
import io
from dataclasses import dataclass, field as dataclass_field
from enum import IntEnum, auto
import re
import json
import time
import hashlib
import hmac
import uuid
from collections import defaultdict
from datetime import datetime, date
from typing import List, Optional, Dict
from zipfile import ZipFile

import requests
from dateutil.relativedelta import relativedelta
from flask import current_app
from flask_babel import gettext
from sqlalchemy import func

from app import config, Language
from app.api.common import get_request_language
from app.business import CountrySettings, BusinessSettings
from app.business.lock import lock_call
from app.business.user import update_user_location
from app.business.risk_screen import RiskScreenBusiness
from app.business.email import send_kyc_pro_result
from app.common import get_country, CeleryQueues
from app.exceptions import InvalidArgument, KycSupportIdDocsChanged
from app.models import KycVerification, db, File, KycVerificationHistory, User, \
    CountrySetting, KYCInstitution, InstitutionCompany, InstitutionDirector, KycCountryConfig, \
    KycVerificationPro, \
    KycVerificationProHistory, LivenessCheckHistory, KycNonDocStats, BigUserCustomer, LivenessCheckProfile, \
    SecurityResetApplication, UnfreezeOperationLog, SecurityToolHistory

from app.utils import AWSBucketPrivate, today, strip_non_alpha_num, \
    celery_task, today_datetime, hide_text, BaseHTTPClient, url_join, datetime_to_str, now


class _ResultParser:

    class TransactionStatus:
        UPLOAD_FAILED = 'UPLOAD_FAILED'     # 证件未上传或上传失败
        PENDING = 'PENDING'                 # 第三方处理中
        DONE = 'DONE'                       # 第三方处理成功
        FAILED = 'FAILED'                   # 第三方处理失败

    @dataclass
    class KycParsedResult:
        transaction_id: str
        transaction_status: str
        kyc_status: KycVerification.Status = None
        reject_type: KycVerificationHistory.RejectType = KycVerificationHistory.RejectType.FINAL
        reasons: List[KycVerification.RejectionReason] = dataclass_field(default_factory=list)
        raw_reasons: List[str] = dataclass_field(default_factory=list)
        # e.g. {'id_type': '', 'id_number': '', 'country': '', 'expiry': '', 'dob': '', 'gender': '', 'full_name': ''}
        info: dict = dataclass_field(default_factory=dict)
        # e.g. {'front': 1, 'back': 2, 'face': 3}
        images: dict = dataclass_field(default_factory=dict)

    @dataclass
    class KycProParsedResult:
        transaction_id: str
        transaction_status: str
        kyc_pro_status: KycVerificationPro.Status = None
        reject_type: KycVerificationProHistory.RejectType = KycVerificationProHistory.RejectType.FINAL
        reasons: List[KycVerificationPro.RejectionReason] = dataclass_field(default_factory=list)
        raw_reasons: List[str] = dataclass_field(default_factory=list)
        # e.g. {'country': '', 'first_name': '', 'middle_name': '', 'last_name': '', 'full_name': '', 'address': ''}
        info: dict = dataclass_field(default_factory=dict)

    # 需要admin二次审核的认证结果
    required_admin_approval_reasons = (
        KycVerification.RejectionReason.BLURRED,
        KycVerification.RejectionReason.HIDDEN_PART_DOCUMENT,
        KycVerification.RejectionReason.DAMAGED_DOCUMENT,
        KycVerification.RejectionReason.MISSING_SIGNATURE,
        KycVerification.RejectionReason.INVALID_WATERMARK,
        KycVerification.RejectionReason.OTHER,
        KycVerification.RejectionReason.AGE_DIFFERENCE_TOO_BIG
    )

    # 需要admin二次审核的拒绝原因
    pro_required_admin_approval_reasons = (
        KycVerificationPro.RejectionReason.INVALID_ADDRESS_PROOF,
        KycVerificationPro.RejectionReason.ADDRESS_PROOF_MISSING_FULL_NAME,
        KycVerificationPro.RejectionReason.ADDRESS_PROOF_NEED_TRANSLATION,
        KycVerificationPro.RejectionReason.OTHER,
        KycVerificationPro.RejectionReason.NAME_MISMATCH,
    )


class KycBusiness:

    class Source(IntEnum):
        SUMSUB = auto()
        SUMSUB_NON_DOC = auto()

    # 超过这个时间未被处理的认证，直接置为失败
    PROCESS_EXPIRE_TIME = 3600 * 24

    MANUAL_AUDIT_ID_TYPES = {
        KycVerification.PlatForm.WEB: {
            'NGA': (KycVerification.IDType.ID_CARD, ),
        },
        KycVerification.PlatForm.APP: {},
    }

    # 第三方产生的拒绝原因
    third_party_rejection_reasons = (
        KycVerification.RejectionReason.DOCUMENT_MANIPULATED,
        KycVerification.RejectionReason.PHOTO_MANIPULATED,
        KycVerification.RejectionReason.FRAUDSTER,
        KycVerification.RejectionReason.CREDIBILITY_PROBLEM,
        KycVerification.RejectionReason.FAKE,
        KycVerification.RejectionReason.PHOTO_MISMATCH,
        KycVerification.RejectionReason.PHOTO_MISMATCHED,

        KycVerification.RejectionReason.OTHER,
        KycVerification.RejectionReason.PUNCHED_DOCUMENT,
        KycVerification.RejectionReason.MISMATCH_PRINTED_BARCODE_DATA,
        KycVerification.RejectionReason.DIGITAL_COPY,
        KycVerification.RejectionReason.NOT_READABLE_DOCUMENT,
        KycVerification.RejectionReason.BLURRED,
        KycVerification.RejectionReason.DOCUMENT_ILLEGIBLE,
        KycVerification.RejectionReason.HIDDEN_PART_DOCUMENT,
        KycVerification.RejectionReason.DAMAGED_DOCUMENT,
        KycVerification.RejectionReason.PRESET_ID_UNMATCHED,
        KycVerification.RejectionReason.INFO_MISMATCHED,
        KycVerification.RejectionReason.WRONG_ID_NUMBER,
        KycVerification.RejectionReason.DOCUMENT_EXISTED,
        KycVerification.RejectionReason.AGE_NOT_ALLOWED,
        KycVerification.RejectionReason.NATIONALITY_ERROR,
        KycVerification.RejectionReason.DOCUMENT_EXPIRED,
        KycVerification.RejectionReason.NO_DOCUMENT,
        KycVerification.RejectionReason.INCOMPATIBLE_LANGUAGE,
        KycVerification.RejectionReason.HANDWRITTEN_NOTE_MISMATCHED,
        KycVerification.RejectionReason.DOCUMENT_UNSUPPORTED,
        KycVerification.RejectionReason.SAMPLE_DOCUMENT,
        KycVerification.RejectionReason.MISSING_FRONT,
        KycVerification.RejectionReason.MISSING_BACK,
        KycVerification.RejectionReason.MISSING_PAGE,
        KycVerification.RejectionReason.ADDITIONAL_DOCUMENT_REQUIRED,
        KycVerification.RejectionReason.DOCUMENT_MISSING,
        KycVerification.RejectionReason.MISSING_SIGNATURE,
        KycVerification.RejectionReason.CAMERA_BLACK_WHITE,
        KycVerification.RejectionReason.INVALID_WATERMARK,
        KycVerification.RejectionReason.MANUAL_REJECTION,
        KycVerification.RejectionReason.ID_IS_SAMPLE,
        KycVerification.RejectionReason.SELFIE_IS_SCREEN_PAPER_VIDEO,

        KycVerification.RejectionReason.SELFIE_MANIPULATED,
        KycVerification.RejectionReason.AGE_DIFFERENCE_TOO_BIG,
        KycVerification.RejectionReason.FACE_NOT_FULLY_VISIBLE,
        KycVerification.RejectionReason.SELFIE_BAD_QUALITY,
        KycVerification.RejectionReason.BLACK_AND_WHITE,
        KycVerification.RejectionReason.LIVENESS_FAILED,
        KycVerification.RejectionReason.FACE_TOO_SMALL,
        KycVerification.RejectionReason.FACE_NOT_FOUND,
        KycVerification.RejectionReason.FACE_TOO_CLOSE,
        KycVerification.RejectionReason.LIVENESS_BAD_QUALITY,
        KycVerification.RejectionReason.UNSUPPORTED_VIDEO_LANGUAGE,
    )

    @staticmethod
    def validate_age(dob: str) -> bool:
        # 年龄须介于 18-70
        date_of_birth = datetime.strptime(dob, '%Y-%m-%d').date()
        today_ = today()
        start_date = today_ - relativedelta(years=70)
        end_date = today_ - relativedelta(years=18)
        return start_date <= date_of_birth <= end_date

    @staticmethod
    def validate_id_number(number: str, country: str, id_type: KycVerification.IDType) -> bool:
        if country in ('IRN', 'IND') and number.isdigit() \
                and id_type == KycVerification.IDType.PASSPORT:
            raise InvalidArgument(message=gettext('证件号码不能为纯数字，请重新填写'))
        if id_type == KycVerification.IDType.PASSPORT and number.isalpha():
            raise InvalidArgument(message=gettext('证件号码不能为纯字母，请重新填写'))
        return 6 <= len(number) <= 32 and re.match(r'^[A-Za-z0-9_-]+$', number) is not None

    @staticmethod
    def convert_id_number(id_number: str) -> str:
        intab = "۰۱۲۳۴۵۶۷۸۹"
        outtab = "0123456789"
        trantab = str.maketrans(intab, outtab)
        t_id_number = id_number.translate(trantab)
        r_id_number = strip_non_alpha_num(t_id_number)
        if len(id_number) == len(r_id_number):
            return r_id_number
        else:
            return ''

    @staticmethod
    def get_user_kyc_info(user: User, lang: Language = Language.DEFAULT) -> dict:
        kyc = KycVerification.query.filter(
            KycVerification.user_id == user.main_user.id,
            KycVerification.status != KycVerification.Status.CANCELLED).order_by(
            KycVerification.id.desc()).first()
        if user.main_user.kyc_verification:
            country = get_country(user.main_user.kyc_verification.country)
            kyc_country_name = country.cn_name if lang == Language.ZH_HANS_CN else country.en_name
            nationality = get_country(user.main_user.kyc_verification.nationality) or country
            kyc_nationality = nationality.cn_name if lang == Language.ZH_HANS_CN else nationality.en_name
        else:
            kyc_country_name = None
            kyc_nationality = None

        if user.kyc_verification:
            if lang == Language.ZH_HANS_CN:
                if user.kyc_first_name and user.kyc_last_name:
                    kyc_name = uncensored_kyc_name = f'{user.kyc_first_name}{user.kyc_last_name}'
                else:
                    kyc_name = uncensored_kyc_name = user.kyc_full_name
            else:
                if user.kyc_first_name and user.kyc_last_name:
                    kyc_name = uncensored_kyc_name = f'{user.kyc_last_name} {user.kyc_first_name}'
                else:
                    kyc_name = uncensored_kyc_name = user.kyc_full_name
        else:
            kyc_name = uncensored_kyc_name = None
        kyc_pro_reject_reason = None
        if user.kyc_status == User.KYCStatus.PASSED:
            kyc_pro = KycVerificationPro.query.filter(
                KycVerificationPro.user_id == user.main_user.id,
            ).order_by(KycVerificationPro.id.desc()).first()
            if kyc_pro:
                kyc_pro_reject_reason = kyc_pro.get_reject_reason()
        return dict(
            kyc_status=user.kyc_status.value,
            kyc_pro_status=user.kyc_pro_status.name, # 不再使用，兼容保留，高级认证相关字段在对应接口中提供
            kyc_number=user.kyc_id_number,
            kyc_name=kyc_name,
            uncensored_kyc_name=uncensored_kyc_name,
            kyc_id_type=gettext(
                user.kyc_verification.id_type.value) if user.kyc_verification else None,
            kyc_country_name=kyc_country_name,
            kyc_nationality=kyc_nationality,
            kyc_reject_reason=kyc.get_reject_reason()
            if kyc and user.kyc_status == User.KYCStatus.FAILED else None,
            kyc_pro_reject_reason=kyc_pro_reject_reason # 不再使用，兼容保留，高级认证相关字段在对应接口中提供
        )

    @classmethod
    def get_third_party_rejection_reason(cls):
        return cls.third_party_rejection_reasons

    @classmethod
    def _get_user_support_non_doc(cls, user: User, user_force_manual: bool):
        if user_force_manual:
            return False
        row = KycNonDocStats.query.filter(
            KycNonDocStats.user_id == user.id,
        ).first()
        if not row:
            return True
        if row.fail_count >= KycNonDocStats.MAX_FAIL_COUNT or row.is_forbidden:
            return False
        return True

    @classmethod
    def _check_user_need_manual(cls, user: User, is_big_customer: bool):
        # 如果用户实名认证是已通过的，则强制进行人工审核
        is_submit_on_pass, kyc_passed = False, False
        if user.kyc_status == User.KYCStatus.PASSED:
            kyc_passed = True
        else:
            r = KycVerification.query.filter(
                KycVerification.submit_type == KycVerification.SubmitType.SUBMIT_ON_PASS,
                KycVerification.user_id == user.main_user.id,
            ).first()
            is_submit_on_pass = bool(r)
        if kyc_passed or is_submit_on_pass:
            return True

        # 如果用户被服务商Final拒绝了，则该服务商国家都走人工通道
        history = KycVerificationHistory.query.filter(
            KycVerificationHistory.user_id == user.main_user.id,
            KycVerificationHistory.reject_type == KycVerificationHistory.RejectType.FINAL,
        ).first()
        if history:
            return True

        if is_big_customer:
            # 产品要求：大客户如果第三方审核失败过，直接走人工
            reject_history = KycVerificationHistory.query.filter(
                KycVerificationHistory.user_id == user.main_user.id,
                KycVerificationHistory.rejection_reason.is_not(None),
            ).first()
            if reject_history:
                return True

        return False

    @classmethod
    def get_all_country_id_types(cls,
                                 platform: str = KycVerification.PlatForm.WEB,
                                 kyc_type: str = User.KYCType.INDIVIDUAL.name,
                                 lang: Language = Language.DEFAULT,
                                 user=None,
                                 support_non_doc: bool = True,
                                 ):
        """
        {
            "countryCode": "AFG",
            "countryName": "Afghanistan",
            "idTypes": [
                {
                    "acquisitionConfig": {
                        "backSide": false   # 是否需要上传反面
                    },
                    "idType": "PASSPORT",
                    "isAccepted": true,     # 该证件类型是否支持第三方审核
                    "isNonDoc": true,       # 是否是非文件认证
                }
            ],
            "service_type": "SUMSUB"    # 审核渠道，取值 MANUAL_AUDIT / SUMSUB
        }
        注：当 service_type != MANUAL_AUDIT 且 idTypes.isAccepted == True 时走第三方服务商，否则走人工审核
        """
        configs = [dict(
            country=c.country,
            service_type=c.service_type,
            supported_id_types=json.loads(c.supported_id_types) if c.supported_id_types else [],
            id_type_pages=json.loads(c.id_type_pages) if c.id_type_pages else {},
            non_doc_id_types=json.loads(c.non_doc_id_types) if c.non_doc_id_types else [],
        ) for c in KycCountryConfig.query.with_entities(
            KycCountryConfig.country,
            KycCountryConfig.service_type,
            KycCountryConfig.supported_id_types,
            KycCountryConfig.id_type_pages,
            KycCountryConfig.non_doc_id_types,
        ).all()]

        def get_id_type_detail(id_type_pages: dict, id_type_: KycVerification.IDType):
            back_side = id_type_pages[id_type_.name] == 'two_side'
            return {
                'acquisitionConfig': {'backSide': back_side},
                'idType': id_type_.name
            }

        country_id_types_map = {}
        for item in configs:
            id_types = [get_id_type_detail(item['id_type_pages'], getattr(KycVerification.IDType, t_))
                        for t_ in item['supported_id_types']]
            country_id_types_map[item['country']] = dict(
                countryCode=item['country'],
                countryName=get_country(item['country']).en_name,
                idTypes=id_types
            )

        is_big_customer = BigUserCustomer.is_big_customer(user.id) if user else False
        forbidden_country_set, manual_country_set = cls.get_forbidden_countries(platform)
        user_force_manual = cls._check_user_need_manual(user, is_big_customer) if user else False
        user_support_non_doc = cls._get_user_support_non_doc(user, user_force_manual) if user else True
        non_doc_country_id_types = defaultdict(lambda: defaultdict(lambda: set()))
        country_config_map = {}
        for c in configs:
            country_config_map[c['country']] = c
            if support_non_doc and user_support_non_doc and c['non_doc_id_types']:
                non_doc_country_id_types[c['service_type']][c['country']] = {id_type for id_type in c['non_doc_id_types']}
        res = []
        for country, config_ in country_config_map.items():
            if kyc_type == User.KYCType.INDIVIDUAL.name \
                    and country in forbidden_country_set \
                    and not is_big_customer:
                continue
            service_type = config_['service_type']
            if country in manual_country_set or user_force_manual:
                service_type = KycVerification.ServiceType.MANUAL_AUDIT
            data = country_id_types_map[country]
            for item in data['idTypes']:
                item['isAccepted'] = service_type != KycVerification.ServiceType.MANUAL_AUDIT

            # 强制进行人工审核的类型
            manual_audit_type_map = cls.MANUAL_AUDIT_ID_TYPES[platform]
            if manual_types := manual_audit_type_map.get(country):
                accepted_id_types = {item['idType'] for item in data['idTypes']}
                manual_types = {item.name for item in manual_types}
                for t in manual_types:
                    if t not in accepted_id_types:
                        t = getattr(KycVerification.IDType, t)
                        type_detail = get_id_type_detail(config_['id_type_pages'], t)
                        type_detail['isAccepted'] = False
                        data['idTypes'].append(type_detail)
                for item in data['idTypes']:
                    if item['idType'] in manual_types:
                        item['isAccepted'] = False

            if support_non_doc and user_support_non_doc:
                id_types = set()
                for item in data['idTypes']:
                    item['isNonDoc'] = False
                    id_types.add(item['idType'])
                non_doc_id_types = non_doc_country_id_types.get(service_type, {}).get(country, set())
                to_change = non_doc_id_types & id_types
                to_add = non_doc_id_types - id_types
                for item in data['idTypes']:
                    if item['idType'] in to_change:
                        item['isNonDoc'] = True
                f_id_types = [
                    {'acquisitionConfig': {'backSide': False}, 'idType': id_type, 'isNonDoc': True, 'isAccepted': True}
                    for id_type in to_add
                ]
                f_id_types.extend(data['idTypes'])
                data['idTypes'] = f_id_types

            data['service_type'] = service_type.name
            res.append(data)

        res.sort(key=lambda x: x['countryName'])
        # 翻译
        if lang == Language.ZH_HANS_CN:
            for item in res:
                name_ = get_country(item['countryCode'])
                if name_:
                    item['countryName'] = name_.cn_name
        return res

    @classmethod
    def get_forbidden_countries(cls, platform: str):
        allow_kyc_key = CountrySettings.kyc_enabled.name
        if platform == KycVerification.PlatForm.WEB:
            allow_kyc_auto_audit_key = CountrySettings.allow_kyc_auto_audit_from_web.name
        else:
            allow_kyc_auto_audit_key = CountrySettings.allow_kyc_auto_audit_from_app.name

        forbidden_country_set, manual_country_set = set(), set()
        records = CountrySetting.query.filter(
            CountrySetting.key.in_((allow_kyc_auto_audit_key, allow_kyc_key)),
            CountrySetting.value == '0',
            CountrySetting.status == CountrySetting.Status.VALID
        ).with_entities(CountrySetting.key, CountrySetting.code).all()
        for item in records:
            if item.key == allow_kyc_key:
                forbidden_country_set.add(item.code)
            if item.key == allow_kyc_auto_audit_key:
                manual_country_set.add(item.code)
        return forbidden_country_set, manual_country_set

    @classmethod
    def is_country_accepted(cls, country: str, id_type: str, platform: str, user=None, support_non_doc: bool = True) -> bool:
        """是否支持第三方审核"""
        types = cls.get_all_country_id_types(platform, user=user, support_non_doc=support_non_doc)
        type_map = {item['countryCode']: item for item in types}
        country_map = type_map.get(country)
        if not country_map:
            raise InvalidArgument
        country_settings = CountrySettings(country)
        if not country_settings.kyc_enabled:
            raise InvalidArgument
        id_types = country_map['idTypes']
        is_allowed = False
        for type_ in id_types:
            if type_['idType'] == id_type and type_['isAccepted']:
                is_allowed = True
                break
        return is_allowed

    @classmethod
    def get_request_id(cls, user_id: int, country: str) -> str:
        configs = KycCountryConfig.query.with_entities(
            KycCountryConfig.country,
            KycCountryConfig.service_type
        ).all()
        country_config_map = dict(configs)
        service_type = country_config_map[country]
        if service_type == KycVerification.ServiceType.SUMSUB:
            data = 'CoinEx To Sumsub ' + str(user_id)
            return hashlib.sha256(data.encode('utf-8')).hexdigest()
        else:
            return str(uuid.uuid4())

    @classmethod
    def get_non_doc_request_id(cls, user_id: int, service_type: KycVerification.ServiceType) -> str:
        if service_type == KycVerification.ServiceType.SUMSUB:
            data = 'CoinEx To Sumsub Non-Doc ' + str(user_id)
            return hashlib.sha256(data.encode('utf-8')).hexdigest()
        else:
            raise InvalidArgument

    @classmethod
    def get_result(cls, history: KycVerificationHistory):
        if history.service_type == KycVerification.ServiceType.SUMSUB:
            if history.doc_type == KycVerification.DocType.NON_DOC:
                return SumsubClient.get_non_doc_result(history)
            else:
                return SumsubClient.get_result(history)

    @classmethod
    def parse_result(cls, data: dict, source: Source) -> _ResultParser.KycParsedResult:
        if source in [cls.Source.SUMSUB, cls.Source.SUMSUB_NON_DOC]:
            return SumsubClient.parse_result(data)
        else:
            raise InvalidArgument

    @classmethod
    def handle_result(cls, user: User,
                      history: KycVerificationHistory,
                      data: dict, *, source: Source) -> Optional[KycVerification]:
        """
        - 创建KYC记录保存图片和基础信息
        - 检查预录入的基础信息与认证结果是否匹配
        """
        from app.schedules.kyc import process_risk_screen_request_task

        parsed_result = cls.parse_result(data, source)
        transaction_status = parsed_result.transaction_status
        if transaction_status == _ResultParser.TransactionStatus.UPLOAD_FAILED:
            cls.upload(history.id)
            return None
        if transaction_status == _ResultParser.TransactionStatus.PENDING:
            return None

        KycVerification.query.filter(
            KycVerification.user_id == user.id,
        ).update(
            dict(status=KycVerification.Status.CANCELLED),
            synchronize_session=False
        )

        reasons = parsed_result.reasons
        reason_str = ','.join([r.name for r in reasons]) or None
        if transaction_status == _ResultParser.TransactionStatus.FAILED:
            user.kyc_status = User.KYCStatus.FAILED
            history.status = KycVerificationHistory.Status.FAILED
            history.rejection_reason = reason_str
            kyc = KycVerification.add(
                history,
                KycVerification.Status.REJECTED,
                reason_str,
            )
            db.session.commit()
            return kyc

        info = parsed_result.info
        if verified_id_num := cls.convert_id_number(info['id_number']):
            info['id_number'] = verified_id_num
        else:
            info.pop('id_number')
        kyc = KycVerification.add(
            history,
            parsed_result.kyc_status,
            reason_str,
            info=info,
            images=parsed_result.images,
        )

        presets = json.loads(history.detail)
        presets['reason_raw'] = parsed_result.raw_reasons
        presets['doc_name'] = info.get('full_name')
        history.detail = json.dumps(presets)
        history.rejection_reason = reason_str
        history.reject_type = parsed_result.reject_type
        history.status = KycVerificationHistory.Status.FINISHED

        if kyc.status == KycVerification.Status.SCREENING:
            document_country = info['country'].upper()
            if document_country != presets['country']:
                kyc.status = KycVerification.Status.REJECTED
                kyc.rejection_reason = KycVerification.RejectionReason.NATIONALITY_ERROR.name

            # 证件类型不匹配的转人工处理（不校验证件号码）
            if history.doc_type == KycVerification.DocType.DOC and presets['id_type'] != info['id_type']:
                kyc.status = KycVerification.Status.AUDIT_REQUIRED
                kyc.rejection_reason = KycVerification.RejectionReason.PRESET_ID_UNMATCHED.name

            # 证件过期
            expiry_date = None
            if info.get('expiry'):
                expiry_date = datetime.strptime(info['expiry'], '%Y-%m-%d').date()
            if expiry_date:
                if document_country == 'IRN' and info['id_type'] == KycVerification.IDType.ID_CARD.name:
                    f_date = date(2025, 12, 31)
                    t_date = date(2027, 12, 31)
                    if expiry_date <= f_date and t_date < today():
                        kyc.status = KycVerification.Status.REJECTED
                        kyc.rejection_reason = KycVerification.RejectionReason.DOCUMENT_EXPIRED.name
                    if f_date < expiry_date < today():
                        kyc.status = KycVerification.Status.REJECTED
                        kyc.rejection_reason = KycVerification.RejectionReason.DOCUMENT_EXPIRED.name
                else:
                    if expiry_date < today():
                        kyc.status = KycVerification.Status.REJECTED
                        kyc.rejection_reason = KycVerification.RejectionReason.DOCUMENT_EXPIRED.name

            if info['dob'] and not cls.validate_age(info['dob']):
                kyc.status = KycVerification.Status.REJECTED
                kyc.rejection_reason = KycVerification.RejectionReason.AGE_NOT_ALLOWED.name

            is_big_customer = BigUserCustomer.is_big_customer(user.id)
            if not get_country(document_country) or \
                    not (is_big_customer or CountrySettings(document_country).kyc_enabled):
                kyc.status = KycVerification.Status.REJECTED
                kyc.rejection_reason = KycVerification.RejectionReason.DOCUMENT_UNSUPPORTED.name

        cls.handle_special_cases(kyc)

        # 查找重复证件号
        if (
            verified_id_num
            and kyc.status in (KycVerification.Status.SCREENING, 
                               KycVerification.Status.PASSED, KycVerification.Status.AUDIT_REQUIRED)
            and KycVerification.query.filter(
                KycVerification.country == kyc.country,
                KycVerification.status.in_(
                    (KycVerification.Status.PASSED, 
                     KycVerification.Status.CREATED, 
                     KycVerification.Status.AUDIT_REQUIRED,
                     KycVerification.Status.SCREENING)
                ),
                KycVerification.id_number == verified_id_num,
                KycVerification.user_id != user.id,
            ).first() is not None
        ):
            user.kyc_status = User.KYCStatus.FAILED
            kyc.status = KycVerification.Status.REJECTED
            kyc.rejection_reason = KycVerification.RejectionReason.DOCUMENT_EXISTED.name
            db.session.commit()
            return kyc

        screen_request = None
        if kyc.status == KycVerification.Status.SCREENING:
            user.kyc_status = User.KYCStatus.PROCESSING
            screen_request = RiskScreenBusiness.new_screen_request_from_kyc(kyc, session_add=True)
        elif kyc.status == KycVerification.Status.REJECTED:
            user.kyc_status = User.KYCStatus.FAILED
        elif kyc.status == KycVerification.Status.AUDIT_REQUIRED:
            user.kyc_status = User.KYCStatus.PROCESSING
        db.session.commit()
        if screen_request:
            update_user_location(user.id)
            process_risk_screen_request_task.delay(screen_request.id)
        return kyc

    @classmethod
    def handle_special_cases(cls, kyc_record: KycVerification):
        """
        对一些国家、证件类型的特殊处理
        """
        _types = (
            ("IDN", KycVerification.IDType.ID_CARD, KycVerification.RejectionReason.DOCUMENT_EXPIRED),
            ("NGA", None, KycVerification.RejectionReason.PHOTO_MISMATCH),
            ("NGA", KycVerification.IDType.ID_CARD, KycVerification.RejectionReason.NOT_READABLE_DOCUMENT),
            ("KOR", None, KycVerification.RejectionReason.NOT_READABLE_DOCUMENT),
            ("JPN", None, KycVerification.RejectionReason.NOT_READABLE_DOCUMENT),
            ("IRN", KycVerification.IDType.ID_CARD, KycVerification.RejectionReason.NOT_READABLE_DOCUMENT),
            ("TWN", None, KycVerification.RejectionReason.NOT_READABLE_DOCUMENT),
            ("FRA", KycVerification.IDType.ID_CARD, KycVerification.RejectionReason.NOT_READABLE_DOCUMENT),
            ("USA", None, KycVerification.RejectionReason.LIVENESS_FAILED),
        )
        
        if kyc_record.status == KycVerification.Status.REJECTED:
            for t in _types:
                country, id_type, rejection_reason = t
                if kyc_record.country == country \
                    and (id_type is None or kyc_record.id_type == id_type) \
                        and kyc_record.rejection_reason == rejection_reason.name:
                    kyc_record.status = KycVerification.Status.AUDIT_REQUIRED
                    break
            if kyc_record.country == 'RUS' \
                and kyc_record.id_type == KycVerification.IDType.DRIVING_LICENSE \
                    and kyc_record.rejection_reason == KycVerification.RejectionReason.DOCUMENT_EXPIRED.name \
                        and kyc_record.doc_expire_date \
                              and datetime(2022, 1, 1).date() <= kyc_record.doc_expire_date <= datetime(2023, 12, 31).date() \
                                and kyc_record.doc_expire_date + relativedelta(years=3) > today():
                kyc_record.status = KycVerification.Status.AUDIT_REQUIRED
            
            country_config = KycCountryConfig.query.filter(
                KycCountryConfig.country == kyc_record.country
            ).first()
            # force admin audit
            if country_config and country_config.is_audit_required:
                kyc_record.status = KycVerification.Status.AUDIT_REQUIRED

    @classmethod
    def get_non_doc_images(cls, history: KycVerificationHistory) -> dict:
        raw_data = cls.get_result(history)
        if not raw_data:
            return {}
        if history.service_type == KycVerification.ServiceType.SUMSUB:
            return SumsubClient.fetch_non_doc_images(raw_data)
        else:
            return {}

    @classmethod
    def upload(cls, history_id: int):
        history = KycVerificationHistory.query.get(history_id)
        detail = json.loads(history.detail)
        country = detail['country']
        configs = KycCountryConfig.query.with_entities(
            KycCountryConfig.country,
            KycCountryConfig.service_type
        ).all()
        country_config_map = dict(configs)
        service_type = country_config_map[country]
        history.service_type = service_type
        db.session.commit()
        if service_type == KycVerification.ServiceType.SUMSUB:
            upload_kyc_user_data_to_sumsub_task.delay(history_id)
        else:
            # 异常情况，不作处理
            pass

    @classmethod
    def get_third_reject_reason_desc(cls, service_type: KycVerification.ServiceType, reason_raw: list):
        if service_type == KycVerification.ServiceType.SUMSUB:
            return SumsubClient.get_reject_reason_desc(reason_raw)
        return []


def _get_image_bytes(file: File):
    if not file:
        return
    url = file.private_url
    resp = requests.get(url)
    if resp.status_code == 200:
        return resp.content


@celery_task(queue=CeleryQueues.REAL_TIME)
@lock_call(with_args=True)
def upload_kyc_user_data_to_sumsub_task(kyc_history_id: int):
    """
    上传初级kyc数据到Sumsub服务
    """
    SumsubClient.upload(kyc_history_id=kyc_history_id)


@celery_task(queue=CeleryQueues.REAL_TIME)
@lock_call(with_args=True)
def upload_kyc_pro_user_data_to_sumsub_task(kyc_pro_history_id: int):
    """
    上传高级kyc数据到Sumsub服务
    """
    SumsubClient.upload_poa(kyc_pro_history_id=kyc_pro_history_id)
    

class KYCInstitutionBusiness:
    DAILY_LIMIT = 5

    def __init__(self, user: User):
        self.user = user.main_user

    def create_institution(self, company_info: Dict, manager_info: List[Dict]):
        self._change_json_fields(company_info)
        self._create(company_info, manager_info)

    @staticmethod
    def _change_json_fields(company_info: Dict):
        json_fields = (
            'certificate_file_ids',
            'structure_file_ids',
            'constitution_file_ids',
            'roster_file_ids',
            'authorization_letter_file_ids',
            'statement_file_ids',
            'coi_file_ids',
            'other_file_ids',
        )
        for field in json_fields:
            value = company_info.get(field)
            json_value = None
            if value:
                json_value = json.dumps(value)
            company_info[field] = json_value

    def _create(self, company_info: Dict, manager_info: List[Dict]):
        institution = KYCInstitution(
            user_id=self.user.id,
            status=KYCInstitution.Status.CREATED,
        )
        db.session_add_and_commit(institution)

        pending = []
        company_info.update(institution_id=institution.id)
        company = InstitutionCompany(**company_info)
        pending.append(company)

        for manager_dict in manager_info:
            manager_dict.update(institution_id=institution.id)
            manager = InstitutionDirector(**manager_dict)
            pending.append(manager)

        db.session.add_all(pending)
        user = self.user
        user.kyc_status = user.KYCStatus.PROCESSING
        db.session.commit()

    def get_last_institution(self):
        institution = KYCInstitution.query.filter(
            KYCInstitution.user_id == self.user.id,
        ).order_by(KYCInstitution.id.desc()).first()
        if not institution:
            return None, None, None

        company = InstitutionCompany.query.filter(
            InstitutionCompany.institution_id == institution.id,
        ).first()

        director = InstitutionDirector.query.filter(
            InstitutionDirector.institution_id == institution.id,
            InstitutionDirector.is_beneficiary.is_(True),
        ).first()
        return institution, company, director

    def get_kyc_status(self) -> dict:
        institution, company, director = self.get_last_institution()
        if not institution:
            return dict()

        if company:
            country = get_country(company.location_code)
            country_name = country.cn_name if get_request_language() == Language.ZH_HANS_CN else country.en_name
            company_info = dict(
                company_name=company.name,
                company_register_code=self._hide_text(company.register_code),
                company_country=country_name,
            )
        else:
            company_info = dict(
                company_name='',
                company_register_code='',
                company_country='',
            )

        if director:
            director_info = dict(
                beneficiary_name=f'{director.first_name}*',
                beneficiary_id_number=self._hide_text(director.id_number),
                beneficiary_id_type=gettext(director.id_type),
            )
        else:
            director_info = dict(
                beneficiary_name='',
                beneficiary_id_number='',
                beneficiary_id_type='',
            )

        institution_ret = dict(
            kyc_status=self.user.kyc_status.value,
            kyc_reject_reason=institution.get_reject_reason()
        )

        institution_ret.update(company_info)
        institution_ret.update(director_info)
        return institution_ret

    @staticmethod
    def _hide_text(number):
        return hide_text(number, 2, 2) if len(number) > 8 else hide_text(number, 3, 3)

    def exceed_daily_limit(self) -> bool:
        model = KYCInstitution
        count = model.query.filter(
            model.user_id == self.user.id,
            model.created_at >= today_datetime()
        ).with_entities(func.count()).scalar() or 0
        return count >= self.DAILY_LIMIT


class KycProBusiness:
    """kyc高级认证"""

    PROCESS_EXPIRE_TIME = 3600 * 24

    # 第三方产生的拒绝原因
    third_party_rejection_reasons = (
        KycVerificationPro.RejectionReason.INVALID_ADDRESS_PROOF,
        KycVerificationPro.RejectionReason.INCOMPLETE_ADDRESS_PROOF,
        KycVerificationPro.RejectionReason.ADDRESS_PROOF_MISSING_FULL_NAME,
        KycVerificationPro.RejectionReason.ADDRESS_PROOF_MISSING_FULL_ADDRESS,
        KycVerificationPro.RejectionReason.ADDRESS_PROOF_DATE_INVALID,
        KycVerificationPro.RejectionReason.ADDRESS_PROOF_MISSING_SIG,
        KycVerificationPro.RejectionReason.SAME_ADDRESS_PROOF,
        KycVerificationPro.RejectionReason.ADDRESS_MISMATCH,
        KycVerificationPro.RejectionReason.ADDRESS_PROOF_MANIPULATED,
        KycVerificationPro.RejectionReason.ADDRESS_PROOF_IS_SCREENSHOT,
        KycVerificationPro.RejectionReason.ADDRESS_PROOF_NEED_TRANSLATION,
        KycVerificationPro.RejectionReason.OTHER,
        KycVerificationPro.RejectionReason.NAME_MISMATCH,
        KycVerificationPro.RejectionReason.COUNTRY_MISMATCH,
        KycVerificationPro.RejectionReason.THIRD_PARTY_SPAM,
    )

    @classmethod
    def get_third_party_rejection_reason(cls):
        return cls.third_party_rejection_reasons

    @classmethod
    def get_third_reject_reason_desc(cls, service_type: KycVerificationPro.ServiceType, reason_raw: list):
        if service_type == KycVerificationPro.ServiceType.SUMSUB:
            return SumsubClient.get_pro_reject_reason_desc(reason_raw)
        return []

    @classmethod
    def get_service_type_mapping(cls, user: User = None):
        configs = KycCountryConfig.query.with_entities(
            KycCountryConfig.country,
            KycCountryConfig.pro_service_type
        ).all()
        final_reject = None
        if user:
            final_reject = KycVerificationProHistory.query.filter(
                KycVerificationProHistory.user_id == user.id,
                KycVerificationProHistory.reject_type == KycVerificationProHistory.RejectType.FINAL,
            ).first()

        res = {}
        for r in configs:
            res[r.country] = r.pro_service_type
            if final_reject:
                res[r.country] = KycVerificationPro.ServiceType.MANUAL_AUDIT
        return res

    @classmethod
    def get_request_id(cls, user_id: int, service_type: KycVerificationPro.ServiceType) -> str:
        if service_type == KycVerificationPro.ServiceType.SUMSUB:
            data = 'CoinEx To Sumsub PoA ' + str(user_id)
            return hashlib.sha256(data.encode('utf-8')).hexdigest()
        else:
            return str(uuid.uuid4())

    @classmethod
    def create_kyc_verification_pro(cls, user: User, data):
        """
        创建高级kyc申请
        """

        user_id = user.id
        KycVerificationPro.query.filter(
            KycVerificationPro.user_id == user_id,
            KycVerificationPro.status == KycVerificationPro.Status.REJECTED
        ).update(
            dict(status=KycVerificationPro.Status.CANCELLED)
        )

        params = data
        service_type_mapping = cls.get_service_type_mapping(user)
        country = params['country']
        service_type = service_type_mapping[country]
        failed_count = KycVerificationPro.query.filter(
            KycVerificationPro.user_id == user_id,
            KycVerificationPro.status.in_([
                KycVerificationPro.Status.REJECTED,
                KycVerificationPro.Status.CANCELLED
            ]),
        ).with_entities(
            func.count(KycVerificationPro.id)
        ).scalar() or 0
        if failed_count >= BusinessSettings.kyc_pro_third_max_count:
            service_type = KycVerificationPro.ServiceType.MANUAL_AUDIT
        params['service_type'] = service_type
        if service_type == KycVerificationPro.ServiceType.MANUAL_AUDIT:
            params['status'] = KycVerificationPro.Status.AUDIT_REQUIRED
        else:
            params['status'] = KycVerificationPro.Status.CREATED
        kyc_pro = KycVerificationPro(user_id=user.id, **params)
        db.session_add_and_flush(kyc_pro)

        history = None
        if service_type != KycVerificationPro.ServiceType.MANUAL_AUDIT:
            request_id = cls.get_request_id(user_id, service_type)
            history = KycVerificationProHistory(
                user_id=user_id,
                request_id=request_id,
                service_type=service_type,
                detail=json.dumps(dict(
                    kyc_pro_id=kyc_pro.id,
                    country=country,
                    first_name=user.kyc_first_name,
                    last_name=user.kyc_last_name,
                    full_name=user.kyc_full_name,
                    pro_name=params['pro_name'],
                    address=params['address'],
                    address_file_ids=params['address_file_ids'],
                    platform=params['platform'].name,
                ))
            )
            db.session_add_and_flush(history)
            kyc_pro.history_id = history.id

        db.session.commit()

        if history:
            cls.upload(history.id)
        return kyc_pro

    @classmethod
    def upload(cls, history_id: int):
        """ 上传数据到第三方服务 """
        history = KycVerificationProHistory.query.get(history_id)
        service_type = history.service_type
        if service_type == KycVerificationPro.ServiceType.SUMSUB:
            upload_kyc_pro_user_data_to_sumsub_task.delay(history_id)
        else:
            # 异常情况，不作处理
            pass

    @classmethod
    def get_result(cls, history: KycVerificationProHistory):
        if history.service_type == KycVerificationPro.ServiceType.SUMSUB:
            return SumsubClient.get_result(history)

    @classmethod
    def parse_result(cls, data: dict, service_type: KycVerificationPro.ServiceType) -> _ResultParser.KycProParsedResult:
        if service_type == KycVerificationPro.ServiceType.SUMSUB:
            return SumsubClient.parse_poa_result(data)
        else:
            raise InvalidArgument

    @classmethod
    def handle_result(cls, user: User, history: KycVerificationProHistory, data: dict):
        """ 处理第三方服务解析结果 """
        def compare_name(kyc_name: str, parsed_name: str) -> bool:
            kyc_name = kyc_name.lower()
            parsed_name = parsed_name.lower()
            parsed_name_li = parsed_name.split(' ')
            parsed_name_1 = ' '.join([n for n in parsed_name_li if n])
            parsed_name_2 = ' '.join([n for n in parsed_name_li[::-1] if n])
            if parsed_name_1 != kyc_name and parsed_name_2 != kyc_name:
                return False
            return True

        service_type = history.service_type
        parsed_result = cls.parse_result(data, service_type)
        transaction_status = parsed_result.transaction_status
        if transaction_status == _ResultParser.TransactionStatus.UPLOAD_FAILED:
            KycProBusiness.upload(history.id)
            return
        if transaction_status == _ResultParser.TransactionStatus.PENDING:
            return
        presets = json.loads(history.detail)
        kyc_pro_id = presets['kyc_pro_id']
        kyc_pro = KycVerificationPro.query.get(kyc_pro_id)
        reasons = parsed_result.reasons
        reason_str = ','.join([r.name for r in reasons]) if reasons else None
        if transaction_status == _ResultParser.TransactionStatus.FAILED:
            history.status = KycVerificationProHistory.Status.FAILED
            history.rejection_reason = reason_str or KycVerificationPro.RejectionReason.OTHER.name
            kyc_pro.status = KycVerificationPro.Status.REJECTED
            kyc_pro.rejection_reason = reason_str or KycVerificationPro.RejectionReason.OTHER.name
            db.session.commit()
            send_kyc_pro_result.delay(kyc_pro.id)
            return

        history.status = KycVerificationProHistory.Status.FINISHED
        history.reject_type = parsed_result.reject_type
        history.rejection_reason = reason_str
        presets['reason_raw'] = parsed_result.raw_reasons
        history.detail = json.dumps(presets)
        db.session.flush()

        kyc_pro.status = parsed_result.kyc_pro_status
        kyc_pro.rejection_reason = reason_str
        db.session.flush()

        info = parsed_result.info
        kyc_country = user.kyc_country
        if kyc_pro.status == KycVerificationPro.Status.PASSED:
            document_name = info.get('full_name', '')
            if not compare_name(user.kyc_full_name, document_name):
                current_app.logger.warning(f"kyc pro compare name not equal, user_id: {user.id}, kyc_pro_id: {kyc_pro_id}, "
                                           f"kyc_name: {user.kyc_full_name}, doc_name: {document_name}")
                kyc_pro.status = KycVerificationPro.Status.AUDIT_REQUIRED
                kyc_pro.rejection_reason = KycVerificationPro.RejectionReason.NAME_MISMATCH.name
            document_country = info.get('country', '')
            if document_country != kyc_country:
                kyc_pro.status = KycVerificationPro.Status.REJECTED
                kyc_pro.rejection_reason = KycVerificationPro.RejectionReason.COUNTRY_MISMATCH.name
            db.session.flush()

        if kyc_pro.status == KycVerification.Status.REJECTED:
            country_config = KycCountryConfig.query.filter(
                KycCountryConfig.country == kyc_country
            ).first()
            if country_config and country_config.pro_is_audit_required:
                kyc_pro.status = KycVerification.Status.AUDIT_REQUIRED
                db.session.flush()

        kyc_pro.parsed_info = json.dumps(info)
        db.session.commit()
        if kyc_pro.status in [KycVerificationPro.Status.PASSED, KycVerificationPro.Status.REJECTED]:
            send_kyc_pro_result.delay(kyc_pro.id)


class SumsubClient(_ResultParser):

    reason_redirect_map = {
        # 视频验证相关
        'APPLICANT_INTERRUPTED_INTERVIEW': {'user_reason': KycVerification.RejectionReason.VIDEO_FAILED,
                                            'desc': '在视频验证中，申请人拒绝结束'},
        'CONNECTION_INTERRUPTED': {'user_reason': KycVerification.RejectionReason.VIDEO_FAILED,
                                   'desc': '视频验证呼叫连接已中断'},
        'NOT_ALL_CHECKS_COMPLETED': {'user_reason': KycVerification.RejectionReason.VIDEO_FAILED,
                                     'desc': '验证尚未完全完成（如KYB还存在未通过的受益人）'},
        'UNSUITABLE_ENV': {'user_reason': KycVerification.RejectionReason.VIDEO_FAILED,
                           'desc': '视频验证中，申请人非独自一人或不可见'},

        # 人像识别问题
        'BAD_AVATAR': {'user_reason': KycVerification.RejectionReason.LIVENESS_FAILED,
                       'desc': '人像识别不符合要求'},
        'BAD_FACE_MATCHING': {'user_reason': KycVerification.RejectionReason.PHOTO_MISMATCHED,
                              'desc': '证件和自拍照之间的面部检查失败'},
        'BAD_SELFIE': {'user_reason': KycVerification.RejectionReason.SELFIE_BAD_QUALITY,
                       'desc': '申请人上传了一张质量不佳的自拍照'},
        'BAD_VIDEO_SELFIE': {'user_reason': KycVerification.RejectionReason.LIVENESS_FAILED,
                             'desc': '申请人上传了一张质量不佳的自拍视频'},
        'BLACK_AND_WHITE': {'user_reason': KycVerification.RejectionReason.BLACK_AND_WHITE,
                            'desc': '申请人上传了证件的黑白照片'},
        'SELFIE_MISMATCH': {'user_reason': KycVerification.RejectionReason.PHOTO_MISMATCHED,
                            'desc': '申请人照片（个人资料图片）与所提供证件上的照片不符'},

        # 证件相关
        'BACK_SIDE_MISSING': {'user_reason': KycVerification.RejectionReason.MISSING_BACK,
                              'desc': '证件背面缺失'},
        'FRONT_SIDE_MISSING': {'user_reason': KycVerification.RejectionReason.MISSING_FRONT,
                               'desc': '证件正面缺失'},
        'FORGERY': {'user_reason': KycVerification.RejectionReason.DOCUMENT_MANIPULATED,
                    'desc': '发现申请人有伪造行为'},
        'BAD_PROOF_OF_IDENTITY': {'user_reason': KycVerification.RejectionReason.DOCUMENT_UNSUPPORTED,
                                  'desc': '申请人上传了错误的身份证件'},
        'DIGITAL_DOCUMENT': {'user_reason': KycVerification.RejectionReason.DIGITAL_COPY,
                             'desc': '申请人上传了该文件的电子版'},
        'GRAPHIC_EDITOR': {'user_reason': KycVerification.RejectionReason.PHOTO_MANIPULATED,
                           'desc': '文档或其数据的亮度、对比度、内容等被更改'},
        'DOCUMENT_DAMAGED': {'user_reason': KycVerification.RejectionReason.PUNCHED_DOCUMENT,
                             'desc': '证件已损坏'},
        'DOCUMENT_DEPRIVED': {'user_reason': KycVerification.RejectionReason.PUNCHED_DOCUMENT,
                              'desc': '申请人相关证件已被剥夺或失效'},
        'ID_INVALID': {'user_reason': KycVerification.RejectionReason.PUNCHED_DOCUMENT,
                       'desc': '证件无效'},
        'DOCUMENT_MISSING': {'user_reason': KycVerification.RejectionReason.DOCUMENT_MISSING,
                             'desc': '视频验证中，申请人拒绝出示或没有所需证件'},
        'DOCUMENT_PAGE_MISSING': {'user_reason': KycVerification.RejectionReason.MISSING_PAGE,
                                  'desc': '证件文档的某些页面丢失'},
        'INCOMPLETE_DOCUMENT': {'user_reason': KycVerification.RejectionReason.HIDDEN_PART_DOCUMENT,
                                'desc': '证件文档中缺少某些信息，或仅部分信息可见'},
        'INCORRECT_SOCIAL_NUMBER': {'user_reason': KycVerification.RejectionReason.WRONG_ID_NUMBER,
                                    'desc': '申请人提供的社会号码（例如 SSN）不正确'},
        'PROBLEMATIC_APPLICANT_DATA': {'user_reason': KycVerification.RejectionReason.PRESET_ID_UNMATCHED,
                                       'desc': '申请人数据与证件中的数据不符'},
        'REQUESTED_DATA_MISMATCH': {'user_reason': KycVerification.RejectionReason.PRESET_ID_UNMATCHED,
                                    'desc': '提供的信息与从证件中识别的信息不匹配'},
        'SELFIE_WITH_PAPER': {'user_reason': KycVerification.RejectionReason.HANDWRITTEN_NOTE_MISMATCHED,
                              'desc': '申请人应上传特殊的自拍照（例如，带有文件和日期的自拍照）'},
        'LOW_QUALITY': {'user_reason': KycVerification.RejectionReason.NOT_READABLE_DOCUMENT,
                        'desc': '照片文件质量较低，无法得出明确的结论'},
        'SCREENSHOTS': {'user_reason': KycVerification.RejectionReason.SELFIE_IS_SCREEN_PAPER_VIDEO,
                        'desc': '申请人上传了截图'},
        'UNSATISFACTORY_PHOTOS': {'user_reason': KycVerification.RejectionReason.NOT_READABLE_DOCUMENT,
                                  'desc': '照片存在问题，例如质量差或信息被掩盖'},
        'DOCUMENT_TEMPLATE': {'user_reason': KycVerification.RejectionReason.ID_IS_SAMPLE,
                              'desc': '所提供的文件是从互联网上下载的模板'},
        'SPAM': {'user_reason': KycVerification.RejectionReason.INFO_MISMATCHED,
                 'desc': '申请人被错误创建，或为垃圾用户（提交不相关图片）'},
        'NOT_DOCUMENT': {'user_reason': KycVerification.RejectionReason.INFO_MISMATCHED,
                         'desc': '提供的文件与验证程序要求无关'},
        'EXPIRATION_DATE': {'user_reason': KycVerification.RejectionReason.DOCUMENT_EXPIRED,
                            'desc': '申请人上传了过期的文件'},
        'ADDITIONAL_DOCUMENT_REQUIRED': {'user_reason': KycVerification.RejectionReason.ADDITIONAL_DOCUMENT_REQUIRED,
                                         'desc': '需要提供额外文件以通过验证'},

        # 负面名单
        'ADVERSE_MEDIA': {'user_reason': KycVerification.RejectionReason.CREDIBILITY_PROBLEM,
                          'desc': '命中负面媒体名单'},
        'BLACKLIST': {'user_reason': KycVerification.RejectionReason.CREDIBILITY_PROBLEM,
                      'desc': '命中黑名单'},
        'BLOCKLIST': {'user_reason': KycVerification.RejectionReason.CREDIBILITY_PROBLEM,
                      'desc': '命中拒绝名单'},
        'COMPROMISED_PERSONS': {'user_reason': KycVerification.RejectionReason.CREDIBILITY_PROBLEM,
                                'desc': '命中CPP相关名单'},
        'CRIMINAL': {'user_reason': KycVerification.RejectionReason.CREDIBILITY_PROBLEM,
                     'desc': '申请人有违法行为'},
        'PEP': {'user_reason': KycVerification.RejectionReason.CREDIBILITY_PROBLEM,
                'desc': '命中PEP名单'},
        'REGULATIONS_VIOLATIONS': {'user_reason': KycVerification.RejectionReason.CREDIBILITY_PROBLEM,
                                   'desc': '违反规定'},
        'SANCTIONS': {'user_reason': KycVerification.RejectionReason.CREDIBILITY_PROBLEM,
                      'desc': '命中制裁名单'},

        # 其他
        'DUPLICATE': {'user_reason': KycVerification.RejectionReason.DOCUMENT_EXISTED,
                      'desc': '该申请人已创建，按规定不允许重复申请'},
        'WRONG_USER_REGION': {'user_reason': KycVerification.RejectionReason.NATIONALITY_ERROR,
                              'desc': '该地区/国家的申请人不允许注册'},
        'CHECK_UNAVAILABLE': {'user_reason': KycVerification.RejectionReason.PRESET_ID_UNMATCHED,
                              'desc': '数据库不可用'},
        'DB_DATA_MISMATCH': {'user_reason': KycVerification.RejectionReason.PRESET_ID_UNMATCHED,
                             'desc': '数据不匹配；无法验证文件'},
        'DB_DATA_NOT_FOUND': {'user_reason': KycVerification.RejectionReason.PRESET_ID_UNMATCHED,
                              'desc': '未找到任何数据；无法验证文件'},
        'AGE_REQUIREMENT_MISMATCH': {'user_reason': KycVerification.RejectionReason.AGE_NOT_ALLOWED,
                                     'desc': '不符合年龄要求'},
        'FRAUDULENT_LIVENESS': {'user_reason': KycVerification.RejectionReason.FRAUDSTER,
                                'desc': '申请人试图绕过活体检查'},
        'FRAUDULENT_PATTERNS': {'user_reason': KycVerification.RejectionReason.FRAUDSTER,
                                'desc': '检测到欺诈行为'},
        'INCONSISTENT_PROFILE': {'user_reason': KycVerification.RejectionReason.FRAUDSTER,
                                 'desc': '不同人的数据或文件被上传给一名申请人'},
        'UNSUPPORTED_LANGUAGE': {'user_reason': KycVerification.RejectionReason.UNSUPPORTED_VIDEO_LANGUAGE,
                                 'desc': '视频认证语言不受支持'},

        # 高级KYC相关
        'BAD_PROOF_OF_ADDRESS': {'user_reason': KycVerification.RejectionReason.BAD_PROOF_OF_ADDRESS,
                                 'desc': '申请人上传了错误的地址证明'},
        'BAD_PROOF_OF_PAYMENT': {'user_reason': KycVerification.RejectionReason.BAD_PROOF_OF_PAYMENT,
                                 'desc': '申请人上传了错误的付款证明'},
        'UNFILLED_ID': {'user_reason': KycVerification.RejectionReason.MISSING_SIGNATURE,
                        'desc': '申请人上传的文件没有签名和盖章'},
        'WRONG_ADDRESS': {'user_reason': KycVerification.RejectionReason.WRONG_ADDRESS,
                          'desc': '文件中的地址与申请人输入的地址不匹配'},
        'INCOMPATIBLE_LANGUAGE': {'user_reason': KycVerification.RejectionReason.INCOMPATIBLE_LANGUAGE,
                                  'desc': '不支持的文件语言，申请人应上传其文件的译文'},

        # 机构KYC相关
        'COMPANY_NOT_DEFINED_BENEFICIARIES': {'user_reason': KycVerification.RejectionReason.COMPANY_NOT_BENEFICIARIES,
                                              'desc': '无法识别并适当核实该实体的受益所有人'},
        'COMPANY_NOT_DEFINED_REPRESENTATIVES': {'user_reason': KycVerification.RejectionReason.COMPANY_NOT_REPRESENTATIVES,
                                                'desc': '无法识别并适当核实该实体的代表'},
        'COMPANY_NOT_DEFINED_STRUCTURE': {'user_reason': KycVerification.RejectionReason.COMPANY_NOT_STRUCTURE,
                                          'desc': '无法识别并适当核实该实体的股权结构'},
        'COMPANY_NOT_VALIDATED_BENEFICIARIES': {'user_reason': KycVerification.RejectionReason.COMPANY_NOT_BENEFICIARIES,
                                                'desc': '实体受益所有人未经验证'},
        'COMPANY_NOT_VALIDATED_REPRESENTATIVES': {'user_reason': KycVerification.RejectionReason.COMPANY_NOT_REPRESENTATIVES,
                                                  'desc': '实体的代表未经验证'},

        'EXPERIENCE_REQUIREMENT_MISMATCH': {'user_reason': KycVerification.RejectionReason.EXPERIENCE_REQUIREMENT_MISMATCH,
                                            'desc': '经验不够（例如驾驶经验不够）'},
        'THIRD_PARTY_INVOLVED': {'user_reason': KycVerification.RejectionReason.THIRD_PARTY_INVOLVED,
                                 'desc': '怀疑申请人为收取报酬创建该账户'}
    }

    pro_reason_redirect_map = {
        'proofOfAddress_listOfDocs': {'user_reason': KycVerificationPro.RejectionReason.INVALID_ADDRESS_PROOF,
                                      'desc': '用户上传了不支持的文件'},
        'proofOfAddress_fullName': {'user_reason': KycVerificationPro.RejectionReason.ADDRESS_PROOF_MISSING_FULL_NAME,
                                    'desc': '文件上未显示用户的全名'},
        'proofOfAddress_fullAddress': {'user_reason': KycVerificationPro.RejectionReason.ADDRESS_PROOF_MISSING_FULL_ADDRESS,
                                       'desc': '文件上未显示用户的完整地址'},
        'proofOfAddress_issueDate': {'user_reason': KycVerificationPro.RejectionReason.ADDRESS_PROOF_DATE_INVALID,
                                     'desc': '文件的签发日期不在3个月内'},
        'proofOfAddress_certifiedForm': {'user_reason': KycVerificationPro.RejectionReason.ADDRESS_PROOF_MISSING_SIG,
                                         'desc': '文件上缺少官方组织的印章或签名'},
        'proofOfAddress_sameDoc': {'user_reason': KycVerificationPro.RejectionReason.SAME_ADDRESS_PROOF,
                                   'desc': '用户在之前验证中已提供了相同的文件'},
        'proofOfAddress_poiPoaCountryMismatch': {'user_reason': KycVerificationPro.RejectionReason.COUNTRY_MISMATCH,
                                                 'desc': '用户在之前验证中提供的身份证件所属国家不同'},
        'proofOfAddress_dataMismatch': {'user_reason': KycVerificationPro.RejectionReason.ADDRESS_MISMATCH,
                                        'desc': '用户的地址不匹配'},
        'badPhoto_editedPoa': {'user_reason': KycVerificationPro.RejectionReason.ADDRESS_PROOF_MANIPULATED,
                               'desc': '检测到图像编辑，已要求提供新的照片'},
        'badPhoto_imageEditor': {'user_reason': KycVerificationPro.RejectionReason.ADDRESS_PROOF_MANIPULATED,
                                 'desc': '检测到图像编辑，已要求提供新的照片'},
        'badPhoto_screenshot': {'user_reason': KycVerificationPro.RejectionReason.ADDRESS_PROOF_IS_SCREENSHOT,
                                'desc': '用户上传了截图，已要求提供新的照片'},
        'badPhoto_incomplete': {'user_reason': KycVerificationPro.RejectionReason.INCOMPLETE_ADDRESS_PROOF,
                                'desc': '文件的所有部分未完全可见'},
        'fake_fakePoa': {'user_reason': KycVerificationPro.RejectionReason.ADDRESS_PROOF_MANIPULATED,
                         'desc': '地址证明不一致'},
        'badDocument_needTranslation': {'user_reason': KycVerificationPro.RejectionReason.ADDRESS_PROOF_NEED_TRANSLATION,
                                        'desc': '已要求提供文件的英文翻译'},
        'badDocument_invalidId': {'user_reason': KycVerificationPro.RejectionReason.INVALID_ADDRESS_PROOF,
                                  'desc': '身份文件无效，已要求提供新的文件'},
        'spam': {'user_reason': KycVerificationPro.RejectionReason.THIRD_PARTY_SPAM,
                 'desc': '上传文件过多，验证被拒绝'},
        'additionalPages_mainPageId': {'user_reason': KycVerificationPro.RejectionReason.INVALID_ADDRESS_PROOF,
                                  'desc': '误传身份证件，系统未识别到地址文件'},
    }

    pro_exclude_reasons = {
        'proofOfAddress', 'badPhoto', 'fake', 'badDocument', 'additionalPages',
    }

    liveness_reason_redirect_map = {
        'selfie_badFaceComparison': LivenessCheckHistory.Reason.BAD_FACE_COMPARISON,
        'selfie_livenessWatermark': LivenessCheckHistory.Reason.WATERMARK,
        'selfie_selfieLiveness': LivenessCheckHistory.Reason.SELFIE_LIVENESS,
        'selfie_selfieWithId': LivenessCheckHistory.Reason.SELFIE_WITH_ID,
        'selfie_videoSelfie': LivenessCheckHistory.Reason.VIDEO_SELFIE,
        'selfie_webcamSelfie': LivenessCheckHistory.Reason.SELFIE_LIVENESS,
        'fraudulentPatterns_deepFake': LivenessCheckHistory.Reason.FAKE,
        'fake_fakeSelfie': LivenessCheckHistory.Reason.FAKE,
        'fraudulentPatterns_livenessWithPhone': LivenessCheckHistory.Reason.WITH_PHONE,
        'fraudulentPatterns_livenessBypass': LivenessCheckHistory.Reason.FAKE,
        'fraudulentPatterns_livenessDifferentPeople': LivenessCheckHistory.Reason.MANY_PEOPLE,
        'fraudulentPatterns_livenessForced': LivenessCheckHistory.Reason.FORCED,
        'fraudulentPatterns_forcedVerification': LivenessCheckHistory.Reason.FORCED,
        'fraudulentPatterns_selfieMismatch': LivenessCheckHistory.Reason.SELFIE_MISMATCH,
    }

    liveness_exclude_reasons = {
        'selfie', 'fraudulentPatterns', 'fake'
    }

    country_to_ours = {
        "RKS": "XKX"
    }

    country_to_sumsub = {
        "XKX": "RKS"
    }

    poa_doc_type = 'UTILITY_BILL'

    @classmethod
    def get_reject_reason_desc(cls, reason_raw: list):
        return list({cls.reason_redirect_map.get(r, {}).get('desc', '其他其他未归类的拒绝理由') for r in reason_raw})

    @classmethod
    def get_pro_reject_reason_desc(cls, reason_raw: list):
        return list({cls.pro_reason_redirect_map.get(r, {}).get('desc', '其他其他未归类的拒绝理由') for r in reason_raw
                if r not in cls.pro_exclude_reasons})

    @classmethod
    def upload(cls, kyc_history_id: int):
        history = KycVerificationHistory.query.get(kyc_history_id)
        if not history:
            return
        detail = json.loads(history.detail)
        id_type = detail['id_type']
        country = detail['country']
        country = cls.country_to_sumsub.get(country, country)
        front_img_id = detail['front']
        back_img_id = detail.get('back')
        face_img_id = detail['face']
        image_files = File.query.filter(
            File.id.in_([front_img_id, back_img_id, face_img_id])
        ).all()
        image_file_map = {item.id: item for item in image_files}
        front_raw = _get_image_bytes(image_file_map.get(front_img_id))
        back_raw = _get_image_bytes(image_file_map.get(back_img_id))
        face_raw = _get_image_bytes(image_file_map.get(face_img_id))

        api_client = SumsubAPI()
        r = cls.get_result(history)
        if r and r.get('id'):
            applicant_id = r['id']
        else:
            level_name = config["SUMSUB_CONFIG"]["level_name"]
            applicant_id = api_client.create_applicant(history.request_id, level_name)

        id_doc_type = cls._get_id_doc_type(id_type)
        if back_img_id:
            api_client.add_document(applicant_id, front_raw, country, id_doc_type, 'FRONT_SIDE')
            api_client.add_document(applicant_id, back_raw, country, id_doc_type, 'BACK_SIDE')
        else:
            api_client.add_document(applicant_id, front_raw, country, id_doc_type)
        api_client.add_document(applicant_id, face_raw, country, 'SELFIE')

        api_client.request_check(applicant_id)

        history.transaction_id = applicant_id
        db.session.commit()

    @classmethod
    def request_check(cls, applicant_id: str):
        api_client = SumsubAPI()
        api_client.request_check(applicant_id)

    @classmethod
    def upload_poa(cls, kyc_pro_history_id: int):
        history = KycVerificationProHistory.query.get(kyc_pro_history_id)
        if not history:
            return
        detail = json.loads(history.detail)
        country = detail['country']
        country = cls.country_to_sumsub.get(country, country)
        address_file_ids = detail['address_file_ids']
        address_file_ids = address_file_ids.split(',')
        image_files = File.query.filter(File.id.in_(address_file_ids)).all()

        api_client = SumsubAPI()
        r = cls.get_result(history)
        if r and r.get('id'):
            applicant_id = r['id']
            inspection_id = r['inspectionId']
            cls.inactive_poa_images(applicant_id, inspection_id)
        else:
            level_name = config["SUMSUB_CONFIG"]["poa_level_name"]
            applicant_id = api_client.create_applicant(history.request_id, level_name)

        for image_file in image_files:
            image_raw = _get_image_bytes(image_file)
            api_client.add_document(applicant_id, image_raw, country, cls.poa_doc_type)

        api_client.request_check(applicant_id)

        history.transaction_id = applicant_id
        db.session.commit()

    @classmethod
    def inactive_poa_images(cls, applicant_id, inspection_id):
        api_client = SumsubAPI()
        while True:
            r = api_client.get_verification_step_result(applicant_id)
            if (poa_data := r.get('PROOF_OF_RESIDENCE', {})) is None:
                break
            image_ids = poa_data.get('imageIds', [])
            for image_id in image_ids:
                api_client.inactive_image(inspection_id, image_id)

    @classmethod
    def get_result(cls, history: KycVerificationHistory):
        if not history:
            return
        request_id = history.request_id
        return cls._get_result(request_id)

    @classmethod
    def _get_result(cls, request_id: str):
        try:
            api_client = SumsubAPI()
            return api_client.get_verification_result(request_id)
        except:     # noqa
            return {}

    @classmethod
    def get_non_doc_result(cls, history: KycVerificationHistory):
        if not history:
            return
        request_id = history.request_id
        applicant_id = history.transaction_id
        api_client = SumsubAPI()
        res = api_client.get_verification_result(request_id)
        non_doc_data = api_client.get_non_doc_data(applicant_id)
        checks = non_doc_data.get('checks', [])
        if not checks:
            return {}
        extracted = checks[-1].get('extractedDoc', {})
        step_data = api_client.get_verification_step_result(applicant_id)
        selfie_data = step_data.get('SELFIE', {})
        res.update({
            'non_doc_data': {
                'user_id': history.user_id,
                'extracted': extracted,
                'selfie': selfie_data
            }
        })
        return res

    @classmethod
    def _get_full_name(cls, first_name, middle_name, last_name):
        name_list = []
        if first_name:
            name_list.append(first_name)
        if middle_name:
            name_list.append(middle_name)
        if last_name:
            name_list.append(last_name)
        return ' '.join(name_list)

    @classmethod
    def parse_result(cls, data: dict) -> _ResultParser.KycParsedResult:
        gender_mapping = {
            'M': KycVerification.Gender.MALE.name,
            'F': KycVerification.Gender.FEMALE.name
        }
        id_type_mapping = {
            'ID_CARD': KycVerification.IDType.ID_CARD.name,
            'PASSPORT': KycVerification.IDType.PASSPORT.name,
            'DRIVERS': KycVerification.IDType.DRIVING_LICENSE.name,
        }

        def _parse_reject_labels(labels: List[str]) -> List[KycVerification.RejectionReason]:
            res = set()
            for label in labels:
                reason = cls.reason_redirect_map.get(label, {}).get('user_reason')
                if reason:
                    res.add(reason)
            if not res:
                return [KycVerification.RejectionReason.OTHER]
            else:
                return list(res)

        def get_result_info(info: dict, is_non_doc_: bool):
            id_docs = info.get('idDocs', [])
            id_doc = id_docs[-1] if id_docs else {}
            country = info.get('country')
            country = cls.country_to_ours.get(country, country)
            id_type = id_type_mapping.get(id_doc.get('idDocType'))
            if is_non_doc_:
                if country == 'NGA' and id_type == 'ID_CARD':   # NIN 属于ID_CARD
                    id_type = 'NIN'
            full_name = cls._get_full_name(id_doc.get('firstName'), id_doc.get('middleName'), id_doc.get('lastName'))
            if not full_name:
                full_name = cls._get_full_name(info.get('firstName'), info.get('middleName'), info.get('lastName'))
            return {
                'id_type': id_type,
                'id_number': id_doc.get('number', ''),
                'country': country,
                'expiry': id_doc.get('validUntil'),
                'dob': info.get('dob'),
                'gender': gender_mapping.get(info.get('gender')),
                'full_name': full_name
            }

        result = cls.KycParsedResult(
            transaction_id=data['id'],
            transaction_status=cls.TransactionStatus.DONE
        )
        review_status = data['review']['reviewStatus']
        is_non_doc = data['review']['levelName'] == config["SUMSUB_CONFIG"]["non_doc_level_name"]
        if review_status == 'init':
            if is_non_doc:
                result.transaction_status = cls.TransactionStatus.PENDING
            else:
                result.transaction_status = cls.TransactionStatus.UPLOAD_FAILED
        elif review_status in ['pending', 'prechecked', 'queued', 'onHold']:
            result.transaction_status = cls.TransactionStatus.PENDING
        elif review_status == 'completed':
            review_result = data['review']['reviewResult']
            if review_result['reviewAnswer'] == 'RED':  # GREEN RED
                reject_labels = review_result.get('rejectLabels', [])
                reasons = _parse_reject_labels(reject_labels)
                result.reasons = reasons
                result.raw_reasons = reject_labels
                reject_type = review_result.get('reviewRejectType')    # RETRY FINAL
                if reject_type == 'RETRY':
                    result.reject_type = KycVerificationHistory.RejectType.RETRY
                    if any(r in cls.required_admin_approval_reasons for r in reasons):
                        result.kyc_status = KycVerification.Status.AUDIT_REQUIRED
                    else:
                        result.kyc_status = KycVerification.Status.REJECTED
                else:
                    result.reject_type = KycVerificationHistory.RejectType.FINAL
                    result.kyc_status = KycVerification.Status.REJECTED
            else:
                result.kyc_status = KycVerification.Status.SCREENING
            result.info = get_result_info(data.get('info', {}), is_non_doc)
            if is_non_doc:
                result.images = cls.fetch_non_doc_images(data)

        else:
            result.transaction_status = cls.TransactionStatus.FAILED
            result.reasons = [KycVerification.RejectionReason.OTHER]

        return result

    @classmethod
    def fetch_non_doc_images(cls, data: dict) -> dict:
        front, face = None, None
        applicant_id = data['id']
        inspection_id = data['inspectionId']
        non_doc_data = data['non_doc_data']
        user_id = non_doc_data['user_id']
        extracted = non_doc_data['extracted']
        doc_face_images = extracted.get('images', [])
        doc_face_image_id = doc_face_images[0].get('imageId') if doc_face_images else None
        api_client = SumsubAPI()
        if doc_face_image_id:
            content_type, size, content = api_client.fetch_image(inspection_id, doc_face_image_id)
            filename = f'{applicant_id}_{doc_face_image_id}'
            front = cls._upload_image(user_id, filename, size, content_type, content)
        selfie_image_ids = non_doc_data['selfie'].get('imageIds', [])
        selfie_image_id = selfie_image_ids[0] if selfie_image_ids else None
        if selfie_image_id:
            content_type, size, content = api_client.fetch_image(inspection_id, selfie_image_id)
            filename = f'{applicant_id}_{selfie_image_id}'
            face = cls._upload_image(user_id, filename, size, content_type, content)

        return {
            'front': front,
            'face': face
        }

    @classmethod
    def _upload_image(cls, user_id, filename, size, content_type, content):
        content_type_map = {
            'image/jpeg': (File.MimeType.IMG_JPG, 'jpg'),
            'image/png': (File.MimeType.IMG_PNG, 'png'),
            'application/pdf': (File.MimeType.FILE, 'pdf'),
        }
        if content_type not in content_type_map:
            return None
        mime_type, suffix = content_type_map[content_type]
        file_key = AWSBucketPrivate.new_file_key(suffix='jpg')
        if not AWSBucketPrivate.put_file(file_key, content):
            raise
        AWSBucketPrivate.get_file_url(file_key)
        file = db.session_add_and_commit(File(
            user_id=user_id,
            bucket=AWSBucketPrivate.bucket_name,
            key=file_key,
            name=filename,
            size=size,
            provider=File.Provider.AWS,
            mime_type=mime_type
        ))
        return file.id

    @classmethod
    def parse_poa_result(cls, data: dict) -> _ResultParser.KycProParsedResult:
        def get_result_info(info: dict):
            id_docs = info.get('idDocs', [])
            id_doc = [i for i in id_docs if i['idDocType'] == cls.poa_doc_type][-1]
            country = id_doc.get('country')
            country = cls.country_to_ours.get(country, country)
            first_name = id_doc.get('firstName', '')
            middle_name = id_doc.get('middleName', '')
            last_name = id_doc.get('lastName', '')
            full_name = cls._get_full_name(first_name, middle_name, last_name)
            address = id_doc.get('address', {}).get('formattedAddress', '')
            return {
                'country': country,
                'first_name': first_name,
                'middle_name': middle_name,
                'last_name': last_name,
                'full_name': full_name,
                'address': address
            }

        def _parse_reject_labels(labels: List[str]) -> List[KycVerificationPro.RejectionReason]:
            res = set()
            for label in labels:
                user_reason = cls.pro_reason_redirect_map.get(label, {}).get('user_reason')
                if user_reason:
                    res.add(user_reason)
            if not res:
                return [KycVerificationPro.RejectionReason.OTHER]
            else:
                return list(res)

        result = cls.KycProParsedResult(
            transaction_id=data['id'],
            transaction_status=cls.TransactionStatus.DONE
        )
        review_status = data['review']['reviewStatus']
        if review_status == 'init':
            result.transaction_status = cls.TransactionStatus.UPLOAD_FAILED
        elif review_status in ['pending', 'prechecked', 'queued', 'onHold']:
            result.transaction_status = cls.TransactionStatus.PENDING
        elif review_status == 'completed':
            result.transaction_status = cls.TransactionStatus.DONE
            review_result = data['review']['reviewResult']
            if review_result['reviewAnswer'] == 'RED':  # GREEN RED
                reject_labels = review_result.get('buttonIds', [])
                reject_labels = [label for label in reject_labels if label not in cls.pro_exclude_reasons]
                reasons = _parse_reject_labels(reject_labels)
                result.reasons = reasons
                result.raw_reasons = reject_labels
                if any(r in cls.pro_required_admin_approval_reasons for r in reasons):
                    result.kyc_pro_status = KycVerificationPro.Status.AUDIT_REQUIRED
                else:
                    result.kyc_pro_status = KycVerificationPro.Status.REJECTED
                reject_type = review_result.get('reviewRejectType')  # RETRY FINAL
                if reject_type == 'RETRY':
                    result.reject_type = KycVerificationProHistory.RejectType.RETRY
                else:
                    result.reject_type = KycVerificationProHistory.RejectType.FINAL
            else:
                result.kyc_pro_status = KycVerificationPro.Status.PASSED
            result.info = get_result_info(data.get('info', {}))

        else:
            result.transaction_status = cls.TransactionStatus.FAILED
            result.reasons = [KycVerificationPro.RejectionReason.OTHER]

        return result

    @staticmethod
    def _get_id_doc_type(id_type: str):
        return {
            KycVerification.IDType.ID_CARD.name: 'ID_CARD',
            KycVerification.IDType.PASSPORT.name: 'PASSPORT',
            KycVerification.IDType.DRIVING_LICENSE.name: 'DRIVERS',
        }[id_type]

    @classmethod
    def upload_non_doc(cls, user_id: int, request_id: str, country: str, id_type: KycVerification.IDType, id_number: str, dob: str) -> str:
        level_name = config["SUMSUB_CONFIG"]["non_doc_level_name"]
        body = cls._get_non_doc_body(country, id_type.name, id_number, dob)
        r = cls._get_result(request_id)
        api_client = SumsubAPI()
        if r and r.get('id'):
            applicant_id = r['id']
        else:
            source_key = config["SUMSUB_CONFIG"]["non_doc_source_key"]
            applicant_id = api_client.create_applicant(request_id, level_name, source_key)
        try:
            r = api_client.submit_non_doc(applicant_id, body)
            if r.get('status') != 'completed':
                raise InvalidArgument(message=gettext("证件号码有误，请重新填写"))
        except BaseHTTPClient.BadResponse as e:
            try:
                e_data = json.loads(e.data)
                error_code = e_data.get('errorCode')
                error_description = e_data.get('description')
            except Exception:
                raise InvalidArgument(message=gettext("证件号码有误，请重新填写"))
            else:
                # https://docs.sumsub.com/reference/error-codes
                if error_code == 9100:  # pre-check failed
                    raise InvalidArgument(message=gettext("证件号码有误，请重新填写"))
                elif error_code == 9105:    # max number of attempts exceeded.
                    row = KycNonDocStats.query.filter(
                        KycNonDocStats.user_id == user_id,
                        KycNonDocStats.service_type == KycVerification.ServiceType.SUMSUB,
                    ).first()
                    if not row:
                        row = KycNonDocStats(
                            user_id=user_id,
                            service_type=KycVerification.ServiceType.SUMSUB,
                            fail_count=0,
                            is_forbidden=True,
                        )
                        db.session.add(row)
                    else:
                        row.is_forbidden = True
                    db.session.commit()
                    conf = KycCountryConfig.query.filter(
                        KycCountryConfig.country == country,
                    ).first()
                    supported_id_types = json.loads(conf.supported_id_types) if conf.supported_id_types else []
                    if id_type.name in supported_id_types:
                        raise KycSupportIdDocsChanged(message=gettext("证件号码有误，请重新填写"))
                    else:
                        raise KycSupportIdDocsChanged(id_type=gettext(id_type.value))
                else:
                    row = KycNonDocStats.query.filter(
                        KycNonDocStats.user_id == user_id,
                        KycNonDocStats.service_type == KycVerification.ServiceType.SUMSUB,
                    ).first()
                    if not row:
                        row = KycNonDocStats(
                            user_id=user_id,
                            service_type=KycVerification.ServiceType.SUMSUB,
                            fail_count=0,
                            is_forbidden=False,
                        )
                        db.session.add(row)
                    row.fail_count += 1
                    db.session.commit()
                    if row.fail_count >= KycNonDocStats.MAX_FAIL_COUNT:
                        conf = KycCountryConfig.query.filter(
                            KycCountryConfig.country == country,
                        ).first()
                        supported_id_types = json.loads(conf.supported_id_types) if conf.supported_id_types else []
                        if id_type.name in supported_id_types:
                            raise KycSupportIdDocsChanged(message=gettext("证件号码有误，请重新填写"))
                        else:
                            raise KycSupportIdDocsChanged(id_type=gettext(id_type.value))
                    else:
                        if error_code == 9104:  # can't find data
                            raise InvalidArgument(message=gettext("证件号码有误，请重新填写"))
                        else:
                            raise InvalidArgument(message=error_description)
        return applicant_id

    @classmethod
    def _get_non_doc_body(cls, country: str, id_type: str, id_number: str, dob: str) -> dict:
        def _get_id_doc_body(country_, id_doc_type_, id_number_, others: dict = None):
            id_doc_body = {
                'idDoc': {
                    'country': country_,
                    'idDocType': id_doc_type_,
                    'number': id_number_,
                }
            }
            if others:
                id_doc_body['idDoc'].update(others)
            return id_doc_body

        def _get_info_body(country_, id_number_):
            return {
                'info': {
                    'country': country_,
                    'tin': id_number_
                }
            }

        body_map = {
            'NGA': {
                KycVerification.IDType.NIN.name: _get_id_doc_body(country, 'ID_CARD', id_number),
                KycVerification.IDType.BVN.name: _get_info_body(country, id_number)
            },
            'BRA': {
                KycVerification.IDType.CPF.name: _get_info_body(country, id_number)
            },
            'IND': {
                KycVerification.IDType.ID_CARD.name: _get_id_doc_body(country, 'ID_CARD', id_number),
            },
            'BGD': {
                # dob YYYY-MM-DD
                KycVerification.IDType.ID_CARD.name: _get_id_doc_body(country, 'ID_CARD', id_number, {'dob': dob})
            },
            'KEN': {
                KycVerification.IDType.ID_CARD.name: _get_id_doc_body(country, 'ID_CARD', id_number),
                KycVerification.IDType.PASSPORT.name: _get_id_doc_body(country, 'PASSPORT', id_number),
            },
            'ZAF': {
                KycVerification.IDType.ID_CARD.name: _get_id_doc_body(country, 'ID_CARD', id_number),
            },
        }
        body = body_map.get(country, {}).get(id_type)
        if not body:
            raise InvalidArgument(f'Invalid Non Doc ID Type, country:{country}, id_type:{id_type}')
        return body

    @classmethod
    def get_access_token(cls, request_id: str, level_name: str, action_id: str = None) -> str:
        api_client = SumsubAPI()
        return api_client.get_access_token(request_id, level_name, action_id)

    @classmethod
    def get_non_doc_access_token(cls, request_id: str) -> str:
        level_name = config["SUMSUB_CONFIG"]["non_doc_level_name"]
        return cls.get_access_token(request_id, level_name)

    @classmethod
    def create_liveness_action(cls, applicant_id: str, action_id: str):
        level_name = config["SUMSUB_CONFIG"]["liveness_action_level_name"]
        api_client = SumsubAPI()
        return api_client.create_application_action(applicant_id, level_name, action_id)

    @classmethod
    def request_action_check(cls, applicant_action_id: str):
        api_client = SumsubAPI()
        api_client.request_action_check(applicant_action_id)

    @classmethod
    def get_action_result(cls, applicant_action_id: str):
        api_client = SumsubAPI()
        return api_client.get_action_result(applicant_action_id)

    @classmethod
    def fetch_action_image(cls, user_id: int, applicant_action_id: str, image_id: int):
        api_client = SumsubAPI()
        content_type, size, content = api_client.fetch_action_image(applicant_action_id, image_id)
        filename = f'{applicant_action_id}_{image_id}'
        image_id = cls._upload_image(user_id, filename, size, content_type, content)
        return image_id

    @classmethod
    def get_liveness_profile(cls, request_id: str):
        return cls._get_result(request_id)

    @classmethod
    def upload_liveness_profile(cls, request_id: str, kyc: KycVerification):
        # https://docs.sumsub.com/reference/import-applicant-profile-from-archive
        source_key = config["SUMSUB_CONFIG"]["liveness_source_key"]
        level_name = config["SUMSUB_CONFIG"]["liveness_level_name"]
        country = cls.country_to_sumsub.get(kyc.country, kyc.country)
        id_doc_type = {
            KycVerification.IDType.ID_CARD.name: 'ID_CARD',
            KycVerification.IDType.PASSPORT.name: 'PASSPORT',
            KycVerification.IDType.DRIVING_LICENSE.name: 'DRIVERS',
        }.get(kyc.id_type.name, 'ID_CARD')

        def get_file_name(ori_file_name):
            li = ori_file_name.split('.')
            if len(li) > 1:
                suffix = li[-1]
                return f'img.{suffix}'
            else:
                return 'img.png'

        id_number = kyc.id_number
        front = _get_image_bytes(kyc.front_img_file)
        back = _get_image_bytes(kyc.back_img_file) if kyc.back_img_file_id else None
        face = _get_image_bytes(kyc.face_img_file)
        create_date = datetime_to_str(kyc.created_at)
        review_date = datetime_to_str(now())
        zip_buf = io.BytesIO()
        with ZipFile(zip_buf, 'w') as zip_f:
            applicant_json_file_data = json.dumps({
                "externalUserId": request_id,
                "sourceKey": source_key,
                "info": {
                    "firstName": kyc.name,
                    "lastName": "",
                    "country": country
                },
                "review": {
                    "createDate": create_date,
                    "reviewDate": review_date,
                    "levelName": level_name,
                    "reviewResult": {
                        "moderationComment": "",
                        "clientComment": "",
                        "reviewAnswer": "GREEN"
                    },
                    "reviewStatus": "completed"
                }
            })
            zip_f.writestr('applicant_file/applicant.json', applicant_json_file_data)

            zip_f.writestr(f'applicant_file/doc/{get_file_name(kyc.front_img_file.name)}', front)
            front_json_data = {
                "idDocType": id_doc_type,
                "country": country,
                "number": id_number,
            }
            if kyc.back_img_file_id:
                front_json_data.update({"idDocSubType": 'FRONT_SIDE'})
            zip_f.writestr('applicant_file/doc/applicantIdDoc.json', json.dumps(front_json_data))
            if kyc.back_img_file_id:
                zip_f.writestr(f'applicant_file/doc_back_side/{get_file_name(kyc.back_img_file.name)}', back)
                zip_f.writestr('applicant_file/doc_back_side/applicantIdDoc.json', json.dumps({
                    "idDocType": id_doc_type,
                    "country": country,
                    "number": id_number,
                    "idDocSubType": 'BACK_SIDE'
                }))
            zip_f.writestr(f'applicant_file/selfie/{get_file_name(kyc.face_img_file.name)}', face)
            zip_f.writestr('applicant_file/selfie/applicantIdDoc.json', json.dumps({
                "idDocType": "SELFIE",
                "country": country,
                "number": id_number,
            }))
        zip_data = zip_buf.getvalue()
        api_client = SumsubAPI()
        applicant_id = api_client.import_applicant_file(zip_data)
        return applicant_id

    @classmethod
    def get_liveness_reasons(cls, reject_labels: []) -> List[LivenessCheckHistory.Reason]:
        reject_labels = [label for label in reject_labels if label not in cls.liveness_exclude_reasons]
        if not reject_labels:
            return [LivenessCheckHistory.Reason.OTHER]
        convert_reasons = set()
        for label in reject_labels:
            reason = cls.liveness_reason_redirect_map.get(label)
            if reason:
                convert_reasons.add(reason)
        if convert_reasons:
            return list(convert_reasons)
        else:
            return [LivenessCheckHistory.Reason.OTHER]


class SumsubAPI(BaseHTTPClient):

    REQUEST_TIMEOUT = 60

    def __init__(self):
        conf: Dict = config["SUMSUB_CONFIG"]
        url = conf["url"]
        super().__init__(url)
        self.secret_key = conf["secret_key"]
        self.app_token = conf["app_token"]

    def _sign_request(self, request: requests.Request) -> requests.PreparedRequest:
        prepared_request = request.prepare()
        now = int(time.time())
        method = request.method.upper()
        path_url = prepared_request.path_url  # includes encoded query params
        # could be None so we use an empty **byte** string here
        body = b'' if prepared_request.body is None else prepared_request.body
        if isinstance(body, str):
            body = body.encode('utf-8')
        data_to_sign = str(now).encode('utf-8') + method.encode('utf-8') + path_url.encode('utf-8') + body
        # hmac needs bytes
        signature = hmac.new(
            self.secret_key.encode('utf-8'),
            data_to_sign,
            digestmod=hashlib.sha256
        )
        prepared_request.headers['X-App-Token'] = self.app_token
        prepared_request.headers['X-App-Access-Ts'] = str(now)
        prepared_request.headers['X-App-Access-Sig'] = signature.hexdigest()
        return prepared_request

    def _do_request(self, req):
        resp = self.session.send(req, timeout=self.REQUEST_TIMEOUT)
        status_code = resp.status_code
        if status_code // 100 == 2:
            return resp
        self._log_error_response(resp)
        raise self.BadResponse(status_code, resp.content.decode())

    def create_applicant(self, request_id, level_name, source_key=None):
        body = {'externalUserId': request_id}
        if source_key:
            body.update({'sourceKey': source_key})
        params = {'levelName': level_name}
        headers = {
            'Content-Type': 'application/json',
            'Content-Encoding': 'utf-8'
        }
        url = url_join(self.base_url, '/resources/applicants', levelName=level_name)
        req = self._sign_request(
            requests.Request(
                'POST', url,
                params=params,
                data=json.dumps(body),
                headers=headers
            )
        )
        response = self._do_request(req).json()
        applicant_id = response['id']
        return applicant_id

    def add_document(self, applicant_id, file_bytes, country, id_doc_type, id_doc_sub_type=None):
        metadata = {"idDocType": id_doc_type, "country": country}
        if id_doc_sub_type:
            metadata.update({"idDocSubType": id_doc_sub_type})
        payload = {"metadata": json.dumps(metadata)}
        url = url_join(self.base_url, f'/resources/applicants/{applicant_id}/info/idDoc')
        req = self._sign_request(
            requests.Request(
                'POST', url,
                data=payload,
                files=[('content', file_bytes)]
            )
        )
        response = self._do_request(req).json()
        return response

    def request_check(self, applicant_id):
        url = url_join(self.base_url, f'/resources/applicants/{applicant_id}/status/pending')
        req = self._sign_request(requests.Request('POST', url))
        response = self._do_request(req).json()
        return response

    def get_applicant_status(self, applicant_id):
        url = url_join(self.base_url, f'/resources/applicants/{applicant_id}/requiredIdDocsStatus')
        req = self._sign_request(requests.Request('GET', url))
        response = self._do_request(req).json()
        return response

    def get_verification_result(self, request_id):
        url = url_join(self.base_url, f'/resources/applicants/-;externalUserId={str(request_id)}/one')
        req = self._sign_request(requests.Request('GET', url))
        response = self._do_request(req).json()
        return response

    def get_verification_step_result(self, applicant_id):
        url = url_join(self.base_url, f'/resources/applicants/{applicant_id}/requiredIdDocsStatus')
        req = self._sign_request(requests.Request('GET', url))
        response = self._do_request(req).json()
        return response

    def inactive_image(self, inspection_id, image_id):
        url = url_join(self.base_url, f'resources/inspections/{inspection_id}/resources/{image_id}')
        resp = self._sign_request(requests.Request('DELETE', url))
        response = self.session.send(resp, timeout=self.REQUEST_TIMEOUT)
        return response.json()

    def get_access_token(self, request_id, level_name, action_id=None):
        url = url_join(self.base_url, f'resources/accessTokens')
        params = {'userId': request_id, 'ttlInSecs': '600', 'levelName': level_name}
        if action_id:
            params.update({'externalActionId': action_id})
        headers = {
            'Content-Type': 'application/json',
            'Content-Encoding': 'utf-8'
        }
        req = self._sign_request(requests.Request('POST', url, params=params, headers=headers))
        response = self._do_request(req).json()
        token = response['token']
        return token

    def submit_non_doc(self, applicant_id, body):
        url = url_join(self.base_url, f'resources/applicants/{applicant_id}/ekyc/submit')
        headers = {
            'Content-Type': 'application/json',
            'Content-Encoding': 'utf-8'
        }
        req = self._sign_request(
            requests.Request(
                'POST', url,
                data=json.dumps(body),
                headers=headers
            )
        )
        response = self._do_request(req).json()
        return response

    def get_non_doc_data(self, applicant_id):
        url = url_join(self.base_url, f'resources/checks/latest')
        params = {'type': 'E_KYC_CHECK', 'applicantId': applicant_id}
        req = self._sign_request(requests.Request('GET', url, params=params))
        response = self._do_request(req).json()
        return response

    def fetch_image(self, inspection_id, image_id):
        url = url_join(self.base_url, f'resources/inspections/{inspection_id}/resources/{image_id}')
        req = self._sign_request(requests.Request('GET', url))
        response = self._do_request(req)
        content_type = response.headers.get('Content-Type')
        size = int(response.headers.get('Content-Length', 0))
        return content_type, size, response.content

    def create_application_action(self, applicant_id, level_name, action_id):
        url = url_join(self.base_url, f'resources/applicantActions/-/forApplicant/{applicant_id}')
        params = {'levelName': level_name}
        body = {
            'externalActionId': action_id,
        }
        headers = {
            'Content-Type': 'application/json',
            'Content-Encoding': 'utf-8'
        }
        req = self._sign_request(requests.Request('POST', url, params=params, data=json.dumps(body), headers=headers))
        response = self._do_request(req).json()
        applicant_action_id = response['id']
        return applicant_action_id

    def request_action_check(self, applicant_action_id):
        url = url_join(self.base_url, f'/resources/applicantActions/{applicant_action_id}/review/status/pending')
        req = self._sign_request(requests.Request('POST', url))
        response = self._do_request(req).json()
        return response

    def get_action_result(self, applicant_action_id):
        url = url_join(self.base_url, f'/resources/applicantActions/{applicant_action_id}/one')
        req = self._sign_request(requests.Request('GET', url))
        response = self._do_request(req).json()
        return response

    def import_applicant_file(self, file_bytes):
        url = url_join(self.base_url, f'/resources/applicants/-/applicantImport')
        headers = {"Content-Type": "application/zip"}
        req = self._sign_request(
            requests.Request('POST', url, data=file_bytes, headers=headers)
        )
        response = self._do_request(req).json()
        applicant_id = response['id']
        return applicant_id

    def fetch_action_image(self, applicant_action_id, image_id):
        url = url_join(self.base_url, f'resources/applicantActions/{applicant_action_id}/images/{image_id}')
        req = self._sign_request(requests.Request('GET', url))
        response = self._do_request(req)
        content_type = response.headers.get('Content-Type')
        size = int(response.headers.get('Content-Length', 0))
        return content_type, size, response.content


class LivenessCheckBusiness:
    VERIFICATION_EXPIRE_TIME = 3600 * 24
    PROCESS_EXPIRE_TIME = 3600 * 30

    required_admin_approval_reasons = {
        LivenessCheckHistory.Reason.BAD_FACE_COMPARISON,
        LivenessCheckHistory.Reason.WATERMARK,
        LivenessCheckHistory.Reason.VIDEO_SELFIE,
        LivenessCheckHistory.Reason.WITH_PHONE,
    }

    @classmethod
    def get_request_id(cls, kyc_id: int) -> str:
        data = 'CoinEx To Sumsub Liveness' + str(kyc_id)
        return hashlib.sha256(data.encode('utf-8')).hexdigest()

    @classmethod
    def get_action_id(cls) -> str:
        return str(uuid.uuid4())

    @classmethod
    def upload_kyc_data(cls, user_id: int):
        user: User = User.query.get(user_id)
        if not user:
            raise InvalidArgument(message='该用户不存在')
        if user.user_type == User.UserType.SUB_ACCOUNT:
            raise InvalidArgument(message='该用户为子账号类型')
        if user.kyc_status != User.KYCStatus.PASSED:
            raise InvalidArgument(message='用户未完成初级KYC')
        kyc = user.kyc_verification
        if not kyc or not kyc.front_img_file_id or not kyc.face_img_file_id:
            raise InvalidArgument(message='该用户没有完整的KYC信息，无法进行生物识别验证')
        if not CountrySettings(user.kyc_country).allow_liveness:
            raise InvalidArgument(message='用户初级KYC认证的国家不支持生物识别验证')

        profile = LivenessCheckProfile.query.filter(
            LivenessCheckProfile.kyc_id == kyc.id
        ).first()
        if not profile:
            request_id = LivenessCheckBusiness.get_request_id(kyc.id)
            r = SumsubClient.get_liveness_profile(request_id)
            if r and r.get('id'):
                applicant_id = r['id']
            else:
                applicant_id = SumsubClient.upload_liveness_profile(request_id, kyc)
            profile = LivenessCheckProfile(
                user_id=kyc.user_id,
                kyc_id=kyc.id,
                request_id=request_id,
                transaction_id=applicant_id,
            )
            db.session.add(profile)
            db.session.commit()
        return kyc

    @classmethod
    def update_check_result(cls, history: LivenessCheckHistory):
        res = SumsubClient.get_action_result(history.transaction_id)
        review = res.get('review', {})
        if review.get('reviewStatus') == 'completed':
            face_img_file_id = cls._fetch_face_image(history.user_id, res)
            if face_img_file_id:
                history.face_img_file_id = face_img_file_id
            review_result = review.get('reviewResult', {})
            if review_result.get('reviewAnswer') == 'RED':
                reject_labels = review_result.get('buttonIds', [])
                reasons = SumsubClient.get_liveness_reasons(reject_labels)
                if history.business not in LivenessCheckHistory.REQUIRE_INSTANT_CHECK_BUSINESSES \
                        and any(r in cls.required_admin_approval_reasons for r in reasons):
                    history.status = LivenessCheckHistory.Status.MANUAL_AUDIT_REQUIRED
                else:
                    history.status = LivenessCheckHistory.Status.REJECTED
                history.reason = ','.join([r.name for r in reasons])
            else:
                history.status = LivenessCheckHistory.Status.PASSED
            cls.update_business(history)
            db.session.commit()

    @classmethod
    def _fetch_face_image(cls, user_id: int, res: dict):
        images = res.get('images', [])
        if not images:
            return
        image_id = images[0]['imageId']
        return SumsubClient.fetch_action_image(user_id, res['id'], image_id)

    @classmethod
    def update_business(cls, history: LivenessCheckHistory, op_user_id: int = None):
        from app.business.security import SecurityBusiness, reset_security_info

        if not history.business_id:
            return
        if history.status not in [LivenessCheckHistory.Status.PASSED, LivenessCheckHistory.Status.REJECTED]:
            return
        user_id = history.user_id
        is_reject = history.status != LivenessCheckHistory.Status.PASSED
        if history.business == LivenessCheckHistory.Business.RESET_SECURITY:
            row = SecurityResetApplication.query.filter(
                SecurityResetApplication.id == history.business_id,
                SecurityResetApplication.reset_type.in_(SecurityResetApplication.ADMIN_SECURITY_RESET_LIST)
            ).first()
            if not row:
                current_app.logger.warning(f'liveness update_business cant find row: {history.business} {history.business_id}')
                return
            if row.balance_usd >= SecurityResetApplication.LIVENESS_AUDIT_BALANCE_USD_THRESHOLD:
                row.status = SecurityResetApplication.StatusType.CREATED
                db.session.commit()
            else:
                if is_reject:
                    row.status = SecurityResetApplication.StatusType.REJECTED
                    if history.custom_admin_reason:
                        row.is_custom_reason = True
                        row.custom_reason = history.custom_admin_reason
                    else:
                        row.is_custom_reason = False
                        row.reason = ','.join([
                            SecurityResetApplication.Reason(v).name
                            for v in [LivenessCheckHistory.Reason[v].value for v in history.reason.split(",")]
                        ]) if history.reason else None
                    db.session.commit()
                    SecurityBusiness.security_reset_reject_notice(row)
                else:
                    row.status = SecurityResetApplication.StatusType.PASSED
                    user = User.query.get(user_id)
                    reset_security_info(user, row.reset_type, SecurityToolHistory.OpRole.LIVENESS,
                                        row.new_email, from_api=False)
                    db.session.commit()
                    SecurityBusiness.security_reset_pass_notice(row)

        elif history.business == LivenessCheckHistory.Business.UNFREEZE_ACCOUNT:
            row = SecurityResetApplication.query.filter(
                SecurityResetApplication.id == history.business_id,
                SecurityResetApplication.reset_type.in_(SecurityResetApplication.ADMIN_UNFREEZE_RESET_LIST)
            ).first()
            if not row:
                current_app.logger.warning(f'liveness update_business cant find row: {history.business} {history.business_id}')
                return
            ori_status = row.status.value
            row.status = SecurityResetApplication.StatusType.CREATED
            db.session.commit()
            new_status = row.status.value
            detail = f'从【{ori_status}】变更为【{new_status}】'
            admin_user_id = op_user_id or user_id
            UnfreezeOperationLog.add(user_id, UnfreezeOperationLog.Type.STATUS, detail, admin_user_id, row.id)
