# -*- coding: utf-8 -*-
import abc
import json
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal
from typing import List

from flask import current_app
from sqlalchemy import func
from sqlalchemy.exc import IntegrityError

from app.business import db, PriceManager, ServerClient, ServerResponseCode, UserSettings, CacheLock, \
    LockKeys, get_user_withdrawable_amount, BusinessSettings
from app.models import User, RedPacket, CBoxTheme, CBoxOnlyNewUserConf, \
    RedPacketHistory, CBoxCode, RedPacketWhiteListUsers
from app.utils import today_datetime, amount_to_str, \
    now, AmountType, ConfigMode
from .constants import COIN_PLACES, LIMIT_AMOUNT, PACK_MAX_AMOUNT, RED_PACKET_SEND_BUSINESS
from .encryption import AESGenerator
from .randompack import generator_luck_package, generator_normal_package
from ..clients.biz_monitor import biz_monitor
from ..wallet import add_withdrawal_amount_to_cache, check_withdrawal_amount_exceeded
from ... import Language
from ...assets import get_asset_config, is_pre_asset
from ...caches import RedPacketGrabCache, CBoxSendHotCoinsCache, CBoxCanTransferCoinCache
from ...caches.operation import CBoxThemeCache
from ...common import BalanceBusiness, IncreaseEvent
from ...exceptions import UnboundMobileOrTwoFactor, CoinNotSupportCBox, \
    InternalServerError, InsufficientBalance, InvalidArgument, \
    CBoxCodeAlreadyUsed, CBoxNotAllowed, CBoxCountLimit, CBoxAmountLimit, \
    CBoxAmountNotEnough, CBoxRateError, WithdrawalForbidden
from ...utils.rand import new_c_box_code


def get_quantize(amount):
    for v in list(reversed(range(-10, 20))):
        q = Decimal(10) ** -v
        if amount <= q:
            return q
    raise CBoxRateError


def get_coin_rate(coin_type):
    rates_cache = PriceManager.assets_to_usd()
    if (rate := rates_cache.get(coin_type, Decimal())) <= Decimal():
        raise CBoxRateError
    return rate


def get_per_amount_count(coin_type):
    rate = get_coin_rate(coin_type)
    per_amount = Decimal("{:.8f}".format(LIMIT_AMOUNT / Decimal(rate)))
    return get_quantize(per_amount)


def get_all_coin_types_rate():
    rates_cache = PriceManager.assets_to_usd()
    data = {}
    coin_types = CBoxCanTransferCoinCache().get_coins()
    for coin_type in coin_types:
        if coin_type not in rates_cache:
            continue
        rate = Decimal(rates_cache[coin_type])
        if rate <= Decimal('0'):
            continue
        rate = rates_cache[coin_type]
        per_amount = Decimal("{:.8f}".format(LIMIT_AMOUNT / Decimal(rate)))
        data[coin_type] = get_quantize(per_amount)
    return data


def get_send_hot_coins() -> List:
    """最近一个月发送的热门币种"""
    cache = CBoxSendHotCoinsCache()
    return cache.get_coins()


def get_today_red_packet_amount(user_id):
    now_date = today_datetime()
    query = RedPacket.query.filter(
        RedPacket.user_id == user_id,
        RedPacket.created_at >= now_date,
        RedPacket.created_at < now_date + timedelta(days=1),
        RedPacket.status.in_(
            [RedPacket.Status.CREATED, RedPacket.Status.RISK,
             RedPacket.Status.PASSED,
             RedPacket.Status.FINISHED])
    ).with_entities(
        func.sum(RedPacket.total_usd).label('today_send_usd')
    ).first()
    return query.today_send_usd if query and query.today_send_usd else Decimal()


def get_rest_red_packet_limit(user_id):
    send_usd = get_today_red_packet_amount(user_id)
    limit = user_send_limit_usd(user_id)
    if send_usd >= limit:
        return Decimal()
    else:
        return limit - send_usd


def user_send_limit_usd(user_id):
    white_list_user = RedPacketWhiteListUsers.query.filter(
        RedPacketWhiteListUsers.user_id == user_id,
        RedPacketWhiteListUsers.status == RedPacketWhiteListUsers.Status.ACTIVE
    ).first()
    if white_list_user:
        res = white_list_user.daily_send_amount_limit
    else:
        res = BusinessSettings.c_box_daily_max_usd
    return res


def get_user_send_info(user_id, coin_type, start_time, end_time, page, limit):
    query = RedPacket.query.filter(
        RedPacket.user_id == user_id,
        RedPacket.status.in_([
                             RedPacket.Status.PASSED,
                             RedPacket.Status.FINISHED,
                             RedPacket.Status.EXPIRED,
                             ])
    ).order_by(RedPacket.created_at.desc())
    if coin_type:
        query = query.filter(
            RedPacket.asset == coin_type
        )
    if start_time:
        query = query.filter(
            RedPacket.created_at >= start_time,
        )
    if end_time:
        query = query.filter(
            RedPacket.created_at <= end_time
        )

    pagination = query.paginate(page, limit, error_out=False)
    page_data = [{
        "total_amount": amount_to_str(v.total_amount, COIN_PLACES),
        "coin_type": v.asset,
        "status": v.status.value,
        "receive_type": v.receive_type.value,
        "count": v.count,
        "time": int(v.created_at.timestamp()),
        "_id": AESGenerator(
            RED_PACKET_SEND_BUSINESS).encrypt(str(v.id)),
    } for v in pagination.items]
    return dict(
        page=page,
        limit=limit,
        total=pagination.total,
        has_next=pagination.has_next,
        has_prev=pagination.has_prev,
        total_pages=pagination.pages,
        data=page_data
    )


def check_user_in_whitelist(user_id):
    if RedPacketWhiteListUsers.query.filter(
        RedPacketWhiteListUsers.user_id == user_id,
        RedPacketWhiteListUsers.status == RedPacketWhiteListUsers.Status.ACTIVE
    ).first():
        return True
    return False


def get_c_box_themes(lang: str, version=None):
    return CBoxThemeCache(Language(lang)).read_themes(version)


def user_has_new_user_conf(user_id):
    conf = CBoxOnlyNewUserConf.query.filter(
        CBoxOnlyNewUserConf.user_id == user_id,
        CBoxOnlyNewUserConf.status == CBoxOnlyNewUserConf.Status.VALID
    ).first()
    return bool(conf)


class CBoxCodeManager(object):

    @classmethod
    def check_code_available(cls, code: str, user_id: int):
        c_code = CBoxCode.query.filter(CBoxCode.code == code).first()
        if not c_code:
            return True
        if c_code.status == CBoxCode.Status.CREATED and (
                user_id == c_code.user_id or c_code.user_id is None):
            return True
        return False

    @classmethod
    def generate_c_box_code(cls, user_id: int) -> str:
        with CacheLock(LockKeys.generate_c_code(), ttl=5, wait=True):
            c_code = CBoxCode.query.filter(
                CBoxCode.status == CBoxCode.Status.CREATED,
                CBoxCode.user_id == user_id
            ).first()
            if c_code:
                return c_code.code
            c_code = CBoxCode.query.filter(
                CBoxCode.status == CBoxCode.Status.CREATED,
                CBoxCode.user_id.is_(None)
            ).first()
            if not c_code:
                return cls.generate_one_code_for_user(user_id)
            c_code.user_id = user_id
            db.session.commit()
            return c_code.code

    @classmethod
    def generate_one_code_for_user(cls, user_id) -> str:
        for _ in range(10000):
            code = new_c_box_code()
            c_code = CBoxCode.query.filter(
                CBoxCode.code == code).first()
            if c_code:
                continue
            else:
                c_code = CBoxCode(
                    code=code,
                    user_id=user_id,
                    status=CBoxCode.Status.CREATED
                )
                db.session_add_and_commit(c_code)
                return c_code.code

    @classmethod
    def pre_generate(cls, count: int = 20000):
        added_code = set()
        while len(added_code) < count:
            new_codes = set(map(lambda _: new_c_box_code(), range(1000)))
            query_ = CBoxCode.query.filter(
                CBoxCode.code.in_(new_codes)
            ).all()
            unused_codes = new_codes - {i.code for i in query_}
            try:
                for code in unused_codes:
                    db.session.add(
                        CBoxCode(code=code)
                    )
                db.session.commit()
                added_code.update(unused_codes)
            except Exception:  # 可能在插入时有其他人插入了相同的code，导致插入失败，忽略即可
                continue


class RuleChecker(object):
    rule_list = ['permission', 'coin', 'count', 'amount', 'auth', 'balance', 'withdraw',
                 'theme_id']

    def __init__(self, user: User, coin_type: str, total_amount: AmountType,
                 count: int, theme_id: int):
        self.user = user
        self.user_id = user.id
        self.coin_type = coin_type
        self.total_amount = total_amount
        self.count = count
        self.theme_id = theme_id

    def verify(self):
        for rule in self.rule_list:
            getattr(self, 'check_{rule}'.format(rule=rule))()
        return True

    def check_theme_id(self):
        if not CBoxTheme.query.filter(
                CBoxTheme.id == self.theme_id,
                CBoxTheme.status == CBoxTheme.Status.VALID).first():
            raise InvalidArgument(message='unsupported c-box theme!')

    def check_permission(self):
        setting = UserSettings(self.user.id)
        if not setting.red_packet_enabled:
            raise CBoxNotAllowed
        if not setting.withdrawals_enabled:
            raise WithdrawalForbidden

    def check_count(self):
        if self.count <= 0 or self.count > PACK_MAX_AMOUNT:
            raise CBoxCountLimit(count=PACK_MAX_AMOUNT)

    def check_amount(self):
        per_amount = get_per_amount_count(self.coin_type)
        if per_amount > self.total_amount / (Decimal('1.0') * self.count):
            raise CBoxAmountLimit(per_amount=per_amount, coin_type=self.coin_type)

    def check_auth(self):
        if not self.user.has_2fa:
            raise UnboundMobileOrTwoFactor

    def check_coin(self):
        if not get_asset_config(self.coin_type, mode=ConfigMode.REAL_TIME).local_transfers_enabled:
            raise CoinNotSupportCBox
        if is_pre_asset(self.coin_type):
            raise CoinNotSupportCBox

    def check_balance(self):
        withdrawable = get_user_withdrawable_amount(self.user_id, self.coin_type)
        withdrawable_amount = withdrawable['withdrawable_asset']
        if withdrawable_amount < self.total_amount:
            raise InsufficientBalance
        send_usd = get_today_red_packet_amount(self.user_id)
        limit_usd = user_send_limit_usd(self.user_id)
        packet_usd = get_coin_rate(self.coin_type) * self.total_amount
        if limit_usd-send_usd < packet_usd:
            raise CBoxAmountNotEnough(limit_usd=limit_usd, send_usd=send_usd)

    def check_withdraw(self):
        check_withdrawal_amount_exceeded(self.user_id, self.coin_type, self.total_amount)


class EmailCBoxRuleChecker(RuleChecker):
    rule_list = ['only_new_user', 'permission', 'coin', 'count', 'amount', 'auth',
                 'withdraw', 'theme_id', 'balance']

    def __init__(self, user: User, coin_type: str, total_amount: AmountType,
                 count: int, theme_id: int, only_new_user: bool):
        super().__init__(user, coin_type, total_amount, count, theme_id)
        self.only_new_user = only_new_user

    def check_only_new_user(self):
        if not self.only_new_user:
            return
        if not user_has_new_user_conf(self.user_id):
            raise InvalidArgument


class CodeCBoxRuleChecker(RuleChecker):
    rule_list = ['code', 'permission', 'coin', 'count', 'amount', 'auth',
                 'withdraw', 'theme_id', 'balance']

    def __init__(self, user: User, coin_type: str, total_amount: AmountType,
                 count: int, theme_id: int, code: str):
        super().__init__(user, coin_type, total_amount, count, theme_id)
        self.code = code

    def check_code(self):
        c_code = CBoxCode.query.filter(CBoxCode.code == self.code.upper()).first()
        if not c_code:
            return True
        elif c_code.status == CBoxCode.Status.CREATED and (c_code.user_id == self.user.id
                                                           or c_code.user_id is None):
            return True
        else:  # 被删除的口令也被认为不可用
            raise CBoxCodeAlreadyUsed


class RedPacketUtils(object):

    def __init__(self, coin_type, amount):
        self.coin_type = coin_type
        self.check_amount(amount)
        self.amount = Decimal(amount)
        self.low_value = False
        self.per_amount = self.limit_amount()

    def coin_rate(self):
        return get_coin_rate(self.coin_type)

    def limit_amount(self):
        rate = self.coin_rate()
        per_amount = Decimal('{:.8f}'.format(LIMIT_AMOUNT / rate))
        if per_amount >= Decimal('1'):
            self.low_value = True
        return get_quantize(per_amount)

    def verify(self):
        if self.low_value and self.amount % Decimal('1') > Decimal():
            raise InvalidArgument

    def cut_amount(self):
        return {
            'low_value': self.low_value,
            'package': {
                'per_amount': self.per_amount,
                'total': self.amount // self.per_amount,
            },
            'rest': self.amount % self.per_amount
        }

    @classmethod
    def check_amount(cls, amount):
        if Decimal(amount) <= Decimal():
            raise InvalidArgument


class SendCBoxManager(metaclass=abc.ABCMeta):

    def send(self):
        with CacheLock(self.lock_key, wait=False):
            db.session.rollback()
            self.run_check()
            red_packet = self.create_red_packet()
            self.handle_balance(red_packet)
            self.generate_receive_histories(red_packet)
            self.monitor_event()
            return self.fmt_red_packet_info(red_packet)

    @property
    @abc.abstractmethod
    def lock_key(self) -> str:
        pass

    @abc.abstractmethod
    def run_check(self):
        pass

    @abc.abstractmethod
    def create_red_packet(self):
        pass

    @staticmethod
    def handle_balance(red_packet: RedPacket):
        balance_server = ServerClient()
        try:
            balance_server.add_user_balance(
                red_packet.user_id,
                red_packet.asset,
                str(-red_packet.total_amount),
                BalanceBusiness.RED_PACKET.value,
                red_packet.id,
                {'remark': 'send red packet'}
            )
            red_packet.status = RedPacket.Status.DEDUCTED
            db.session.commit()

        except balance_server.BadResponse as e:
            if e.code == ServerResponseCode.INSUFFICIENT_BALANCE:
                red_packet.status = RedPacket.Status.CANCELLED
                db.session.commit()
                raise InsufficientBalance
            error_msg = f'red packet {red_packet.id} ' \
                        f'user {red_packet.user_id} ' \
                        f'amount {red_packet.total_amount} ' \
                        f'coin {red_packet.asset} deducted error'
            current_app.logger.error(error_msg)
            red_packet.status = RedPacket.Status.FAILED
            db.session.commit()
            raise InternalServerError

    @staticmethod
    def generate_receive_histories(red_packet: RedPacket):
        utils = RedPacketUtils(red_packet.asset, red_packet.total_amount)
        if red_packet.packet_type == RedPacket.PacketType.NORMAL:
            cut_dict = {
                'package': {
                    'per_amount': red_packet.total_amount / red_packet.count,
                }
            }
            amount_list = generator_normal_package(cut_dict, red_packet.count)
        else:
            cut_dict = utils.cut_amount()
            amount_list = generator_luck_package(cut_dict, red_packet.count)
        valid_days = red_packet.valid_days
        red_packet.effective_at = now()
        red_packet.expired_at = now() + timedelta(days=valid_days)
        red_packet.status = RedPacket.Status.PASSED
        db.session.flush()
        records = [RedPacketHistory(
            # 红包生效时间
            effective_at=red_packet.effective_at,
            # 红包过期时间
            expired_at=red_packet.expired_at,
            red_packet_id=red_packet.id,
            asset=red_packet.asset,
            # 金额
            amount=packet_amount,
            status=RedPacketHistory.Status.CREATED
        ) for packet_amount in amount_list]
        db.session.add_all(records)
        db.session.commit()
        cache = RedPacketGrabCache(red_packet.id)
        cache.delete()
        for record in records:
            cache.rpush(json.dumps({
                'coin_type': record.asset,
                'history_id': record.id,
                'amount': amount_to_str(record.amount, COIN_PLACES)
            }))
        cache.expire(valid_days*86400)
        add_withdrawal_amount_to_cache(red_packet.user_id, red_packet.asset, red_packet.total_amount)

    @abc.abstractmethod
    def fmt_red_packet_info(self, red_packet):
        pass

    def monitor_event(self):
        pass


class SendEmailCBoxManager(SendCBoxManager):

    def __init__(self, user: User, red_packet_type: RedPacket.PacketType,
                 coin_type: str, total_amount: AmountType, count: int,
                 greeting: str, theme_id: int, only_new_user: bool, valid_days: int):
        self.user = user
        self.red_packet_type = red_packet_type
        self.coin_type = coin_type
        self.total_amount = total_amount
        self.count = count
        self.greeting = greeting
        self.theme_id = theme_id
        self.only_new_user = only_new_user
        self.valid_days = valid_days

    @property
    def lock_key(self) -> str:
        return LockKeys.send_red_packet(self.user.id)

    def run_check(self):
        checker = EmailCBoxRuleChecker(
            self.user, self.coin_type, self.total_amount,
            self.count, self.theme_id, self.only_new_user
        )
        checker.verify()

    def create_red_packet(self) -> RedPacket:
        red_packet = RedPacket(
            user_id=self.user.id,
            user_name=self.user.email,
            asset=self.coin_type,
            total_amount=self.total_amount,
            total_usd=get_coin_rate(self.coin_type) * self.total_amount,
            count=self.count,
            greet=self.greeting,
            status=RedPacket.Status.CREATED,
            packet_type=self.red_packet_type,
            receive_type=RedPacket.ReceiveType.EMAIL,
            only_new_user=self.only_new_user,
            theme_id=self.theme_id,
            valid_days=self.valid_days
        )
        db.session.add(red_packet)
        db.session.commit()
        return red_packet

    def fmt_red_packet_info(self, red_packet):
        theme = CBoxTheme.query.get(red_packet.theme_id)
        return dict(
            account=self.user.name_displayed,
            amount=amount_to_str(red_packet.total_amount, COIN_PLACES),
            coin_type=red_packet.asset,
            theme=CBoxTheme.img_src(theme.cover_file_key),
            theme_style=theme.style.name,
            greeting=red_packet.greet,
            c_box_id=AESGenerator(
                RED_PACKET_SEND_BUSINESS).encrypt(str(red_packet.id))
        )

    def monitor_event(self):
        biz_monitor.increase_counter(IncreaseEvent.SEND_C_BOX_COUNT, with_source=True)
        biz_monitor.increase_counter(IncreaseEvent.SEND_EMAIL_C_BOX_COUNT, with_source=True)


class SendCodeCBoxManager(SendCBoxManager):

    def __init__(self, user: User, red_packet_type: RedPacket.PacketType,
                 coin_type: str, total_amount: AmountType, count: int,
                 greeting: str, theme_id: int, code: str, valid_days: int):
        self.user = user
        self.red_packet_type = red_packet_type
        self.coin_type = coin_type
        self.total_amount = total_amount
        self.count = count
        self.greeting = greeting
        self.theme_id = theme_id
        self.code = code
        self.valid_days = valid_days

    @property
    def lock_key(self) -> str:
        return LockKeys.send_red_packet(self.user.id)

    def run_check(self):
        checker = CodeCBoxRuleChecker(
            self.user, self.coin_type, self.total_amount,
            self.count, self.theme_id, self.code
        )
        checker.verify()

    def create_red_packet(self) -> RedPacket:
        c_code = CBoxCode.query.filter(CBoxCode.code == self.code.upper()).first()
        if not c_code:
            try:
                c_code = CBoxCode(
                    code=self.code.upper(),
                    user_id=self.user.id,
                    status=CBoxCode.Status.CREATED
                )
                db.session_add_and_commit(c_code)
            except IntegrityError:  # 已经有别的用户先使用了该口令，导致插入重复报错
                raise CBoxCodeAlreadyUsed
        else:
            if c_code.status != CBoxCode.Status.CREATED:
                raise CBoxCodeAlreadyUsed
            if c_code.user_id is None:
                c_code.user_id = self.user.id
                db.session.commit()
            if c_code.user_id != self.user.id:
                raise CBoxCodeAlreadyUsed
        c_code.status = CBoxCode.Status.USED
        red_packet = RedPacket(
            user_id=self.user.id,
            user_name=self.user.email,
            asset=self.coin_type,
            total_amount=self.total_amount,
            total_usd=get_coin_rate(self.coin_type) * self.total_amount,
            count=self.count,
            greet=self.greeting,
            status=RedPacket.Status.CREATED,
            packet_type=self.red_packet_type,
            receive_type=RedPacket.ReceiveType.CODE,
            theme_id=self.theme_id,
            code_id=c_code.id,
            valid_days=self.valid_days
        )
        db.session.add(red_packet)
        db.session.commit()
        return red_packet

    def fmt_red_packet_info(self, red_packet):
        theme = CBoxTheme.query.get(red_packet.theme_id)
        return dict(
            account=self.user.name_displayed,
            amount=amount_to_str(red_packet.total_amount, COIN_PLACES),
            coin_type=red_packet.asset,
            theme=CBoxTheme.img_src(theme.cover_file_key),
            theme_style=theme.style.name,
            greeting=red_packet.greet,
            code=self.code,
            c_box_id=AESGenerator(
                RED_PACKET_SEND_BUSINESS).encrypt(str(red_packet.id))
        )

    def monitor_event(self):
        biz_monitor.increase_counter(IncreaseEvent.SEND_C_BOX_COUNT, with_source=True)
        biz_monitor.increase_counter(IncreaseEvent.SEND_CODE_C_BOX_COUNT, with_source=True)
