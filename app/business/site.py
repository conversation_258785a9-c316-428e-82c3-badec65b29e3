# -*- coding: utf-8 -*-

from __future__ import annotations
from decimal import Decimal
from typing import Mapping
from typing import Optional

from ..utils import ConfigField, BaseConfig, ConfigMode, now
from ..models import (SiteSetting as SiteSettingModel,
                      CountrySetting as CountrySettingModel,
                      BusinessSetting as BusinessSettingModel,
                      db)
from ..caches import SiteSettingCache, CountrySettingCache, BusinessSettingCache
from ..common import get_country
import json


class _SiteSettings(BaseConfig):

    F = ConfigField

    deposits_enabled: bool = F(bool, '开启充值', default=True)
    withdrawals_enabled: bool = F(bool, '开启提现', default=True)
    trading_enabled: bool = F(bool, '开启全站交易', default=True)
    spot_trading_enabled: bool = F(bool, '开启现货交易', default=True, remark="含现货、(碎币)兑换、杠杆、策略交易")
    local_transfers_enabled: bool = F(bool, '开启站内转账', default=True)
    external_transfers_enabled: bool = F(bool, '开启外部转账', default=True, remark="矿池转账")
    perpetual_trading_enable: bool = F(bool, '开启合约交易', default=True)
    perpetual_transfers_enabled: bool = F(bool, '开启合约资产划转', default=True)
    exchange_order_enabled: bool = F(bool, '开启兑换', default=True)
    spot_grid_strategy_enabled: bool = F(bool, '开启现货网格策略', default=True)
    p2p_trading_enabled: bool = F(bool, '开启P2P全站交易(资产负债)', default=True)
    p2p_trading_enabled_manual: bool = F(bool, '开启P2P全站交易(手动)', default=True,
                                         remark='开启后，具体限制逻辑和P2P全站交易(资产负债)一样，但这个用于需要手动关闭P2P的场景')
    pledge_enabled: bool = F(bool, '开启借贷', default=True)
    withdrawals_disabled_by_risk_control: bool = F(bool, '全站提现受限(资产负债)', default=False,
                                                   remark='开启后，全站提现会被卡待审核')
    withdrawals_disabled_by_risk_control_fuse: bool = F(bool, '全站提现受限(提现熔断)', default=False,
                                                        remark='开启后，具体限制逻辑和全站提现受限(资产负债)一样，但这个用于全站提现熔断风控的场景')
    withdrawals_disabled_by_risk_control_signed_cancel: bool = F(bool, '全站提现受限(提现取消)', default=False,
                                                                 remark='开启后，具体限制逻辑和全站提现受限(资产负债)一样，但这个用于全站提现签名取消风控的场景')
    deposits_disabled_by_risk_control: bool = F(bool, '全站充值受限', default=False,
                                                remark="开启后，全站充值会在WEB卡住不入账")

    forbidden_amm_markets: set = F(set, '禁止的AMM市场', default=())
    forbidden_margin_markets: set = F(set, '禁止的杠杆市场', default=())
    forbidden_perpetual_transfer_in_assets: set = F(set, '禁止划转的合约币种', default=())
    forbidden_investment_transfer_in_assets: set = F(set, '禁止划转的理财币种', default=())

    fee_deduction_asset: str = F(str, '手续费抵扣币种', default='CET')
    fee_deduction_rate: Decimal = F(Decimal, '手续费抵扣率', default='0.8')
    contract_fee_deduction_rate: Decimal = F(Decimal, '合约手续抵扣率', default='0.8')  # not used

    user_daily_withdrawal_limit_without_kyc: Decimal \
        = F(Decimal, '非 KYC 用户每日提现限额 / USD', default='10000')
    user_30_days_withdrawal_limit_without_kyc: Decimal \
        = F(Decimal, '非 KYC 用户近30日提现限额 / USD', default='50000')
    user_daily_withdrawal_limit_with_kyc: Decimal \
        = F(Decimal, 'KYC 用户每日提现限额 / USD', default='1000000')
    user_daily_withdrawal_limit_with_kyc_pro: Decimal \
        = F(Decimal, '高级 KYC 用户每日提现限额 / USD', default='5000000')
    user_daily_withdrawal_limit_with_kyc_institution: Decimal \
        = F(Decimal, 'KYC 机构用户每日提现限额 / USD', default='5000000')

    def snapshot_mode(self):
        return _SiteSettings(ConfigMode.SNAPSHOT)

    def _get_all(self) -> dict:
        return SiteSettingCache().get_values()

    def _get_one(self, name: str) -> Optional[str]:
        return SiteSettingCache().get_value(name)

    def _set_one(self, name: str, value: str):
        model = SiteSettingModel
        row = model.query \
            .filter(model.key == name) \
            .first()
        if not row:
            row = model(key=name)
            db.session.add(row)
        row.value = value
        row.status = model.Status.VALID
        db.session.commit()
        SiteSettingCache().set_value(name, value)

    def _del_one(self, name: str):
        model = SiteSettingModel
        row = model.query \
            .filter(model.key == name) \
            .first()
        if not row:
            return
        row.status = model.Status.DELETED
        row.updated_at = now()
        db.session.commit()
        SiteSettingCache().del_value(name)

    @property
    def is_p2p_trading_enabled(self):
        """p2p_trading_enabled、p2p_trading_enabled_manual其中一个值为True即返回True"""
        return self.p2p_trading_enabled or self.p2p_trading_enabled_manual

    @property
    def is_withdrawals_disabled_by_risk_control(self):
        """withdrawals_disabled_by_risk_control、withdrawals_disabled_by_risk_control_fuse、
        withdrawals_disabled_by_risk_control_signed_cancel其中一个值为True即返回True"""
        return (self.withdrawals_disabled_by_risk_control or
                self.withdrawals_disabled_by_risk_control_fuse or
                self.withdrawals_disabled_by_risk_control_signed_cancel)


SiteSettings = _SiteSettings(ConfigMode.REAL_TIME)


class CountrySettings(BaseConfig):

    F = ConfigField

    kyc_enabled: bool = F(bool, '允许 KYC', default=True)
    kyc_required: bool = F(bool, '新用户强制 KYC', default=False)
    kyc_required_after_user_id: int = F(int, '强制KYC开始新用户', default=0)
    allow_kyc_auto_audit_from_app = F(bool, 'APP允许第三方审核', default=True)
    allow_kyc_auto_audit_from_web = F(bool, 'WEB允许第三方审核', default=True)
    allow_register_from_app = F(bool, 'APP允许注册', default=True)
    allow_register_from_web = F(bool, 'WEB允许注册', default=True)
    allow_register = F(bool, '允许注册', default=True)
    allow_binding_mobile = F(bool, '是否允许绑定手机', default=True)

    allow_visit = F(bool, '允许访问', default=True)
    ip_reminder_on = F(bool, '未登录仅提醒', default=False)
    allow_login = F(bool, 'IP国家允许登录', default=True)
    kyc_allow_login = F(bool, 'KYC国家允许登录', default=True)
    only_withdrawal: bool = F(bool, '仅提现', default=False)
    allow_liveness = F(bool, '是否支持生物识别验证', default=True)

    def __init__(self, code: str, mode: ConfigMode = ConfigMode.SNAPSHOT):
        if (country := get_country(code)) is None:
            raise ValueError(f'invalid country code: {code!r}')
        self._code = country.iso_3
        super().__init__(mode)

    @property
    def code(self):
        return self._code

    def _get_all(self) -> dict:
        return CountrySettingCache(self._code).get_values()

    def _get_one(self, name: str) -> Optional[str]:
        return CountrySettingCache(self._code).get_value(name)

    def _set_one(self, name: str, value: str):
        model = CountrySettingModel
        row = model.query \
            .filter(model.code == self._code,
                    model.key == name) \
            .first()
        if not row:
            row = model(code=self._code, key=name)
            db.session.add(row)
        row.value = value
        row.status = model.Status.VALID
        db.session.commit()
        CountrySettingCache(self._code).set_value(name, value)

    def _del_one(self, name: str):
        model = CountrySettingModel
        row = model.query \
            .filter(model.code == self._code,
                    model.key == name) \
            .first()
        if not row:
            return
        row.status = model.Status.DELETED
        row.updated_at = now()
        db.session.commit()
        CountrySettingCache(self._code).del_value(name)


def _validate_snapshot_config(value: str) -> bool:
    from app.assets import list_all_assets

    # noinspection PyBroadException
    try:
        data = json.loads(value)
    except Exception:
        return False
    if not isinstance(data, list):
        return False
    all_assets = set(list_all_assets())
    for assets, ts in data:
        if isinstance(assets, str):
            assets = [assets, ]
        if not isinstance(ts, int):
            return False
        if not all([asset in all_assets for asset in assets]):
            return False
    return True


def _validate_tmp_asset_config(value: str) -> bool:
    from app.assets import list_all_assets
    try:
        data = json.loads(value)
    except Exception:
        return False
    if not isinstance(data, list):
        return False
    all_assets = set(list_all_assets())
    for ele in data:
        if len(ele) != 3:
            return False
    for (asset, start_ts, end_ts) in data:
        if asset not in all_assets:
            return False
        if not isinstance(start_ts, int) or not isinstance(end_ts, int):
            return False
        if int(start_ts) > int(end_ts):
            return False
    return True


class _BusinessSettings(BaseConfig):

    F = ConfigField

    market_pl_days: int = F(int, '交易对盈亏分析支持最大天数', default=2*365)
    market_pl_balance_history_count: int = F(int, '交易对盈亏分析资产流水最大条数', default=1000)
    market_pl_deal_history_count: int = F(int, '交易对盈亏分析成交明细最大条数', default=1000)

    deal_pl_days: int = F(int, '成交统计盈亏分析支持最大天数', default=2*365)
    deal_pl_record_count: int = F(int, '成交统计盈亏分析成交明细最大条数', default=1000)

    account_pl_snapshot_seconds: int = F(int, "账户总资产盈亏数据生成时间(秒)", default=7200)
    account_pl_user_data_cache_time: int = F(int, "账户总资产盈亏实时数据缓存时间(秒)", default=900)

    operation_data_emails: set = F(set, "运营数据日报/周报邮箱", default=())
    coin_rate_emails: set = F(set, "币种涨跌幅周报/月报邮箱", default=())
    new_coin_monthly_emails: set = F(set, "上线币种数据月报邮箱", default=())
    asset_analysis_emails: set = F(set, "币种复盘月报邮箱", default=())
    top_search_emails: set = F(set, "热搜币种/市场数据推送邮箱", default=())
    new_asset_monitor_analysis_emails: set = F(set, "新币上线数据监测邮箱", default=())
    risk_report_emails: set = F(set, "风控数据日/月报邮箱", default=())

    cet_exchange_user_id: int = F(int, "CET回购指定账户ID", default=0)
    cet_exchange_rate: Decimal = F(Decimal, '指定账号回购比例', default=Decimal('0'))
    cet_buy_back_percent: Decimal = F(Decimal, "CET回购收入比例", default=Decimal('0.2'))

    disabled_conversion_assets: set = F(set, "禁止小额兑换的币种", default=())
    disabled_exchange_markets: set = F(set, "禁止兑换的市场", default=())
    support_app_update: bool = F(bool, "是否支持APP内升级", default=True)
    app_cert_check_enabled: bool = F(bool, "安卓端是否指纹进行匹配", default=False)
    # json field
    airdrop_balance_snapshot: str = F(
        str, '空投持仓快照时间', default='[]', validator=_validate_snapshot_config)
    # json field loads之后的格式为 [ ["USDT", start_ts, end_ts], ....]
    tmp_assets_config: str = F(
        str, '换币币种配置', default='[]', validator=_validate_tmp_asset_config)

    email_validate_whitelist: set = F(set, "不进行邮箱新设备验证的邮箱列表", default=())
    spot_depth_snapshot_days: int = F(int, "现货深度快照保留时间", default=7)
    perpetual_depth_snapshot_days: int = F(int, "合约深度快照保留时间", default=7)

    amm_fee_transfer_period_hours: int = F(
        int, "AMM分红发放周期(小时)", default=1, validator=lambda x: 1 <= x <= 24 and 24 % x == 0, remark="要能被24整除"
    )

    special_ambassador_agent_user_id: int = F(int, "特殊大使代理用户", default=0)
    fifth_box_ip_max_open_num: int = F(int, "5周年单个IP领取盲盒数目限制", default=5)
    margin_renew_reserve_ratio: Decimal = F(Decimal, '续借准备金率', default=Decimal('0.6'))
    site_asset_monitor_analysis_emails: set = F(set, "站内数据监测邮箱", default=())
    site_monitor_internal_whitelist_emails: set = F(set, "站内不进行告警监控的邮箱列表", default=())
    login_ip_locking_whitelist_ips: set = F(set, "登录IP锁定-IP白名单", default=())
    c_box_limit_users: set = F(set, "发C-Box有风控账号", default=())
    c_box_daily_max_usd = F(Decimal, "C-BOX全站账号每日默认额度", default=Decimal('2000'))
    tip_limit_amount: Decimal = F(Decimal, '打赏全站账号每日默认额度', default=Decimal('5000'))
    asset_circulation_alert_limit_min_rate = F(Decimal, "流通量触发告警阈值下限", default=Decimal('0.5'))
    asset_circulation_alert_limit_max_rate = F(Decimal, "流通量触发告警阈值上限", default=Decimal('2'))
    kyc_pro_third_max_count: int = F(int, "高级KYC拒绝后转人工次数", default=3,
                                     remark="某个用户被拒绝次数≥该值，则后续提交的订单都走人工审核")
    user_name_whitelist: set = F(set, '用户名设置白名单', default=())
    asset_volatility_tb_whitelist: set = F(set, "异常波动自动同步提示条币种白名单", default=(), remark='白名单内币种不会自动同步')
    banter_user_ids: set = F(set, 'banter授权用户ids', default=set(),
                             remark='banter授权用户ids')
    # split_percents: [保险基金的比例、理财收益的比例、平台收入的比例]
    margin_interest_split_percents = F(
        tuple,
        "杠杆利息分配",
        default=tuple([Decimal('0.3'), Decimal('0.55'), Decimal('0.15')]),
        remark='分别是 保险基金的比例、理财收益的比例、平台收入的比例',
    )
    pledge_interest_split_percents = F(
        tuple,
        "借贷利息分配",
        default=tuple([Decimal('0.3'), Decimal('0.55'), Decimal('0.15')]),
        remark='分别是 保险基金的比例、理财收益的比例、平台收入的比例',
    )
    not_require_kyc_by_timezone_offsets = F(set, '不强制KYC的Timezone-Offset', default=(210,), remark='210表示伊朗')

    def _get_all(self) -> Mapping[str, str]:
        return BusinessSettingCache().get_values()

    def _get_one(self, name: str) -> Optional[str]:
        return BusinessSettingCache().get_value(name)

    def _set_one(self, name: str, value: str):
        model = BusinessSettingModel
        row = model.query \
            .filter(model.key == name) \
            .first()
        if not row:
            row = model(key=name)
            db.session.add(row)
        row.value = value
        row.status = model.Status.VALID
        db.session.commit()
        BusinessSettingCache().set_value(name, value)

    def _del_one(self, name: str):
        model = BusinessSettingModel
        row = model.query \
            .filter(model.key == name) \
            .first()
        if not row:
            return
        row.status = model.Status.DELETED
        row.updated_at = now()
        db.session.commit()
        BusinessSettingCache().del_value(name)

    @property
    def margin_interest_fund_percent(self) -> Decimal:
        """ 杠杆利息分配给保险基金的比例 """
        split_percents = self.margin_interest_split_percents  # [保险基金、理财收益、平台收入的比例]
        v = Decimal(split_percents[0])  # noqa
        return v

    @property
    def margin_interest_investment_percent(self) -> Decimal:
        """ 杠杆利息分配给理财收益比例 """
        split_percents = self.margin_interest_split_percents  # [保险基金、理财收益、平台收入的比例]
        v = Decimal(split_percents[1])  # noqa
        return v

    @property
    def pledge_interest_fund_percent(self) -> Decimal:
        """ 借贷利息分配给保险基金的比例 """
        split_percents = self.pledge_interest_split_percents  # [保险基金、理财收益、平台收入的比例]
        v = Decimal(split_percents[0])  # noqa
        return v

    @property
    def pledge_interest_investment_percent(self) -> Decimal:
        """ 借贷利息分配给理财收益比例 """
        split_percents = self.pledge_interest_split_percents  # [保险基金、理财收益、平台收入的比例]
        v = Decimal(split_percents[1])  # noqa
        return v


BusinessSettings = _BusinessSettings(ConfigMode.REAL_TIME)
