from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal, ROUND_DOWN
from functools import wraps

from flask import current_app
from sqlalchemy import or_, and_

from app import config
from app.business import UserPreferences
from app.business.p2p.config import p2p_setting
from app.caches.p2p import P2pAssetConfigCache, CountryFiatCache, P2pFiatMarketCache, PayChannelCache, UniqueOptionCache, \
    P2pFiatCache
from app.common import P2pBusinessType, Currency
from app.exceptions.p2p import P2pExceptionMap, P2pExceptionCode
from app.models import P2pOrder, P2pUser, User, P2pOrderComplaint
from app.models.mongo import DEFAULT_ASSET_USDT
from app.models.mongo.p2p.pay_channel import P2pCountryFiat
from app.models.mongo.p2p.advertising import P2pAdvertisingMySQL, AutoOfflineAdvReason
from app.utils import quantize_amount, today_datetime


def unique_option(option: UniqueOptionCache.OptionType):
    """Unique option"""

    def decorator(_func):
        @wraps(_func)
        def wrapper(*args, **kwargs):
            unique_type, unique_id = kwargs.get("unique_type"), kwargs.get("unique_id")
            if unique_type and unique_id:
                if UniqueOptionCache().is_unique(unique_id, unique_type, option):
                    current_app.logger.warning("库存重复提交")
                    return
            func_result = _func(*args, **kwargs)
            if unique_type and unique_id:
                UniqueOptionCache().add_option(unique_id, unique_type, option)
            return func_result
        return wrapper

    return decorator


class P2pUtils:

    MERCHANT_KYC_EXCLUDE_FIAT_MAPPER = {
        "CUB": ['USD'],
    }

    @classmethod
    def get_user_traded_merchants(cls, user_id: int):
        return {
            i.merchant_id for i in P2pOrder.query.filter(
                P2pOrder.status == P2pOrder.Status.FINISHED,
                P2pOrder.customer_id == user_id
            ).with_entities(
                P2pOrder.merchant_id
            ).all()
        }

    @classmethod
    def cancel_not_received_order_by_user(cls, user_id: int, side: P2pBusinessType):
        from app.business.p2p.order_factor import P2pOrderFactor
        """取消未接单的订单"""
        orders = P2pOrder.query.filter(
            P2pOrder.status == P2pOrder.Status.CREATED,
            or_(
                and_(
                    P2pOrder.customer_id == user_id,
                    P2pOrder.side == side
                ),
                and_(
                    P2pOrder.merchant_id == user_id,
                    P2pOrder.side == P2pBusinessType.reverse(side)
                )
            )
        ).all()
        for order in orders:
            P2pOrderFactor(order).system_cancel(P2pOrder.CancelType.SYSTEM_CANCEL)

    @classmethod
    def offline_advertising_by_merchant(
            cls,
            merchant_id: int,
            adv_type: P2pBusinessType = None,
            reason: AutoOfflineAdvReason = AutoOfflineAdvReason.RISK_CONTROL
    ):
        """自动下架商家的广告"""
        from app.business.p2p.advertising import P2pAdvertisingManger
        query = P2pAdvertisingMySQL.query.filter(
            P2pAdvertisingMySQL.user_id == merchant_id,
            P2pAdvertisingMySQL.status == P2pAdvertisingMySQL.Status.ONLINE,
        )
        if adv_type:
            query = query.filter(P2pAdvertisingMySQL.adv_type == adv_type)
        advs = query.with_entities(
            P2pAdvertisingMySQL.mongo_id,
        ).all()
        for adv in advs:
            P2pAdvertisingManger(str(adv.mongo_id)).auto_offline(reason)

    @classmethod
    def get_p2p_assets(cls):
        return P2pAssetConfigCache.get_assets()

    @classmethod
    def get_all_valid_fiats(cls) -> list[str]:
        return sorted(P2pFiatCache().smembers())

    @classmethod
    def get_all_invalid_fiats(cls):
        invalid_fiats = P2pCountryFiat.get_invalid_fiats()
        return invalid_fiats

    @classmethod
    def get_all_p2p_fiats(cls):
        return sorted(P2pCountryFiat.get_all_fiats())

    @classmethod
    def get_merchant_exclude_fiats(cls, country_code) -> list[str]:
        return cls.MERCHANT_KYC_EXCLUDE_FIAT_MAPPER.get(country_code, [])

    @classmethod
    def get_merchant_p2p_fiats(cls, country_code: str):
        all_fiats = cls.get_all_valid_fiats()
        if not country_code:
            return all_fiats
        exclude_fiats = cls.get_merchant_exclude_fiats(country_code)
        return sorted(set(all_fiats) - set(exclude_fiats))

    @classmethod
    def get_p2p_countries(cls):
        country_mapper = CountryFiatCache().hgetall()
        return list(country_mapper.keys())

    @classmethod
    def fmt_base_amount(cls, base: str, amount: Decimal, rounding=ROUND_DOWN):
        info = P2pAssetConfigCache.get_asset_info(base)
        if not info:
            return amount
        return quantize_amount(amount, info['precision'], rounding)

    @classmethod
    def fmt_quote_amount(cls, quote: str, asset: str, amount: Decimal):
        info = P2pFiatMarketCache(asset).get_fiat_info(quote)
        if not info:
            return amount
        return quantize_amount(amount, info['precision'])

    @classmethod
    def check_p2p_cancel_limit_user_id(cls, user_id):
        p2p_user = P2pUser.get_by_user_id(user_id)
        return cls.check_user_p2p_cancel_limit(p2p_user)

    @classmethod
    def check_user_p2p_cancel_limit(cls, user: P2pUser):
        model = P2pOrder
        status = model.Status
        # 3. 检查用户取消订单
        today = today_datetime()
        cancel_count = model.query.filter(
            model.status == status.CANCELED,
            model.cancel_user_id == user.user_id,
            model.cancel_type.in_(model.cancel_count_types()),
            model.created_at >= today,
            model.created_at < today + timedelta(days=1),
        ).count()
        limit = p2p_setting.daily_merchant_cancel_order_limit if \
            user.is_merchant else p2p_setting.daily_user_cancel_order_limit
        if cancel_count >= limit:
            return True
        return False

    @classmethod
    def check_user_complaint_limit(cls, user_id):
        model = P2pOrderComplaint
        complaint_count = model.get_user_query(user_id).filter(
            model.complaint_status.in_([
                model.Status.CREATED,
                model.Status.PENDING,
            ])
        ).count()
        if complaint_count >= p2p_setting.complaint_offline_adv_limit:
            return True
        return False

    @classmethod
    def order_finished_remove_cache(cls, order: P2pOrder):
        pass

    @classmethod
    def gen_name_word(cls, nickname: str):
        return nickname[0].upper() if nickname else ''

    @classmethod
    def get_fiat_by_country_code(cls, country_code: str):
        if country_code and (fiat := CountryFiatCache().read_one_country(country_code)):
            return fiat
        return Currency.USD.name

    @classmethod
    def get_user_country_code(cls, user: User):
        return user.kyc_country or user.location_code

    @classmethod
    def get_merchant_adv_fiats(cls, user_id):
        return {
            adv.quote for adv in P2pAdvertisingMySQL.query.filter_by(
                user_id=user_id
            ).with_entities(
                P2pAdvertisingMySQL.quote
            ).all()
        }

    @classmethod
    def get_active_pay_ids(cls, pay_channel_ids: list[str]):
        """获取有效的支付方式"""
        if not pay_channel_ids:
            return []
        active_keys = PayChannelCache.get_many(pay_channel_ids).keys()
        return [i for i in pay_channel_ids if i in active_keys]

    @classmethod
    def get_active_user_pay_channel(cls, user_id: int, user_pay_channel_ids: list[str]):
        from app.business.p2p.pay_channel import UserPayChannelBus
        pay_channel_list = UserPayChannelBus(user_id).get_channel_map_list(user_pay_channel_ids)
        return [data['user_pay_channel_id'] for data in pay_channel_list]

    @classmethod
    def change_biz_user_id(cls, biz_user_id: str) -> int:
        p2p_user = P2pUser.query.filter(
            P2pUser.biz_user_id == biz_user_id
        ).first()
        if not p2p_user:
            return 0
        return p2p_user.user_id

    @classmethod
    def get_biz_user_id_map(cls, user_ids: list[int]):
        return {i.user_id: i.biz_user_id for i in P2pUser.query.filter(
            P2pUser.user_id.in_(user_ids)
        ).with_entities(
            P2pUser.user_id,
            P2pUser.biz_user_id
        ).all()}

    @classmethod
    def get_p2p_asset_to_fiat_rate(cls, asset: str, fiat: str) -> Decimal:
        """p2p允许没有汇率配置的情况"""

        fiat_info = P2pFiatMarketCache(asset).get_fiat_info(fiat)
        price = Decimal(fiat_info.get('fair_price', 0))
        if price <= Decimal():
            return Decimal()
        return quantize_amount(price, fiat_info["precision"])

    @classmethod
    def get_quote_default_adv_limit(cls, base: str, quote: str, price: Decimal) -> tuple[Decimal, Decimal]:
        """获取默认广告限制(单位quote)"""
        cache = P2pFiatMarketCache(base)
        fiat_config = cache.get_fiat_info(quote)
        if not fiat_config:
            return Decimal(), Decimal()
        default_min_limit = Decimal(fiat_config['min_limit'])
        default_max_limit = Decimal(fiat_config['max_limit'])
        fiat_price = Decimal(fiat_config.get('fair_price', 0)) or price
        system_min_limit = cache.format_limit_amount(default_min_limit * fiat_price)
        system_max_limit = cache.format_limit_amount(default_max_limit * fiat_price)
        return system_min_limit, system_max_limit

    @classmethod
    def get_base_default_adv_limit(cls, base: str, quote: str) -> tuple[Decimal, Decimal]:
        """获取默认广告限制(单位base)"""
        cache = P2pFiatMarketCache(base)
        fiat_config = cache.get_fiat_info(quote)
        if not fiat_config:
            return Decimal(), Decimal()
        default_min_limit = Decimal(fiat_config['min_limit'])
        default_max_limit = Decimal(fiat_config['max_limit'])
        system_min_limit = cache.format_limit_amount(default_min_limit)
        system_max_limit = cache.format_limit_amount(default_max_limit)
        return system_min_limit, system_max_limit

    @classmethod
    def find_intersection(cls, adv_online_list: list, merchant_open_shop_list: list) -> list:
        """
        查找两个列表的交集
        :param adv_online_list: 广告在线时间列表
        :param merchant_open_shop_list: 商家营业状态时间列表
        """
        data = []
        i, j = 0, 0
        while i < len(adv_online_list) and j < len(merchant_open_shop_list):
            adv_start, adv_end = adv_online_list[i]
            shop_start, shop_end = merchant_open_shop_list[j]
            # 查找相交部分
            intersection_start = max(adv_start, shop_start)
            intersection_end = min(adv_end, shop_end)
            if intersection_start <= intersection_end:
                data.append([intersection_start, intersection_end])
            # 移动指针
            if adv_end < shop_end:
                i += 1
            else:
                j += 1
        return data

    @classmethod
    def find_union(cls, adv_online_list: list):
        """
        计算多个广告在线时间的并集
        :param adv_online_list: [[start_time, end_time],[]..., [[start_time, end_time], [], ...], ....]
        :return: [[start_time, end_time], [], ...]
        """
        data = []
        adv_online_list.sort(key=lambda x: (x[0], x[1]))

        for i in adv_online_list:
            if not data or data[-1][1] < i[0]:
                data.append(i)
            else:
                data[-1] = (data[-1][0], max(data[-1][1], i[1]))
        return data

    @classmethod
    def get_user_usdt_trade_fiat(cls, user):
        # 已废弃，详情查看 P2pUserLastFiatCache
        trade_fiat = UserPreferences(user.id).p2p_trade_fiat
        valid_fiats = P2pFiatMarketCache(DEFAULT_ASSET_USDT).get_fiats()
        if trade_fiat not in valid_fiats:
            trade_fiat = Currency.USD.name
        return trade_fiat


def export_amount(value: Decimal):
    return int(value) if value == int(value) else float(value)


def send_p2p_alert(content, at=None):
    from app.business import send_alert_notice
    if not at:
        at = config["ADMIN_CONTACTS"]["slack_at"].get("p2p_notice")
    send_alert_notice(
        content,
        config["ADMIN_CONTACTS"]["p2p_notice"],
        expired_seconds=60 * 60 * 2,
        at=at
    )


def check_margin_enough_balance(user_id, amount, asset):
    from app.business import ServerClient
    balance = ServerClient().get_user_balances(user_id, asset=asset)
    if balance.get(asset, {}).get('available', 0) < amount:
        raise P2pExceptionMap[P2pExceptionCode.MARGIN_NO_ENOUGH]
