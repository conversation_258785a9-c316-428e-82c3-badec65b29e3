import json
from collections import defaultdict
from datetime import timedelta
from decimal import Decimal
from enum import Enum
from typing import List

from sqlalchemy import func, or_

from app.business import UserSettings, ExchangeLogDB
from app.business.p2p.config import p2p_setting
from app.business.user import get_user_kyc_country_map
from app.business.user_status import P2pTPlusTChangeType
from app.business.wallet import WithdrawalRestrictedFundHelper
from app.caches.p2p import P2pTPlusNNotifyCache
from app.models import P2pTPlusNRule, P2pUser, User, UserSetting, P2pOrderComplaint, P2pUserTradeSummary, \
    P2pOrder, P2pUserMargin, SubAccount, P2pUserTPlusNRecord, db, UserStatusChangeHistory
from app.models.wallet import WithdrawalRestrictedFund
from app.utils import batch_iter, now, datetime_to_time


class RuleKey(Enum):
    KYC_COUNTRY = "KYC地区"
    REGISTRATION_TIME = "账号注册时间"
    IS_HIGH_RISK_USER = "是否开启P2P高风险权限"
    APPEAL_RATE = "历史申诉率"
    USER_IDENTITY = "用户身份"
    TRADE_LIMIT = "交易受限"
    TRADE_FIATS = "交易法币"
    PAID_MARGIN_ENOUGH = "是否已完全缴纳商家保证金"
    P2P_DEAL_COUNT = "已完成P2P订单数"
    P2P_DEAL_AMOUNT = "已完成P2P订单累计金额"
    AVG_BALANCE_7_DAYS = "近7天帐户资产均值"


class Operator(Enum):
    LE = '<='
    GE = '>='
    EQ = '='
    NE = '!='
    IN = 'in'
    NOT_IN = 'not in'


class Choice(Enum):
    YES = '是'
    NO = '否'


class UserIdentity(Enum):
    ALL = '全部'
    MERCHANT = '商家'
    NORMAL = '普通用户'


class P2pTPlusNBiz:

    def __init__(self):
        self.global_setting_days = p2p_setting.t_plus_n_days
        self.user_ids = self._get_user_ids()
        self.rules = self.get_p2p_rules_records()

    @classmethod
    def _get_user_ids(cls) -> List[int]:
        users = P2pUser.query.filter(
            P2pUser.status == P2pUser.Status.ACTIVE
        ).with_entities(
            P2pUser.user_id
        ).all()
        return [r.user_id for r in users]

    @classmethod
    def get_p2p_rules_records(cls):
        rules = P2pTPlusNRule.query.filter(
            P2pTPlusNRule.status == P2pTPlusNRule.Status.VALID
        ).all()
        return rules

    def _get_user_setting_days_mapper(self):
        from app.utils.config_ import _convert

        res = {}
        key = UserSettings.p2p_t_plus_n_days
        for ch_user_ids in batch_iter(self.user_ids, 5000):
            rows = UserSetting.query.filter(
                UserSetting.status == UserSetting.Status.VALID,
                UserSetting.user_id.in_(ch_user_ids),
                UserSetting.key == key.name
            ).with_entities(
                UserSetting.user_id,
                UserSetting.value,
            ).all()
            for r in rows:
                res[r.user_id] = _convert(key.type, r.value)
        return res

    def _get_rely_data(self):
        res = {}
        for ch_user_ids in batch_iter(self.user_ids, 5000):
            summaries = (P2pUserTradeSummary.query.filter(
                P2pUserTradeSummary.user_id.in_(ch_user_ids)
            ).with_entities(
                P2pUserTradeSummary.user_id,
                P2pUserTradeSummary.deal_count,
                P2pUserTradeSummary.deal_amount,
            ).all())
            for s in summaries:
                res[s.user_id] = {
                    'p2p_deal_count': s.deal_count,
                    'p2p_deal_amount': s.deal_amount,
                }
        return res

    def _get_kyc_country_user_data(self, rely_data: dict):
        return get_user_kyc_country_map(self.user_ids)

    def _get_registration_time_user_data(self, rely_data: dict):
        res = {}
        now_ = now()
        for ch_user_ids in batch_iter(self.user_ids, 5000):
            rows = User.query.filter(
                User.id.in_(ch_user_ids)
            ).with_entities(
                User.id,
                User.created_at
            ).all()
            for r in rows:
                interval = now_ - r.created_at
                res[r.id] = interval.days
        return res

    def _get_is_high_risk_user_user_data(self, rely_data: dict):
        from app.utils.config_ import _convert
        res = {}
        key = UserSettings.p2p_high_risk_user
        for ch_user_ids in batch_iter(self.user_ids, 5000):
            rows = UserSetting.query.filter(
                UserSetting.status == UserSetting.Status.VALID,
                UserSetting.user_id.in_(ch_user_ids),
                UserSetting.key == key.name
            ).with_entities(
                UserSetting.user_id,
                UserSetting.value,
            ).all()
            for r in rows:
                value = _convert(key.type, r.value)
                res[r.user_id] = Choice.YES.name if value else Choice.NO.name
        return res

    def _get_appeal_rate_user_data(self, rely_data: dict):
        # 历史申诉率 = 被申诉成功订单数量 / 已完成订单数量
        res = {}
        for ch_user_ids in batch_iter(self.user_ids, 5000):
            complaints = P2pOrderComplaint.query.filter(
                P2pOrderComplaint.complaint_status == P2pOrderComplaint.Status.FINISHED,
                P2pOrderComplaint.plaintiff_id.in_(ch_user_ids),
            ).group_by(
                P2pOrderComplaint.plaintiff_id,
            ).with_entities(
                P2pOrderComplaint.plaintiff_id,
                func.count(func.distinct(P2pOrderComplaint.order_id)).label("order_count"),
            ).all()
            order_complaint_mapper = {c.plaintiff_id: c.order_count for c in complaints}
            for uid in ch_user_ids:
                order_complaint_count = order_complaint_mapper.get(uid, 0)
                order_count = rely_data.get(uid, {}).get('p2p_deal_count', 0)
                rate = Decimal(order_complaint_count) / Decimal(order_count) if order_count else Decimal()
                res[uid] = min(rate, Decimal(1))
        return res

    def _get_user_identity_user_data(self, rely_data: dict):
        res = {}
        users = P2pUser.query.filter(
            P2pUser.status == P2pUser.Status.ACTIVE
        ).with_entities(
            P2pUser.user_id,
            P2pUser.merchant_id,
        ).all()
        for r in users:
            res[r.user_id] = UserIdentity.MERCHANT.name if r.merchant_id else UserIdentity.NORMAL.name
        return res

    def _get_trade_limit_user_data(self, rely_data: dict):
        from app.utils.config_ import _convert

        keys = {
            UserSettings.spot_trading_disabled_by_admin,
            UserSettings.margin_trading_disabled_by_admin,
            UserSettings.perpetual_trading_disabled_by_admin,
            UserSettings.trading_disabled_by_risk_control,
            UserSettings.margin_loan_disabled_by_risk_control,
            UserSettings.spot_trading_disabled_by_risk_control,
            UserSettings.red_packet_disabled_by_risk_control,
        }
        key_mapper = {k.name: k.type for k in keys}
        res = {}
        for ch_user_ids in batch_iter(self.user_ids, 5000):
            rows = UserSetting.query.filter(
                UserSetting.status == UserSetting.Status.VALID,
                UserSetting.user_id.in_(ch_user_ids),
                UserSetting.key.in_(list(key_mapper.keys()))
            ).with_entities(
                UserSetting.user_id,
                UserSetting.key,
                UserSetting.value,
            ).all()
            for r in rows:
                value = _convert(key_mapper[r.key], r.value)
                if value:
                    res[r.user_id] = Choice.YES.name
        return res

    def _get_trade_fiats_user_data(self, rely_data: dict):
        # 查询用户过去30笔已完成订单
        user_fiat_list = defaultdict(list)
        for ch_user_ids in batch_iter(self.user_ids, 2000):
            rows = P2pOrder.query.filter(
                P2pOrder.status == P2pOrder.Status.FINISHED,
                or_(
                    P2pOrder.customer_id.in_(ch_user_ids),
                    P2pOrder.merchant_id.in_(ch_user_ids)
                )
            ).with_entities(
                P2pOrder.customer_id,
                P2pOrder.merchant_id,
                P2pOrder.quote
            ).order_by(P2pOrder.id.desc()).all()
            for r in rows:
                if len(user_fiat_list[r.customer_id]) < 30:
                    user_fiat_list[r.customer_id].append(r.quote)
                if len(user_fiat_list[r.merchant_id]) < 30:
                    user_fiat_list[r.merchant_id].append(r.quote)
        res = {user_id: set(fiat_list) for user_id, fiat_list in user_fiat_list.items()}
        return res

    def _get_paid_margin_enough_user_data(self, rely_data: dict):
        res = {}
        for ch_user_ids in batch_iter(self.user_ids, 5000):
            rows = P2pUserMargin.query.filter(
                P2pUserMargin.user_id.in_(ch_user_ids)
            ).with_entities(
                P2pUserMargin.user_id,
                P2pUserMargin.require_margin,
                P2pUserMargin.paid_margin,
            ).all()
            for r in rows:
                res[r.user_id] = Choice.YES.name if r.paid_margin >= r.require_margin else Choice.NO.name
        return res

    def _get_p2p_deal_count_user_data(self, rely_data: dict):
        res = {user_id: data.get('p2p_deal_count', 0) for user_id, data in rely_data.items()}
        return res

    def _get_p2p_deal_amount_user_data(self, rely_data: dict):
        res = {user_id: data.get('p2p_deal_amount', Decimal(0)) for user_id, data in rely_data.items()}
        return res

    def _get_avg_balance_7_days_user_data(self, rely_data: dict):
        days = 7
        table_name = 'user_slice_account_balance_sum'
        end_date = ExchangeLogDB.get_slice_table_latest_date(table_name)
        start_date = end_date - timedelta(days=days)

        sub_main_mapper = {}
        for ch_user_ids in batch_iter(self.user_ids, 5000):
            sub_query = SubAccount.query.filter(
                SubAccount.main_user_id.in_(ch_user_ids)
            ).with_entities(
                SubAccount.main_user_id,
                SubAccount.user_id
            ).all()
            for r in sub_query:
                sub_main_mapper[r.user_id] = r.main_user_id
        all_user_ids = set(sub_main_mapper.keys()) | set(self.user_ids)

        index_users_mapper = defaultdict(set)
        for user_id in all_user_ids:
            idx = ExchangeLogDB.user_slice_account_balance_sum_hash(user_id)
            index_users_mapper[idx].add(user_id)

        total_usd_mapper = defaultdict(Decimal)
        for idx, user_ids in index_users_mapper.items():
            table = ExchangeLogDB.user_slice_account_balance_sum_table(idx)
            for ch_user_ids in batch_iter(user_ids, 5000):
                user_id_str = ','.join(map(str, ch_user_ids))
                where_clause = f"user_id in ({user_id_str}) and report_date>'{start_date}' and report_date<='{end_date}'"
                r = table.select("user_id, sum(balance_usd)",
                                 where=where_clause,
                                 group_by="user_id")
                for user_id, balance in r:
                    main_user_id = sub_main_mapper.get(user_id, user_id)
                    total_usd_mapper[main_user_id] += balance

        res = {user_id: balance_usd / Decimal(days) for user_id, balance_usd in total_usd_mapper.items()}
        return res

    def get_user_data_mapper(self):
        condition_keys = set()
        for r in self.rules:
            conditions = json.loads(r.group_condition)
            condition_keys |= {c['key'] for c in conditions}

        rely_data = {}
        rely_keys = {RuleKey.USER_IDENTITY.name, RuleKey.P2P_DEAL_COUNT.name, RuleKey.P2P_DEAL_AMOUNT.name}
        if condition_keys & rely_keys:
            rely_data = self._get_rely_data()

        raw_data_mapper = {}
        for key in condition_keys:
            raw_data_mapper[key] = getattr(self, f'_get_{key.lower()}_user_data')(rely_data)

        user_data_mapper = defaultdict(lambda: {
            RuleKey.KYC_COUNTRY.name: str(),
            RuleKey.REGISTRATION_TIME.name: int(),
            RuleKey.IS_HIGH_RISK_USER.name: str(),
            RuleKey.APPEAL_RATE.name: Decimal(),
            RuleKey.USER_IDENTITY.name: str(),
            RuleKey.TRADE_LIMIT.name: str(),
            RuleKey.TRADE_FIATS.name: set(),
            RuleKey.PAID_MARGIN_ENOUGH.name: str(),
            RuleKey.P2P_DEAL_COUNT.name: int(),
            RuleKey.P2P_DEAL_AMOUNT.name: Decimal(),
            RuleKey.AVG_BALANCE_7_DAYS.name: Decimal(),
        })
        for key, data in raw_data_mapper.items():
            for uid in self.user_ids:
                user_data_mapper[uid][key] = data.get(uid)
        return user_data_mapper

    def calculate_match_rules(self, user_data_mapper: dict):
        res = defaultdict(list)
        for user_id, user_data in user_data_mapper.items():
            for rule in self.rules:
                conditions = json.loads(rule.group_condition)
                if self.check_match_rule(conditions, user_data):
                    res[user_id].append(rule.id)
        return res

    @classmethod
    def check_match_rule(cls, conditions: list, user_data: dict):
        for c in conditions:
            key, op, value = c['key'], c['op'], c['value']
            if key == RuleKey.KYC_COUNTRY.name:
                if op == Operator.EQ.name:
                    if user_data[key] not in value:
                        return False
                elif op == Operator.NE.name:
                    if user_data[key] in value:
                        return False
                else:
                    raise RuntimeError(f"t_plus_n check_match_rule failed, key:{key}, op:{op}")
            elif key in {RuleKey.REGISTRATION_TIME.name, RuleKey.P2P_DEAL_COUNT.name}:
                user_value = user_data[key] or 0
                if op == Operator.GE.name:
                    if user_value < value:
                        return False
                elif op == Operator.LE.name:
                    if user_value > value:
                        return False
                else:
                    raise RuntimeError(f"t_plus_n check_match_rule failed, key:{key}, op:{op}")
            elif key in {RuleKey.IS_HIGH_RISK_USER.name, RuleKey.TRADE_LIMIT.name}:
                user_value = user_data[key] or Choice.NO.name
                if op == Operator.EQ.name:
                    if user_value != value:
                        return False
                else:
                    raise RuntimeError(f"t_plus_n check_match_rule failed, key:{key}, op:{op}")
            elif key in {RuleKey.APPEAL_RATE.name, RuleKey.P2P_DEAL_AMOUNT.name, RuleKey.AVG_BALANCE_7_DAYS.name}:
                user_value = user_data[key] or Decimal(0)
                if op == Operator.GE.name:
                    if user_value < value:
                        return False
                elif op == Operator.LE.name:
                    if user_value > value:
                        return False
                else:
                    raise RuntimeError(f"t_plus_n check_match_rule failed, key:{key}, op:{op}")
            elif key == RuleKey.USER_IDENTITY.name:
                user_value = user_data[key] or UserIdentity.NORMAL.name
                if op == Operator.EQ.name:
                    if value in [UserIdentity.NORMAL.name, UserIdentity.MERCHANT.name]:
                        if user_value != value:
                            return False
                    else:   # ALL
                        pass
                elif op == Operator.NE.name:
                    if value in [UserIdentity.NORMAL.name, UserIdentity.MERCHANT.name]:
                        if user_value == value:
                            return False
                    else:   # ALL
                        return False
                else:
                    raise RuntimeError(f"t_plus_n check_match_rule failed, key:{key}, op:{op}")
            elif key == RuleKey.TRADE_FIATS.name:
                user_value = user_data[key] or set()
                if op == Operator.EQ.name:
                    if not (user_value & set(value)):
                        return False
                else:
                    raise RuntimeError(f"t_plus_n check_match_rule failed, key:{key}, op:{op}")
            elif key == RuleKey.PAID_MARGIN_ENOUGH.name:
                user_value = user_data[key]
                if op == Operator.EQ.name:
                    if user_value != value:
                        return False
                else:
                    raise RuntimeError(f"t_plus_n check_match_rule failed, key:{key}, op:{op}")

        return True

    def get_effect_rule(self, match_rule_ids: List[int]):
        if not match_rule_ids:
            return None
        match_rule_ids = set(match_rule_ids)
        rules = [rule for rule in self.rules if rule.id in match_rule_ids]
        rules.sort(key=lambda x: x.limit_days, reverse=True)
        return rules[0]

    def update_user_days(self, match_rules_mapper: dict):
        user_setting_days_mapper = self._get_user_setting_days_mapper()
        for ch_user_ids in batch_iter(self.user_ids, 2000):
            user_records = P2pUserTPlusNRecord.query.filter(
                P2pUserTPlusNRecord.user_id.in_(ch_user_ids)
            ).all()
            user_record_mapper = {i.user_id: i for i in user_records}
            need_notify_user_ids = set()
            for user_id in ch_user_ids:
                match_rule_ids = match_rules_mapper.get(user_id, [])
                effect_rule = self.get_effect_rule(match_rule_ids)
                effect_rule_id = effect_rule.id if effect_rule else 0
                rules_effect_days = effect_rule.limit_days if effect_rule else 0
                user_setting_days = user_setting_days_mapper.get(user_id)
                user_record = user_record_mapper[user_id]
                need_notify = self.update_user_t_plus_n(
                    user_record, match_rule_ids, effect_rule_id, rules_effect_days, user_setting_days)
                if need_notify:
                    need_notify_user_ids.add(user_id)
            db.session.commit()
            if need_notify_user_ids:
                P2pTPlusNNotifyCache().hmset({str(user_id): str(user_id) for user_id in need_notify_user_ids})

    def update_user_t_plus_n(
            self, record: P2pUserTPlusNRecord,
            match_rule_ids: List[int], effect_rule_id: int, rules_effect_days: int,
            user_setting_days: int
    ) -> bool:
        def user_record_dict(r: P2pUserTPlusNRecord) -> dict:
            r = r.to_dict()
            r['created_at'] = datetime_to_time(r['created_at'])
            r['updated_at'] = datetime_to_time(r['updated_at'])
            return r

        user_id = record.user_id
        old = user_record_dict(record)
        old_real_days = record.real_days
        old_real_rule_id = record.real_rule

        real_rule_id, real_days = P2pUserTPlusNRecord.get_real_rule_id_and_days(
            self.global_setting_days, user_setting_days, effect_rule_id, rules_effect_days)
        need_notify = real_days > old_real_days
        if (
                set(match_rule_ids) == set(record.match_rules)
                and effect_rule_id == record.effect_rule
                and rules_effect_days == record.rules_effect_days
                and user_setting_days == record.user_setting_days
                and self.global_setting_days == record.global_setting_days
                and real_rule_id == record.real_rule
                and real_days == record.real_days
        ):
            # not change
            return need_notify

        record.match_rules = match_rule_ids
        record.effect_rule = effect_rule_id
        record.rules_effect_days = rules_effect_days
        record.user_setting_days = user_setting_days
        record.global_setting_days = self.global_setting_days
        record.real_rule = real_rule_id
        record.real_days = real_days

        if old_real_rule_id != real_rule_id or old_real_days != real_days:
            db.session.add(UserStatusChangeHistory(
                user_id=user_id,
                type=UserStatusChangeHistory.Type.P2P_T_PLUS_N.name,
                action=P2pTPlusTChangeType.UPDATE.name,
                detail=json.dumps(dict(
                    old=old,
                    new=user_record_dict(record)
                ))
            ))

            add_days = real_days - old_real_days
            time_delta = timedelta(days=add_days)
            log_old_data = {
                't_plus_n_rule_id': old_real_rule_id,
                't_plus_n_days': old_real_days,
            }
            log_new_data = {
                't_plus_n_rule_id': real_rule_id,
                't_plus_n_days': real_days,
            }
            WithdrawalRestrictedFundHelper.update_user_funds(
                user_id, time_delta, log_old_data, log_new_data, WithdrawalRestrictedFund.BizType.P2P
            )

        return need_notify

    def domain(self):
        user_data_mapper = self.get_user_data_mapper()
        match_rules_mapper = self.calculate_match_rules(user_data_mapper)
        self.update_user_days(match_rules_mapper)
