# -*- coding: utf-8 -*-

from hashlib import sha512
from base64 import b64encode
from types import FunctionType
from typing import List, Union, Optional

from decorator import decorate

from ..caches import LockCache
from ..exceptions import FrequencyExceeded
from ..utils import func_to_str, func_args_to_str

import time


class LockKeys:

    @classmethod
    def two_fa(cls, user_id: int):
        return f'2fa:{user_id}'

    @classmethod
    def webauthn(cls, user_id: int):
        return f'webauthn:{user_id}'

    @classmethod
    def func_lock(cls, key: str):
        return f'func_lock:{key}'

    @classmethod
    def setting_lock(cls, key: str):
        return f'setting_lock:{key}'

    @classmethod
    def api_lock(cls, key: str):
        return f'api_lock:{key}'
    
    @classmethod
    def qrcode_login(cls, key: str):
        return f'qrcode_login_lock:{key}'

    @classmethod
    def user_withdrawal(cls, user_id: int):
        return f'user_withdrawal:{user_id}'

    @classmethod
    def deposit(cls, deposit_id: int):
        return f'deposit:{deposit_id}'

    @classmethod
    def kyt_deposit_audit(cls, id_: int):
        return f'kyt_deposit_audit:{id_}'

    @classmethod
    def edd_audit(cls, id_: int):
        return f'edd_audit:{id_}'

    @classmethod
    def deposit_audit(cls, deposit_id: int):
        return f'deposit_audit:{deposit_id}'

    @classmethod
    def withdrawal(cls, withdrawal_id: int):
        return f'withdrawal:{withdrawal_id}'
    
    @classmethod
    def withdrawal_address(cls, user_id: int):
        return f'withdrawal_address:{user_id}'
    
    @classmethod
    def withdrawal_approver(cls, user_id: int):
        return f'withdrawal_approver:{user_id}'

    @classmethod
    def perpetual_balance_transfer_in(cls, user_id: int, coin_type: str):
        return f'perpetual_balance_transfer_in:{user_id}:{coin_type}'

    @classmethod
    def perpetual_balance_transfer_out(cls, user_id: int, coin_type: str):
        return f'perpetual_balance_transfer_out:{user_id}:{coin_type}'

    @classmethod
    def adjust_leverage(cls, user_id: int, market: str):
        return f'enable_contract:{user_id}:{market}'

    @classmethod
    def perpetual_put_limit(cls, user_id: int, market: str):
        return f'perpetual_put_limit:{user_id}:{market}'

    @classmethod
    def perpetual_loss_profit(cls, user_id: int, market: str):
        return f'perpetual_loss_profit:{user_id}:{market}'

    @classmethod
    def perpetual_put_market(cls, user_id: int, market: str):
        return f'perpetual_put_market:{user_id}:{market}'

    @classmethod
    def perpetual_put_limit_close(cls, user_id: int, market: str):
        return f'perpetual_put_limit_close:{user_id}:{market}'

    @classmethod
    def perpetual_put_stop_limit(cls, user_id: int, market: str):
        return f'perpetual_put_stop_limit:{user_id}:{market}'

    @classmethod
    def perpetual_put_stop_market(cls, user_id: int, market: str):
        return f'perpetual_put_stop_market:{user_id}:{market}'

    @classmethod
    def perpetual_cancel_order(cls, user_id: int):
        return f'perpetual_cancel_order:{user_id}'

    @classmethod
    def perpetual_cancel_stop_order(cls, user_id: int):
        return f'perpetual_cancel_stop_order:{user_id}'

    @classmethod
    def perpetual_adjust_margin(cls, user_id: int, market: str):
        return f'perpetual_adjust_margin:{user_id}:{market}'

    @classmethod
    def perpetual_insurance_transfer(cls, asset: str):
        return f"perpetual_insurance_transfer:{asset}"

    @classmethod
    def investment_balance_transfer(cls, user_id: int):
        return f"investment_balance_transfer:{user_id}"

    @classmethod
    def margin_loan_or_flat(cls, user_id: int):
        return f"margin_loan_or_flat:{user_id}"

    @classmethod
    def user_margin_account(cls, user_id: int, account_id: int) -> str:
        return f"user_margin_account:{user_id}:{account_id}"

    @classmethod
    def margin_insurance_transfer(cls, asset: str):
        return f"margin_insurance_transfer:{asset}"

    @classmethod
    def insurance_to_admin_transfer(cls, asset: str):
        return f"insurance_to_admin_transfer:{asset}"

    @classmethod
    def credit_update_balance(cls, user_id: int, asset: str):
        return f'update_credit_balance:{user_id}:{asset}'

    @classmethod
    def amm_system_user_balance(cls, user_id: int):
        return f'amm_system_user_balance:{user_id}'

    @classmethod
    def grab_red_packet(cls, email: str):
        return f'grab_red_packet:{email}'

    @classmethod
    def send_red_packet(cls, user_id: int):
        return f'send_red_packet:{user_id}'

    @classmethod
    def create_broker_auth_api(cls, user_id: int):
        return f'create_broker_auth_api:{user_id}'

    @classmethod
    def generate_c_code(cls):
        return 'generate_c_code'

    @classmethod
    def app_entrance(cls):
        return 'app_entrance'

    @classmethod
    def app_activate(cls):
        return 'app_activate'

    @classmethod
    def referral_activity(cls):
        return 'referral_activity'

    @classmethod
    def login_page_market(cls):
        return 'login_page_market'

    @classmethod
    def ambassador_star(cls):
        return 'ambassador_star'

    @classmethod
    def viabtc_pool_transfer_status_update(cls, order_id):
        return f'viabtc_pool_transfer_order:{order_id}'

    @classmethod
    def activity_deposit_send_gift(cls, aty_id):
        return f'activity_deposit_send_gift:{aty_id}'

    @classmethod
    def activity_trade_send_gift(cls, aty_id):
        return f'activity_trade_send_gift:{aty_id}'

    @classmethod
    def ambassador_activity_send_gift(cls, aty_id):
        return f'ambassador_activity_send_gift:{aty_id}'

    @classmethod
    def deposit_bonus_activity_send_gift(cls, aty_id):
        return f'deposit_bonus_activity_send_gift:{aty_id}'

    @classmethod
    def activity_deposit_update_rank(cls, aty_id):
        return f'activity_deposit_update_rank:{aty_id}'

    @classmethod
    def local_transfer_reconciliation(cls, asset: str):
        return f'local_transfer_reconciliation:{asset}'

    @classmethod
    def cold_wallet_history(cls, asset: str, chain: str):
        return f'cold_wallet_history:{asset}:{chain}'

    @classmethod
    def simplex_fiat(cls, user_id: int):
        return f'simplex_fiat:{user_id}'

    @classmethod
    def process_lendable_amount(cls, asset: str):
        return f'process_lendable_amount:{asset}'

    @classmethod
    def sub_account_transfer(cls, from_user_id: int, to_user_id: int) -> str:
        return f'sub_account_transfer:{from_user_id}:{to_user_id}'

    @classmethod
    def sub_account_created(cls, main_user_id: int) -> str:
        return f'sub_account_created:{main_user_id}'

    @classmethod
    def sub_account_operation(cls, sub_user_id: int) -> str:
        # 子账号相关操作（禁用、取消禁用、授权、取消授权等）
        return f'sub_account_created:{sub_user_id}'

    @classmethod
    def follow_market(cls, user_id, trade_type):
        return f'follow_market:{trade_type}:{user_id}'
    
    @classmethod
    def market_price_alert(cls, user_id: int):
        return f"market_price_alert:{user_id}"

    @classmethod
    def amm_liquidity(cls, market: str):
        return f"amm_liquidity:{market}"

    @classmethod
    def referral_code(cls):
        return "referral_code"

    @classmethod
    def new_user_referral(cls):
        return "new_user_referral"

    @classmethod
    def security_question(cls, user_id: int):
        return f"security_question:{user_id}"

    @classmethod
    def mining_activity(cls, activity_id: int, user_id: int):
        return f"mining_activity:{activity_id}:{user_id}"

    @classmethod
    def kyc_operation(cls, user_id: int):
        return f"kyc_operation:{user_id}"

    @classmethod
    def kyc_pro_operation(cls, user_id: int):
        return f"kyc_pro_operation:{user_id}"

    @classmethod
    def kyc_liveness_operation(cls, user_id: int):
        return f"kyc_liveness_operation:{user_id}"

    @classmethod
    def screen_request(cls, request_id: int):
        return f"screen_request:{request_id}"

    @classmethod
    def edit_operation_sort(cls, operation_name: str):
        return f"edit_operation_sort:{operation_name}"

    @classmethod
    def airdrop_activity(cls, activity_id: int, user_id: int):
        return f"airdrop_activity:{activity_id}:{user_id}"

    @classmethod
    def deposit_bonus_activity(cls, activity_id: int, cfg_id: int, user_id: int):
        return f"deposit_bonus_activity:{activity_id}:{cfg_id}:{user_id}"

    @classmethod
    def discount_activity(cls, activity_id: int, user_id: int):
        return f"discount_activity:{activity_id}:{user_id}"

    @classmethod
    def abnormal_deposit_application(cls, application_id: int):
        return f"abnormal_deposit_application:{application_id}"

    @classmethod
    def exchange_order(cls, exchange_order_id: int):
        return f"exchange_order:{str(exchange_order_id)}"

    @classmethod
    def auto_invest_order(cls, plan_id: int):
        return f"auto_invest_order:{str(plan_id)}"

    @classmethod
    def auto_invest_task(cls, plan_id: int):
        return f"auto_invest_task:{str(plan_id)}"

    @classmethod
    def create_strategy(cls, user_id: int):
        return f'create_strategy:{user_id}'

    @classmethod
    def user_strategy(cls, strategy_id: int):
        return f'user_strategy:{strategy_id}'

    @classmethod
    def user_pledge(cls, user_id: int, loan_asset: str):
        return f'user_pledge:{user_id}:{loan_asset}'

    @classmethod
    def exchange_order_sys_user(cls):
        return f"exchange_order_sys_user"

    @classmethod
    def ieo_activity(cls, activity_id: int, user_id: int):
        return f"ieo_activity:{activity_id}:{user_id}"

    @classmethod
    def ieo_order(cls, order_id: int):
        return f"ieo_order:{order_id}"

    @classmethod
    def discount_order(cls, order_id: int):
        return f"discount_order:{order_id}"

    @classmethod
    def receive_coupon(cls, pool_id: int):
        return f"receive_coupon:{pool_id}"

    @classmethod
    def exchange_coupon(cls, pool_id: int):
        return f"exchange_coupon:{pool_id}"

    @classmethod
    def tax_export(cls, user_id: int):
        return f'tax_export:{user_id}'

    @classmethod
    def use_coupon(cls, user_coupon_id: int):
        return f"use_coupon:{user_coupon_id}"

    @classmethod
    def use_coupon_by_user(cls, user_id: int):
        return f"use_coupon_by_user:{user_id}"

    @classmethod
    def invest_inc_coupon_transfer(cls, history_id: int):
        return f"invest_inc_coupon_transfer:{history_id}"

    @classmethod
    def fifth_box(cls, box_type: str):
        return f'fifth_box:{box_type}'

    @classmethod
    def sixth_remain_reward(cls, reward_type: str):
        return f'sixth_remain_reward:{reward_type}'

    @classmethod
    def sixth_activity(cls, user_id: int):
        return f'sixth_activity:{user_id}'

    @classmethod
    def sixth_activity_coupon(cls, pool_id: int):
        return f'sixth_activity_coupon:{pool_id}'

    @classmethod
    def seventh_remain_reward(cls, reward_type: str):
        return f'seventh_remain_reward:{reward_type}'

    @classmethod
    def seventh_activity(cls, user_id: int):
        return f'seventh_activity:{user_id}'

    @classmethod
    def seventh_activity_coupon(cls, pool_id: int):
        return f'seventh_activity_coupon:{pool_id}'

    @classmethod
    def copy_trader_favorite(cls, user_id: int):
        return f"copy_trader_favorite:{user_id}"

    @classmethod
    def copy_trader(cls, user_id: int):
        return f"copy_trader:{user_id}"

    @classmethod
    def copy_trader_transfer(cls, user_id: int):
        return f"copy_trader_transfer:{user_id}"

    @classmethod
    def copy_follower(cls, user_id: int):
        return f"copy_follower:{user_id}"

    @classmethod
    def copy_follower_sub(cls, sub_user_id: int):
        return f"copy_follower_sub:{sub_user_id}"

    @classmethod
    def launch_pool(cls, pool_id: int):
        return f'launch_pool:{pool_id}'

    @classmethod
    def pre_trading_fee_transfer(cls, asset: str):
        return f'pre_trading_fee_transfer:{asset}'

    @classmethod
    def pre_trading(cls, user_id: int, asset: str):
        return f'pre_trading:{user_id}:{asset}'

    @classmethod
    def pre_trading_settle(cls, asset: str):
        return f'pre_trading_settle:{asset}'

    @classmethod
    def risk_config_group(cls, group_name: str):
        return f'risk_config_group:{group_name}'

    @classmethod
    def set_settle_switch(cls, user_id: int, market: str):
        return f'set_settle_switch:{user_id}:{market}'

    @classmethod
    def running_user_tag(cls, report_date_str: str, num: int, high: bool = False):
        return f'running_user_tag:{report_date_str}:{num}:{high}'

    @classmethod
    def running_user_tag_handler(cls, handler_name: str):
        return f'running_user_tag_handler:{handler_name}'
    
    @classmethod
    def third_party_account_operation(cls, third_party_id: str):
        """第三方账号操作(注册、绑定、解绑)"""
        return f'third_party_account_operation:{third_party_id}'

    @classmethod
    def add_activity_user_pool(cls, pool_id):
        return f"add_activity_user_pool:{pool_id}"
    
    @classmethod
    def wallet_sign(cls, chain: str):
        return f"wallet_sign_{chain}"
    
    @classmethod
    def satoshi_bid(cls):
        return "satoshi_bid"

    @classmethod
    def staking_operation(cls, asset: str, user_id: int = None):
        return f"staking_operation:{user_id}:{asset}" if user_id else f"staking_operation:{asset}" 

    @classmethod
    def insight_operation(cls, id_):
        return f'insight_operation:{id_}'

    @classmethod
    def academy_operation(cls, id_):
        return f'academy_operation:{id_}'

    @classmethod
    def create_user_pay_channel(cls, user_id: int):
        return f"create_user_pay_channel_{user_id}"

    @classmethod
    def update_user_pay_channel(cls, user_id: int, item_id: str):
        return f"update_user_pay_channel_{user_id}_{item_id}"

    @classmethod
    def p2p_advertising(cls, adv_id):
        return f"p2p_advertising:{adv_id}"

    @classmethod
    def p2p_create_advertising(cls, user_id: int, base: str, adv_type: str):
        return f"p2p_create_advertising_{user_id}_{base}_{adv_type}"

    @classmethod
    def p2p_advertising_online(cls, user_id: int):
        return f"p2p_advertising_online:{user_id}"

    @classmethod
    def create_p2p_order(cls, user_id: int):
        return f"create_p2p_order_{user_id}"

    @classmethod
    def update_p2p_user(cls, user_id: int):
        return f"update_p2p_user:{user_id}"

    @classmethod
    def p2p_order(cls, order_id: int):
        return f"p2p_order_{order_id}"

    @classmethod
    def create_p2p_order_complaint(cls, order_id):
        return f"create_p2p_order_complaint_{order_id}"

    @classmethod
    def p2p_order_complaint(cls, complaint_id):
        return f"create_p2p_order_complaint_{complaint_id}"

    @classmethod
    def p2p_order_complaint_user(cls, user_id, complaint_id):
        return f"create_p2p_order_complaint_{user_id}_{complaint_id}"

    @classmethod
    def p2p_order_trans(cls, trans_id):
        return f"p2p_order_trans_{trans_id}"

    @classmethod
    def p2p_order_stock(cls, trans_id):
        return f"p2p_order_stock_{trans_id}"

    @classmethod
    def p2p_put_pay_channel(cls, channel_name):
        return f"p2p_put_pay_channel_{channel_name}"
    
    @classmethod
    def admin_export(cls):
        return "admin_export"
    
    @classmethod
    def transfer_cleaned_balance(cls, trans_id: int):
        return f"transfer_cleaned_balance:{trans_id}"

    @classmethod
    def airdrop_equity_send(cls, user_equity_id: int):
        return f"airdrop_equity_send:{user_equity_id}"

    @classmethod
    def cashback_equity_settle(cls, user_id: int):
        return f"cashback_equity_settle:{user_id}"

    @classmethod
    def p2p_margin_trans(cls, trans_id: int):
        return f"p2p_margin_trans:{trans_id}"

    @classmethod
    def p2p_user_margin(cls, user_id: int):
        return f"p2p_user_margin:{user_id}"

    @classmethod
    def p2p_margin_status(cls, trans_id: int):
        return f"p2p_margin_status:{trans_id}"

    def p2p_margin_to_refund(cls, user_id: int):
        return f"p2p_margin_refund:{user_id}"

    @classmethod
    def comment_tip_operation(cls, user_id: int):
        return f"comment_tip_operation:{user_id}"

    @classmethod
    def mission_group_user(cls):
        return "mission_group_user"

    @classmethod
    def ambassador_package(cls, package_id: int):
        return f"ambassador_package:{package_id}"
    
    @classmethod
    def balance_cost_update(cls):
        return "balance_cost_update"


class Locked(FrequencyExceeded):

    def __init__(self, key: str):
        super().__init__(data=key)


class CacheLock:

    def __init__(self, key: str, *,
                 ttl: int = None,
                 wait: Union[bool, float] = False):
        self._key = key
        self._ttl = ttl or 3600
        self._wait = wait
        self._cache = LockCache(key)

    def __enter__(self):
        self.acquire()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.release()

    def test(self) -> bool:
        return self._cache.exists()

    def acquire(self, *, wait: Union[bool, float] = None):
        if wait is None:
            wait = self._wait
        max_wait = 60 if isinstance(wait, bool) else wait

        t0 = time.time()
        while not self._cache.set('1', ex=self._ttl, nx=True):
            if not wait or time.time() - t0 >= max_wait:
                raise Locked(self._key)
            time.sleep(0.2)

    def release(self):
        self._cache.delete()


def _gen_args_key(func: FunctionType, args: tuple, kwargs: dict,
                  only: Optional[list] = None) -> str:
    key = func_args_to_str(func, args, kwargs, only=only)
    if len(key) > 88:  # ceil(512 / 8 * log(256, 64)) == 86
        key = b64encode(sha512(key.encode()).digest()).decode()
    return key


def lock_call(key: str = None, *,
              with_args: Union[bool, int, str, List[Union[int, str]]] = False,
              wait: Union[bool, float] = False,
              ttl: int = None):
    """
    Locks a function call until it's finished.
    :param key: unique key for the lock (defaults to '{module}.{__qualname__}')
    :param wait: whether to wait if already locked (float for max waiting time)
    :param with_args: whether to include arguments in the key
    :param lock_type: cache / db
    :param ttl: TTL of lock

    Examples:
        @lock_call('abc')
        @lock_call(wait=True)
        @lock_call(wait=60)
        @lock_call(with_args=True)
        @lock_call(with_args=[0, 'x'])
    """
    if key is not None and not isinstance(key, str):
        raise TypeError(
            f'invalid key: {key!r}'
            f' (Did you forget to append parentheses to the decorator?)')

    if isinstance(with_args, str) \
            or (isinstance(with_args, int) and not isinstance(with_args, bool)):  # bool is int too
        with_args = [with_args]

    def dec(func):
        nonlocal key
        key = key or LockKeys.func_lock(func_to_str(func))

        def wrapper(_func, *args, **kwargs):
            if with_args is False:
                _key = key
            else:
                _args_key = _gen_args_key(
                    _func, args, kwargs,
                    None if with_args is True else with_args)
                _key = f'{key}:{_args_key}'
            _lock_args = dict(wait=wait)
            if ttl is not None:
                _lock_args['ttl'] = ttl
            _lock = CacheLock(_key, **_lock_args)
            with _lock:
                return _func(*args, **kwargs)

        return decorate(func, wrapper)

    return dec
