# -*- coding: utf-8 -*-
import datetime
import json
import re
from collections import defaultdict
from datetime import date, timedelta
from decimal import Decimal
from enum import Enum
from gettext import gettext
from typing import Tuple, Dict, Set, Union, Optional, List, Iterable, Any
from sqlalchemy import or_, func
from pyroaring import BitMap
from flask import current_app

from werkzeug.datastructures import ImmutableMultiDict
from app.business.clients.server import PerpetualServerClient
from app.business.market_maker import MarketMakerHelper
from app.business.site import CountrySettings, BusinessSettings
from app.common.constants import EmailCodeType
from app.exceptions.basic import NotSupportedAsPolicyRequires, NotSupportedByCountryNotKYC, InvalidArgument, \
    ServiceUnavailable
from app.models.kyc import KYCInstitution, UserRiskScreen, InstitutionCompany
from app.utils.http_client import RESTClient
from app.utils.net import get_url_base
from app.utils.push import MobilePusher, PushTag
from app.config import config
from app.caches.auth import EmailCodeCache, User<PERSON>ogin<PERSON>okenCache, ApiAuthCache, UserLoginTokenDeleteCache
from app.models.margin import MarginLoanOrder
from app.models.referral import AmbassadorAgentHistory, ReferralHistory
from app.models.p2p import P2pOrder, P2pOrderComplaint, P2pUserMargin
from app.models.copy_trading import CopyTraderUser
from app.models.risk_control import RiskUser
from app.models.security import SecurityResetApplication, SecurityToolHistory
from app.models.user import (
    ApiAuth, KycVerificationPro, MarketMaker, RiskUserSource, SignOffUser, ThirdPartyAccount, UserLoginState,
    LoginRelationHistory, UserPreference, UserSetting, OperationLog
)
from app.models.user import UserPreference as UserPreferenceModel
from app.models.operation import NameBlockWords
from app.models.strategy import StrategyRunUserStatus
from app.models.wallet import AssetChainConfig, Withdrawal
from .func_cache import cached
from app.models.operation import UserChannelStatistics

from .clients import ServerClient
from .lock import CacheLock, LockKeys, lock_call
from .prices import PriceManager
from ..assets import asset_to_chains, list_all_assets
from ..caches import (UserSettingsCache, UserPreferencesCache,
                      UserActivenessCache, PerpetualMarketCache)
from ..caches.operation import CurrentPortraitHashCache
from ..caches.prices import InvisibleAssetsCache
from ..caches.user import UserVisitPermissionCache, NameBlockWordsCache, UserConfigKeyCache
from ..caches.user import AbnormalUserCache, UserRealTimeActivenessCache, UserActiveMemCache
from ..common import (
    TwoFAType, Language, Currency, get_country, PricingBasisType, GuideType,
    CeleryQueues, PerpetualMarketType, CountryInfo
)
from ..exceptions import NotSupportedByCountryInOnlyWithdrawal, WithdrawalForbiddenAfterSecurityEditing, \
    WithdrawalForbiddenAfterWithdrawPasswordEditing, NameTooLong, AccountNameToLong, AccountNameExists
from ..models import (
    SubAccount, UserActivenessHistory, LoginHistory, User, UserExtra,
    db, KycVerification, ClearedUser, CountrySetting,
)
from ..models.admin_tag import AdminTagUser
from ..utils import (
    ConfigField, BaseConfig, ConfigMode, GeoIP, today,
    get_mobile_country, current_timestamp, quantize_amount,
    timestamp_to_datetime, batch_iter, celery_task, AWSBucketPublic, AWSBucketPrivate,
)
from ..utils.config_ import _convert
from ..utils.date_ import now, date_to_datetime

from flask_babel import gettext as _

from ..utils.file import open_file_from_url, ThumbnailScale

LanguageCountries = ImmutableMultiDict([
    (_v[0], _country) for _v in
    [
        (Language.EN_US,
         ['USA', 'GBR', 'IND', 'CAN', 'SGP', 'AUS', 'PHL', 'PAK',
          'ZAF', 'NZL', 'BGD', 'IRL', 'NGA', 'NLD', 'GHA']),
        (Language.ZH_HANS_CN, ['CHN']),
        (Language.ZH_HANT_HK, ['HKG', 'TWN', 'MYS', 'MAC']),
        (Language.FA_IR, ['IRN', 'AFG', 'TJK']),
        (Language.AR_AE,
         ['ARE', 'SAU', 'EGY', 'DZA', 'IRQ', 'QAT', 'OMN', 'BHR', 'KWT',
          'SYR', 'MAR', 'LBN', 'JOR', 'LBY', 'TUN', 'YEM', 'MRT', 'SOM']),
        (Language.TR_TR, ['TUR', 'AZE']),
        (Language.RU_KZ, ['RUS', 'BLR', 'UKR', 'KAZ', 'KGZ']),
        (Language.ES_ES,
         ['ESP', 'ARG', 'BOL', 'CHL', 'COL', 'CRI', 'CUB', 'DOM', 'ECU',
          'SLV', 'GNQ', 'GTM', 'HND', 'MEX', 'NIC', 'PAN', 'PRY', 'PER',
          'PRI', 'URY', 'VEN', 'GIB']),
        (Language.PT_PT, ['BRA', 'PRT']),
        (Language.FR_FR,
         ['FRA', 'CHE', 'BEL', 'LUX', 'MCO', 'MAR', 'DZA', 'TUN', 'SDN',
          'BEN', 'CIV', 'CMR', 'COD', 'SEN', 'MDG', 'MLI', 'REU', 'TCD',
          'TGO', 'BFA', 'GIN', 'GAB', 'BDI', 'HTI', 'MRT']),
        (Language.DE_DE, ['DEU', 'AUT', 'CHE', 'LUX']),
        (Language.JA_JP, ['JPN']),
        (Language.KO_KP, ['KOR']),
        (Language.VI_VN, ['VNM']),
        (Language.ID_ID, ['IDN'])]
    for _country in _v[1]
    ])


class UserSettings(BaseConfig):

    F = ConfigField
    MODEL = UserSetting

    _supports_valid_intervals = True

    daily_withdrawal_limit: Decimal = F(Decimal, '日提现限额', default=None)
    withdrawal_limit_30_days: Decimal = F(Decimal, '30日提现限额', default=None)

    login_disabled_by_admin: bool | ConfigField = F(bool, '禁止登录', default=False)

    spot_trading_disabled_by_admin: bool = F(bool, '禁止现货交易', default=False,
                                             remark="含现货、(碎币)兑换、策略交易")
    margin_trading_disabled_by_admin: bool = F(bool, '禁止杠杆交易', default=False)
    perpetual_trading_disabled_by_admin: bool = F(bool, '禁止永续交易', default=False)

    # https://viabtc.yuque.com/description/coinex/pe3361mzk9rch1p9#qktRT
    # 以下属性不能直接修改，请调用 P2pPermissionManager 类去修改
    p2p_all_disabled: bool = F(bool, 'P2P批量权限禁止', default=False)
    p2p_buy_disabled: bool = F(bool, 'p2p禁止下买单', default=False)
    p2p_sell_disabled: bool = F(bool, 'p2p禁止下卖单', default=False)

    p2p_merchant_buy_accept_disabled: bool = F(bool, 'p2p买单广告禁止接单', default=False)
    p2p_merchant_sell_accept_disabled: bool = F(bool, 'p2p卖单广告禁止接单', default=False)
    p2p_disabled_trans: bool = F(bool, 'p2p禁止放币', default=False)
    # 临时处理方案，后续会去除
    p2p_high_risk_user: bool = F(bool, 'p2p高风险用户', default=False, remark="打开该权限后，该用户做为被申诉方会被立即禁止提现。")
    p2p_t_plus_n_days: int = F(int, 'p2p T+N限制', default=None, remark="可选0-10")

    margin_loan_enabled: bool | ConfigField = F(bool, '允许杠杆借贷', default=True, remark="不影响自动续借")

    withdrawals_disabled_by_admin: bool | ConfigField = F(bool, '禁止提现', default=False)
    withdrawals_disabled_after_security_editing: bool | ConfigField \
        = F(bool, '安全工具修改后禁止提现', default=False)

    withdrawals_disabled_after_withdraw_password_editing: bool | ConfigField = F(bool, '提现密码修改后禁止提现', default=False)

    withdrawals_disabled_due_to_margin_arrears: bool \
        = F(bool, '杠杆欠款禁止提现', default=False, admin_editable=False)

    withdrawals_disabled_due_to_pledge_arrears: bool \
        = F(bool, '质押欠款禁止提现', default=False, admin_editable=False)

    amm_withdrawals_disabled: bool = F(bool, '禁止提取AMM流动性', default=False)

    withdrawals_disabled_due_to_credit_risk: bool \
        = F(bool, '授信风险禁止提现', default=False, admin_editable=False)

    spot_trading_disabled_by_credit: bool \
        = F(bool, '授信设置-禁止现货交易', default=False, admin_editable=False,
            remark="因受限设置交易受限，禁止现货交易")
    perpetual_limited_by_credit: bool \
        = F(bool, '授信设置-合约仅平/减仓', admin_editable=False, default=False)
    only_allowed_margin_accounts_by_credit: set \
        = F(set, '授信设置-仅允许交易的杠杆市场', admin_editable=False, default=(),
            remark="因受限设置交易受限，仅允许在指定的杠杆市场交易，不限制其他杠杆市场，为空时不限制。")

    forbidden_margin_accounts: set \
        = F(set, '爆仓中禁止杠杆交易', admin_editable=False, default=())
    forbidden_margin_flat: set \
        = F(set, '爆仓中禁止杠杆还币', admin_editable=False, default=())

    sub_account_transfer_out_disabled: bool \
        = F(bool, '禁止主子账户划出', default=False, admin_editable=False)

    trading_disabled_by_risk_control: bool \
        = F(bool, '风控禁止交易', admin_editable=False, default=False, remark="禁止所有类型交易")
    margin_loan_disabled_by_risk_control: bool \
        = F(bool, '风控禁止杠杆借贷', admin_editable=False, default=False)
    sub_account_transfer_disabled_by_risk_control: bool \
        = F(bool, '风控禁止主/子账户划转', admin_editable=False, default=False)
    red_packet_disabled_by_risk_control: bool \
        = F(bool, '风控禁止发红包', admin_editable=False, default=False)
    spot_trading_disabled_by_risk_control: bool \
        = F(bool, '风控禁止现货交易', admin_editable=False, default=False)
    perpetual_limited_by_risk_control: bool \
        = F(bool, '风控合约仅平/减仓', admin_editable=False, default=False)
    perpetual_transfer_out_disabled_by_risk_control: bool \
        = F(bool, '风控禁止合约划出', admin_editable=False, default=False)
    margin_transfer_out_disabled_by_risk_control: bool \
        = F(bool, '风控禁止杠杆划出', admin_editable=False, default=False)
    only_allowed_margin_accounts_by_risk_control: set \
        = F(set, '风控仅允许交易的杠杆市场', admin_editable=False, default=(),
            remark="仅允许在指定的杠杆市场交易，不限制其他杠杆市场，为空时不限制。")
    balance_in_assets_disabled_by_risk_control: set \
        = F(set, '风控卡充值币种集合', admin_editable=False, default=(),
            remark="风控仅限制集合中币种充值不会入帐，不限制其他币种，为空时不限制。")

    def __init__(self, user_id: int, mode: ConfigMode = ConfigMode.SNAPSHOT):
        self._user_id = user_id
        super().__init__(mode)

    @property
    def login_enabled(self) -> bool:
        return not any((
            self.login_disabled_by_admin,
        ))

    @property
    def withdrawals_enabled(self) -> bool:
        return not any([
            self.withdrawals_disabled_by_admin,
            self.withdrawals_disabled_due_to_margin_arrears,
            self.withdrawals_disabled_due_to_pledge_arrears,
            self.withdrawals_disabled_due_to_credit_risk,
            self.withdrawals_disabled_after_security_editing,
            self.withdrawals_disabled_after_withdraw_password_editing
        ])

    @property
    def withdrawals_sendable(self) -> bool:
        return not any([
            self.withdrawals_disabled_by_admin,
            self.withdrawals_disabled_due_to_margin_arrears,
            self.withdrawals_disabled_due_to_pledge_arrears,
            self.withdrawals_disabled_due_to_credit_risk
        ])

    @property
    def amm_withdrawals_enabled(self) -> bool:
        return not any([
            self.amm_withdrawals_disabled
        ])

    @property
    def spot_trading_enabled(self) -> bool:
        """只限制现货交易，不限制杠杆"""
        return not any((
            self.spot_trading_disabled_by_admin,
            self.trading_disabled_by_risk_control,
            self.spot_trading_disabled_by_risk_control,
            self.spot_trading_disabled_by_credit,
        ))

    @property
    def margin_trading_enabled(self) -> bool:
        return not any((
            self.margin_trading_disabled_by_admin,
            self.trading_disabled_by_risk_control
        ))

    @property
    def perpetual_trading_enabled(self) -> bool:
        return not any((
            self.perpetual_trading_disabled_by_admin,
            self.trading_disabled_by_risk_control
        ))

    @property
    def perpetual_limited(self) -> bool:
        return self.perpetual_limited_by_risk_control or self.perpetual_limited_by_credit

    @property
    def sub_account_transfer_enabled(self) -> bool:
        return not any([
            self.sub_account_transfer_disabled_by_risk_control
        ])

    @property
    def margin_transfer_out_enabled(self) -> bool:
        return not any([
            self.margin_transfer_out_disabled_by_risk_control
        ])

    @property
    def perpetual_transfer_out_enabled(self) -> bool:
        return not any([
            self.perpetual_transfer_out_disabled_by_risk_control
        ])

    @property
    def red_packet_enabled(self) -> bool:
        return not any([
            self.red_packet_disabled_by_risk_control
        ])

    def check_withdrawals_disabled_after_editing(self):
        if self.withdrawals_disabled_after_security_editing:
            raise WithdrawalForbiddenAfterSecurityEditing
        elif self.withdrawals_disabled_after_withdraw_password_editing:
            raise WithdrawalForbiddenAfterWithdrawPasswordEditing

    def can_margin_loan(self) -> bool:
        return self.margin_loan_enabled and not self.margin_loan_disabled_by_risk_control

    def can_margin_account_trade(self, account_id: int) -> bool:
        if account_id in self.forbidden_margin_accounts:
            return False
        accounts1 = self.only_allowed_margin_accounts_by_risk_control
        cond1 = accounts1 and account_id in accounts1 if accounts1 else True
        accounts2 = self.only_allowed_margin_accounts_by_credit
        cond2 = accounts2 and account_id in accounts2 if accounts2 else True
        return cond1 and cond2

    def _get_all(self) -> Tuple[Dict[str, str],
                                Dict[str, BaseConfig.TimeInterval]]:
        values = {}
        valid_intervals = {}
        cache = UserSettingsCache(self._user_id)
        for name, data in cache.get_values().items():
            value, valid_from, valid_till \
                = cache.load_value_with_valid_interval(data)
            if value is None:
                continue
            values[name] = value
            valid_intervals[name] = valid_from, valid_till
        return values, valid_intervals

    def _get_one(self, name: str) -> Optional[str]:
        cache = UserSettingsCache(self._user_id)
        value, valid_from, valid_till \
            = cache.load_value_with_valid_interval(cache.get_value(name))
        if value is None:
            return None
        _now = current_timestamp()
        if (valid_from is not None and valid_from > _now
                or valid_till is not None and valid_till <= _now):
            return None
        return value

    def _set_one(self, name: str, value: str):
        cache = UserSettingsCache(self._user_id)
        if cache.load_value_with_valid_interval(cache.get_value(name)) \
                == (value, None, None):
            return
        model = self.MODEL
        row = model.query \
            .filter(model.user_id == self._user_id,
                    model.key == name) \
            .first()
        if not row:
            row = model(user_id=self._user_id, key=name)
            db.session.add(row)
        row.value = value
        row.status = model.Status.VALID
        row.valid_from = None
        row.valid_till = None
        db.session.commit()
        cache.set_value(
            name, cache.dump_value_with_valid_interval(value, None, None))
        UserConfigKeyCache(self.MODEL, name, value).add(self._user_id)

    def _del_one(self, name: str):
        model = self.MODEL
        row = model.query \
            .filter(model.user_id == self._user_id,
                    model.key == name) \
            .first()
        if not row:
            return
        row.status = model.Status.DELETED
        db.session.commit()
        UserSettingsCache(self._user_id).del_value(name)

    def _set_one_with_valid_interval(self, name: str, value: str,
                                     valid_from: float = None,
                                     valid_till: float = None):
        model = self.MODEL
        row = model.query \
            .filter(model.user_id == self._user_id,
                    model.key == name) \
            .first()
        if not row:
            row = model(user_id=self._user_id, key=name)
            db.session.add(row)
        row.value = value
        row.valid_from = (timestamp_to_datetime(valid_from)
                          if valid_from is not None
                          else None),
        row.valid_till = (timestamp_to_datetime(valid_till)
                          if valid_till is not None
                          else None)
        row.status = model.Status.VALID
        db.session.commit()
        cache = UserSettingsCache(self._user_id)
        cache.set_value(
            name,
            cache.dump_value_with_valid_interval(value, valid_from, valid_till)
        )

    def safe_add(self, field: ConfigField, value: str):
        def _add(c, v):
            if isinstance(c, tuple):
                c += (v,)
            elif isinstance(c, list):
                c.append(v)
            elif isinstance(c, set):
                c.add(v)
            elif isinstance(c, dict):
                c.update(v)
            else:
                raise TypeError
            return c
        return self._safe_op(field, value, _add)

    def safe_remove(self, field: ConfigField, value: str):
        def _remove(c, v):
            if isinstance(c, tuple):
                i = c.index(v)
                c = tuple(x for x in range(len(c)) if x != i)
            elif isinstance(c, list):
                c.remove(v)
            elif isinstance(c, set):
                c.discard(v)
            elif isinstance(c, dict):
                del c[v]
            else:
                raise TypeError
            return c
        return self._safe_op(field, value, _remove)

    def _safe_op(self, field, value, op):
        if not field.is_collection_type:
            raise ValueError
        with CacheLock(LockKeys.setting_lock(f'{self._user_id}:{field.name}'), ttl=30, wait=3):
            s = UserSettings(self._user_id, ConfigMode.REAL_TIME)   # use realtime value
            v = s._get(field.name)
            v = type(v)(v)  # convert patched type to source type
            v = op(v, value)
            self._set(field.name, v)
        return v

    def get_field_record(self, field):
        model = self.MODEL
        row = model.query \
            .filter(model.user_id == self._user_id,
                    model.key == field) \
            .first()
        return row


class UserPreferences(BaseConfig):

    F = ConfigField
    MODEL = UserPreference

    # 用户最新设置的语言（无论是app端还是web端），接口层面返回用户语言相关的内容时，用language
    language: Language = F(Language, '语言', default=Language.EN_US)
    # 标识用户在app端设置的语言，此处默认值设置为None，app推送时用app_language
    app_language: Language = F(Language, 'App语言', default=None)
    # 标识用户在web端设置的语言，此处默认值设置为None，web推送时用web_language
    web_language: Language = F(Language, 'Web语言', default=None)
    timezone_offset: int = F(int, '时区 / min', default=0)  # in minutes
    currency: Currency = F(Currency, '汇率币种', default=Currency.USD)
    host_url: str = F(str, '访问域名', default=config['SITE_URL'])
    withdrawal_fee_asset: str = F(str, '提现手续费币种', default='')  # 只用于前端提现时展示，提现接口不会读取
    allows_announcement_emails: bool | ConfigField = F(bool, '邮件推送-公告', default=True)
    allows_activity_emails: bool | ConfigField = F(bool, '邮件推送-活动', default=True)
    allows_blog_emails: bool | ConfigField = F(bool, '邮件推送-博客', default=True)
    allows_deposit_withdrawal_emails: bool | ConfigField = F(bool, '邮件推送-充值提现', default=True)
    allows_announcement_app: bool | ConfigField = F(bool, 'APP推送-公告', default=True)
    allows_activity_app: bool | ConfigField = F(bool, 'APP推送-活动', default=True)
    allows_blog_app: bool | ConfigField = F(bool, 'APP推送-博客', default=True)

    app_deposit_withdrawal_notice: bool = F(bool, '充值提现通知', default=True)
    app_exchange_notice: bool = F(bool, '兑换通知', default=True)
    app_ieo_notice: bool = F(bool, 'DOCK订单通知', default=True)
    app_holding_asset_notice: bool = F(bool, '持仓币种通知', default=False)
    app_favorite_asset_notice: bool = F(bool, '自选币种通知', default=False)
    app_asset_level_break_notice: bool = F(bool, '币种价格突破通知', default=True)
    app_new_asset_rise_notice: bool = F(bool, '新币上涨提醒', default=True)
    app_spot_limit_order_notice: bool = F(bool, '币币-限价订单通知', default=True)
    app_spot_stop_order_notice: bool = F(bool, '币币-计划委托通知', default=True)
    app_perpetual_limit_order_notice: bool = F(bool, '合约-限价订单通知', default=True)
    app_perpetual_stop_order_notice: bool = F(bool, '合约-计划委托通知', default=True)
    app_perpetual_profit_loss_notice: bool = F(bool, '合约-止盈止损通知', default=True)
    app_information_notice: bool = F(bool, '新资讯通知', default=True)

    allows_announcement_navigation: bool = F(bool, '允许展示交易页公告导航条', default=True)
    two_fa_type: TwoFAType = F(TwoFAType, '2FA', default=TwoFAType.NONE)
    order_confirmation: bool = F(bool, '币币下单确认', default=True)
    perpetual_order_confirmation: bool = F(bool, '合约下单确认', default=True)
    cet_discount_enabled: bool = F(bool, '开启 CET 抵扣', default=False)
    perpetual_pricing_basis: PricingBasisType = F(PricingBasisType, '合约计价基准', default=PricingBasisType.SIGN_PRICE)
    # enable margin agreement.
    opening_margin_function = F(bool, '开通杠杆协议', default=False)
    opening_perpetual_trading = F(bool, '开通合约协议', default=False)
    opening_sub_account_function = F(bool, '开启子账号功能', default=False)
    opening_account_profit_loss = F(bool, '开通账户盈亏分析', default=True)
    opening_withdraw_password = F(bool, '开启提现密码', default=False)
    opening_web_login_ip_locking = F(bool, '开启登录IP锁定(Web)', default=False)
    opening_web_security_login_duration = F(bool, '开启安全登录时长(Web)', default=False)
    opening_launch_pool_mining = F(bool, '开通挖矿协议', default=False)
    opening_deposit_bonus = F(bool, '开通充值福利用户协议', default=False)

    opening_trade_password = F(bool, '开启交易密码', default=False)

    anti_phishing_code = F(str, '防钓鱼码', default='')
    auto_put_margin_loan_order = F(bool, '杠杆自动借币', default=False)
    auto_flat_margin_loan_order = F(bool, '杠杆自动还币', default=False)
    sorted_own_assets: List[str] = F(list, '持仓币种排序', default=None)
    view_perpetual_tutorial_video: bool = F(bool, '观看合约视频', default=False)
    view_perpetual_tutorial_question: bool = F(bool, '完成合约测试题', default=False)
    sorted_new_entrances: List[str] = F(list, '安卓快捷入口排序', default=[])
    ios_sorted_new_entrances: List[str] = F(list, 'IOS快捷入口排序', default=[])
    sorted_new_entrances_version3: List[str] = F(list, '安卓快捷入口排序version3', default=[])
    ios_sorted_new_entrances_version3: List[str] = F(list, 'IOS快捷入口排序version3', default=[])
    guide_type: GuideType = F(GuideType, "用户引导类型", default=GuideType.HAS_TRADE_USER)

    deposit_guide: bool = F(bool, '充值引导', default=False)
    fiat_guide: bool = F(bool, '法币引导', default=False)
    spot_guide: bool = F(bool, '现货引导', default=False)
    self_forbid: bool = F(bool, '自我冻结', default=False)  # 只用于记录， 不做权限控制
    # margin_course: bool = F(bool, '杠杆教程', default=bool)
    # margin_guide: bool = F(bool, '杠杆引导', default=bool)
    # perpetual_course: bool = F(bool, '合约教程', default=bool)
    # perpetual_guide: bool = F(bool, '合约引导', default=bool)
    # perpetual_trade_guide: bool = F(bool, '合约下单引导', default=bool)

    news_popup_display: bool = F(bool, '交易页快讯弹窗', default=True)
    # p2p
    opening_p2p_function = F(bool, '开启p2p功能', default=False)
    opening_p2p_merchant_function = F(bool, '开启p2p商家功能', default=False)
    p2p_trade_fiat = F(str, '用户最后一次选择的法币', default="")     # 旧版本app使用，已废弃
    p2p_question = F(bool, 'p2p答题', default=False)

    app_spot_grid_take_profit_notice: bool = F(bool, '现货网格止盈通知', default=True)
    app_spot_grid_stop_loss_notice: bool = F(bool, '现货网格止损通知', default=True)
    app_spot_grid_exceed_price_notice: bool = F(bool, '现货市价超出网格价格区间', default=True)
    app_auto_invest_profit_arrive_notice: bool = F(bool, '自动定投到达盈利目标通知', default=True)
    followed_kline_analysts: List[int] = F(list, '关注的K线分析师ID', default=[])
    only_view_followed_analysts: bool = F(bool, '仅看关注的分析师', default=False)
    view_coinex_protocol: bool = F(bool, 'CoinEx服务协议弹窗', default=False)
    only_allow_sms_verification: bool = F(bool, '仅支持SMS发送验证码', default=False)
    third_party_message_accounts: Set[str] = F(set, '第三方短信账号', default=())
    view_comment_user_tos: bool = F(bool, '用户是否已同意评论服务条款', default=False)
    view_comment_user_agreement: bool = F(bool, '用户是否已同意评论用户协议', default=False)
    user_info_confirmed_for_comment: bool = F(bool, '用户是否已确认在评论服务中确认用户信息', default=False)
    app_comment_message_up_notice: bool = F(bool, '币种评论互动消息赞提醒', default=True)
    app_comment_message_reply_notice: bool = F(bool, '币种评论互动消息回复提醒', default=True)
    app_comment_message_at_notice: bool = F(bool, '币种评论互动消息at提醒', default=True)
    app_comment_message_tip_notice: bool = F(bool, '币种评论互动消息打赏提醒', default=True)

    perpetual_profit_real_rank_anonymous: bool = F(bool, '合约盈亏排行榜设置匿名', default=True)

    def __init__(self, user_id: int, mode: ConfigMode = ConfigMode.SNAPSHOT):
        self._user_id = user_id
        super().__init__(mode)

    def _get_all(self) -> dict:
        return UserPreferencesCache(self._user_id).get_values()

    def _get_one(self, name: str) -> Optional[str]:
        return UserPreferencesCache(self._user_id).get_value(name)

    def _set_one(self, name: str, value: str):
        cache = UserPreferencesCache(self._user_id)
        if cache.get_value(name) == value:
            return
        model = self.MODEL
        row = model.query \
            .filter(model.user_id == self._user_id,
                    model.key == name) \
            .first()
        if not row:
            row = model(user_id=self._user_id, key=name)
            db.session.add(row)
        row.value = value
        row.status = model.Status.VALID
        db.session.commit()
        cache.set_value(name, value)
        UserConfigKeyCache(self.MODEL, name, value).add(self._user_id)

    def _del_one(self, name: str):
        model = self.MODEL
        row = model.query \
            .filter(model.user_id == self._user_id,
                    model.key == name) \
            .first()
        if not row:
            return
        row.status = model.Status.DELETED
        db.session.commit()
        UserPreferencesCache(self._user_id).del_value(name)

    @classmethod
    def guide_list(cls):
        _list = [cls.deposit_guide, cls.fiat_guide, cls.spot_guide,
                 cls.view_perpetual_tutorial_video]
        return [i.name for i in _list]


def get_credit_user_and_sub_balance_to_usd(user_id: int) -> Decimal:
    """ 获取授信用户整账户的 现货、理财、合约、杠杆、AMM、质押资产 （注: 整账户=主账户+子账户） """
    from app.business.amm import batch_get_user_amm_assets
    from app.business.pledge.helper import get_user_pledge_unflat_amount_dict

    # 获取子账号，并剔除掉可执行新策略的策略子账号(无资产)
    main_user_id = user_id
    usable_sty_subs = StrategyRunUserStatus.query.filter(
        StrategyRunUserStatus.main_user_id == user_id,
        StrategyRunUserStatus.status == StrategyRunUserStatus.Status.USABLE,
    ).with_entities(StrategyRunUserStatus.user_id).all()
    usable_sty_sub_ids = {i.user_id for i in usable_sty_subs}
    sub_accounts = SubAccount.query.filter(
        SubAccount.main_user_id == user_id,
        SubAccount.is_visible.is_(True),
    ).with_entities(SubAccount.user_id).all()
    sub_ids = [sub.user_id for sub in sub_accounts]
    user_ids = list(set(sub_ids + [user_id]) - usable_sty_sub_ids)

    client = ServerClient()
    asset_price_map = PriceManager.assets_to_usd()
    total_spot_usd = Decimal(0)
    include_assets = set(list_all_assets()) - InvisibleAssetsCache().read()
    for user_id in user_ids:
        # 现货、杠杆、理财、质押
        accounts_balances_map = client.get_user_accounts_balances(user_id)
        for _account_id, assets in accounts_balances_map.items():
            for asset, balances in assets.items():
                if asset not in include_assets:
                    continue
                _price = asset_price_map.get(asset, Decimal('0'))
                total_spot_usd += (balances["available"] + balances["frozen"]) * _price

    total_margin_unflat_usd = Decimal(0)
    for chunk_uids in batch_iter(user_ids, 1000):
        # sql:18s
        ch_loans = MarginLoanOrder.query.filter(
            MarginLoanOrder.user_id.in_(chunk_uids),
            MarginLoanOrder.status == MarginLoanOrder.StatusType.PASS,
        ).with_entities(
            MarginLoanOrder.asset,
            func.sum(MarginLoanOrder.unflat_amount),
        ).group_by(
            MarginLoanOrder.asset,
        ).all()
        for _asset, _unflat_amount in ch_loans:
            _price = asset_price_map.get(_asset, Decimal('0'))
            total_margin_unflat_usd += _unflat_amount * _price

    total_pledge_unflat_usd = Decimal(0)
    pledge_unflat_map = get_user_pledge_unflat_amount_dict(main_user_id)  # 只有主账号可以质押
    for _pl_asset, _pl_amount in pledge_unflat_map.items():
        _price = asset_price_map.get(_pl_asset, Decimal('0'))
        total_pledge_unflat_usd += _pl_amount * _price

    total_amm_usd = Decimal(0)
    user_amm_assets_map = batch_get_user_amm_assets(user_ids)
    for user_id, asset_amount_map in user_amm_assets_map.items():
        for asset, amount in asset_amount_map.items():
            total_amm_usd += amount * asset_price_map.get(asset, Decimal('0'))

    p_client = PerpetualServerClient()
    p_market_list = PerpetualMarketCache().get_market_list()
    p_asset_to_markets = defaultdict(list)
    p_market_data = PerpetualMarketCache().hgetall()
    for p_market, _info in p_market_data.items():
        # PerpetualMarketCache.balance_asset_to_markets
        _info = json.loads(_info)
        if _info['type'] == PerpetualMarketType.DIRECT:
            p_asset_to_markets[_info['money']].append(p_market)
        else:
            p_asset_to_markets[_info['stock']].append(p_market)

    total_perpetual_usd = Decimal(0)
    for user_id in user_ids:
        position_market_map = {i["market"]: i for i in p_client.position_pending(user_id)}
        perpetual_balances = p_client.get_user_balances(user_id)
        for asset, balance in perpetual_balances.items():
            equity = balance["balance_total"]
            for _market in p_asset_to_markets[asset]:
                if _market in p_market_list:
                    position_data = position_market_map.get(_market, defaultdict(Decimal))
                    equity += Decimal(position_data["margin_amount"])
                    equity += Decimal(position_data["profit_unreal"])
            total_perpetual_usd += equity * asset_price_map.get(asset, Decimal())

    total_usd = (total_spot_usd + total_amm_usd + total_perpetual_usd -
                 total_margin_unflat_usd - total_pledge_unflat_usd)
    return quantize_amount(total_usd, 8)


def get_user_accounts_balances_dict(user_id: int, included_assets: Iterable[str] | None = None) -> Dict[int, Decimal]:
    # 获取账号所有资产（不包含合约）
    user_accounts_balances_dict = ServerClient() \
        .get_user_accounts_balances(user_id)
    assets_dict = {}
    price_map = PriceManager.assets_to_usd()
    for account_id, assets in user_accounts_balances_dict.items():
        tmp = Decimal()
        for asset, values in assets.items():
            if included_assets and asset not in included_assets:
                continue
            item_usd_rate = price_map.get(asset, 0)
            market_value = ((values['available'] + values['frozen'])
                            * item_usd_rate)
            tmp += market_value
        assets_dict[int(account_id)] = tmp

    return assets_dict


def delete_user_login_token(login_token: str | UserLoginState):
    if isinstance(login_token, str):
        login_state = UserLoginState.query.filter(
            UserLoginState.token == login_token,
        ).first()
    else:
        login_state = login_token
    if login_state:
        # 删除用户登录的TOKEN，清除后用户无法登录
        UserLoginTokenCache(login_state.user_id).del_token(login_state.token)
        # 更新用户的登录状态，极端情况下可能会导致登录状态显示异常，无其他影响
        UserLoginTokenDeleteCache().add(token=login_state.token)


def is_user_active(user_id: int, date_: Union[date, int]) -> bool:
    if UserActivenessHistory.is_user_active(user_id, date_):
        return True
    if date_ in ((today_ := today()), today_ + timedelta(days=-1)):
        return UserActivenessCache(date_).has_user(user_id)
    return False


def set_user_active(user_id: int, platform: str):
    UserActiveMemCache().add(user_id, platform)


def is_user_real_time_active(user_id: int) -> bool:
    """先尝试从当前缓存查找，没有记录的话找上一个缓存"""
    ts = current_timestamp(to_int=True)
    if UserRealTimeActivenessCache(ts).has_user(user_id):
        return True
    return UserRealTimeActivenessCache(ts - 300).has_user(user_id)


def filter_active_users(start: Union[date, int] = None,
                        end: Union[date, int] = None) -> Set[int]:
    """查询为闭区间查询[start, end]"""
    users = set(UserActivenessHistory.filter_active_users(start, end))
    for date_ in ((today_ := today()), today_ + timedelta(days=-1)):
        if (start is None or date_ >= start) and (end is None or date_ <= end):
            users.update(UserActivenessCache(date_).get_users())
    return users


def import_active_user_histories():
    histories = defaultdict(lambda: defaultdict(int))
    for user_id, at in LoginHistory.query \
            .filter(LoginHistory.successful) \
            .with_entities(LoginHistory.user_id, LoginHistory.updated_at) \
            .order_by(LoginHistory.id):
        index, offset = UserActivenessHistory.date_to_offset(at.date())
        users = histories[index]
        users[user_id] = UserActivenessHistory.or_flags(users[user_id], offset)

    for index, users in histories.items():
        for user_id, flags in users.items():
            flags_rev = UserActivenessHistory.reverse_flags(flags)
            db.session.add(UserActivenessHistory(
                user_id=user_id,
                index=index,
                flags=flags,
                flags_rev=flags_rev
            ))
        db.session.commit()


def get_user_kyc_country(user_id):
    from app.models import KYCInstitution, InstitutionCompany
    kyc_ind = KycVerification.query.filter(
        # 包括筛查中的KYC记录
        KycVerification.status.in_([KycVerification.Status.PASSED, KycVerification.Status.SCREENING]),
        KycVerification.user_id == user_id,
    ).order_by(KycVerification.id.desc()).first()
    if kyc_ind:
        return kyc_ind.country

    kyc_ins = KYCInstitution.query.filter(
        KYCInstitution.status == KYCInstitution.Status.PASSED,
        KYCInstitution.user_id == user_id,
    ).order_by(KYCInstitution.id.desc()).first()
    if kyc_ins:
        company = InstitutionCompany.query.filter(
            InstitutionCompany.institution_id == kyc_ins.id).first()
        if company:
            return company.location_code


def get_user_remark(user: User) -> str:
    user_remark = user.remark or ''
    risk_source = RiskUserSource.query.filter(
        RiskUserSource.user_id == user.id,
        RiskUserSource.source == RiskUserSource.Source.RISK_CONTROL_BATCH_FORBID
    ).first()
    if (r:= risk_source) and risk_source.remark:
        if not user_remark:
            user_remark = r.remark
        else:
            user_remark += "; " + r.remark
    return user_remark

def get_users_remark_map(user_ids: List[int]) -> Dict[int, str]:

    users = User.query.filter(User.id.in_(user_ids)).with_entities(User.id, User.remark).all()
    user_remark = {user.id: user.remark or '' for user in users}

    risk_sources = RiskUserSource.query.filter(
        RiskUserSource.user_id.in_(user_ids),
        RiskUserSource.source == RiskUserSource.Source.RISK_CONTROL_BATCH_FORBID
    ).with_entities(RiskUserSource.user_id, RiskUserSource.remark).all()

    for user_id, remark in risk_sources:
        if not user_remark[user_id]:
            user_remark[user_id] = remark
        else:
            user_remark[user_id] += "; " + remark
    return user_remark

def update_user_location(user_id: int, tz_offset: int = None) -> Optional[str]:
    """更新逻辑参考：https://app.clickup.com/t/86ephhjuv"""
    user: User = User.query.get(user_id)
    if user is None:
        raise ValueError(f'invalid user id: {user_id!r}')
    if user.is_sub_account:
        return None

    origin_location = user.location_code
    update_to_country = None
    if (kyc_country := get_user_kyc_country(user_id)) is not None:
        update_to_country = get_country(kyc_country)
    elif tz_offset is not None:
        # kyc状态变为非 通过/风险筛查 的其他状态时，通过get_user_kyc_country查不到kyc_country，此时无需判断用户国家是否为IRN
        # 以防止用户只有kyc国家是伊朗，在取消kyc后，无法执行后续更新用户的国家逻辑的情况
        if origin_location == "IRN":
            return
        if is_irn_tz_offset(tz_offset):
            update_to_country = get_country("IRN")
    if not update_to_country and (mob_country := get_mobile_country(user.mobile)):
        update_to_country = get_country(mob_country)

    if not update_to_country:
        if not origin_location:
            row = LoginHistory.query.filter(
                LoginHistory.user_id == user_id,
                LoginHistory.successful.is_(True)).order_by(LoginHistory.id.desc()).first()
            if not row:
                update_to_country = _get_special_lang_country(user_id)
                if not update_to_country:
                    return None
            else:
                update_to_country = get_country(GeoIP(row.ip).country_code)
                if not update_to_country:
                    update_to_country = _get_special_lang_country(user_id)
                    if not update_to_country:
                        return None
                else:
                    update_to_country = _get_special_country_code(user_id, update_to_country)
        else:
            update_to_country = _get_special_lang_country(user_id)
            if not update_to_country:
                row = LoginHistory.query.filter(
                    LoginHistory.user_id == user_id,
                    LoginHistory.successful.is_(True)).order_by(LoginHistory.id.desc()).first()
                if not row:
                    return None
                else:
                    update_to_country = get_country(GeoIP(row.ip).country_code)
                    if not update_to_country:
                        return None
                    else:
                        ip = user.registration_ip
                        register_country = get_country(GeoIP(ip).country_code)
                        if register_country and CountrySettings(register_country.iso_3).kyc_enabled:
                            update_to_country = register_country
        if not update_to_country:
            return None
        region_codes = get_forbidden_region_code()
        disabled_country_codes = []
        for _code in region_codes:
            _obj = get_country(_code)
            if _obj:
                disabled_country_codes.append(_obj.iso_3)
        if update_to_country.iso_3 in disabled_country_codes:
            return None

    code = update_to_country.iso_3
    sub_user_ids = [v.user_id for v in SubAccount.query.filter(
        SubAccount.main_user_id == user.id
    ).with_entities(SubAccount.user_id)]
    for _user_ids in batch_iter(sub_user_ids, 500):
        User.query.filter(
            User.id.in_(_user_ids)
        ).update({User.location_code: code}, synchronize_session=False)
    user.location_code = code
    db.session.commit()
    return code


def update_register_user_location(user_id: int, tz_offset: int = None):
    user: User = User.query.get(user_id)
    if user is None:
        raise ValueError(f'invalid user id: {user_id!r}')
    if user.is_sub_account:
        return None

    code = None
    if tz_offset is not None and is_irn_tz_offset(tz_offset):
        code = "IRN"

    if not code:
        ip = user.registration_ip
        update_to_country = get_country(GeoIP(ip).country_code)
        if not update_to_country:
            update_to_country = _get_special_lang_country(user_id)
            if not update_to_country:
                return None
        else:
            update_to_country = _get_special_country_code(user_id, update_to_country)
            if not update_to_country:
                return None
        code = update_to_country.iso_3
    user.location_code = code  # 注册无需更新子账号
    db.session.commit()
    return code


def check_and_update_user_special_location(user_id):
    user: User = User.query.get(user_id)
    if user is None:
        return
    if user.is_sub_account:
        return

    origin_location = user.location_code
    if get_user_kyc_country(user_id) is not None:
        return

    if origin_location == "IRN":
        return

    pref = UserPreferences(user_id)
    tz_offset = pref.timezone_offset
    if tz_offset is not None and is_irn_tz_offset(tz_offset):
        user.location_code = "IRN"
        db.session.commit()


def is_irn_tz_offset(tz_offset):
    return tz_offset == 210  # 3.5 * 60


def _get_special_country_code(user_id: int, country: CountryInfo) -> CountryInfo | None:
    codes = ['IRN', 'IDN', 'MYS']
    if country.iso_3 not in codes:
        update_to_country = _get_special_lang_country(user_id)
        return update_to_country or country
    return country


def _get_special_lang_country(user_id: int) -> CountryInfo | None:
    lang_to_code = {
        Language.FA_IR: 'IRN',
        Language.ID_ID: 'IDN',
    }
    lang = UserPreferences(user_id).language
    if lang in lang_to_code:
        update_to_country = get_country(lang_to_code[lang])
        return update_to_country
    return None


def require_user_not_only_withdrawal(user: User):
    """对应国家开启“仅支持提现”模式后，对应国家用户（符合下面其中一条都属于对应国家用户）
        1、完成对应国家的KYC用户；
        2、绑定对应国家手机的用户
    将关闭一些功能(充值、交易、理财、AMM、杠杆、红包)
    """
    if user.is_sub_account:
        kyc_user_id = user.main_user_id
    else:
        kyc_user_id = user.id

    permission = UserVisitPermissionCache().get_user_permission(kyc_user_id)
    if permission == UserVisitPermissionCache.ONLY_WITHDRAWAL_VALUE:
        if (risk_screen := UserRiskScreen.query.filter(
            UserRiskScreen.user_id == kyc_user_id,
        ).first()) and risk_screen.status == UserRiskScreen.Status.RISKED:
            raise NotSupportedAsPolicyRequires
        raise NotSupportedByCountryInOnlyWithdrawal


def require_user_kyc(user: User):
    from app.api.common.request import get_request_ip

    if user.is_sub_account:
        user = user.main_user
    if user.kyc_status == User.KYCStatus.PASSED:
        return
    permission = UserVisitPermissionCache().get_user_permission(user.id)
    if permission == UserVisitPermissionCache.ONLY_WITHDRAWAL_WHITELIST_VALUE:
        return
    user_pref = UserPreferences(user.id)
    if user_pref.timezone_offset in BusinessSettings.not_require_kyc_by_timezone_offsets:
        # 按照用户的时区判断不强制KYC限制
        return
    _ip_info = GeoIP(get_request_ip())
    ip_country_code = _ip_info.country_code
    if ip_country_code:
        country_settings = None
        # noinspection PyBroadException
        try:
            country_settings = CountrySettings(ip_country_code)
        except Exception:
            pass
        if country_settings and country_settings.kyc_required and user.id > country_settings.kyc_required_after_user_id:
            raise NotSupportedByCountryNotKYC


def check_user_sign_off(user: User) -> Dict:
    from app.business.credit import has_credit_unflat_asset
    from app.business import BalanceManager

    user_id = user.id
    sub_accounts = SubAccount.query.filter(
        SubAccount.main_user_id == user_id
    ).all()
    sub_user_ids = [item.user_id for item in sub_accounts]
    configs = AssetChainConfig.query.filter(
        AssetChainConfig.key == 'is_visible',
        AssetChainConfig.value == '0'
    ).with_entities(AssetChainConfig.asset, AssetChainConfig.chain, AssetChainConfig.value).all()
    asset_chain_mapping = asset_to_chains()
    excluded_asset_chain_mapping = defaultdict(set)
    for _config in configs:
        excluded_asset_chain_mapping[_config.asset].add(_config.chain)
    excluded_assets = set()
    for _asset, _chains in excluded_asset_chain_mapping.items():
        _all_chains = set(asset_chain_mapping.get(_asset, set()))
        if _all_chains and set(_chains) & _all_chains == _all_chains:
            excluded_assets.add(_asset)

    price_map = PriceManager.assets_to_usd()
    price_map = {k: v for k, v in price_map.items() if k not in excluded_assets}
    balance_usd = BalanceManager(user_id, sub_user_ids,
                                 price_map).get_current_balance_usd()
    
    def _check():

        def _check_last_reset_record() -> bool:
            last_reset_record = SecurityToolHistory.query.filter(
                SecurityToolHistory.user_id == user_id,
            ).order_by(SecurityToolHistory.id.desc()).with_entities(SecurityToolHistory.created_at).first()
            if last_reset_record and now() - last_reset_record.created_at <= timedelta(days=7):
                return False
            if balance_usd > Decimal('20'):
                return False
            return True
        
        def _check_risk_user() -> bool:
            risk_user = RiskUser.query.filter(
                RiskUser.user_id == user_id,
                RiskUser.status.in_((RiskUser.Status.AUDIT_REQUIRED, RiskUser.Status.AUDIT_REJECTED))
            ).first()
            return risk_user is None

        def _check_margin_loans() -> bool:
            margin_loan = MarginLoanOrder.query.filter(
                MarginLoanOrder.user_id == user_id,
                MarginLoanOrder.status.in_(
                    [MarginLoanOrder.StatusType.PASS,
                    MarginLoanOrder.StatusType.ARREARS,
                    MarginLoanOrder.StatusType.BURST]),
                MarginLoanOrder.unflat_amount + MarginLoanOrder.interest_amount > 0
            ).first()
            return margin_loan is None

        def _check_credit_unflat_asset() -> bool:
            return not has_credit_unflat_asset(user)

        def _check_withdrawal() -> bool:
            withdrawal = Withdrawal.query.filter(Withdrawal.user_id == user_id,
                            Withdrawal.status.in_(
                                [Withdrawal.Status.CREATED,
                                 Withdrawal.Status.AUDIT_REQUIRED,
                                 Withdrawal.Status.AUDITED])).first()
            return withdrawal is None

        def _check_pending_orders() -> bool:
            client = ServerClient()
            orders = client.user_pending_orders(user_id, account_id=-1, limit=1, page=1)
            if orders:
                return False
            orders = client.user_pending_stop_orders(user_id, account_id=-1, limit=1, page=1)
            if orders:
                return False
            perpetual_client = PerpetualServerClient()
            orders = perpetual_client.pending_order(user_id, None, limit=1)['records']
            if orders:
                return False
            orders = perpetual_client.pending_stop(user_id, None, limit=1)['records']
            if orders:
                return False
            positions = perpetual_client.position_pending(user_id)
            if positions:
                return False
            return True

        def _check_password_updated() -> bool:

            created_ts = int(user.created_at.timestamp())
            if user.login_password_updated_at:
                password_updated_ts = int(user.login_password_updated_at.timestamp())
            else:
                password_updated_ts = created_ts
            current_ts = current_timestamp(to_int=True)
            # 此处含义是密码更新时间及注册时间的判等
            if password_updated_ts - created_ts > 5 and \
                current_ts - password_updated_ts < 3600 * 24:
                return False
            return True
        
        def _check_p2p() -> bool:
            # p2p 相关校验
            o_model = P2pOrder
            p2p_order = o_model.query.filter(
                or_(o_model.customer_id == user_id,
                    o_model.merchant_id == user_id),
                o_model.status.in_((
                    o_model.Status.CREATED,
                    o_model.Status.CONFIRMED,
                    o_model.Status.PAID,
                    o_model.Status.TO_FINISH,
                ))
            ).first()

            c_model = P2pOrderComplaint
            p2p_complaint = c_model.query.filter(
                or_(c_model.plaintiff_id == user_id,
                    c_model.defendant_id == user_id),
                c_model.complaint_status.in_((
                    c_model.Status.CREATED,
                    c_model.Status.PENDING,
                ))
            ).first()
            
            m_model = P2pUserMargin
            margin = m_model.query.filter(
                m_model.user_id == user_id,
            ).first()
            if margin and margin.paid_margin:
                return False

            if p2p_order or p2p_complaint or UserSettings(user_id).p2p_disabled_trans:
                return False
            return True
        
        for k, f in list(locals().items()):
            if not k.startswith('_check'):
                continue
            if not f():
                return dict(
                    can_sign_off=False,
                    balance_usd=balance_usd
                )

        return dict(
                    can_sign_off=True,
                    balance_usd=balance_usd
                )
    
    return _check()
        


def do_user_sign_off(user: User) -> bool:
    """
    1. 清除登录态
    2. 重置安全工具（手机，totp，通讯密钥）
    3. 解除返佣关系
    4. 删除做市商身份
    5. 删除api key
    6. 删除主子账号用户名
    7. 删除子账号授权关系，删除其他主账号给的授权关系
    8. 解除第三方账号绑定
    9. 取消KYC申请及重置安全工具申请
    10.把用户注销状态通知到第三方服务
    """
    from app.business.security import reset_security_info
    from app.business.sub_account import delete_sub_account_manager_relation

    def _notice_third_party_service():
        # 评论服务
        comment_url_base = get_url_base(config['CLIENT_CONFIGS']['comment_internal']['url'])
        _comment_client = RESTClient(comment_url_base)
        _comment_client.post('/res/user/info', {
            'user_id': user.id,
            'is_signed_off': True
        })

    sign_off_info = check_user_sign_off(user)
    if not sign_off_info['can_sign_off']:
        return False
    user_id = user.id
    sub_user_ids = SubAccount.query.filter(
        SubAccount.main_user_id == user_id
    ).with_entities(SubAccount.user_id).all()
    sub_user_ids = [item.user_id for item in sub_user_ids]
    for id_ in [user_id, ] + sub_user_ids:
        login_cache = UserLoginTokenCache(id_)
        tokens = login_cache.clear_tokens()
        UserLoginState.clear_tokens(tokens)
    mobile = user.mobile
    if user.mobile:
        reset_security_info(user, SecurityResetApplication.ResetType.MOBILE, SecurityToolHistory.OpRole.USER)
    if user.totp_auth_key:
        reset_security_info(user, SecurityResetApplication.ResetType.TOTP, SecurityToolHistory.OpRole.USER)
    if user.web_authn_list:
        reset_security_info(user, SecurityResetApplication.ResetType.WEBAUTHN, SecurityToolHistory.OpRole.USER)

    delete_sub_account_manager_relation(main_user_id=user_id)
    ReferralHistory.query.filter(or_(ReferralHistory.referrer_id == user_id,
                                          ReferralHistory.referree_id == user_id)).update(
        {'status': ReferralHistory.Status.EXPIRED}, synchronize_session=False
    )
    AmbassadorAgentHistory.query.filter(or_(
        AmbassadorAgentHistory.user_id == user_id,
        AmbassadorAgentHistory.ambassador_id == user_id,
    )).update(
        {'status': AmbassadorAgentHistory.Status.DELETED}, synchronize_session=False
    )
    for m in MarketMaker.MakerType:
        MarketMakerHelper(m).delete(user_id)
    ApiAuth.query.filter(
        ApiAuth.status == ApiAuth.Status.VALID,
        ApiAuth.user_id.in_([user_id, ] + sub_user_ids)).update(
        {'status': ApiAuth.Status.DELETED}, synchronize_session=False
    )
    User.query.filter(
        User.id.in_([user_id, ] + sub_user_ids)).update(
            {'name': ''}, synchronize_session=False
        )

    sign_off_user = SignOffUser.get_or_create(
        user_id=user_id
    )
    sign_off_user.balance_usd = sign_off_info['balance_usd']
    sign_off_user.email = user.email or ''
    sign_off_user.mobile = mobile
    db.session.add(sign_off_user)
    for t in EmailCodeType:
        EmailCodeCache(user.email, t).delete()
    user.email = None
    user.email_updated_at = now()
    # noinspection PyBroadException
    try:
        MobilePusher().delete_device_report(user.id)
        sign_off_user.has_device_logged_out = True
    except Exception:
        current_app.logger.error(f'delete_device_report failed user_id:{user.id}')
    ThirdPartyAccount.query.filter(
        ThirdPartyAccount.user_id == user_id
    ).update({'status': ThirdPartyAccount.Status.DELETED}, synchronize_session=False)

    KycVerification.query.filter(
        KycVerification.user_id == user_id,
        KycVerification.status.in_([KycVerification.Status.CREATED,
                                    KycVerification.Status.AUDIT_REQUIRED,
                                    KycVerification.Status.SCREENING])
    ).update({'status': KycVerification.Status.CANCELLED}, synchronize_session=False)

    KycVerificationPro.query.filter(
        KycVerificationPro.user_id == user_id,
        KycVerificationPro.status == KycVerificationPro.Status.CREATED
    ).update({'status': KycVerificationPro.Status.CANCELLED}, synchronize_session=False)

    KYCInstitution.query.filter(
        KYCInstitution.user_id == user_id,
        KYCInstitution.status == KYCInstitution.Status.CREATED
    ).update({'status': KYCInstitution.Status.CANCELED}, synchronize_session=False)
    if user.kyc_status == User.KYCStatus.PROCESSING:
        user.kyc_status = User.KYCStatus.NONE
    SecurityResetApplication.query.filter(
        SecurityResetApplication.user_id == user_id,
        SecurityResetApplication.status == SecurityResetApplication.StatusType.CREATED
    ).update({'status': SecurityResetApplication.StatusType.REJECTED}, synchronize_session=False)

    _notice_third_party_service()
    db.session.commit()
    return True


@celery_task(queue=CeleryQueues.REAL_TIME)
@lock_call(with_args='login_history_id')
def update_login_relation_history(login_history_id: int, is_registration: bool):
    """
    is_registration:是否注册相关的登录记录
    """
    record = LoginHistory.query.get(login_history_id)
    if not record:
        return
    new_login_relation_history(record, is_registration)


def new_login_relation_history(record: LoginHistory, is_registration: bool):
    from app.api.common.request import RequestPlatform

    platform_string = record.platform
    try:
        _platform = RequestPlatform(platform_string)
    except ValueError:
        _platform = RequestPlatform.WEB

    platform = LoginRelationHistory.Platform.WEB
    if _platform.is_ios():
        platform = LoginRelationHistory.Platform.IOS
    elif _platform.is_android():
        platform = LoginRelationHistory.Platform.ANDROID

    db.session_add_and_commit(LoginRelationHistory(
        login_history_id=record.id,
        user_id=record.user_id,
        ip=record.ip,
        is_registration=is_registration,
        device_id=record.device_id,
        platform=platform,
        device=record.user_agent_for_admin
    ))

def update_user_two_fa_type(user: User):
    types = \
    (
        (TwoFAType.WEBAUTHN, bool(user.web_authn_list)),
        (TwoFAType.TOTP, bool(user.totp_auth_key)),
        (TwoFAType.MOBILE, bool(user.mobile)),
        (TwoFAType.NONE, True),
    )
    pref = UserPreferences(user.id)
    type_ = pref.two_fa_type

    # 如原有类型可用，则不更新
    for t, flag in types:
        if t == type_ and flag and type_ != TwoFAType.NONE:
            return
    for t, flag in types:
        if flag:
            pref.two_fa_type = t
            break


class UserRepository:
    """User相关的操作"""

    DEFAULT_AVATAR_LIST = [
        "avatar-webp/coinex_default_avatar_base.webp",  # 默认头像
        "avatar-webp/coinex_default_avatar_robot.webp",
        "avatar-webp/coinex_default_avatar_robot_front.webp",
        "avatar-webp/coinex_default_avatar_dog.webp",
        "avatar-webp/coinex_default_avatar_cat.webp",
        "avatar-webp/coinex_default_avatar_leopard.webp",
        "avatar-webp/coinex_default_avatar_bear.webp",
        "avatar-webp/coinex_default_avatar_man.webp",
        "avatar-webp/coinex_default_avatar_woman.webp",
        "avatar-webp/coinex_default_avatar_brain_VR.webp",
        "avatar-webp/coinex_default_avatar_crypto_vision.webp",
        "avatar-webp/coinex_default_avatar_headset_VR.webp",
        "avatar-webp/coinex_default_avatar_nft_explorer.webp"
    ]

    # 支持中文, emoji表情， 英文数字，中间允许有一个空格。
    RE_USER_NAME = re.compile(
        r"^[A-Za-z0-9\u4e00-\u9fa5\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF]+"
        r"( [A-Za-z0-9\u4e00-\u9fa5\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF]+)?$",
    )

    RE_NAME_BLANK = re.compile(r'^\S+( \S+)?$')

    RE_NAME_WORD = re.compile(
        r'^[A-Za-z0-9\u4e00-\u9fa5\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF ]+'
    )

    RE_ACCOUNT_NAME = re.compile(r'^[0-9a-zA-Z]+$')

    DEFAULT_ACCOUNT_NAME = re.compile(r'^user\d+', re.IGNORECASE)

    SENSITIVE_WORDS = [_("客服"), _("官方"), _("支持"), _("申诉")]

    @classmethod
    def get_user_avatar_url(cls, avatar_key: str):
        if not avatar_key:
            avatar_key = UserRepository.get_default_avatar()
        if avatar_key in cls.DEFAULT_AVATAR_LIST:
            return AWSBucketPublic.get_file_url(avatar_key)
        return AWSBucketPublic.get_file_url(avatar_key)

    @classmethod
    def check_user_name_has_block_word(cls, name: str):
        name = cls.format_word(name)
        words_set = NameBlockWordsCache(NameBlockWords.BlockType.USER_NAME.name).read()
        return not words_set.isdisjoint(set(
            [name[i: j] for i in range(len(name)) for j in range(i + 1, len(name) + 1)]
        ))

    @classmethod
    def get_system_default_avatar(cls):
        return cls.DEFAULT_AVATAR_LIST[0]

    @classmethod
    def get_system_default_avatar_url(cls):
        return AWSBucketPublic.get_file_url(cls.get_system_default_avatar())

    @classmethod
    def get_default_avatar(cls):
        default_portrait = CurrentPortraitHashCache().get_default_portrait()
        return default_portrait or cls.get_system_default_avatar()

    @classmethod
    def check_account_name_has_block_word(cls, account_name: str):
        account_name = account_name.lower()
        words_set = NameBlockWordsCache(NameBlockWords.BlockType.ACCOUNT_NAME.name).read()
        return not words_set.isdisjoint(set(
            [account_name[i: j] for i in range(len(account_name)) for j in range(i + 1, len(account_name) + 1)]
        ))

    @classmethod
    def format_word(cls, word):
        return word.lower().replace(" ", "")

    @classmethod
    def check_nickname_rules(cls, user: User, name: str):
        # 要求详细返回用户错误
        if len(name) > 40:
            raise NameTooLong
        if not cls.RE_NAME_WORD.fullmatch(name):
            raise InvalidArgument(message=gettext("用户名不得包含特殊字符"))
        if not cls.RE_NAME_BLANK.fullmatch(name):
            raise InvalidArgument(message=gettext("只允许在字符之间设置一个空格"))
        if not cls.RE_USER_NAME.fullmatch(name):
            raise InvalidArgument(message=gettext("只允许一个空格，不得包含特殊字符"))
        if cls.check_user_is_had_block_word(user, name):
            raise InvalidArgument(message=gettext("该用户名无法设置"))

    @classmethod
    def check_user_is_had_block_word(cls, user: User, name: str):
        if user.is_sub_account:
            return False
        whitelist = BusinessSettings.user_name_whitelist or set()
        if user.id in whitelist:
            return False
        return cls.check_user_name_has_block_word(name)

    @classmethod
    def update_user_name(
            cls,
            user: User,
            name: str,
            auto_commit: bool = True,
            is_check_name: bool = True
    ):
        """
        更新用户名称
        :param user: 用户模型
        :param name: 修改的用户名
        :param auto_commit: 是否自动提交，保证事务的一致性
        :param is_check_name:
        :return:
        """
        from app.api.common import get_request_platform
        if not user:
            return
        if is_check_name:
            cls.check_nickname_rules(user, name)
        user.name = name
        now_ = now()
        user.updated_at = now_
        trader_user: CopyTraderUser = CopyTraderUser.query.filter(
            CopyTraderUser.user_id == user.id,
        ).first()
        if trader_user:
            trader_user.nickname = name
            trader_user.nickname_updated_at = now_
        OperationLog.add(
            user.id,
            OperationLog.Operation.EDIT_NAME, name,
            get_request_platform(),
            auto_commit
        )
        if auto_commit:
            db.session.commit()

    @classmethod
    def _check_account_name(cls, account_name: str):
        # 这里不区分大小写
        if UserExtra.query.filter(
            UserExtra.account_name == account_name
        ).first():
            raise AccountNameExists
        if cls.DEFAULT_ACCOUNT_NAME.fullmatch(account_name):
            update_user_id = int(re.findall(r'\d+', account_name)[0])
            max_user_id = User.query.with_entities(func.max(User.id)).scalar()
            # 防止用户占用新用户默认的账户名
            if update_user_id >= max_user_id:
                raise InvalidArgument(message=gettext("该账户名无法设置"))
            other_account_name = UserExtra.query.filter(
                UserExtra.user_id == update_user_id
            ).with_entities(
                UserExtra.account_name
            ).scalar()
            if other_account_name:
                raise AccountNameExists

    @classmethod
    def update_user_account_name(cls, user: User, account_name: str, auto_commit: bool = True):
        from app.api.common import get_request_platform
        if not user or user.is_sub_account:
            return
        # 默认账号名 不做修改
        if account_name == UserExtra.default_account_name(user.id):
            return
        cls.check_update_account_name(user, account_name)
        user_extra = user.extra
        db.session.add(user_extra)
        user_extra.account_name = account_name
        OperationLog.add(
            user.id,
            OperationLog.Operation.EDIT_ACCOUNT_NAME,
            account_name,
            get_request_platform(),
            auto_commit
        )
        if auto_commit:
            db.session.commit()

    @classmethod
    def check_update_account_name(cls, user: User, account_name: str):
        if account_name == UserExtra.default_account_name(user.id):
            return
        if len(account_name) > 20:
            raise AccountNameToLong
        if not cls.RE_ACCOUNT_NAME.fullmatch(account_name):
            raise InvalidArgument(message=gettext("账户名只支持数字和字母"))
        if user.extra.check_has_set_account_name():
            raise InvalidArgument(message=gettext("账户名只允许修改一次"))
        cls._check_account_name(account_name)
        whitelist = BusinessSettings.user_name_whitelist or set()
        if user.id not in whitelist and cls.check_account_name_has_block_word(account_name):
            raise InvalidArgument(message=gettext("该账户名无法设置"))

    @classmethod
    def update_user_avatar(cls, user: User, avatar: str, auto_commit: bool = True, is_admin=False):
        from app.api.common import get_request_platform
        if not user:
            return
        # 当用户设置的默认头像，需要将用户头像重置为空，方便前端处理展示逻辑
        if avatar == cls.get_system_default_avatar():
            avatar = ""
        user_extra = user.extra
        db.session.add(user_extra)

        if avatar.startswith(AWSBucketPrivate.path):
            # 说明是旧版本app上传的私有文件类型头像，需转为公有类型并生成缩略图
            avatar_file_url = AWSBucketPrivate.get_file_url(avatar)
            avatar = avatar.removeprefix(f"{AWSBucketPrivate.path}/")
            suffix = avatar.split('.')[-1]
            if not AWSBucketPublic.put_file_with_thumbnail(
                avatar, open_file_from_url(avatar_file_url), suffix,
                size=[
                    ThumbnailScale('small', 100, 100),
                    ThumbnailScale('medium', 100, 100),
                    ThumbnailScale('large', 100, 100),
                ]
            ):
                raise ServiceUnavailable

        user_extra.avatar = avatar
        if not is_admin:
            OperationLog.add(
                user.id,
                OperationLog.Operation.EDIT_USER_AVATAR,
                avatar,
                get_request_platform(),
                auto_commit
            )
        if auto_commit:
            db.session.commit()

    @classmethod
    def get_register_users(
            cls, start: Union[datetime.datetime, datetime.date],
            end: Union[datetime.datetime, datetime.date]):
        from .utils import query_records_by_time_range
        if not isinstance(start, datetime.datetime):
            start = date_to_datetime(start)
        if not isinstance(end, datetime.datetime):
            end = date_to_datetime(end)
        records = query_records_by_time_range(User, start, end)

        return records

    @classmethod
    def get_register_user_ids(cls, start: datetime.datetime, end: datetime.datetime):
        records = cls.get_register_users(start, end)
        return {i.id for i in records}

    @classmethod
    def get_user_language_map(cls, user_ids: Iterable[int]) -> Dict[int, str]:
        user_language_map = {}
        for ids in batch_iter(user_ids, 5000):
            preferences = (
                UserPreference.query.filter(
                    UserPreference.user_id.in_(ids),
                    UserPreference.key == "language",
                    UserPreference.status == UserPreference.Status.VALID,
                ).with_entities(
                    UserPreference.user_id, UserPreference.value
                ).all()
            )
            user_language_map.update({i.user_id: i.value for i in preferences})
        return user_language_map

    @classmethod
    def get_user_country_cn_name(cls, location_code):
        location = c.cn_name if (c := get_country(location_code)) else "其他"
        return location

    @classmethod
    def get_user_location_code_dic(cls, end: datetime.datetime):

        records = User.query.filter(
            User.created_at <= end
        ).with_entities(
            User.id,
            User.location_code
        ).all()
        return {i.id: i.location_code for i in records}

    @classmethod
    def get_user_lang_dic(cls, user_ids):
        res = dict()
        for uids in batch_iter(user_ids, 2000):
            records = UserPreference.query.filter(
                UserPreference.status == UserPreference.Status.VALID,
                UserPreference.user_id.in_(uids),
                UserPreference.key == 'language').all()
            res.update({i.user_id: i.value for i in records})
        return res

    @classmethod
    def get_user_tag_notice_dic(cls, user_ids: Iterable, tag: PushTag):
        if tag == PushTag.INFORMATION:
            return cls.get_user_app_notice_dic(user_ids, 'app_information_notice')
        elif tag == PushTag.ASSET_PRICE_BREAK:
            return cls.get_user_app_notice_dic(user_ids, 'app_asset_level_break_notice')
        elif tag == PushTag.NEW_ASSET_RISE:
            return cls.get_user_app_notice_dic(user_ids, 'app_new_asset_rise_notice')
        else:
            return dict()

    @classmethod
    def get_user_app_notice_dic(cls, user_ids, query_key):
        res = dict()
        for uids in batch_iter(user_ids, 2000):
            records = UserPreference.query.filter(
                UserPreference.status == UserPreference.Status.VALID,
                UserPreference.user_id.in_(uids),
                UserPreference.key == query_key).with_entities(
                UserPreference.user_id,
                UserPreference.value
            ).all()
            res.update({i.user_id: i.value for i in records})
        return res

    @classmethod
    def get_users_id_email_map(cls, user_ids: Iterable[int]) -> Dict:
        res = dict()
        for ids in batch_iter(user_ids, 2000):
            users = User.query.filter(
                User.id.in_(ids)
            ).with_entities(
                User.id,
                User.email
            ).all()
            for item in users:
                res[item.id] = item.email or ''
        return res

    @classmethod
    def get_user_info_map(cls, user_ids: Iterable[int]) -> Dict:
        res = dict()
        for ids in batch_iter(user_ids, 2000):
            users = User.query.filter(
                User.id.in_(ids)
            ).all()
            for item in users:
                res[item.id] = item
        return res

    @classmethod
    def get_login_forbidden_users(cls) -> Set[int]:
        return UserConfigKeyCache.get_login_disabled_by_admin_ids()

    @classmethod
    def get_cleared_user_ids(cls, filter_user_ids: Optional[Iterable[int]] = None) -> Set[int]:
        """仅获取admin添加的被清退用户，未包含被清退用户的子账户"""
        return ClearedUser.get_user_ids(filter_user_ids=filter_user_ids)

    @classmethod
    def get_cleared_users_with_subaccounts(cls) -> Set[int]:
        cleared_main_users = cls.get_cleared_user_ids()
        sub_accounts = SubAccount.query.with_entities(
            SubAccount.user_id,
            SubAccount.main_user_id
        ).all()
        ret = set()
        for user_id, main_user_id in sub_accounts:
            if main_user_id in cleared_main_users:
                ret.add(user_id)
        return ret | cleared_main_users

    @classmethod
    def get_internal_users(cls, include_sub_account: bool=True) -> Set[int]:
        """获取用户Tag系统里的 内部账号"""
        from .admin_tag import TagType

        internal_user_ids = {r.user_id for r in AdminTagUser.query.filter(
            AdminTagUser.tag_id == TagType.INTERNAL_ACCOUNT.value,
            AdminTagUser.status == AdminTagUser.Status.PASSED,
        ).with_entities(
            AdminTagUser.user_id
        ).all()}
        if include_sub_account:
            internal_user_ids = set(cls.merge_sub_account_into_main_ids(
                internal_user_ids,
                sub_account_type=SubAccount.Type.NORMAL
            ))
        return internal_user_ids

    @classmethod
    def get_abnormal_users(cls):
        """获取异常用户: 包括 清退用户, 禁止登陆, 羊毛党风控"""
        return AbnormalUserCache().get_users()

    @classmethod
    def is_abnormal_user(cls, user_id: int):
        return AbnormalUserCache().has_user(user_id)

    @classmethod
    def merge_sub_account_into_main_ids(cls, main_user_ids, sub_account_type=None):
        from app.models import SubAccount
        recs = SubAccount.query.filter(
            SubAccount.main_user_id.in_(main_user_ids)
        ).with_entities(
            SubAccount.user_id,
            SubAccount.main_user_id,
        )
        if sub_account_type:
            recs = recs.filter(SubAccount.type == sub_account_type)
        sub_main_dic = dict(recs)
        sub_main_dic.update({i: i for i in main_user_ids})
        return sub_main_dic

    @classmethod
    def get_user_last_active_at(cls, user_id):
        """获取用户最近活跃时间（精确到天）"""
        date_ = today()
        cache = UserActivenessCache(date_)
        if cache.has_user(user_id):
            return date_
        else:
            return UserActivenessHistory.get_user_latest_active_date(user_id)

    @classmethod
    def get_sub_main_acc_dic(cls):
        records = SubAccount.query.with_entities(
            SubAccount.user_id,
            SubAccount.main_user_id
        ).all()
        return dict(records)

    @classmethod
    def get_sign_off_users(cls, user_ids: Iterable = None) -> Set[int]:
        query = SignOffUser.query.with_entities(SignOffUser.user_id)
        if user_ids:
            query = query.filter(SignOffUser.user_id.in_(user_ids))
        return {i.user_id for i in query.all()}

    @classmethod
    def get_user_account_info(cls, user: tuple[int, User]) -> dict[str, Any]:
        if isinstance(user, int):
            user = User.query.get(user)
        user_extra = user.extra
        return dict(
            name=user.nickname,
            account_name=user_extra.display_account_name,
            avatar=user_extra.avatar_url
        )

    @classmethod
    def get_user_account_info_mapper(cls, user_ids: Iterable[int]) -> dict[int, dict[str, Any]]:
        extra_mapper, user_nickname_mapper = {}, {}
        for ids_ in batch_iter(user_ids, 1000):
            extra_mapper.update({
                i.user_id: i for i in UserExtra.query.filter(
                    UserExtra.user_id.in_(ids_)
                ).all()
            })
            user_query = User.query.filter(User.id.in_(ids_)).all()
            user_nickname_mapper.update({i.id: i.nickname for i in user_query})
        data = {}
        default_avatar_key = cls.get_default_avatar()
        for user_id in user_ids:
            user_extra = extra_mapper.get(user_id)
            avatar_key = user_extra.avatar if user_extra else default_avatar_key
            data[user_id] = dict(
                name=user_nickname_mapper.get(user_id, ""),
                account_name=user_extra.display_account_name if user_extra else UserExtra.default_account_name(user_id),
                avatar=user_extra.avatar_url if user_extra else AWSBucketPublic.get_file_url(default_avatar_key),
                avatar_key=avatar_key
            )
        return data

    @classmethod
    def display_nickname(cls, user_name: str, account_name: str) -> str:
        return f"{user_name}(@{account_name})"

    @classmethod
    def get_channel_user_ids_map(cls, channels: list[str]) -> dict[str, set[int]]:
        """ 按channel获取注册user_ids，非实时。update_user_channel_statistics_schedule """
        st_rows = UserChannelStatistics.query.filter(
            UserChannelStatistics.channel.in_(channels),
        ).with_entities(
            UserChannelStatistics.channel,
            UserChannelStatistics.user_bitmap,
        ).all()
        st_row_map = {i.channel: i for i in st_rows}
        channel_user_ids = {}
        for channel in channels:
            reg_user_ids = set()
            st_row = st_row_map.get(channel)
            if st_row and st_row.user_bitmap:
                reg_user_ids.update(BitMap.deserialize(st_row.user_bitmap))
            channel_user_ids[channel] = reg_user_ids
        return channel_user_ids

    @classmethod
    def get_channel_user_ids(cls, channels: list[str]) -> set[int]:
        """ 按channel获取注册user_ids，非实时 """
        channel_user_ids_map = cls.get_channel_user_ids_map(channels)
        merge_user_ids = set()
        for v in channel_user_ids_map.values():
            merge_user_ids.update(v)
        return merge_user_ids

    @classmethod
    def get_all_channels(cls) -> list[str]:
        """ 注册用户的全部channel，非实时 """
        st_rows = UserChannelStatistics.query.with_entities(
            UserChannelStatistics.channel,
        ).all()
        channels = [i.channel for i in st_rows]
        return channels


def process_user_api_permission(main_user_id: int):
    q = SubAccount.query.filter(
        SubAccount.main_user_id == main_user_id
    ).with_entities(SubAccount.user_id).all()
    all_uids = [main_user_id] + [v.user_id for v in q]
    api_q = ApiAuth.query.filter(
        ApiAuth.user_id.in_(all_uids),
        ApiAuth.status == ApiAuth.Status.VALID
    ).all()
    need_update_ids = set()
    for record in api_q:
        if record.withdrawals_enabled or record.trading_enabled:
            need_update_ids.add(record.access_id)
            record.withdrawals_enabled = False
            record.trading_enabled = False
    db.session.commit()
    for access_id in need_update_ids:
        ApiAuthCache(access_id).delete()


@cached(600)
def get_forbidden_region_code() -> List[str]:
    # 获取禁止访问的国家列表，iso_2
    q = CountrySetting.query.filter(
        CountrySetting.key == CountrySettings.allow_visit.name,
        CountrySetting.value == '0',  # TODO: fix
        CountrySetting.status == CountrySetting.Status.VALID
    ).with_entities(CountrySetting.code).all()
    return [get_country(v.code).iso_2 for v in q]


class UserVisitPermissionHelper(object):

    class UserVisitType(Enum):
        LOGIN_DISABLED_BY_ADMIN = 'login_disabled_by_admin'
        CLEARED_USER = 'cleared_user'
        ONLY_WITHDRAWAL = 'only_withdrawal'
        SIGN_OFF = 'sign_off'

    @classmethod
    def get_cleared_whitelist_user_ids(cls, include_sub_accounts: bool = False):
        from app.models import OnlyWithdrawalWhitelistUser
        q = OnlyWithdrawalWhitelistUser.query.with_entities(
            OnlyWithdrawalWhitelistUser.Status.VALID
        ).with_entities(
            OnlyWithdrawalWhitelistUser.user_id
        ).all()
        user_ids = {v.user_id for v in q}
        if include_sub_accounts:
            _mapping = SubAccount.get_main_user_sub_mapping(user_ids)
            for _main_user_id, _sub_ids in _mapping.items():
                user_ids |= _mapping[_main_user_id]
        return user_ids

    @classmethod
    def get_user_ids(cls,
                     _types: Optional[List[UserVisitType]] = None,
                     filter_user_ids: Optional[Iterable[int]] = None,
                     include_sub_accounts: bool = False
                     ):
        user_ids = set()
        if not _types:
            _types = [v for v in cls.UserVisitType]
        for _type in _types:
            type_user_ids = set()
            if _type == cls.UserVisitType.LOGIN_DISABLED_BY_ADMIN:
                type_user_ids = UserConfigKeyCache.get_login_disabled_by_admin_ids(filter_user_ids)
            if _type == cls.UserVisitType.CLEARED_USER:
                type_user_ids = ClearedUser.get_user_ids(ClearedUser.Status.FORBIDDEN,
                                                         filter_user_ids)
            if _type == cls.UserVisitType.ONLY_WITHDRAWAL:
                type_user_ids = ClearedUser.get_user_ids(ClearedUser.Status.WITHDRAWAL_ONLY,
                                                         filter_user_ids)
            if _type == cls.UserVisitType.SIGN_OFF:
                type_user_ids = SignOffUser.get_user_ids(filter_user_ids)

            user_ids |= type_user_ids
        if include_sub_accounts and user_ids:
            q = SubAccount.query.with_entities(
                SubAccount.status == SubAccount.Status.VALID,
                SubAccount.type == SubAccount.Type.NORMAL
            ).with_entities(SubAccount.user_id, SubAccount.main_user_id).all()
            main_sub_list_mapping = defaultdict(set)
            for v in q:
                main_sub_list_mapping[v.main_user_id].add(v.user_id)
            has_sub_user_ids = set(main_sub_list_mapping.keys()) & user_ids
            for _main_user_id in has_sub_user_ids:
                user_ids |= main_sub_list_mapping[_main_user_id]

        return user_ids


class UnfreezeAccountHelper:
    need_unfreeze_permissions = ['login_disabled_by_admin',
                                     'spot_trading_disabled_by_admin',
                                     'margin_trading_disabled_by_admin',
                                     'perpetual_trading_disabled_by_admin',
                                     'withdrawals_disabled_by_admin',
                                     ]

    @classmethod
    def unfreeze_account(cls, user_id: int):
        user_setting = UserSettings(user_id)
        for permission_key in cls.need_unfreeze_permissions:
            user_setting.set(permission_key, False)
        UserPreferences(user_id).self_forbid = False

    @classmethod
    def freeze_account(cls, user_id: int):
        user_setting = UserSettings(user_id)
        for permission_key in cls.need_unfreeze_permissions:
            user_setting.set(permission_key, True)
        UserPreferences(user_id).self_forbid = True

    @classmethod
    def has_user(cls, user_id: int):
        return UserPreferences(user_id).self_forbid


def get_user_kyc_country_map(user_ids, country=None):
    k_model = KycVerification
    k_map = {}
    for ch_user_ids in batch_iter(user_ids, 2000):
        query = k_model.query.filter(
            k_model.status == k_model.Status.PASSED,
            k_model.user_id.in_(ch_user_ids)
        ).with_entities(
            k_model.user_id,
            k_model.country
        )
        if country:
            query = query.filter(k_model.country == country)
        k_map.update({i.user_id: i.country for i in query.all()})

    other_ids = set(user_ids) - set(k_map.keys())
    i_model = KYCInstitution
    c_model = InstitutionCompany
    for ch_user_ids in batch_iter(other_ids, 2000):
        i_query = i_model.query.join(c_model, c_model.id == i_model.id).filter(
            i_model.user_id.in_(list(ch_user_ids)),
            i_model.status == i_model.Status.PASSED
        ).with_entities(
            c_model.location_code.label("country"),
            i_model.user_id,
        )
        if country:
            i_query = i_query.filter(c_model.location_code == country)
        k_map.update({i.user_id: i.country for i in i_query.all()})
    return k_map


def batch_get_user_pref_fields_map(user_ids: set[int], fields: list[str]) -> dict[int, dict]:
    """ 批量查询UserPreferences """
    for field in fields:
        getattr(UserPreferences, field)

    result = {}
    for user_id in user_ids:
        result[user_id] = {}
        for field in fields:
            _cfg_field = getattr(UserPreferences, field)
            result[user_id][field] = _cfg_field.default

    for ch_ids in batch_iter(user_ids, 2000):
        ch_rows = UserPreferenceModel.query.filter(
            UserPreferenceModel.status == UserPreferenceModel.Status.VALID,
            UserPreferenceModel.user_id.in_(ch_ids),
            UserPreferenceModel.key.in_(fields),
        ).with_entities(
            UserPreferenceModel.user_id,
            UserPreferenceModel.key,
            UserPreferenceModel.value,
        ).all()
        for r in ch_rows:
            _cfg_field = getattr(UserPreferences, r.key)
            result[r.user_id][r.key] = _convert(_cfg_field.type, r.value)

    return result
