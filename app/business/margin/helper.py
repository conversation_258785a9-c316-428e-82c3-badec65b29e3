#!/usr/bin/python
# -*- coding: utf-8 -*-
import copy
import json
from collections import defaultdict
from decimal import Decimal, ROUND_HALF_UP
from enum import Enum
from functools import cached_property
from typing import Dict, Tuple, Any, Optional, NamedTuple

from flask import current_app
from sqlalchemy import func, or_

from .. import PriceManager, SPOT_ACCOUNT_ID
from ..clients import ServerClient
from ..clients.biz_monitor import biz_monitor
from ..external_dbs import TradeLogDB
from app.business.fee_constant import MARGIN_DAY_RATE_VIP_LEVEL_DISCOUNT_MAP
from app.caches import (
    <PERSON>Cache, MarginAccountNameCache,
    MarginAccountIdCache, IndexPriceCache,
    MarginAssetLoanFixPowerCache,
)
from app.caches.admin import (
    RealTimeMarginAssetStatisticCache,
    RealTimeMarginMarketStatisticCache,
    RealTimeMarginMarketRealLeverageCache,
)
from app.exceptions import (
    MarginA<PERSON>unt<PERSON>otO<PERSON>, MarginIndexPriceError,
    Mar<PERSON>LoanOrderNotFound,
    MarginBalanceCannotGet, OrderException, OrderExceptionMap,
    )
from app.models import (
    MarginAssetRule, MarginLoanOrder, MarginLiquidationRate, MarginIndexDetail,
    MarginReceivableInterestHistory, MarginInsuranceHistory, MarginInsurance,
    MarginInterestHistory, MarginLiquidationOrder, Market, MarginAccount,
    UserMarginDayRate, VipUser, SubAccount, InvestmentAccount,
    UserMarginAssetRule, UserMarginAccountRule, MarginRealInsuranceHistory,
)
from app.models import db
from app.utils import amount_to_str, now, quantize_amount, timestamp_to_date
from app.utils.parser import JsonEncoder


# 转账风险率固定值
from ...common import PrecisionEnum, BalanceEvent
from ...models.mongo.margin import AssetFloatingRateMySQL

USDT_ASSET = 'USDT'

TRANSFER_RATE_DICT = {
    2: Decimal("2"),
    3: Decimal("1.5"),
    4: Decimal("1.35"),
    5: Decimal("1.25"),
    10: Decimal('1.11')
}

SELL_TYPE = "sell_type"
BUY_TYPE = "buy_type"

LOAN_TYPE = 'loan'
INTEREST_TYPE = 'interest'


class BalanceType(Enum):
    ALL = 'all'
    AVAILABLE = 'available'
    FROZEN = 'frozen'


"""
借币利率动态调整
● 当基准≤60%，借币利率为X；
● 当60%＜基准≤70%，借币利率为1.5X；
● 当70%＜基准≤80%，借币利率为2X；
● 当80%＜基准≤90%，借币利率为2.5X；
● 当90%＜基准，借币利率为3X；
"""
LOAN_RATE_RULES = [
    # (percent, 利率倍数)
    (Decimal("0.6"), Decimal("1")),
    (Decimal("0.7"), Decimal("1.5")),
    (Decimal("0.8"), Decimal("2")),
    (Decimal("0.9"), Decimal("2.5")),
    (Decimal("1"), Decimal("3")),
]


class LoanAssetDayRateHelper:
    """ 借币币种-日利率相关逻辑
    杠杆和借贷的利率已统一（不包括用户特殊利率表）
    借币币种会配置一个基础利率MarginAssetRule.basic_day_rate，VIP1 ~ VIP5 在basic_day_rate上再打一个折扣
    """

    @classmethod
    def _basic_rate_to_vip_rates(cls, asset_basic_rate_map: dict[str, Decimal]) -> dict[str, dict[int, Decimal]]:
        """ 基础日利率 to vip等级日利率map """
        result = {}
        for asset, basic_rate in asset_basic_rate_map.items():
            vip_level_day_rate_map = {}
            for level, disc in MARGIN_DAY_RATE_VIP_LEVEL_DISCOUNT_MAP.items():
                vip_level_day_rate_map[level] = quantize_amount(basic_rate * disc, 8)
            result[asset] = vip_level_day_rate_map
        return result

    @classmethod
    def get_asset_vip_rates(cls, asset: str) -> dict[int, Decimal]:
        """ 获取某个借币币种，全部vip等级的日利率 """
        asset_rule: MarginAssetRule = MarginAssetRule.query.filter(
            MarginAssetRule.asset == asset,
        ).with_entities(
            MarginAssetRule.basic_day_rate,
        ).first()
        if not asset_rule:
            raise ValueError(f"MarginAssetRule asset {asset} not exist")
        asset_vip_rates = cls._basic_rate_to_vip_rates({asset: asset_rule.basic_day_rate})[asset]
        return asset_vip_rates

    @classmethod
    def batch_get_asset_vip_rates_map(cls, assets: list[str]) -> dict[str, dict[int, Decimal]]:
        """ 批量获取借币币种，全部vip等级的日利率 """
        asset_rule_rows: list[MarginAssetRule] = MarginAssetRule.query.filter(
            MarginAssetRule.asset.in_(assets),
        ).with_entities(
            MarginAssetRule.asset,
            MarginAssetRule.basic_day_rate,
        ).all()
        asset_basic_day_rate_map = dict(asset_rule_rows)
        diff_assets = set(assets) - set(asset_basic_day_rate_map)
        if diff_assets:
            raise ValueError(f"MarginAssetRule assets: {diff_assets} not exist")

        result = cls._basic_rate_to_vip_rates(asset_basic_day_rate_map)
        return result

    @classmethod
    def batch_get_asset_vip_final_day_rates_map(cls, assets: set[str]) -> dict[str, dict[int, Decimal]]:
        """ 返回借币币种-vip等级-最终日利率，不包括用户的特殊利率
            1. VIP利率
            2. 动态系数（只对非USDT、USDC生效）
            2. 浮动利率算法（只对USDT生效）

        {
            # asset: {vip0: vip0_final_day_rate ...}
            "CET": {0: "0.001", 1: "0.0009", 2: "0.0008", 3: "0.0007", 4: "0.0006", 5: "0.0005"}
        }
        """
        asset_rule_rows: list[MarginAssetRule] = MarginAssetRule.query.filter(
            # 不筛选状态，外部保证
            MarginAssetRule.asset.in_(assets),
        ).with_entities(
            MarginAssetRule.asset,
            MarginAssetRule.basic_day_rate,
        ).all()
        asset_basic_day_rate_map = dict(asset_rule_rows)
        asset_vip_rates_map = cls._basic_rate_to_vip_rates(asset_basic_day_rate_map)

        asset_fix_power_map = {
            _asset: Decimal(fix_power)
            for _asset, fix_power in MarginAssetLoanFixPowerCache().hgetall().items()
        }

        fin_result = {}
        usdt_sp_rate = get_special_asset_rate(USDT_ASSET) or Decimal()
        for _asset, vip_level_day_rates in asset_vip_rates_map.items():
            if _asset != USDT_ASSET:
                _power = asset_fix_power_map.get(_asset, Decimal(1))
                vip_level_fin_day_rates = {level: rate * _power for level, rate in vip_level_day_rates.items()}
            else:
                vip_level_fin_day_rates = {level: max(rate, usdt_sp_rate) for level, rate in vip_level_day_rates.items()}
            fin_result[_asset] = vip_level_fin_day_rates
        return fin_result


def get_special_asset_rate(asset: str) -> None | Decimal:
    # only for usdt
    f_config = AssetFloatingRateMySQL.query.filter(
        AssetFloatingRateMySQL.asset == asset
    ).first()
    f_rate = f_config.rate if f_config else None
    return f_rate


def get_user_day_rate_fee(user_id: int, asset: str):
    # 子账号的【VIP杠杆日息】= 主账号的【VIP杠杆日息】
    sub_user = SubAccount.query.filter(
        SubAccount.user_id == user_id,
        SubAccount.status == SubAccount.Status.VALID,
    ).first()

    dt = now()
    q = UserMarginDayRate.query.filter(
        UserMarginDayRate.user_id == user_id,
        UserMarginDayRate.status == UserMarginDayRate.StatusType.PASS,
        or_(
            UserMarginDayRate.expired_time >= dt,
            UserMarginDayRate.expired_time.is_(None)
        )
    ).first()
    if not q:
        # 某个账号设置了特殊杠杆日息, 子账号也享受相应的特殊杠杆日息 (如果子账号也设置了, 优先用子账号的)
        if sub_user:
            q = UserMarginDayRate.query.filter(
                UserMarginDayRate.user_id == sub_user.main_user_id,
                UserMarginDayRate.status == UserMarginDayRate.StatusType.PASS,
                or_(UserMarginDayRate.expired_time >= dt, UserMarginDayRate.expired_time.is_(None)),
            ).first()

    if q:
        # 存在杠杆特殊费率配置,则不乘以基准,直接返回
        return q.fee
    else:
        if sub_user:
            # 子账号，用主账号的
            q = VipUser.query.filter(
                VipUser.status == VipUser.StatusType.PASS,
                VipUser.user_id == sub_user.main_user_id,
            ).first()
        else:
            q = VipUser.query.filter(
                VipUser.status == VipUser.StatusType.PASS,
                VipUser.user_id == user_id,
            ).first()
        vip_level = q.level if q else 0
        asset_vip_rates = LoanAssetDayRateHelper.get_asset_vip_rates(asset)
        rate = asset_vip_rates[vip_level]
    if asset == USDT_ASSET:
        return max(get_special_asset_rate(asset) or Decimal(), rate)
    _cache_fix_power = MarginAssetLoanFixPowerCache().hget(asset)
    fix_power = Decimal(_cache_fix_power) if _cache_fix_power else Decimal('1')
    rate *= fix_power
    return rate


def wrap_result(sell_amount, buy_amount):
    return {
        SELL_TYPE: sell_amount,
        BUY_TYPE: buy_amount
    }


def get_exchange_market_name(exchange: MarginIndexDetail.ExchangeNameType,
                             base_asset: str, quote_asset: str,
                             special_assets_mapping: Optional[Dict] = None):
    # special_asset_mapping = {
    #     MarginIndexDetail.ExchangeNameType.BITFINEX: {
    #         "DASH": "DSH",
    #         "IOTA": "IOT",
    #         "QTUM": "QTM",
    #         "USDT": "UST"
    #     }
    # }
    special_assets_mapping = special_assets_mapping or {}
    convert_base = special_assets_mapping.get(exchange, {}).get(base_asset, base_asset)
    convert_quote = special_assets_mapping.get(exchange, {}).get(quote_asset, quote_asset)
    rule_mapping = {
        # BTCUSDT
        MarginIndexDetail.ExchangeNameType.COINEX:
            lambda base, quote: f"{base}{quote}",
        # btcusdt
        MarginIndexDetail.ExchangeNameType.HUOBIGLOBAL:
            lambda base, quote: f"{base}{quote}".lower(),
        # BTCUSDT
        MarginIndexDetail.ExchangeNameType.BINANCE:
            lambda base, quote: f"{base}{quote}",
        # BTCUSDT
        MarginIndexDetail.ExchangeNameType.BINANCEUS:
            lambda base, quote: f"{base}{quote}",
        # BTC-USDT
        MarginIndexDetail.ExchangeNameType.OKEX:
            lambda base, quote: f"{base}-{quote}",
        # btc_usdt
        MarginIndexDetail.ExchangeNameType.GATEIO:
            lambda base, quote: f"{base}_{quote}".lower(),
        # BTC-USDT
        MarginIndexDetail.ExchangeNameType.KUCOIN:
            lambda base, quote: f"{base}-{quote}",
        # BTCUSDT
        MarginIndexDetail.ExchangeNameType.BITFINEX:
            lambda base, quote: f"t{base}{quote}",
        # BTC-USDT
        MarginIndexDetail.ExchangeNameType.BITTREX:
            lambda base, quote: f"{base}-{quote}",
        # USDT_BTC
        MarginIndexDetail.ExchangeNameType.POLONIEX:
            lambda base, quote: f"{quote}_{base}",
        # BTCUSDT
        MarginIndexDetail.ExchangeNameType.BYBIT:
            lambda base, quote: f"{base}{quote}",
        # BTC/USDT
        MarginIndexDetail.ExchangeNameType.BITSTAMP:
            lambda base, quote: f"{base}/{quote}",
        # btcusdt
        MarginIndexDetail.ExchangeNameType.HTX:
            lambda base, quote: f"{base}{quote}".lower(),
        # BTCUSDT
        MarginIndexDetail.ExchangeNameType.MEXC:
            lambda base, quote: f"{base}{quote}",
        # BTCUSDT
        MarginIndexDetail.ExchangeNameType.BITGET:
            lambda base, quote: f"{base}{quote}",
        MarginIndexDetail.ExchangeNameType.BINGX:
            lambda base, quote: f"{base}_{quote}",
    }
    return rule_mapping[exchange](convert_base, convert_quote)


class MarginUserAccountInfo(object):

    def __init__(self, user_id):
        self.user_id = user_id

    @cached_property
    def all_market_data(self):
        q = Market.query.with_entities(
            Market.name,
            Market.base_asset,
            Market.quote_asset
        ).all()
        return {v.name: dict(base_asset=v.base_asset,
                             quote_asset=v.quote_asset) for v in q}

    @cached_property
    def all_margin_data(self):
        q = MarginAccount.query.with_entities(
            MarginAccount.id,
            MarginAccount.name,
            MarginAccount.leverage,
            MarginAccount.warning_rate,
            MarginAccount.max_liquidation_rate
        ).all()
        return {v.name: dict(
            account_id=v.id,
            leverage=v.leverage,
            warning_rate=v.warning_rate,
            max_liquidation_rate=v.max_liquidation_rate) for v in q}

    @cached_property
    def all_account_ids(self):
        return [account_id for account_id in MarginAccountNameCache.list_online_markets().keys()]

    @cached_property
    def all_account_info(self):
        result = {}
        _all_market_data = self.all_market_data
        _all_margin_data = self.all_margin_data
        for account_id, market in MarginAccountNameCache.list_online_markets().items():
            _market_data = _all_market_data[market]
            _margin_data = _all_margin_data[market]
            result[account_id] = {
                "account_id": account_id,
                "leverage": _margin_data['leverage'],
                "market_type": market,
                "sell_asset_type": _market_data["base_asset"],
                "buy_asset_type": _market_data["quote_asset"],
                "warning_rate": _margin_data["warning_rate"],
                "max_liquidation_rate": _margin_data["max_liquidation_rate"],
            }
        return result

    @cached_property
    def all_liquidation_rules(self) -> Dict[int, Any]:
        account_ids = self.all_account_info.keys()
        details = MarginLiquidationRate.query.filter(
            MarginLiquidationRate.account_id.in_(account_ids),
            MarginLiquidationRate.status == MarginLiquidationRate.StatusType.PASS
        ).all()
        all_rates = [dict(account_id=v.account_id,
                          amount=v.liquidation_amount,
                          rate=v.liquidation_rate) for v in details]
        result = {}
        for account_id in account_ids:
            details = [v for v in all_rates if v["account_id"] == account_id]
            result[account_id] = dict(
                max_liquidation_rate=self.all_account_info[account_id]['max_liquidation_rate'],
                details=sorted(details, key=lambda d: d["amount"], reverse=True)
            )
        return result

    def get_liquidation_rate(self, account_id: int, amount: Decimal):
        rules = self.all_liquidation_rules[account_id]
        details = rules["details"]
        if amount >= details[0]["amount"]:
            return rules["max_liquidation_rate"]
        for index, detail in enumerate(details):
            if amount > detail["amount"]:
                return details[index - 1]["rate"]
        return details[-1]["rate"]

    def get_user_margin_balance_data(self):
        c = ServerClient()
        balances_result = c.get_user_accounts_balances(self.user_id)
        result = defaultdict(lambda: {
            SELL_TYPE: dict(available=Decimal(), frozen=Decimal()),
            BUY_TYPE: dict(available=Decimal(), frozen=Decimal())
        })
        for account_id, data in balances_result.items():
            account_id = int(account_id)
            if account_id > 0 and account_id in self.all_account_info.keys():
                for asset, balance_data in data.items():
                    if asset == self.all_account_info[account_id]["sell_asset_type"]:
                        result[account_id][SELL_TYPE] = balance_data
                    if asset == self.all_account_info[account_id]["buy_asset_type"]:
                        result[account_id][BUY_TYPE] = balance_data
        return result

    def get_all_except_status(self):
        """
        查询是否有爆仓欠款记录
        :return:
        """
        account_ids = self.all_account_info.keys()
        records = MarginLoanOrder.query.filter(
            MarginLoanOrder.account_id.in_(self.all_account_info.keys()),
            MarginLoanOrder.user_id == self.user_id,
            MarginLoanOrder.status == MarginLoanOrder.StatusType.ARREARS,
        ).all()
        result = {account_id: {"arrears": False} for account_id in account_ids}
        for record in records:
            result[record.account_id][MarginLoanOrder.StatusType.ARREARS.value] = True
        return result

    def get_all_account_data(self):
        records = MarginLoanOrder.query.filter(MarginLoanOrder.user_id == self.user_id,
                                               MarginLoanOrder.status.in_(
                                                   [MarginLoanOrder.StatusType.ARREARS,
                                                    MarginLoanOrder.StatusType.BURST,
                                                    MarginLoanOrder.StatusType.PASS,
                                                    ])). \
            with_entities(
            MarginLoanOrder.account_id,
            MarginLoanOrder.asset,
            func.sum(MarginLoanOrder.unflat_amount).label('total_unflat_amount'),
            func.sum(MarginLoanOrder.interest_amount).label('total_interest_amount')
        ).group_by(MarginLoanOrder.account_id, MarginLoanOrder.asset).all()
        result = {
            account_id: {
                LOAN_TYPE: wrap_result(Decimal(), Decimal()),
                INTEREST_TYPE: wrap_result(Decimal(), Decimal())
            }
            for account_id in self.all_account_info
        }
        for v in records:
            if v.asset == self.all_account_info[v.account_id]["sell_asset_type"]:
                result[v.account_id][LOAN_TYPE][SELL_TYPE] = v.total_unflat_amount
                result[v.account_id][INTEREST_TYPE][SELL_TYPE] = v.total_interest_amount
            if v.asset == self.all_account_info[v.account_id]["buy_asset_type"]:
                result[v.account_id][LOAN_TYPE][BUY_TYPE] = v.total_unflat_amount
                result[v.account_id][INTEREST_TYPE][BUY_TYPE] = v.total_interest_amount
        return result

    @classmethod
    def get_user_liquidation_order(cls, user_id: int):
        burst_orders = [item.order_id for item in MarginLiquidationOrder.query.filter(
            MarginLiquidationOrder.status == MarginLiquidationOrder.Status.CREATE,
            MarginLiquidationOrder.user_id == user_id,
        )]
        return burst_orders

    @classmethod
    def validate_liquidation_order(cls, user_id: int, order_id: int):
        #
        burst_orders = cls.get_user_liquidation_order(user_id)
        if order_id in burst_orders:
            raise OrderExceptionMap[OrderException.SYSTEM_ORDER]


class MarginAccountHelper(object):
    LOAN_RATE = Decimal('1.02')

    def __init__(self, user_id: int, account_id: int):
        """
        :param user_id:
        :param account_id:
        计算杠杆账户最大可转出逻辑, calculate_transfer_out_max_amount
        计算杠杆账户最大可借币逻辑, calculate_loan_max_amount
        """
        self.user_id = user_id
        self.account_id = account_id
        self.server_client = ServerClient(current_app.logger)

    @cached_property
    def account_detail(self):
        return self.get_account_info(self.account_id)

    @classmethod
    def get_account_info(cls, account_id: int):
        margin_cache = MarginAccountIdCache(account_id).dict
        if not margin_cache:
            raise MarginAccountNotOpen
        market = margin_cache['name']
        market_cache = MarketCache(market).dict
        result = {
            "account_id": account_id,
            "leverage": margin_cache['leverage'],
            "base_asset_max_loan": margin_cache['base_asset_max_loan'],
            "quote_asset_max_loan": margin_cache['quote_asset_max_loan'],
            "market_type": market,
            "sell_asset_type": market_cache["base_asset"],
            "buy_asset_type": market_cache["quote_asset"],
        }
        return result

    def get_index_price(self):
        market_type = self.account_detail['market_type']
        price = IndexPriceCache().get_price(market_type)
        if not price or price <= Decimal('0'):
            raise MarginIndexPriceError
        return Decimal(price)

    @cached_property
    def query_user_data(self) -> Dict:
        """
        查询借币订单计算当前用户的定价货币和交易货币的待还以及利息数据
        :return:
        """
        records = MarginLoanOrder.query.filter(
            MarginLoanOrder.user_id == self.user_id,
            MarginLoanOrder.account_id == self.account_id,
            MarginLoanOrder.status.in_(
                [MarginLoanOrder.StatusType.ARREARS,
                 MarginLoanOrder.StatusType.BURST,
                 MarginLoanOrder.StatusType.CREATE,
                 MarginLoanOrder.StatusType.PASS])). \
            with_entities(
            MarginLoanOrder.asset,
            func.sum(MarginLoanOrder.unflat_amount).label('total_unflat_amount'),
            func.sum(MarginLoanOrder.interest_amount).label('total_interest_amount')
        ).group_by(MarginLoanOrder.asset).all()
        sell_unflat_amount = Decimal("0")
        sell_interest_amount = Decimal("0")
        buy_unflat_amount = Decimal("0")
        buy_interest_amount = Decimal("0")
        for v in records:
            if v.asset == self.account_detail["sell_asset_type"]:
                sell_unflat_amount = v.total_unflat_amount
                sell_interest_amount = v.total_interest_amount
            if v.asset == self.account_detail["buy_asset_type"]:
                buy_unflat_amount = v.total_unflat_amount
                buy_interest_amount = v.total_interest_amount
        return {
            LOAN_TYPE: wrap_result(sell_unflat_amount, buy_unflat_amount),
            INTEREST_TYPE: wrap_result(sell_interest_amount, buy_interest_amount)
        }

    @classmethod
    def query_all_user_data(cls):
        """
        查询所有账号借币订单计算当前用户的定价货币和交易货币的待还以及利息数据, 用于生成快照
        :return:
        """
        records = MarginLoanOrder.query.filter(MarginLoanOrder.status.in_(
            [MarginLoanOrder.StatusType.ARREARS,
             MarginLoanOrder.StatusType.BURST,
             MarginLoanOrder.StatusType.PASS])). \
            with_entities(
            MarginLoanOrder.user_id,
            MarginLoanOrder.asset,
            MarginLoanOrder.account_id,
            func.sum(MarginLoanOrder.unflat_amount).label('total_unflat_amount'),
            func.sum(MarginLoanOrder.interest_amount).label('total_interest_amount')
        ).group_by(MarginLoanOrder.user_id, MarginLoanOrder.asset,
                   MarginLoanOrder.account_id).all()
        return records

    @classmethod
    def query_all_user_data_with_burst(cls):
        data = cls.query_all_user_data()
        account_info_map = {}
        records = defaultdict(lambda: defaultdict(lambda: defaultdict(list)))
        for item in data:
            records[item.user_id][item.account_id]['values'].append(item)
        for user_id, account_values in records.items():
            for account_id, values_dict in account_values.items():
                if account_id not in account_info_map:
                    account_info_map[account_id] = cls._get_account_info(account_id)

                sell_unflat_amount = Decimal("0")
                sell_interest_amount = Decimal("0")
                buy_unflat_amount = Decimal("0")
                buy_interest_amount = Decimal("0")
                for item in values_dict['values']:
                    if item.asset == account_info_map[account_id]['sell_asset_type']:
                        sell_unflat_amount = item.total_unflat_amount
                        sell_interest_amount = item.total_interest_amount
                    if item.asset == account_info_map[account_id]['buy_asset_type']:
                        buy_unflat_amount = item.total_unflat_amount
                        buy_interest_amount = item.total_interest_amount

                index_price = cls(0, account_id).get_index_price()
                amount = (sell_unflat_amount + sell_interest_amount) + (
                        buy_interest_amount + buy_unflat_amount) / index_price
                if amount > 0:
                    burst_rate = cls._calculate_burst_rate(account_id, amount) or 0
                else:
                    burst_rate = 0
                records[user_id][account_id]['burst_rate'] = burst_rate
                records[user_id][account_id]['index_price'] = index_price
        return records

    def get_user_warn_rate(self):
        """
        风险率=[(定价货币总资产-定价货币未还利息)/指数价格+(交易货币总资产-交易货币未还利息)]/(定价货币借入资产/指数价格+交易货币借入资产)*100%
        :return: 获取当前用户的风险率
        """
        index_price = self.get_index_price()

        sell_unflat_amount = self.query_user_data[LOAN_TYPE][SELL_TYPE]
        buy_unflat_amount = self.query_user_data[LOAN_TYPE][BUY_TYPE]
        sell_interest_amount = self.query_user_data[INTEREST_TYPE][SELL_TYPE]
        buy_interest_amount = self.query_user_data[INTEREST_TYPE][BUY_TYPE]

        sell_asset_amount = self.get_margin_balance[SELL_TYPE]
        buy_asset_amount = self.get_margin_balance[BUY_TYPE]

        if buy_unflat_amount == Decimal('0') and sell_unflat_amount == Decimal('0'):
            raise MarginLoanOrderNotFound

        return self._calculate_warn_rate(buy_interest_amount, buy_unflat_amount, buy_asset_amount,
                                         sell_interest_amount, sell_unflat_amount,
                                         sell_asset_amount, index_price)

    @classmethod
    def _calculate_warn_rate(cls, buy_interest_amount, buy_unflat_amount, buy_asset_amount,
                             sell_interest_amount, sell_unflat_amount, sell_asset_amount,
                             index_price):
        x = (
                    buy_asset_amount - buy_interest_amount) / index_price + sell_asset_amount - sell_interest_amount
        y = (buy_unflat_amount / index_price) + sell_unflat_amount
        return Decimal(x / y)

    def _get_margin_balance(self, balance_type: BalanceType):
        if balance_type not in BalanceType:
            raise MarginBalanceCannotGet
        balance_data = self.server_client.get_user_balances(self.user_id,
                                                            account_id=self.account_id)
        sell_asset_type = self.account_detail["sell_asset_type"]
        buy_asset_type = self.account_detail["buy_asset_type"]
        if balance_type == BalanceType.ALL:
            sell_asset_amount = Decimal(
                balance_data[sell_asset_type][BalanceType.AVAILABLE.value]) + \
                                balance_data[sell_asset_type][BalanceType.FROZEN.value]
            buy_asset_amount = Decimal(balance_data[buy_asset_type][BalanceType.AVAILABLE.value]) + \
                               balance_data[buy_asset_type][BalanceType.FROZEN.value]
        else:
            sell_asset_amount = Decimal(balance_data[sell_asset_type][balance_type.value])
            buy_asset_amount = Decimal(balance_data[buy_asset_type][balance_type.value])
        return wrap_result(sell_asset_amount, buy_asset_amount)

    @property
    def get_margin_balance(self):
        return self._get_margin_balance(BalanceType.ALL)

    @property
    def get_margin_available_balance(self):
        return self._get_margin_balance(BalanceType.AVAILABLE)

    @property
    def get_margin_frozen(self):
        return self._get_margin_balance(BalanceType.FROZEN)

    def get_user_free_order(self):
        """
        检查用户是否有日利率为0的订单
        :return:
        """
        record = MarginLoanOrder.query.filter(
            MarginLoanOrder.user_id == self.user_id,
            MarginLoanOrder.status.in_([MarginLoanOrder.StatusType.PASS,
                                        MarginLoanOrder.StatusType.BURST,
                                        MarginLoanOrder.StatusType.ARREARS]),
            MarginLoanOrder.day_rate == Decimal('0')
        ).first()
        if record:
            return True
        else:
            return False

    def calculate_transfer_out_max_amount(self):
        """
        计算用户杠杆账户中某一币种最大可转出金额

        rules:
            1、最多支持8位小数，舍位处理，如小数点后尾数为0，则不显示0；

            2、从现货账户到杠杆账户，最多可转数量算法：最多可转等于对应币种在现货账户的可用余额；

            3、从杠杆账户到现货账户，最多可转数量算法：

            • 如果当前杠杆账户对处于未借币状态，可转数量等于对应币种在杠杆账户的可用余额；

            • 如果有借币，且当前杠杆账户的风险率小于或等于转账风险率，则最多可转数量为0；

            • 如果有借币，且当前杠杆交易对的风险率大于转账风险率，那么：

                         交易货币的最多可转数量=（风险率-转账风险率）*（定价货币借入资产/市价+交易货币借入资产）

                         定价货币的最多可转数量=（风险率-转账风险率）*（定价货币借入资产+交易货币借入资产*市价）

            备注：

            1、每个杠杆交易对的转账风险率都是独立的，支持后台配置；

            2、当最多可转数量大于可用数量时，最多可转数量取可用数量；

            3、服务器的最多可转数量校验要比前端的交验大2%。

            4、2倍杠杆的转账风险率=200%，3倍杠杆的转账风险率=150%，4倍杠杆的转账风险率=135%，5倍杠杆的转账风险率=125%

            :return:
        """

        record = MarginLoanOrder.query.filter(
            MarginLoanOrder.account_id == self.account_id,
            MarginLoanOrder.user_id == self.user_id,
            MarginLoanOrder.status.in_([MarginLoanOrder.StatusType.PASS,
                                        MarginLoanOrder.StatusType.CREATE,  # CREATE可能已经给用户加了资产
                                        MarginLoanOrder.StatusType.BURST,
                                        MarginLoanOrder.StatusType.ARREARS])).first()
        # 当前没有未完成的借币订单, 返回当前账户可用余额
        if not record:
            return self.get_margin_available_balance

        if self.get_user_free_order():
            return wrap_result(Decimal("0"), Decimal("0"))

        # 如果有借币，且当前杠杆账户的风险率小于或等于转账风险率，则最多可转数量为0
        user_warn_rate = self.get_user_warn_rate()
        trans_rate = TRANSFER_RATE_DICT[self.account_detail["leverage"]]
        if user_warn_rate <= trans_rate:
            return wrap_result(Decimal("0"), Decimal("0"))
        else:
            delta = user_warn_rate - trans_rate
            # 使用指数价格
            rate = self.get_index_price()
            # 交易货币的最多可转数量 =（风险率-转账风险率）*（定价货币借入资产/市价+交易货币借入资产）
            sell_asset_amount = \
                quantize_amount(
                    delta * (
                        self.get_user_loan_amount[BUY_TYPE] / rate +
                        self.get_user_loan_amount[SELL_TYPE]),
                        8,
                        rounding=ROUND_HALF_UP)
            # 定价货币的最多可转数量 =（风险率-转账风险率）*（定价货币借入资产+交易货币借入资产*市价）
            buy_asset_amount = \
                quantize_amount(
                    delta * (
                        self.get_user_loan_amount[BUY_TYPE] +
                        self.get_user_loan_amount[SELL_TYPE] * rate),
                        8,
                        rounding=ROUND_HALF_UP)

            # 如果计算出来的最大可转大于可用余额则去余额
            if sell_asset_amount >= self.get_margin_available_balance[SELL_TYPE]:
                sell_asset_amount = self.get_margin_available_balance[SELL_TYPE]
            sell_asset_amount = min(sell_asset_amount, self.get_margin_available_balance[SELL_TYPE])
            buy_asset_amount = min(buy_asset_amount, self.get_margin_available_balance[BUY_TYPE])
            return wrap_result(sell_asset_amount, buy_asset_amount)

    @cached_property
    def get_user_loan_amount(self):
        """
        :return: 获取用户已借数量
        """
        sell_loan_amount = self.query_user_data[LOAN_TYPE][SELL_TYPE]
        buy_loan_amount = self.query_user_data[LOAN_TYPE][BUY_TYPE]
        return wrap_result(sell_loan_amount, buy_loan_amount)

    @cached_property
    def get_user_interest_amount(self):
        """
        :return: 获取用户总利息
        """
        sell_interest_amount = self.query_user_data[INTEREST_TYPE][SELL_TYPE]
        buy_interest_amount = self.query_user_data[INTEREST_TYPE][BUY_TYPE]
        return wrap_result(sell_interest_amount, buy_interest_amount)

    @cached_property
    def get_margin_asset_config(self):
        sell_asset_config = MarginAssetRule.query.filter(
            MarginAssetRule.asset == self.account_detail["sell_asset_type"],
            MarginAssetRule.status == MarginAssetRule.StatusType.OPEN).first()
        buy_asset_config = MarginAssetRule.query.filter(
            MarginAssetRule.asset == self.account_detail["buy_asset_type"],
            MarginAssetRule.status == MarginAssetRule.StatusType.OPEN).first()
        return wrap_result(sell_asset_config, buy_asset_config)

    @cached_property
    def get_margin_market_config(self) -> dict:
        """ 获取对应杠杆市场配置 """
        margin_account_config = MarginAccountIdCache(self.account_id).dict
        if not margin_account_config:
            raise MarginAccountNotOpen
        return margin_account_config

    @cached_property
    def get_user_available_special_margin_asset_config(self) -> Dict[str, Decimal]:
        """
        获取用户可用的杠杆特殊借币额度
        - 用户杠杆特殊借币额度优先级最高，高于通用借币配置，且不再与市场借币配置做交叉判断
        - Use git blame click and show the history version
        """
        assets = [self.account_detail["sell_asset_type"], self.account_detail["buy_asset_type"]]
        user_rules = UserMarginAssetRule.query.filter(
            UserMarginAssetRule.user_id == self.user_id,
            UserMarginAssetRule.asset.in_(assets),
            UserMarginAssetRule.status == UserMarginAssetRule.StatusType.PASS,
        ).all()

        spe_asset_max_loan_map = {rule.asset: rule.max_loan for rule in user_rules}
        return spe_asset_max_loan_map

    def _calculate_asset_mortgage_amount_server(self):
        """
            资产抵押可借数量=(账户总资产-未还借入资产-未还利息)*(最大杠杆倍数-1)- 未还借入资产

            备注1：计算交易货币可借数量时，资产统一按市价换成交易货币再计算；
            计算定价货币可借数量时，资产统一按市价换成定价货币再计算

            备注2：服务端计算“资产抵押可借数量”时，要乘以1.02系数

            服务端的数值
        :return:
        """
        result = self._calculate_asset_mortgage_amount()
        return wrap_result(result[SELL_TYPE] * self.LOAN_RATE, result[BUY_TYPE] * self.LOAN_RATE)

    def _calculate_asset_mortgage_amount(self):
        """
            资产抵押可借数量=(账户总资产-未还借入资产-未还利息)*(最大杠杆倍数-1)- 未还借入资产

            备注1：计算交易货币可借数量时，资产统一按市价换成交易货币再计算；
            计算定价货币可借数量时，资产统一按市价换成定价货币再计算

            备注2：服务端计算“资产抵押可借数量”时，要乘以1.02系数

            前端的数值
        :return:
        """
        balance = self.get_margin_balance

        convert_asset_to_sell = lambda x, y: x + y / self.get_index_price()
        convert_asset_to_buy = lambda x, y: x * self.get_index_price() + y
        amount_to_sell = convert_asset_to_sell(balance[SELL_TYPE], balance[BUY_TYPE])
        loan_amount_to_sell = convert_asset_to_sell(self.get_user_loan_amount[SELL_TYPE],
                                                    self.get_user_loan_amount[BUY_TYPE])
        interest_amount_to_sell = convert_asset_to_sell(self.get_user_interest_amount[SELL_TYPE],
                                                        self.get_user_interest_amount[BUY_TYPE])
        amount_to_buy = convert_asset_to_buy(balance[SELL_TYPE], balance[BUY_TYPE])
        loan_amount_to_buy = convert_asset_to_buy(self.get_user_loan_amount[SELL_TYPE],
                                                  self.get_user_loan_amount[BUY_TYPE])
        interest_amount_to_buy = convert_asset_to_buy(self.get_user_interest_amount[SELL_TYPE],
                                                      self.get_user_interest_amount[BUY_TYPE])
        leverage = Decimal(str(self.account_detail["leverage"]))
        sell_mortgage_amount = (amount_to_sell - loan_amount_to_sell - interest_amount_to_sell) * (
                leverage - Decimal('1')) - loan_amount_to_sell
        buy_mortgage_amount = (amount_to_buy - loan_amount_to_buy - interest_amount_to_buy) * (
                leverage - Decimal('1')) - loan_amount_to_buy
        sell_mortgage_amount = quantize_amount(
                sell_mortgage_amount, 8, ROUND_HALF_UP)
        buy_mortgage_amount = quantize_amount(
                buy_mortgage_amount, 8, ROUND_HALF_UP)
        return wrap_result(sell_mortgage_amount, buy_mortgage_amount)

    def _calculate_system_rest_amount(self):
        """
            系统剩余可借数量=借币池可借总额-借币池已借出数量+所有用户的理财账户的总币数
        :return:
        """
        from app.assets import get_asset_config
        base_asset = self.account_detail["sell_asset_type"]
        quote_asset = self.account_detail["buy_asset_type"]
        sell_is_open = self.get_margin_asset_config[SELL_TYPE]
        buy_is_open = self.get_margin_asset_config[BUY_TYPE]

        return wrap_result(
            sell_amount=max(get_asset_config(base_asset).lendable_amount, Decimal('0'))
            if sell_is_open else Decimal(),
            buy_amount=max(get_asset_config(quote_asset).lendable_amount, Decimal('0'))
            if buy_is_open else Decimal()
        )

    def calculate_system_rest_amount(self):
        """

            系统剩余可借数量=借币池可借总额-借币池已借出数量
        :return:
        """
        return self._calculate_system_rest_amount()

    def _calculate_user_rest_amount_for_market(self):
        """
        对应市场对应币种剩余可借数量=对应市场对应币种可借总额-对应市场对应币种已借数量
        对应市场对应币种可借总额： 见 get_user_special_margin_market_config 的说明
        """
        user_loan_amount = self.get_user_loan_amount
        margin_account_config = self.get_margin_market_config
        user_special_config = self.get_user_special_margin_market_config
        if user_special_config:
            base_asset_max_loan = user_special_config['base_asset_max_loan']
            sell_amount = base_asset_max_loan - user_loan_amount[SELL_TYPE]

            quote_asset_max_loan = user_special_config['quote_asset_max_loan']
            buy_amount = quote_asset_max_loan - user_loan_amount[BUY_TYPE]
        else:
            if base_asset_max_loan := margin_account_config.get("base_asset_max_loan"):
                sell_amount = base_asset_max_loan - user_loan_amount[SELL_TYPE]
            else:
                sell_amount = Decimal("0")

            if quote_asset_max_loan := margin_account_config.get("quote_asset_max_loan"):
                buy_amount = quote_asset_max_loan - user_loan_amount[BUY_TYPE]
            else:
                buy_amount = Decimal("0")

        return wrap_result(sell_amount=sell_amount, buy_amount=buy_amount)

    @cached_property
    def get_user_special_margin_market_config(self) -> Dict[str, Decimal]:
        """
        获取用户可用的杠杆市场特殊借币额度
        - 用户杠杆市场特殊借币额度优先级最高，高于通用借币市场配置
        """
        market = self.account_detail["market_type"]
        model = UserMarginAccountRule
        user_rule = model.query.filter(
            model.user_id == self.user_id,
            model.market == market,
            model.status == model.StatusType.PASS,
        ).first()
        if user_rule:
            return {
                'base_asset_max_loan': user_rule.base_asset_max_loan,
                'quote_asset_max_loan': user_rule.quote_asset_max_loan,
            }

        return {}

    def _calculate_user_rest_amount_for_asset(self):
        """
        对应币种剩余可借数量=对应币种可借总额-对应用户对应币种已借数量
        对应币种可借总额： 见 get_user_special_margin_asset_config 的说明
        """
        records = (
            MarginLoanOrder.query.filter(
                MarginLoanOrder.user_id == self.user_id,
                MarginLoanOrder.asset.in_(
                    [
                        self.account_detail["sell_asset_type"],
                        self.account_detail["buy_asset_type"],
                    ]
                ),
                MarginLoanOrder.status.in_(
                    [
                        MarginLoanOrder.StatusType.ARREARS,
                        MarginLoanOrder.StatusType.BURST,
                        MarginLoanOrder.StatusType.PASS,
                    ]
                ),
            )
            .with_entities(
                MarginLoanOrder.asset,
                func.sum(MarginLoanOrder.unflat_amount).label("total_unflat_amount"),
            )
            .group_by(MarginLoanOrder.asset)
            .all()
        )
        sell_unflat_amount = Decimal("0")
        buy_unflat_amount = Decimal("0")
        for v in records:
            if v.asset == self.account_detail["sell_asset_type"]:
                sell_unflat_amount = v.total_unflat_amount
            if v.asset == self.account_detail["buy_asset_type"]:
                buy_unflat_amount = v.total_unflat_amount

        user_special_config = self.get_user_available_special_margin_asset_config
        sell_asset = self.account_detail["sell_asset_type"]
        sell_amount = self._get_user_rest_asset_amount(SELL_TYPE, user_special_config, sell_asset, sell_unflat_amount)

        buy_asset = self.account_detail["buy_asset_type"]
        buy_amount = self._get_user_rest_asset_amount(BUY_TYPE, user_special_config, buy_asset, buy_unflat_amount)

        return wrap_result(sell_amount=sell_amount, buy_amount=buy_amount)

    def _get_user_rest_asset_amount(
            self,
            asset_type: str,
            user_special_config: dict,
            asset: str,
            unflat_amount: Decimal
    ) -> Decimal:
        if user_special_config and user_special_config.get(asset):
            max_loan = user_special_config[asset]
            amount = max_loan - unflat_amount
        else:  # 无特殊借币配置/关闭
            configs = self.get_margin_asset_config
            asset_config = configs[asset_type]
            if asset_config:
                max_loan = asset_config.max_loan
                amount = max_loan - unflat_amount
            else:
                amount = Decimal("0")
        return amount

    def _calculate_user_rest_amount(self):
        """
        单个用户单个币种剩余可借数量=Min(对应币种剩余可借数量，对应市场对应币种剩余可借数量)
        """
        asset_rest = self._calculate_user_rest_amount_for_asset()
        market_rest = self._calculate_user_rest_amount_for_market()
        return wrap_result(
            sell_amount=min(asset_rest[SELL_TYPE], market_rest[SELL_TYPE]),
            buy_amount=min(asset_rest[BUY_TYPE], market_rest[BUY_TYPE]),
        )

    @cached_property
    def _get_sell_asset_arrears_status(self):
        """
        定价货币
        查询是否有欠款记录
        :return:
        """
        sell_loan_record = MarginLoanOrder.query.filter(
            MarginLoanOrder.account_id == self.account_id,
            MarginLoanOrder.user_id == self.user_id,
            MarginLoanOrder.asset == self.account_detail["sell_asset_type"],
            MarginLoanOrder.status.in_(
                [MarginLoanOrder.StatusType.BURST, MarginLoanOrder.StatusType.ARREARS])).first()
        return True if sell_loan_record else False

    @cached_property
    def _get_buy_asset_arrears_status(self):
        """
        交易货币
        查询是否有欠款记录
        :return:
        """
        buy_loan_record = MarginLoanOrder.query.filter(
            MarginLoanOrder.account_id == self.account_id,
            MarginLoanOrder.user_id == self.user_id,
            MarginLoanOrder.asset == self.account_detail["buy_asset_type"],
            MarginLoanOrder.status.in_(
                [MarginLoanOrder.StatusType.BURST, MarginLoanOrder.StatusType.ARREARS])).first()
        return True if buy_loan_record else False

    def get_user_arrears_status(self):
        """
        查询是否有欠款记录
        :return:
        """
        record = MarginLoanOrder.query.filter(
            MarginLoanOrder.account_id == self.account_id,
            MarginLoanOrder.user_id == self.user_id,
            MarginLoanOrder.status == MarginLoanOrder.StatusType.ARREARS).first()
        return True if record else False

    def get_user_burst_status(self):
        """
        查询是否处在爆仓状态
        :return:
        """
        record = MarginLoanOrder.query.filter(
            MarginLoanOrder.account_id == self.account_id,
            MarginLoanOrder.user_id == self.user_id,
            MarginLoanOrder.status == MarginLoanOrder.StatusType.BURST).first()
        return True if record else False

    def calculate_loan_max_amount(self):
        """
        计算用户杠杆账户中某一币种最大可借金额

            可借：最多支持4位小数，舍位处理，如小数点后尾数为0，则不显示0；点击可借数量后，自动在数量输入框录入可借数量；

            可借数量=Min(资产抵押可借数量，系统剩余可借数量，单个用户单个币种剩余可借数量)

            资产抵押可借数量=(账户总资产-未还借入资产-未还利息)*(最大杠杆倍数-1)- 未还借入资产

            备注1：计算交易货币可借数量时，资产统一按市价换成交易货币再计算；    计算定价货币可借数量时，资产统一按市价换成定价货币再计算

            备注2：服务端计算“资产抵押可借数量”时，要乘以1.02系数

            系统剩余可借数量=借币池可借总额-借币池已借出数量

            备注：每个币种的借币池都是隔离的

            单个用户单个币种剩余可借数量=单个用户单个币种可借总额-单个用户单个币种已借数量

            备注：每个币种的单个用户单个币种可借都是隔离的
            :return:
        """
        # 先判断借币配置是否开启以及是否有欠款记录
        if self.get_margin_asset_config[SELL_TYPE] and not self._get_sell_asset_arrears_status:
            sell_amount = min([self._calculate_asset_mortgage_amount(),
                               self._calculate_system_rest_amount(),
                               self._calculate_user_rest_amount()], key=lambda x: x[SELL_TYPE])[
                SELL_TYPE]
        else:
            sell_amount = Decimal('0')

        # 先判断借币配置是否开启以及是否有欠款记录
        if self.get_margin_asset_config[BUY_TYPE] and not self._get_buy_asset_arrears_status:
            buy_amount = min([self._calculate_asset_mortgage_amount(),
                              self._calculate_system_rest_amount(),
                              self._calculate_user_rest_amount()], key=lambda x: x[BUY_TYPE])[
                BUY_TYPE]
        else:
            buy_amount = Decimal('0')
        return wrap_result(sell_amount=sell_amount if sell_amount > 0 else Decimal('0'),
                           buy_amount=buy_amount if buy_amount > 0 else Decimal('0'))

    def calculate_loan_max_amount_server(self):
        """
        计算用户杠杆账户中某一币种最大可借金额

            可借：最多支持4位小数，舍位处理，如小数点后尾数为0，则不显示0；点击可借数量后，自动在数量输入框录入可借数量；

            可借数量=Min(资产抵押可借数量，系统剩余可借数量，单个用户单个币种剩余可借数量)

            资产抵押可借数量=(账户总资产-未还借入资产-未还利息)*(最大杠杆倍数-1)- 未还借入资产

            备注1：计算交易货币可借数量时，资产统一按市价换成交易货币再计算；    计算定价货币可借数量时，资产统一按市价换成定价货币再计算

            备注2：服务端计算“资产抵押可借数量”时，要乘以1.02系数

            系统剩余可借数量=借币池可借总额-借币池已借出数量

            备注：每个币种的借币池都是隔离的

            单个用户单个币种剩余可借数量=单个用户单个币种可借总额-单个用户单个币种已借数量

            备注：每个币种的单个用户单个币种可借都是隔离的
            :return:
        """
        # 先判断借币配置是否开启以及是否有欠款记录
        if self.get_margin_asset_config[SELL_TYPE] and not self._get_sell_asset_arrears_status:
            sell_amount = min([self._calculate_asset_mortgage_amount_server(),
                               self._calculate_system_rest_amount(),
                               self._calculate_user_rest_amount()], key=lambda x: x[SELL_TYPE])[
                SELL_TYPE]
        else:
            sell_amount = Decimal('0')

        # 先判断借币配置是否开启以及是否有欠款记录
        if self.get_margin_asset_config[BUY_TYPE] and not self._get_buy_asset_arrears_status:
            buy_amount = min([self._calculate_asset_mortgage_amount_server(),
                              self._calculate_system_rest_amount(),
                              self._calculate_user_rest_amount()], key=lambda x: x[BUY_TYPE])[
                BUY_TYPE]
        else:
            buy_amount = Decimal('0')
        return wrap_result(sell_amount=sell_amount if sell_amount > 0 else Decimal('0'),
                           buy_amount=buy_amount if buy_amount > 0 else Decimal('0'))

    def calculate_unflat_and_interest_amount(self):
        """
        应还数量：对应杠杆账户选中币种的所有借币订单，包括未还借币数量与未还利息。

            1、借币数量：对应杠杆账户选中币种的所有未还借币数量，最多支持4位小数，进位处理，如小数点后尾数为0，则不显示0；

            2、利息：对应杠杆账户选中币种的所有未还利息，最多支持8位小数，进位处理，如小数点后尾数为0，则不显示0；

        :return:
        """

        return wrap_result(
            self.get_user_loan_amount[SELL_TYPE] + self.get_user_interest_amount[SELL_TYPE],
            self.get_user_loan_amount[BUY_TYPE] + self.get_user_interest_amount[BUY_TYPE])

    def calculate_burst_price(self):
        """
        强平价: (定价货币借入资产*强平风险率+定价货币未还利息-定价货币总资产)
                /(交易货币总资产-交易货币未还利息-交易货币借入资产*强平风险率)
        :return:
        """
        return self._calculate_burst_price(self.get_user_loan_amount[BUY_TYPE],
                                           self.get_user_interest_amount[BUY_TYPE],
                                           self.get_margin_balance[BUY_TYPE],
                                           self.get_user_loan_amount[SELL_TYPE],
                                           self.get_user_interest_amount[SELL_TYPE],
                                           self.get_margin_balance[SELL_TYPE],
                                           self.calculate_burst_rate())

    @classmethod
    def _calculate_burst_price(cls, buy_loan_amount, buy_interest_amount, buy_balance,
                               sell_loan_amount, sell_interest_amount, sell_balance, burst_rate):
        x = buy_loan_amount * burst_rate + buy_interest_amount - buy_balance
        y = sell_balance - sell_interest_amount - sell_loan_amount * burst_rate
        return Decimal(x / y)

    def calculate_burst_rate(self):
        """
        强平风险率: 根据市场配置获取风险率
        :return:
        """
        amount = self.calculate_unflat_and_interest_amount()[SELL_TYPE] + \
                 self.calculate_unflat_and_interest_amount()[BUY_TYPE] / self.get_index_price()
        return self._calculate_burst_rate(self.account_id, amount)

    @classmethod
    def _calculate_burst_rate(cls, account_id, amount):
        margin_cache = MarginAccountIdCache(account_id).dict
        details = cls.get_liquidation_rate(account_id)
        if amount >= details[0].liquidation_amount:
            return margin_cache["max_liquidation_rate"]
        for index, detail in enumerate(details):
            if amount > detail.liquidation_amount:
                return details[index - 1].liquidation_rate
        return details[-1].liquidation_rate

    @classmethod
    def get_liquidation_rate(cls, account_id: int, reverse: bool = True):
        details = MarginLiquidationRate.query.filter(
            MarginLiquidationRate.account_id == account_id
        ).all()
        return sorted(details, key=lambda d: d.liquidation_amount, reverse=reverse)


class MarginInsuranceOperation(object):

    @classmethod
    def interest_and_fund_record(cls, order: MarginLoanOrder, amount: Decimal, fund_rate: Decimal):
        """
        不提交,函数外进行提交
        """
        record = MarginInsurance.query.filter(
            MarginInsurance.asset == order.asset).first()
        change_amount = quantize_amount(fund_rate * amount, PrecisionEnum.COIN_PLACES)
        MarginInsurance.query.filter(
            MarginInsurance.id == record.id
        ).update(
            {
                MarginInsurance.amount: MarginInsurance.amount + change_amount,
                MarginInsurance.real_amount: MarginInsurance.real_amount + change_amount
            }
        )
        balance, real_balance = record.amount, record.real_amount
        insurance_record = MarginInsuranceHistory(
            amount=change_amount,
            balance=balance,
            asset=order.asset,
            history_type=MarginInsuranceHistory.HistoryType.INTEREST)
        real_insurance_record = MarginRealInsuranceHistory(
            amount=change_amount,
            balance=real_balance,
            asset=order.asset,
            history_type=MarginRealInsuranceHistory.HistoryType.INTEREST
        )
        history = MarginReceivableInterestHistory(
            amount=amount,
            market_name=order.market_name,
            asset=order.asset,
            user_id=order.user_id,
            fund_percent=fund_rate,
            margin_loan_order_id=order.id
        )
        db.session.add(insurance_record)
        db.session.add(real_insurance_record)
        db.session.add(history)
        db.session.flush()

    @classmethod
    def liquidation_and_fund_record(cls,  asset: str, amount: Decimal):
        """
        函数不进行提交
        """
        insurance_record = MarginInsurance.query.filter(
            MarginInsurance.asset == asset).first()
        MarginInsurance.query.filter(
            MarginInsurance.id == insurance_record.id
        ).update(
            {
                MarginInsurance.amount: MarginInsurance.amount - amount,
                MarginInsurance.real_amount: MarginInsurance.real_amount - amount
            }
        )
        db.session.add(MarginInsuranceHistory(
            amount=-amount,
            asset=asset,
            balance=insurance_record.amount,
            history_type=MarginInsuranceHistory.HistoryType.LIQUIDATION
        ))
        db.session.add(MarginRealInsuranceHistory(
            amount=-amount,
            asset=asset,
            balance=insurance_record.real_amount,
            history_type=MarginRealInsuranceHistory.HistoryType.LIQUIDATION
        ))

    @classmethod
    def liquidation_fee_and_fund_record(cls, asset: str, fee_amount: Decimal):
        """
        函数不进行提交
        """
        insurance_record = MarginInsurance.query.filter(
            MarginInsurance.asset == asset).first()
        MarginInsurance.query.filter(
            MarginInsurance.id == insurance_record.id
        ).update(
            {
                MarginInsurance.amount: MarginInsurance.amount + fee_amount,
                MarginInsurance.real_amount: MarginInsurance.real_amount + fee_amount
            }
        )
        detail = MarginInsuranceHistory(
            amount=fee_amount,
            balance=insurance_record.amount,
            asset=asset,
            history_type=MarginInsuranceHistory.HistoryType.LIQUIDATION_INCOME)
        real_detail = MarginRealInsuranceHistory(
            amount=fee_amount,
            balance=insurance_record.real_amount,
            asset=insurance_record.asset,
            history_type=MarginRealInsuranceHistory.HistoryType.LIQUIDATION_INCOME)
        db.session.add(detail)
        db.session.add(real_detail)


class MarginInterestOperation(object):

    def __init__(self, user_id: int, loan_order: MarginLoanOrder, amount: Decimal):
        """
        :param user_id: 用户id
        :param loan_order: 借币订单对象
        :param amount: 利息归还总额 Decimal
        用于还币时候插入利息归还记录
        """
        self.user_id = user_id
        self.loan_order = loan_order
        self.amount = amount

    def record(self):
        self._insert_interest_record()

    def _insert_interest_record(self):
        """
        插入利息归还记录
        :return:
        """
        record = MarginInterestHistory(amount=self.amount,
                                       market_name=self.loan_order.market_name,
                                       asset=self.loan_order.asset,
                                       user_id=self.user_id,
                                       margin_loan_order_id=self.loan_order.id)
        db.session.add(record)
        db.session.flush()


class MarginHelper(object):

    @classmethod
    def get_market_type_by_coin(cls, coin, asset_type='', status=''):
        query = Market.query
        if asset_type == 'buy_asset_type':
            query = query.filter(Market.base_asset == coin)
        elif asset_type == 'sell_asset_type':
            query = query.filter(Market.quote_asset == coin)
        else:
            query = query.filter(or_(Market.base_asset == coin,
                                     Market.quote_asset == coin))
        # 杠杆市场也属于现货市场
        # trade_type这个字段已经废弃了
        market_list = query.filter().all()
        market_type_list = [market.name for market in market_list]
        if status:
            status_market_type_list = cls.market_type_list(status)
            return [market_type for market_type in market_type_list
                    if market_type in status_market_type_list]
        return market_type_list

    @classmethod
    def get_coin_by_market_type(cls, market_type, status=''):
        market = Market.query.filter(Market.name == market_type).first()
        coin_list = list()
        coin_list.append(market.base_asset)
        coin_list.append(market.quote_asset)
        if status:
            status_coin_list = cls.coin_list(status)
            return [coin for coin in coin_list if coin in status_coin_list]
        return coin_list

    @staticmethod
    def account_id_list(status=''):
        query = MarginAccount.query
        if status:
            query = query.filter(MarginAccount.status == status)
        account_list = query.all()
        account_id_list = [account.account_id for account in account_list]
        return account_id_list

    @staticmethod
    def coin_list(status=''):
        query = MarginAssetRule.query
        if query:
            query = query.filter(MarginAssetRule.status == status)
        config_list = query.all()
        coin_list = [config.asset for config in config_list]
        return coin_list

    @staticmethod
    def market_type_list(status=''):
        query = MarginAccount.query
        if status:
            query = query.filter(MarginAccount.status == status)
        account_list = query.all()
        market_type_list = [account.name for account in account_list]
        return market_type_list

    @staticmethod
    def get_market_type_by_account_id(account_id):
        return str(MarginAccountIdCache(account_id).dict['name'])

    @staticmethod
    def get_account_id_by_market_type(market_type):
        return int(MarginAccountNameCache(market_type).dict['id'])

    @staticmethod
    def group_margin_loan_order() -> Dict[Tuple[int, int, str], Decimal]:
        margin_loan = MarginLoanOrder.query.filter(
            MarginLoanOrder.status.in_([
                MarginLoanOrder.StatusType.ARREARS,
                MarginLoanOrder.StatusType.BURST,
                MarginLoanOrder.StatusType.PASS,
            ])
        ).with_entities(
            MarginLoanOrder.account_id,
            MarginLoanOrder.user_id,
            MarginLoanOrder.asset,
            func.sum(
                MarginLoanOrder.unflat_amount + MarginLoanOrder.interest_amount
                     ).label('unflat_amount')
        ).group_by(
            MarginLoanOrder.account_id,
            MarginLoanOrder.user_id,
            MarginLoanOrder.asset
        )

        result = {(account_id, user_id, asset): amount for account_id, user_id, asset, amount in margin_loan}
        return result

    @staticmethod
    def group_margin_loan_order_with_interest() -> Dict[Tuple[int, int, str], Dict]:
        margin_loan = MarginLoanOrder.query.filter(
            MarginLoanOrder.status.in_([
                MarginLoanOrder.StatusType.ARREARS,
                MarginLoanOrder.StatusType.BURST,
                MarginLoanOrder.StatusType.PASS,
            ])
        ).with_entities(
            MarginLoanOrder.account_id,
            MarginLoanOrder.user_id,
            MarginLoanOrder.asset,
            func.sum(
                MarginLoanOrder.interest_amount
            ).label("interest_amount"),
            func.sum(
                MarginLoanOrder.unflat_amount + MarginLoanOrder.interest_amount
                     ).label('total_amount')
        ).group_by(
            MarginLoanOrder.account_id,
            MarginLoanOrder.user_id,
            MarginLoanOrder.asset
        )
        result = {(account_id, user_id, asset): dict(amount=total_amount,
                                                     interest_amount=interest_amount)
                  for account_id, user_id, asset, interest_amount, total_amount in margin_loan}
        return result


def get_user_margin_balances(user_id: int, market: str | None = None):
    helper = MarginUserAccountInfo(user_id)
    status_result = helper.get_all_except_status()
    account_result = helper.get_all_account_data()
    balance_result = helper.get_user_margin_balance_data()
    index_prices = IndexPriceCache().get_all_price()
    record_list = []

    class UserMarketDetail(NamedTuple):
        index_price: Decimal
        buy_balance: Decimal
        sell_balance: Decimal
        buy_loan: Decimal
        sell_loan: Decimal
        buy_interest: Decimal
        sell_interest: Decimal

        def get_warn_rate(self):
            _x = (self.buy_balance - self.buy_interest) / self.index_price + \
                self.sell_balance - self.sell_interest
            _y = self.buy_loan / self.index_price + self.sell_loan
            return quantize_amount(Decimal(_x / _y), PrecisionEnum.RATE_PLACES)

        def get_all_to_flat_amount(self):
            return (self.buy_loan + self.buy_interest) / self.index_price \
                                 + self.sell_loan + self.sell_interest

        def get_burst_price(self, _burst_rate: Decimal):
            _x1 = self.buy_loan * _burst_rate + \
                   self.buy_interest - self.buy_balance
            _y1 = self.sell_balance - self.sell_interest - self.sell_loan * burst_rate
            return quantize_amount(Decimal(_x1 / _y1), PrecisionEnum.COIN_PLACES) if _y1 != Decimal() else Decimal()

    for account_id, data in helper.all_account_info.items():
        _market = data['market_type']
        if market and _market != market:
            # 指定市场
            continue
        if _market in index_prices:
            index_price = index_prices[_market]
        else:
            index_price = IndexPriceCache().get_price(_market)

        market_data = copy.copy(data)
        market_data['balance_data'] = balance_result[account_id]
        market_data['status'] = status_result[account_id]
        market_data["loan"] = account_result[account_id]["loan"]
        market_data["interest"] = account_result[account_id]["interest"]
        has_loan = sum(market_data["loan"].values())
        if has_loan > 0:
            user_market_detail = UserMarketDetail(
                index_price=index_price,
                buy_balance=sum(balance_result[account_id][BUY_TYPE].values()),
                sell_balance=sum(balance_result[account_id][SELL_TYPE].values()),
                buy_loan=account_result[account_id]["loan"][BUY_TYPE],
                sell_loan=account_result[account_id]["loan"][SELL_TYPE],
                buy_interest=account_result[account_id]["interest"][BUY_TYPE],
                sell_interest=account_result[account_id]["interest"][SELL_TYPE],
            )
            market_data['rate'] = user_market_detail.get_warn_rate()
            all_to_flat_amount = user_market_detail.get_all_to_flat_amount()
            burst_rate = helper.get_liquidation_rate(account_id, all_to_flat_amount)
            burst_price = user_market_detail.get_burst_price(burst_rate)
            if burst_price > 0:
                market_data['burst_price'] = burst_price
            else:
                market_data['burst_price'] = ''
        else:
            market_data['rate'] = ''
            market_data['burst_price'] = ''
        record_list.append(market_data)
    record_list.sort(key=lambda _x: _x['account_id'], reverse=False)
    return record_list


class MarginAssetStatistic:
    """ 杠杆资产统计 """

    @classmethod
    def save_real_time_total_balance(cls):
        slice_time = cls._get_slice_time()
        sum_balance = cls._format_statistic_data(slice_time)
        real_time_cache = RealTimeMarginAssetStatisticCache()
        real_time_cache.hmset({
            'timestamp': slice_time,
            'data': json.dumps(sum_balance)
        })

        market_statistic_data, market_real_leverage_map = cls._format_market_data_from_asset_data(slice_time, sum_balance)
        # 杠杆市场纬度-统计数据 缓存
        market_statistic_cache = RealTimeMarginMarketStatisticCache()
        market_statistic_cache.hmset(
            {
                "timestamp": slice_time,
                "data": json.dumps(market_statistic_data, cls=JsonEncoder),
            }
        )
        # 杠杆市场-实际杠杆倍数 缓存
        market_real_leverage_cache = RealTimeMarginMarketRealLeverageCache(timestamp_to_date(float(slice_time)))
        market_real_leverage_cache.save_hourly_real_leverages(slice_time, market_real_leverage_map)
        return sum_balance

    @classmethod
    def _format_market_data_from_asset_data(cls, slice_time: int, asset_statistic_data: dict) -> Tuple[dict, dict]:
        """ 从币种纬度的统计数据 聚合 市场纬度的统计数据 """
        from app.assets import list_all_assets

        market_statistic_map = {}   # 市场统计信息
        market_real_leverage_map = {}  # 市场实际杠杆倍数

        all_usd_balance = Decimal(asset_statistic_data["usd_balance"])
        all_usd_unpay_amount = Decimal(asset_statistic_data["usd_unpay_amount"])
        all_real_leverage = quantize_amount(all_usd_balance / (all_usd_balance - all_usd_unpay_amount), 2)
        market_statistic_map["__ALL__"] = {
            "usd_balance": all_usd_balance,
            "usd_unpay_amount": all_usd_unpay_amount,
            "real_leverage": all_real_leverage,
        }
        market_real_leverage_map["__ALL__"] = all_real_leverage

        market_stat_info_map = defaultdict(
            lambda: {
                "count": 0,  # 持币人数
                "unpay_count": 0,  # 待还人数
                "usd_balance": Decimal(),  # 总市值USD
                "usd_unpay_amount": Decimal(),  # 待还市值USD
                "real_leverage": Decimal(),  # 实际杠杆倍数：总市值 /（总市值-待还市值）
            }
        )
        asset_list = list_all_assets()
        for asset, asset_data in asset_statistic_data.items():
            if asset not in asset_list:
                continue
            for asset_market_data in asset_data.get("market_data", []):
                # 累加 币种下的每个市场 的数据
                market = asset_market_data["market_name"]
                stat_info = market_stat_info_map[market]
                stat_info["usd_balance"] += Decimal(asset_market_data["usd_balance"])
                stat_info["usd_unpay_amount"] += Decimal(asset_market_data["usd_unpay_amount"])

        # 计算实际杠杆倍数, 设置持币人数、待还人数
        market_unpay_user_count_map = cls._get_market_unpay_user_count()
        market_has_asset_user_count_map = cls._get_market_has_asset_user_count(slice_time)
        for market, stat_info in market_stat_info_map.items():
            diff_usd = stat_info["usd_balance"] - stat_info["usd_unpay_amount"]  # 总市值 - 待还市值
            if diff_usd != Decimal():
                real_leverage = quantize_amount(stat_info["usd_balance"] / diff_usd, 2)
            else:
                real_leverage = Decimal()
            stat_info["real_leverage"] = real_leverage
            stat_info["count"] = market_has_asset_user_count_map.get(market, 0)
            stat_info["unpay_count"] = market_unpay_user_count_map.get(market, 0)
            market_real_leverage_map[market] = real_leverage

        market_statistic_map.update(market_stat_info_map)
        return market_statistic_map, market_real_leverage_map

    @classmethod
    def get_real_time_market_statistic_data(cls):
        """ 获取市场纬度的统计数据 """
        value = RealTimeMarginMarketStatisticCache().value
        if not value:
            return 0, {}
        return value["timestamp"], json.loads(value["data"], parse_float=Decimal)

    @classmethod
    def _get_slice_time(cls):
        slice_time = TradeLogDB.get_slice_history_timestamp()
        return slice_time

    @classmethod
    def get_real_time_total_balance(cls):
        value = RealTimeMarginAssetStatisticCache().value
        if not value:
            return 0, {}

        return value['timestamp'], json.loads(
            value['data'], parse_float=Decimal)

    @classmethod
    def _get_sum_total_balance(cls, slice_time):
        user_sql = "select sum(balance) balance, count(distinct user_id) count_user, asset, account " \
                   "from slice_balance_{slice_time} " \
                   f"where account > 0 and account < {InvestmentAccount.ACCOUNT_ID}" \
                   " group by asset, account;".format(slice_time=slice_time)
        cursor = TradeLogDB.cursor()
        cursor.execute(user_sql)

        result = defaultdict(dict)
        for item in cursor.fetchall():
            balance, count_user, asset, market_id = item
            result[asset].update({market_id: {
                    "balance": balance,
                    "having_users": count_user
                }})
        return result

    @classmethod
    def _format_statistic_data(cls, slice_time):
        # 注意：如果改了此方法返回值的结构，_format_market_data_from_asset_data 可能也需要修改
        markets = MarginAccount.query.all()
        market_id_name_mapping = {i.id: i.name for i in markets}
        market_name_id_mapping = {i.name: i.id for i in markets}
        asset_balance_map = cls._get_sum_total_balance(slice_time)
        asset_unpay_map = cls._get_sum_unpay_asset()
        data = {}
        all_usd_balance = Decimal()
        all_usd_unpay_amount = Decimal()
        for asset, balance_market_map in asset_balance_map.items():
            rate = PriceManager.asset_to_usd(asset)
            market_unpay_map = asset_unpay_map.get(asset, {})
            asset_market_data = []
            asset_balance = Decimal()
            asset_usd_balance = Decimal()
            asset_unpay_amount = Decimal()
            asset_usd_unpay_amount = Decimal()
            asset_having_users = 0
            asset_unpay_count = 0
            unpay_market_ids = {market_name_id_mapping[name] for name in market_unpay_map.keys()}
            for market_id in unpay_market_ids | set(balance_market_map.keys()):
                balance_data = balance_market_map.get(market_id, {})
                market_name = market_id_name_mapping[market_id]
                unpay_info = market_unpay_map.get(market_name, {})
                balance = balance_data.get("balance", Decimal())
                having_users = balance_data.get("having_users", 0)
                unpay_count = unpay_info.get("user_count", 0)
                unpay_amount = unpay_info.get("unflat_amount", Decimal())
                usd_balance = rate * balance
                usd_unpay_amount = rate * unpay_amount

                asset_balance += balance
                asset_usd_balance += usd_balance
                asset_unpay_amount += unpay_amount
                asset_having_users += having_users
                asset_unpay_count += unpay_count
                asset_usd_unpay_amount += usd_unpay_amount

                asset_market_data.append({
                    "market_name": market_name,
                    "count": having_users,
                    "balance": amount_to_str(balance, 8),
                    "unpay_count": unpay_count,
                    "unpay_amount": amount_to_str(unpay_amount, 8),
                    "usd_balance": amount_to_str(usd_balance, 2),
                    "usd_unpay_amount": amount_to_str(usd_unpay_amount, 2),
                })

            all_usd_balance += asset_usd_balance
            all_usd_unpay_amount += asset_usd_unpay_amount

            data[asset] = {
                "asset": asset,
                "count": asset_having_users,
                "balance": amount_to_str(asset_balance, 8),
                "unpay_count": asset_unpay_count,
                "unpay_amount": amount_to_str(asset_unpay_amount, 8),
                "usd_balance": amount_to_str(asset_usd_balance, 2),
                "usd_unpay_amount": amount_to_str(asset_usd_unpay_amount, 2),
                "market_data": asset_market_data
            }

        data["usd_balance"] = amount_to_str(all_usd_balance, 2)
        data["usd_unpay_amount"] = amount_to_str(all_usd_unpay_amount, 2)
        return data

    @classmethod
    def _get_sum_unpay_asset(cls):
        # 待还资产(借币+利息)
        records = MarginLoanOrder.query.with_entities(
            MarginLoanOrder.asset,
            MarginLoanOrder.market_name,
            func.sum(MarginLoanOrder.interest_amount).label("interest_amount"),
            func.sum(MarginLoanOrder.unflat_amount).label("unflat_amount"),
            func.count(MarginLoanOrder.user_id.distinct()).label("user_count")
        ).filter(
            MarginLoanOrder.status.in_(
                [MarginLoanOrder.StatusType.PASS,
                 MarginLoanOrder.StatusType.ARREARS]),
        ).group_by(MarginLoanOrder.asset, MarginLoanOrder.market_name).all()
        result = defaultdict(dict)
        for item in records:
            interest_amount = item.interest_amount or 0
            unflat_amount = item.unflat_amount or 0
            result[item.asset].update({item.market_name: {
                    'unflat_amount': interest_amount + unflat_amount,
                    'user_count': item.user_count or 0
            }})
        return result

    @classmethod
    def _get_market_has_asset_user_count(cls, slice_time: int) -> Dict[str, int]:
        """ 市场纬度-持币人数 """
        user_sql = (
            "select account, count(distinct user_id) count_user "
            "from slice_balance_{slice_time} "
            f"where account > 0 and account < {InvestmentAccount.ACCOUNT_ID}"
            " group by account;".format(slice_time=slice_time)
        )
        cursor = TradeLogDB.cursor()
        cursor.execute(user_sql)

        result = {}
        markets = MarginAccount.query.all()
        market_id_name_mapping = {i.id: i.name for i in markets}
        for item in cursor.fetchall():
            market_id, count_user = item
            market_name = market_id_name_mapping.get(market_id)
            result[market_name] = count_user
        return result

    @classmethod
    def _get_market_unpay_user_count(cls) -> Dict[str, int]:
        """ 市场纬度-待还人数 """
        # 对于某个市场，有些人2个币可能都有，不能直接用币种的统计，要去重
        records = (
            MarginLoanOrder.query.with_entities(
                MarginLoanOrder.market_name,
                func.count(MarginLoanOrder.user_id.distinct()).label("user_count"),
            )
            .filter(
                MarginLoanOrder.status.in_(
                    [
                        MarginLoanOrder.StatusType.PASS,
                        MarginLoanOrder.StatusType.ARREARS,
                    ]
                ),
            )
            .group_by(MarginLoanOrder.market_name)
            .all()
        )

        result = {}
        for item in records:
            result[item.market_name] = item.user_count or 0
        return result


def report_margin_transfer_biz_event(from_account):
    if from_account == SPOT_ACCOUNT_ID:
        event = BalanceEvent.SPOT_TO_MARGIN
    else:
        event = BalanceEvent.MARGIN_TO_SPOT
    biz_monitor.increase_counter(event)
