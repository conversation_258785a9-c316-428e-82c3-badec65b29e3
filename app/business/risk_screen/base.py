# -*- coding: utf-8 -*-

import datetime
import json
from typing import Optional, Type
from uuid import uuid4

from app.models import (
    db,
    User,
    KycVerification,
    UserRiskScreen,
    UserRiskScreenRequest,
    UserRiskScreenCase,
)
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog
from app.models.mongo.op_log import OPNamespaceObjectRisk


class BaseScreenClient:
    name = ""

    def __init__(self, screen_request: UserRiskScreenRequest):
        self.screen_request = screen_request

    def process_screen_request(self):
        raise NotImplementedError

    def sync_invalid_profiles(self, case: UserRiskScreenCase) -> int:
        raise NotImplementedError


def get_screen_client(user_id: int) -> Optional[Type[BaseScreenClient]]:
    from app.business.risk_screen.dowjones import DowJonesScreenClient

    return DowJonesScreenClient


class RiskScreenBusiness:
    def __init__(self, user_id: int):
        self.user_id = user_id

    @classmethod
    def update_user_risk_status(cls, user_id: int, new_status: UserRiskScreen.Status,
                                op_user_id: int = None):
        # 更新用户风险状态
        from app.business.email import send_kyc_result_email, send_risk_screen_failed_email
        from app.caches.operation import RiskScreenOldKycUserCache

        assert new_status in [UserRiskScreen.Status.PASSED, UserRiskScreen.Status.RISKED]
        user_risk_status: UserRiskScreen = UserRiskScreen.get_or_create(user_id=user_id)
        user_risk_status.status = new_status
        db.session.add(user_risk_status)
        # 更新kyc记录状态, 不管筛查是否通过，kyc状态都认为是通过的
        kyc_row: KycVerification = (
            KycVerification.query.filter(
                KycVerification.user_id == user_id,
                KycVerification.status.in_(
                    [
                        KycVerification.Status.SCREENING,
                        KycVerification.Status.PASSED,
                    ]
                ),
            )
            .order_by(KycVerification.id.desc())
            .first()
        )
        if kyc_row:
            old_data = kyc_row.to_dict(enum_to_name=True)
            user = User.query.get(user_id)
            if new_status == UserRiskScreen.Status.PASSED:
                user.kyc_status = User.KYCStatus.PASSED
                kyc_row.status = KycVerification.Status.PASSED
            else:
                user.kyc_status = User.KYCStatus.FAILED
                kyc_row.rejection_reason = KycVerification.RejectionReason.REGULATORY_ENFORCEMENT.name
                kyc_row.status = KycVerification.Status.REJECTED

            if op_user_id:
                AdminOperationLog.new_edit(
                    user_id=op_user_id,
                    ns_obj=OPNamespaceObjectRisk.Kyc,
                    old_data=old_data,
                    new_data=kyc_row.to_dict(enum_to_name=True),
                    target_user_id=kyc_row.user_id,
                )
        db.session.commit()

        if new_status == UserRiskScreen.Status.PASSED:
            if kyc_row and user_id not in RiskScreenOldKycUserCache().user_ids:
                send_kyc_result_email.delay(kyc_row.id)
        else:
            send_risk_screen_failed_email.delay(user_id)

    @classmethod
    def new_uuid(cls) -> str:
        return uuid4().hex.upper()

    @classmethod
    def new_user_risk_screen_status(cls, user_id):
        r = UserRiskScreen.get_or_create(user_id=user_id)
        db.session.add(r)

    @classmethod
    def new_individual_screen_request(
        cls,
        user_id: int,
        name: str,
        country: str,
        gender: str,
        date_of_birth: datetime.date,
        type_: UserRiskScreenRequest.Type,
    ) -> UserRiskScreenRequest:
        gender_str = date_of_birth_str = None
        if str(gender).upper() in ["MALE", "FEMALE"]:
            gender_str = str(gender)
        if date_of_birth:
            # noinspection PyBroadException
            try:
                date_of_birth_str = date_of_birth.strftime("%Y-%m-%d")
            except Exception:
                pass
        info = {
            "name": name,
            "country": country,
            "gender": gender_str,
            "date_of_birth": date_of_birth_str,
        }
        RiskScreenBusiness.cancel_old_screen_request(user_id)
        req = UserRiskScreenRequest(
            user_id=user_id,
            type=type_,
            status=UserRiskScreenRequest.Status.CREATED,
            info=json.dumps(info),
        )
        return req

    @classmethod
    def new_screen_request_from_kyc(
        cls,
        kyc_row,
        source: UserRiskScreenRequest.Source = UserRiskScreenRequest.Source.KYC_USER,
        session_add: bool = True,
    ) -> Optional[UserRiskScreenRequest]:
        req = None
        if isinstance(kyc_row, KycVerification):
            req = cls.new_individual_screen_request(
                user_id=kyc_row.user_id,
                name=kyc_row.full_name,
                country=kyc_row.country,
                gender=getattr(kyc_row, "gender", None),
                date_of_birth=getattr(kyc_row, "date_of_birth", None),
                type_=UserRiskScreenRequest.Type.INDIVIDUAL,
            )
        # company, director

        if req:
            req.source = source
            if session_add:
                db.session.add(req)
            return req

    @classmethod
    def process_screen_request(cls, request_id: int):
        from app.business import CacheLock, LockKeys

        with CacheLock(LockKeys.screen_request(request_id), wait=False):
            db.session.rollback()
            r: UserRiskScreenRequest = UserRiskScreenRequest.query.get(request_id)

            client_class = get_screen_client(user_id=r.user_id)
            if r.status == UserRiskScreenRequest.Status.CREATED:
                assert not r.third_party
                r.third_party = client_class.name
                r.track_no = cls.new_uuid()
                r.status = UserRiskScreenRequest.Status.PROCESSING
                db.session.commit()

            if r.status == UserRiskScreenRequest.Status.PROCESSING:
                client_class(r).process_screen_request()
                r.status = UserRiskScreenRequest.Status.FINISHED
                db.session.commit()

    @classmethod
    def cancel_screen_request(cls,  user_id: int, is_commit=False) -> None:
        UserRiskScreenRequest.query.filter(
            UserRiskScreenRequest.user_id == user_id
        ).update(dict(status=UserRiskScreenRequest.Status.CANCELLED), synchronize_session=False)

        UserRiskScreenCase.query.filter(
            UserRiskScreenCase.user_id == user_id
        ).update(dict(status=UserRiskScreenCase.Status.CANCELLED), synchronize_session=False)

        UserRiskScreen.query.filter(
            UserRiskScreen.user_id == user_id
        ).update(dict(status=UserRiskScreen.Status.CANCELLED), synchronize_session=False)

        if is_commit:
            db.session.commit()

    @classmethod
    def cancel_old_screen_request(cls, user_id: int, is_commit=False) -> None:
        UserRiskScreenRequest.query.filter(
            UserRiskScreenRequest.user_id == user_id
        ).update(dict(status=UserRiskScreenRequest.Status.CANCELLED), synchronize_session=False)

        UserRiskScreenCase.query.filter(
            UserRiskScreenCase.user_id == user_id
        ).update(dict(status=UserRiskScreenCase.Status.CANCELLED), synchronize_session=False)

        r = UserRiskScreen.get_or_create(user_id=user_id)
        r.status = UserRiskScreen.Status.CREATED
        db.session.add(r)

        if is_commit:
            db.session.commit()

    @classmethod
    def get_user_risk_screen_status(cls, user_id: int) -> Optional[UserRiskScreen.Status]:
        record = UserRiskScreen.query.filter(UserRiskScreen.user_id == user_id).first()
        if record:
            return record.status

    @classmethod
    def get_case_screen_client(cls, case: UserRiskScreenCase):
        from app.business.risk_screen.refinitiv import RefinitivScreenClient
        from app.business.risk_screen.dowjones import DowJonesScreenClient

        if case.third_party == UserRiskScreenRequest.ThirdParty.Refinitiv:
            return RefinitivScreenClient(None)
        else:
            return DowJonesScreenClient(None)

    @classmethod
    def sync_invalid_profiles(cls, case: UserRiskScreenCase) -> int:
        client = cls.get_case_screen_client(case)
        num = client.sync_invalid_profiles(case)
        return num
