# -*- coding: utf-8 -*-
import json
import time
from collections import defaultdict
from datetime import timedelta
from decimal import Decimal

from celery.schedules import crontab
from flask import current_app
from sqlalchemy import or_, and_, func

from app.common import CeleryQueues
from app.caches.operation import RiskScreenOldKycUserCache
from app.business import lock_call, CacheLock, LockKeys, PriceManager
from app.business.kyc import KycBusiness, KycProBusiness, LivenessCheckBusiness
from app.business.email import send_kyc_result_email
from app.business.risk_screen import RiskScreenBusiness
from app.models import KycVerificationHistory, User, db, KycVerification, \
    KycVerificationStatistics, UserRiskScreenRequest, Deposit, KycVerificationPro, \
    KycVerificationProHistory, LivenessCheckHistory
from app.utils import scheduled, batch_iter, now, today, route_module_to_celery_queue, celery_task
from app import config

route_module_to_celery_queue(__name__, CeleryQueues.REAL_TIME)


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_kyc_status_schedule():
    """
    更新第三方KYC审核状态
    """
    items = KycVerificationHistory.query.filter(
        KycVerificationHistory.status == KycVerificationHistory.Status.CREATED
    ).with_entities(
        KycVerificationHistory.id,
        KycVerificationHistory.user_id,
    ).all()

    user_ids = {item.user_id for item in items}
    users = []
    for ids_ in batch_iter(user_ids, 1000):
        records = User.query.filter(User.id.in_(ids_)).all()
        users.extend(records)
    user_map = {item.id: item for item in users}
    for item in items:
        user = user_map[item.user_id]
        with CacheLock(LockKeys.kyc_operation(user.id), wait=False):
            db.session.rollback()
            history: KycVerificationHistory = KycVerificationHistory.query.get(item.id)
            if history.status != KycVerificationHistory.Status.CREATED:
                continue
            
            presets = json.loads(history.detail)
            kyc = None
            # 大于24小时的需要人工确认
            if history.created_at + timedelta(seconds=KycBusiness.PROCESS_EXPIRE_TIME) < now():
                if history.doc_type == KycVerification.DocType.DOC or (
                    history.doc_type == KycVerification.DocType.NON_DOC and user.kyc_status == User.KYCStatus.PROCESSING
                ):
                    kyc = KycVerification.add(
                        history,
                        KycVerification.Status.AUDIT_REQUIRED,
                        KycVerification.RejectionReason.SERVICE_TIME_OUT.name,
                    )
                history.status = KycVerificationHistory.Status.FAILED
                history.rejection_reason = KycVerification.RejectionReason.SERVICE_TIME_OUT.name
                if kyc:
                    presets['kyc_id'] = kyc.id
                history.detail = json.dumps(presets)
                history.reject_type = KycVerificationHistory.RejectType.FINAL
                db.session.commit()
                continue
            # 对于提交资料失败的，进行重试
            if not history.transaction_id:
                KycBusiness.upload(history.id)
                continue

            if history.service_type == KycVerification.ServiceType.SUMSUB:
                raw_data = KycBusiness.get_result(history)
                if not raw_data:
                    continue
                if history.doc_type == KycVerification.DocType.NON_DOC:
                    source = KycBusiness.Source.SUMSUB_NON_DOC
                else:
                    source = KycBusiness.Source.SUMSUB
                kyc = KycBusiness.handle_result(user, history, raw_data, source=source)
            if kyc:
                history = KycVerificationHistory.query.get(history.id)
                presets = json.loads(history.detail)
                presets['kyc_id'] = kyc.id
                history.detail = json.dumps(presets)
                db.session.commit()
                if kyc.status == KycVerification.Status.REJECTED:
                    send_kyc_result_email.delay(kyc.id)


def _update_kyc_statistics(report_date):
    start_date, end_date = report_date, report_date + timedelta(days=1)

    kyc_records = KycVerification.query.filter(
        KycVerification.status.in_([
            KycVerification.Status.PASSED,
            KycVerification.Status.REJECTED,
        ]),
        or_(
            and_(
                KycVerification.audited_at >= start_date,
                KycVerification.audited_at < end_date
            ),
            and_(
                KycVerification.created_at >= start_date,
                KycVerification.created_at < end_date
            )
        )
    ).all()
    statistics = defaultdict(lambda: defaultdict(int))
    for item in kyc_records:
        item: KycVerification
        service_type = item.service_type
        if item.auditor_id:
            if item.status == KycVerification.Status.PASSED:
                statistics[service_type]['admin_passed_count'] += 1
                statistics['']['admin_passed_count'] += 1
            if service_type != KycVerification.ServiceType.MANUAL_AUDIT:
                statistics[service_type]['second_audited_count'] += 1
                statistics['']['second_audited_count'] += 1
            statistics[service_type]['admin_count'] += 1
            statistics['']['admin_count'] += 1
        else:
            if item.status == KycVerification.Status.PASSED:
                statistics[service_type]['third_party_passed_count'] += 1
                statistics['']['third_party_passed_count'] += 1
            statistics[service_type]['third_party_count'] += 1
            statistics['']['third_party_count'] += 1

    new_rows = []
    for service_type, data_ in statistics.items():
        service_type = service_type or None
        report = KycVerificationStatistics.get_or_create(
            report_date=start_date,
            service_type=service_type
        )
        for k, v in data_.items():
            setattr(report, k, v)
        new_rows.append(report)
    db.session.bulk_save_objects(new_rows)
    db.session.commit()


@scheduled(crontab(hour=0, minute='*/15'))
@lock_call()
def update_kyc_statistics_schedule():
    last_record = KycVerificationStatistics.query.order_by(
        KycVerificationStatistics.report_date.desc()
    ).first()
    today_ = today()
    yesterday = today_ - timedelta(days=1)
    if last_record and last_record.report_date == yesterday:
        return
    if last_record:
        report_date = last_record.report_date + timedelta(days=1)
    else:
        report_date = yesterday
    while report_date < today_:
        _update_kyc_statistics(report_date)
        report_date += timedelta(days=1)


@celery_task
@lock_call(with_args=True)
def process_risk_screen_request_task(request_id: int):
    # 处理风险筛查请求
    RiskScreenBusiness.process_screen_request(request_id)


@scheduled(crontab(hour="*/1", minute=15))
@lock_call()
def retry_process_risk_screen_request_schedule():
    # 风险筛查重试逻辑
    proc_requests = UserRiskScreenRequest.query.filter(
        UserRiskScreenRequest.status == UserRiskScreenRequest.Status.PROCESSING,
    ).with_entities(UserRiskScreenRequest.id).all()
    for r in proc_requests:
        process_risk_screen_request_task.delay(r.id)

    created_requests = UserRiskScreenRequest.query.filter(
        UserRiskScreenRequest.status == UserRiskScreenRequest.Status.CREATED,
        UserRiskScreenRequest.third_party.is_(None),
    ).with_entities(UserRiskScreenRequest.id).all()
    for r in created_requests:
        process_risk_screen_request_task.delay(r.id)


@scheduled(crontab(minute=10, hour="2"))
@lock_call()
def scan_large_deposit_user_to_risk_screen_schedule():
    """ 扫描充值大户进行风险筛查，已风险筛查的用户忽略 """

    # 过去30天的累计充值金额达到5万USD
    end_date = today()
    start_date = end_date - timedelta(days=30)
    min_deposit_usd = 50000

    deposit_rows = (
        Deposit.query.filter(
            Deposit.created_at >= start_date,
            Deposit.created_at <= end_date,
            Deposit.status.in_(
                [
                    Deposit.Status.FINISHED,
                    Deposit.Status.TO_HOT,
                ]
            ),
        )
        .with_entities(
            Deposit.user_id.label("user_id"),
            Deposit.asset.label("asset"),
            func.sum(Deposit.amount).label("amount"),
        )
        .group_by(Deposit.user_id, Deposit.asset)
        .all()
    )

    asset_price_map = PriceManager.assets_to_usd()
    deposit_usd_map = defaultdict(Decimal)
    for item in deposit_rows:
        deposit_usd_map[item.user_id] += item.amount * asset_price_map.get(item.asset, 0)
    deposit_user_ids = list(deposit_usd_map.keys())

    users = []
    for chunk_user_ids in batch_iter(deposit_user_ids, 1000):
        chunk_users = User.query.filter(User.id.in_(chunk_user_ids)).all()
        users.extend(chunk_users)
    user_map = {i.id: i for i in users}

    # 子账户充值 合并到 主账户
    main_user_deposit_usd_map = defaultdict(Decimal)
    for user_id in deposit_user_ids:
        if user_id in user_map:
            user = user_map[user_id]
            main_user_id = user.main_user_id if user.is_sub_account else user_id
            main_user_deposit_usd_map[main_user_id] += deposit_usd_map.get(user_id)

    large_deposit_user_ids = [user_id for user_id, usd in main_user_deposit_usd_map.items() if usd >= min_deposit_usd]
    new_screen_num = 0
    cache = RiskScreenOldKycUserCache()
    for chunk_user_ids in batch_iter(large_deposit_user_ids, 100):
        screen_reqs = (
            UserRiskScreenRequest.query.filter(UserRiskScreenRequest.user_id.in_(chunk_user_ids))
            .with_entities(UserRiskScreenRequest.user_id)
            .all()
        )
        screened_user_ids = {i.user_id for i in screen_reqs}

        # 已筛查的忽略， 有KYC的才筛查
        to_screen_user_ids = set(chunk_user_ids) - screened_user_ids
        kyc_rows = KycVerification.query.filter(
            KycVerification.user_id.in_(to_screen_user_ids),
            KycVerification.status == KycVerification.Status.PASSED,
        ).all()

        new_screen_reqs = []
        for kyc_row in kyc_rows:
            req = RiskScreenBusiness.new_screen_request_from_kyc(kyc_row, source=UserRiskScreenRequest.Source.LARGE_DEPOSIT_USER)
            db.session.add(req)
            new_screen_reqs.append(req)
            cache.add_user_id(kyc_row.user_id)
        db.session.commit()

        new_screen_num += len(new_screen_reqs)
        for idx, _req in enumerate(new_screen_reqs):
            process_risk_screen_request_task.delay(_req.id)
            if idx and idx % 5 == 0:
                time.sleep(0.1)  # 限频
    current_app.logger.warning(f"scan_large_deposit_user_to_risk_screen new_screen_num: {new_screen_num}")


@scheduled(crontab(minute='*/5'))
@lock_call()
def auto_flush_merchant_kyc_status_schedule():
    """
    自动刷新高级KYC状态
    """
    k_model = KycVerification

    MINUTES = 60
    # 获取最近一小时kyc取消的商家
    kyc_rows = k_model.query.filter(
        k_model.updated_at >= now() - timedelta(minutes=MINUTES),
        k_model.status.in_([
            k_model.Status.REJECTED,
            k_model.Status.CANCELLED
        ]),
    ).all()
    user_ids = {i.user_id for i in kyc_rows}

    pro_model = KycVerificationPro
    pro_user_ids = {
        k.user_id for k in pro_model.query.filter(
            pro_model.status.in_([pro_model.Status.PASSED, pro_model.Status.CREATED]),
            pro_model.user_id.in_(user_ids),
        ).with_entities(
            pro_model.user_id
        ).all()
    }
    need_reject_user_ids = [i.user_id for i in pro_user_ids]

    if need_reject_user_ids:
        pro_model.query.filter(
            pro_model.user_id.in_(need_reject_user_ids),
            pro_model.status.in_([pro_model.Status.PASSED, pro_model.Status.CREATED])
        ).update({
            pro_model.status: pro_model.Status.REJECTED,
            pro_model.rejection_reason: pro_model.RejectionReason.KYC_INVALID.name,
            pro_model.auditor_id: config['OFFICIAL_ADMIN_USER_ID'],
            pro_model.audited_at: now()
        }, synchronize_session=False)
        db.session.commit()



@scheduled(crontab(minute='*/5'))
@lock_call()
def update_kyc_pro_status_schedule():
    """
    更新第三方高级KYC审核状态
    """
    items = KycVerificationProHistory.query.filter(
        KycVerificationProHistory.status == KycVerificationProHistory.Status.CREATED
    ).with_entities(
        KycVerificationProHistory.id,
        KycVerificationProHistory.user_id,
    ).all()

    user_ids = {item.user_id for item in items}
    users = []
    for ids_ in batch_iter(user_ids, 1000):
        records = User.query.filter(User.id.in_(ids_)).all()
        users.extend(records)
    user_map = {item.id: item for item in users}

    for item in items:
        item: KycVerificationProHistory

        user = user_map[item.user_id]
        with CacheLock(LockKeys.kyc_pro_operation(user.id), wait=False):
            db.session.rollback()
            history = KycVerificationProHistory.query.get(item.id)
            if history.status != KycVerificationProHistory.Status.CREATED:
                continue
            kyc_pro_id = json.loads(history.detail)['kyc_pro_id']
            kyc_pro = KycVerificationPro.query.get(kyc_pro_id)
            if kyc_pro.status in {KycVerificationPro.Status.PASSED, KycVerificationPro.Status.REJECTED}:
                history.rejection_reason = kyc_pro.rejection_reason
                history.reject_type = KycVerificationProHistory.RejectType.FINAL
                history.status = KycVerificationProHistory.Status.FAILED
                db.session.commit()
                continue

            # 大于24小时的需要人工确认
            if history.created_at + timedelta(seconds=KycProBusiness.PROCESS_EXPIRE_TIME) < now():
                kyc_pro.status = KycVerificationPro.Status.AUDIT_REQUIRED
                kyc_pro.rejection_reason = KycVerificationPro.RejectionReason.SERVICE_TIME_OUT.name
                history.rejection_reason = KycVerificationPro.RejectionReason.SERVICE_TIME_OUT.name
                history.reject_type = KycVerificationProHistory.RejectType.FINAL
                history.status = KycVerificationProHistory.Status.FAILED
                db.session.commit()
                continue

            # 未在第三方服务创建记录
            if not history.transaction_id:
                KycProBusiness.upload(history.id)
                continue

            if history.service_type == KycVerificationPro.ServiceType.SUMSUB:
                raw_data = KycProBusiness.get_result(history)
                if not raw_data:
                    continue
                KycProBusiness.handle_result(user, history, raw_data)


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_liveness_status_schedule():
    """
    更新第三方生物识别认证状态
    """
    items = LivenessCheckHistory.query.filter(
        LivenessCheckHistory.status.in_([
            LivenessCheckHistory.Status.CREATED,
            LivenessCheckHistory.Status.AUDIT_REQUIRED
        ])
    ).with_entities(
        LivenessCheckHistory.id,
        LivenessCheckHistory.user_id
    ).all()
    for item in items:
        with CacheLock(LockKeys.kyc_liveness_operation(item.user_id), wait=False):
            db.session.rollback()
            history = LivenessCheckHistory.query.get(item.id)
            if history.status not in [LivenessCheckHistory.Status.CREATED, LivenessCheckHistory.Status.AUDIT_REQUIRED]:
                continue
            if history.status == LivenessCheckHistory.Status.CREATED \
                    and history.created_at + timedelta(seconds=LivenessCheckBusiness.VERIFICATION_EXPIRE_TIME) < now():
                history.status = LivenessCheckHistory.Status.CANCELLED
                db.session.commit()
                continue
            if history.status == LivenessCheckHistory.Status.AUDIT_REQUIRED \
                    and history.created_at + timedelta(seconds=LivenessCheckBusiness.PROCESS_EXPIRE_TIME) < now():
                history.status = LivenessCheckHistory.Status.REJECTED
                LivenessCheckBusiness.update_business(history)
                db.session.commit()
                continue
            if not history.transaction_id:
                continue
            if history.business != LivenessCheckHistory.Business.MANUAL and not history.business_id:
                continue
            LivenessCheckBusiness.update_check_result(history)

