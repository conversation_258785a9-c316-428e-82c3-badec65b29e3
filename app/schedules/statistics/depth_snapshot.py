# -*- coding: utf-8 -*-
from collections import defaultdict
import json
from decimal import Decimal
from functools import partial

from celery.schedules import crontab

from app.business.third_exchange import PERPETUAL_HANDLERS, SPOT_HANDLERS
from app.common import CeleryQueues
from app.common.constants import OrderSideType
from app.models.base import db
from app.models.market_liquidity import Exchange, ExchangeMarketPrices, MarketType
from app.utils import (
    route_module_to_celery_queue, scheduled, current_timestamp, celery_task
)

from app.caches import MarketCache, PerpetualMarketCache
from app.business.external_dbs import ExchangeLogDB, PerpetualLogDB, TradeLogDB
from app.business import BusinessSettings
from app.business import lock_call, PriceManager, ServerClient, PerpetualServerClient
from app.utils.parser import JsonEncoder

route_module_to_celery_queue(__name__, CeleryQueues.THIRD_EXCHANGE)


def get_spot_market_order_depth_data(market: str):
    cache = MarketCache(market).dict
    base_asset, quote_asset = cache["base_asset"], cache["quote_asset"]
    depths = cache["depths"]
    c = ServerClient()
    base_asset_rate = PriceManager.asset_to_usd(base_asset)
    quote_asset_rate = PriceManager.asset_to_usd(quote_asset)
    results = []
    default_depth = Decimal(cache["default_depth"])
    ts = current_timestamp(to_int=True)
    snapshot_ts = ts - ts % 60
    for depth in depths:
        if Decimal(depth) < default_depth:
            continue
        data = c.market_order_depth(market=market, limit=100, interval=str(depth))
        if not data:
            continue
        results.append([depth,
                        market,
                        base_asset_rate,
                        quote_asset_rate,
                        json.dumps(data),
                        snapshot_ts
                        ])
    return results


def get_perpetual_market_order_depth_data(market: str):
    cache = PerpetualMarketCache().read_aside()
    if market not in cache:
        return []
    market_info = cache[market]
    money, stock = market_info["money"], market_info["stock"]
    depths = market_info["merge"]
    default_depth = Decimal(Decimal(market_info["default_merge"]))
    c = PerpetualServerClient()
    money_rate = PriceManager.asset_to_usd(money) if money != "USD" else Decimal("1")
    stock_rate = PriceManager.asset_to_usd(stock) if stock != "USD" else Decimal("1")
    results = []
    ts = current_timestamp(to_int=True)
    snapshot_ts = ts - ts % 60
    for depth in depths:
        if Decimal(depth) < default_depth:
            continue
        data = c.market_order_depth(market=market, limit=50, interval=str(depth))
        if not data:
            continue
        results.append([depth,
                        market,
                        money_rate,
                        stock_rate,
                        json.dumps(data),
                        snapshot_ts
                        ])
    return results


@scheduled(crontab(hour='*/1', minute='5,9,35,39'))
@lock_call()
def spot_depth_snapshot_schedule():
    ts = current_timestamp(to_int=True)
    today_ts = ts - ts % 86400
    snapshot_ts = ts - ts % (30 * 60)
    table = ExchangeLogDB.spot_depth_snapshot_table(today_ts)
    table.create_if_not_exists()
    if has_record(table, snapshot_ts):
        return

    depth_results = get_spot_markets_depth_data(snapshot_ts)
    for record in depth_results:
        table.insert(*record)
    table.flush()


@scheduled(crontab(hour='*/1', minute='4,8,34,38'))
@lock_call()
def perpetual_depth_snapshot_schedule():
    ts = current_timestamp(to_int=True)
    today_ts = ts - ts % 86400
    snapshot_ts = ts - ts % (30 * 60)
    table = ExchangeLogDB.perpetual_depth_snapshot_table(today_ts)
    table.create_if_not_exists()
    if has_record(table, snapshot_ts):
        return
    depth_results = get_perpetual_markets_depth_data(snapshot_ts)

    for record in depth_results:
        table.insert(*record)
    table.flush()


def has_record(table, snapshot_ts):
    fields = ['id']
    where = f'snapshot_ts={snapshot_ts}'
    limit_ = 1
    recs = table.select(*fields, where=where, limit=limit_, order_by='id desc')
    return bool(recs)

def get_spot_markets_depth_data(snapshot_ts):
    records = bid_ask_data_from_slice_table(TradeLogDB, TradeLogDB.SLICE_INTERVAL)
    if not records:
        return []
    bid_ask_map = aggregate_records(records)
    results = []
    for market, bid_ask_data in bid_ask_map.items():
        cache = MarketCache(market).dict
        base_asset, quote_asset = cache["base_asset"], cache["quote_asset"]
        depths = cache["depths"]
        depth_lis = [Decimal(d) for d in depths]
        depth_lis.sort(reverse=False)
        depth = depth_lis[0]

        base_asset_rate = PriceManager.asset_to_usd(base_asset)
        quote_asset_rate = PriceManager.asset_to_usd(quote_asset)
        
        results.append([
            depth, 
            market, 
            base_asset_rate, 
            quote_asset_rate, 
            json.dumps(bid_ask_data, cls=JsonEncoder),
            snapshot_ts
        ])
    return results

def bid_ask_data_from_slice_table(db_, interval):
    ts = current_timestamp(to_int=True)
    table = db_.slice_order_table(ts, interval)
    if not table or not table.exists():
        ts -= interval
        table = db_.slice_order_table(ts, interval)
    if not table or not table.exists():
        return []
    res = []
    start_id = 0
    limit = 50000
    while True:
        fields = ['id', 'market', 'price', 'left', 'side']
        where = f'id > {start_id}'
        order_by = 'id asc'
        records = table.select(*fields, where=where, limit=limit, order_by=order_by)
        if not records:
            break
        start_id = records[-1][0]
        res.extend(records)
    return res
    

def aggregate_records(records):
    bids_map, asks_map = defaultdict(lambda: defaultdict(Decimal)), defaultdict(lambda: defaultdict(Decimal))
    for record in records:
        _, market, price, amount, side = record
        if side == OrderSideType.BUY:
            bids_map[market][price] += amount
        else:
            asks_map[market][price] += amount
    markets = set(bids_map.keys()) | set(asks_map.keys())
    res = dict()
    for market in markets:
        bids = bids_map[market]
        asks = asks_map[market]
        bids_sorted = sorted(bids.items(), key=lambda x: x[0], reverse=True)
        asks_sorted = sorted(asks.items(), key=lambda x: x[0], reverse=False)
        res[market] = {'asks': asks_sorted, 'bids': bids_sorted}
    return res


def get_perpetual_markets_depth_data(snapshot_ts):
    records = bid_ask_data_from_slice_table(PerpetualLogDB, PerpetualLogDB.SLICE_INTERVAL)
    if not records:
        return []
    bid_ask_map = aggregate_records(records)
    results = []
    cache = PerpetualMarketCache().read_aside()
    for market, bid_ask_data in bid_ask_map.items():
       if market not in cache:
           continue
       market_info = cache[market]
       money, stock = market_info["money"], market_info["stock"]
       depths = market_info["merge"]
       depth_lis = [Decimal(d) for d in depths]
       depth_lis.sort(reverse=False)
       depth = depth_lis[0]

       money_rate = PriceManager.asset_to_usd(money) if money != "USD" else Decimal("1")
       stock_rate = PriceManager.asset_to_usd(stock) if stock != "USD" else Decimal("1")

       results.append([
           depth, 
           market, 
           money_rate, 
           stock_rate, 
           json.dumps(bid_ask_data, cls=JsonEncoder),
           snapshot_ts
       ])
    return results


@scheduled(crontab(hour='*/1', minute='1,31'))
@lock_call()
def third_exchange_perpetual_depth_snapshot_schedule():
    for handler in PERPETUAL_HANDLERS:
        third_exchange_perpetual_depth_snapshot_task.delay(handler)


@celery_task
@lock_call(with_args=True)
def third_exchange_perpetual_depth_snapshot_task(handler):
    ts = current_timestamp(to_int=True)
    today_ts = ts - ts % 86400
    handler_cls = PERPETUAL_HANDLERS[handler]
    ret = handler_cls().build_depth_data()
    table = ExchangeLogDB.third_exchange_perpetual_depth_snapshot_table(today_ts, table=handler_cls.tag)
    table.create_if_not_exists()
    for record in ret:
        table.insert(*record)
    table.flush()


@scheduled(crontab(hour='*/1', minute='1,31'))
@lock_call()
def third_exchange_spot_depth_snapshot_schedule():
    for handler in SPOT_HANDLERS:
        third_exchange_spot_depth_snapshot_task.delay(handler)


@celery_task
@lock_call(with_args=True)
def third_exchange_spot_depth_snapshot_task(handler):
    ts = current_timestamp(to_int=True)
    today_ts = ts - ts % 86400
    handler_cls = SPOT_HANDLERS[handler]
    ret = handler_cls().build_spot_depth_data()
    table = ExchangeLogDB.third_exchange_spot_depth_snapshot_table(today_ts, table=handler_cls.tag)
    table.create_if_not_exists()
    for record in ret:
        table.insert(*record)
    table.flush()


@scheduled(crontab(hour="1", minute="0"))
@lock_call()
def cleanup_expired_snapshot_schedule():
    spot_days = BusinessSettings.spot_depth_snapshot_days
    perpetual_days = BusinessSettings.perpetual_depth_snapshot_days
    ExchangeLogDB.drop_xdays_ago_table(ExchangeLogDB.spot_depth_snapshot_table, days=spot_days)
    ExchangeLogDB.drop_xdays_ago_table(ExchangeLogDB.perpetual_depth_snapshot_table, days=perpetual_days)
    for handler in PERPETUAL_HANDLERS.values():
        ori_table = ExchangeLogDB.third_exchange_perpetual_depth_snapshot_table
        table = partial(ori_table, table=handler.tag)
        ExchangeLogDB.drop_xdays_ago_table(table, days=perpetual_days)
    for handler in SPOT_HANDLERS.values():
        ori_table = ExchangeLogDB.third_exchange_spot_depth_snapshot_table
        table = partial(ori_table, table=handler.tag)
        ExchangeLogDB.drop_xdays_ago_table(table, days=spot_days)


@scheduled(crontab(minute='*/1'))
@lock_call()
def get_exchanges_spot_market_prices_schedule():
    """获取Coinex+第三方交易所现货市场价格"""
    get_coinex_spot_market_prices_task.delay()
    for handler in SPOT_HANDLERS:
        get_third_exchanges_spot_market_prices_task.delay(handler)

@celery_task
@lock_call(with_args=True)
def get_coinex_spot_market_prices_task():
    tickers = ServerClient().get_all_market_tickers()
    res = [(m, ticker["last"]) for m, ticker in tickers.items() if '_' not in m]  # 剔除PRE_MARKET和 MARKET_INDEX
    ts = current_timestamp(to_int=True)
    snapshot_ts = ts - ts % 60
    first_rec = ExchangeMarketPrices.query.filter(
        ExchangeMarketPrices.report_time == snapshot_ts,
        ExchangeMarketPrices.market_type == MarketType.SPOT,
        ExchangeMarketPrices.exchange == Exchange.CoinEx
    ).first()
    if first_rec:
        return
    for market, price in res:
        db.session.add(ExchangeMarketPrices(
            report_time=snapshot_ts,
            market_type=MarketType.SPOT,
            exchange=Exchange.CoinEx,
            market=market,
            price=price
        ))
    db.session.commit()


@celery_task
@lock_call(with_args=True)
def get_third_exchanges_spot_market_prices_task(handler):
    handler_cls = SPOT_HANDLERS[handler]
    handler = handler_cls()
    ret = handler.get_market_price_list()
    ts = current_timestamp(to_int=True)
    snapshot_ts = ts - ts % 60
    exchange = Exchange(handler.tag)
    first_rec = ExchangeMarketPrices.query.filter(
        ExchangeMarketPrices.report_time == snapshot_ts,
        ExchangeMarketPrices.market_type == MarketType.SPOT,
        ExchangeMarketPrices.exchange == exchange
    ).first()
    if first_rec:
        return
    for market, price in ret:
        db.session.add(ExchangeMarketPrices(
            report_time=snapshot_ts,
            market_type=MarketType.SPOT,
            exchange=exchange,
            market=market,
            price=price
        ))
    db.session.commit()
    

@scheduled(crontab(minute='*/1'))
@lock_call()
def get_exchanges_perpetual_market_prices_schedule():
    """获取Coinex+第三方交易所永续市场价格"""
    get_coinex_perpetual_market_prices_task.delay()
    for handler in PERPETUAL_HANDLERS:
        get_third_exchange_perpetual_market_prices_task.delay(handler)


@celery_task
@lock_call(with_args=True)
def get_coinex_perpetual_market_prices_task():
    tickers = PerpetualServerClient().get_market_status()
    res = []
    for m, info in tickers.items():
        res.append((m, info['last']))
    ts = current_timestamp(to_int=True)
    snapshot_ts = ts - ts % 60
    first_rec = ExchangeMarketPrices.query.filter(
        ExchangeMarketPrices.report_time == snapshot_ts,
        ExchangeMarketPrices.market_type == MarketType.PERPETUAL,
        ExchangeMarketPrices.exchange == Exchange.CoinEx
    ).first()
    if first_rec:
        return
    for market, price in res:
        db.session.add(ExchangeMarketPrices(
            report_time=snapshot_ts,
            market_type=MarketType.PERPETUAL,
            exchange=Exchange.CoinEx,
            market=market,
            price=price
        ))
    db.session.commit()


@celery_task
@lock_call(with_args=True)
def get_third_exchange_perpetual_market_prices_task(handler):
    handler_cls = PERPETUAL_HANDLERS[handler]
    handler = handler_cls()
    ret = handler.get_market_price_list()
    ts = current_timestamp(to_int=True)
    snapshot_ts = ts - ts % 60
    exchange = Exchange(handler.tag)
    first_rec = ExchangeMarketPrices.query.filter(
        ExchangeMarketPrices.report_time == snapshot_ts,
        ExchangeMarketPrices.market_type == MarketType.PERPETUAL,
        ExchangeMarketPrices.exchange == exchange   
    ).first()
    if first_rec:
        return
    for market, price in ret:
        db.session.add(ExchangeMarketPrices(
            report_time=snapshot_ts,
            market_type=MarketType.PERPETUAL,
            exchange=exchange,
            market=market,
            price=price
        ))
    db.session.commit()


@scheduled(crontab(hour="8", minute="35"))
@lock_call()
def expire_exchanges_market_prices_schedule():
    """删除过期的价格数据"""
    ts = current_timestamp(to_int=True)
    snapshot_ts = ts - 86400
    ExchangeMarketPrices.query.filter(
        ExchangeMarketPrices.report_time < snapshot_ts
    ).delete()
    db.session.commit()


