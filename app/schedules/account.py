# -*- coding: utf-8 -*-
import datetime
import traceback
from collections import defaultdict
from datetime import date, timedelta
from decimal import Decimal
from typing import Union, List, Dict

from flask import current_app
from celery.schedules import crontab
from sqlalchemy import and_, or_
from app.common.constants import PrecisionEnum

from app.utils.amount import quantize_amount
from app.config import config

from ..business import TradeSummaryDB, lock_call, ServerResponseCode, LockAssetHelper
from ..business.share_window import SharePopWindowRepository, real_time_update_share_window, \
    daily_update_share_window
from ..business.export.tax_data import export_tax_data
from ..business.balance import MerkleBusiness
from ..business.external_dbs import PerpetualSummaryDB
from ..business.perpetual.position import get_user_profit_real_usd_map
from ..common import CeleryQueues, BalanceBusiness
from ..common.constants import PerpetualMarketType
from ..models import UserTradeSummary, User, SubAccount, db, AssetPrice, \
    LockedAssetBalance, UserTradeFeeSummary, SpotAssetTradeSummary, \
    UserShareWindowRecord, PerpetualAssetTradeSummary, TaxExportHistory, \
    UserExchangeSummary, UserPerpetualProfitRealSummary
from ..models.exchange import AssetExchangeOrder, AssetExchangeOrderFee, ExchangeFeeType
from ..utils import scheduled, route_module_to_celery_queue, batch_iter
from ..utils.date_ import now, date_to_datetime

route_module_to_celery_queue(__name__, CeleryQueues.DAILY)


def calc_user_summary(start_date: date, system: UserTradeSummary.System, user_ids: List[int] = None,
                      hour_ts: int = None):
    if system == UserTradeSummary.System.SPOT:
        _db = TradeSummaryDB
    else:
        _db = PerpetualSummaryDB
    coin_rate = AssetPrice.get_close_price_map(start_date)
    if hour_ts:
        records = _db.group_by_user_asset_by_hours(start_date, hour_ts, user_ids)
    else:
        records = _db.group_by_user_asset_by_date(start_date, user_ids)
    if not records:
        return dict()
    all_user_ids = [item['user_id'] for item in records]
    master_user_ids = [item.user_id for item in
                       User.query.filter(
                           User.id.in_(all_user_ids),
                           User.is_sub_account is False)]
    # results子账号
    ret_sub_user_ids = list(set(all_user_ids) ^ set(master_user_ids))
    sub_user_dict = {user_id: [] for user_id in master_user_ids}

    sub_users = SubAccount.query.filter(
        SubAccount.main_user_id.in_(master_user_ids),
        SubAccount.status == SubAccount.Status.VALID
    )

    for item in sub_users:
        sub_user_dict[item.main_user_id].append(item.user_id)
    sub_user_ids = [item.user_id for item in sub_users]
    # results中除去主账号以及有主账号的子账号，只剩子账号,避免后面算重复
    net_sub_user_ids = list(set(ret_sub_user_ids) ^ set(sub_user_ids))

    balance_dict = defaultdict(lambda: defaultdict(Decimal))
    if system == UserTradeSummary.System.SPOT:
        for item in records:
            _rate = coin_rate[item['money_asset']]
            info = balance_dict[item['user_id']]
            info['trade_amount'] += item['deal_volume'] * _rate
            info['maker_amount'] += item['maker_volume'] * _rate
            info['taker_amount'] += item['taker_volume'] * _rate
    else:
        for item in records:
            info = balance_dict[item['user_id']]
            info['trade_amount'] += item['deal_amount']
            info['maker_amount'] += item['maker_amount']
            info['taker_amount'] += item['taker_amount']

    user_balance = defaultdict(lambda: defaultdict(Decimal))
    for master_user_id in sub_user_dict:
        info = user_balance[master_user_id]
        for k, v in balance_dict[master_user_id].items():
            info[k] += v
        for sub_user_id in sub_user_dict[master_user_id]:
            for k, v in balance_dict[sub_user_id].items():
                info[k] += v
    for sub_user_id in net_sub_user_ids:
        if sub_user_id == 0:
            continue
        sub_user: User = User.query.get(sub_user_id)
        # 可能出现非法的user_id，待server排查
        if sub_user is None:
            current_app.logger.error(f"user_trade_summary found invalid user_id: {sub_user_id}")
            continue
        info = user_balance[sub_user.main_user.id]
        for k, v in balance_dict[sub_user_id].items():
            info[k] += v
    return user_balance


def update_user_trade_summary(
        start_date: date,
        system: Union[UserTradeSummary.System, str],
        force_update: bool = False
):
    if not TradeSummaryDB.is_data_completed(start_date) or \
            not PerpetualSummaryDB.is_data_completed(start_date):
        return
    if isinstance(system, str):
        system = UserTradeSummary.System[system]
    first_record = UserTradeSummary.query.filter(
        UserTradeSummary.report_date == start_date,
        UserTradeSummary.system == system
    ).first()
    if first_record and not force_update:
        return

    user_balance = calc_user_summary(start_date, system)
    if not user_balance:
        return
    data = sorted(
        list(user_balance.items()),
        key=lambda x: x[1]['trade_amount'],
        reverse=True
    )
    rank = 1
    if force_update:
        UserTradeSummary.query.filter(
            UserTradeSummary.report_date == start_date,
            UserTradeSummary.system == system
        ).delete(synchronize_session=False)
    for user_id, info in data:
        trade_amount = info['trade_amount']
        taker_amount = info['taker_amount']
        maker_amount = info['maker_amount']
        if trade_amount <= Decimal():
            continue
        db.session.add(
            UserTradeSummary(
                report_date=start_date,
                user_id=user_id,
                system=system,
                trade_amount=trade_amount,
                maker_amount=maker_amount,
                taker_amount=taker_amount,
                rank=rank
            )
        )
        rank += 1
    db.session.commit()


def calc_user_fee_summary(start_date: date, system: UserTradeSummary.System, user_ids: List[int] = None,
                          hour_ts: int = None) -> Dict[int, Dict[str, Decimal]]:
    if system == UserTradeFeeSummary.System.SPOT:
        _db = TradeSummaryDB
    else:
        _db = PerpetualSummaryDB
    coin_rate = AssetPrice.get_close_price_map(start_date)
    if hour_ts:
        records = _db.group_by_user_fee_by_hours(start_date, hour_ts, user_ids)
    else:
        records = _db.group_by_user_fee_by_date(start_date, user_ids)
    if not records:
        return dict()
    all_user_ids = [item['user_id'] for item in records]
    master_user_ids = [item.user_id for item in
                       User.query.filter(
                           User.id.in_(all_user_ids),
                           User.is_sub_account is False)]
    ret_sub_user_ids = list(set(all_user_ids) ^ set(master_user_ids))
    sub_user_dict = {user_id: [] for user_id in master_user_ids}

    sub_users = SubAccount.query.filter(
        SubAccount.main_user_id.in_(master_user_ids),
        SubAccount.status == SubAccount.Status.VALID
    )

    for item in sub_users:
        sub_user_dict[item.main_user_id].append(item.user_id)
    sub_user_ids = [item.user_id for item in sub_users]
    net_sub_user_ids = list(set(ret_sub_user_ids) ^ set(sub_user_ids))

    balance_dict = defaultdict(lambda: defaultdict(Decimal))

    for item in records:
        _rate = coin_rate[item['asset']] if item['asset'] in coin_rate else Decimal()
        info = balance_dict[item['user_id']]
        info['trade_fee_amount'] += item['fee'] * _rate

    user_balance = defaultdict(lambda: defaultdict(Decimal))
    for master_user_id in sub_user_dict:
        info = user_balance[master_user_id]
        for k, v in balance_dict[master_user_id].items():
            info[k] += v
        for sub_user_id in sub_user_dict[master_user_id]:
            for k, v in balance_dict[sub_user_id].items():
                info[k] += v
    for sub_user_id in net_sub_user_ids:
        if sub_user_id == 0:
            continue
        sub_user: User = User.query.get(sub_user_id)
        # 可能出现非法的user_id，待server排查
        if sub_user is None:
            current_app.logger.error(f"user_trade_summary found invalid user_id: {sub_user_id}")
            continue
        info = user_balance[sub_user.main_user.id]
        for k, v in balance_dict[sub_user_id].items():
            info[k] += v
    return user_balance


def update_user_trade_fee_summary(
        start_date: date,
        system: Union[UserTradeFeeSummary.System, str],
        force_update: bool = False
):
    if isinstance(system, str):
        system = UserTradeFeeSummary.System[system]
    first_record = UserTradeFeeSummary.query.filter(
        UserTradeFeeSummary.report_date == start_date,
        UserTradeFeeSummary.system == system
    ).first()
    if first_record and not force_update:
        return
    user_fee_balance = calc_user_fee_summary(start_date, system)
    if not user_fee_balance:
        return
    data = sorted(
        list(user_fee_balance.items()),
        key=lambda x: x[1]['trade_fee_amount'],
        reverse=True
    )
    rank = 1

    if force_update:
        UserTradeFeeSummary.query.filter(
            UserTradeFeeSummary.report_date == start_date,
            UserTradeFeeSummary.system == system
        ).delete(synchronize_session=False)
    for user_id, info in data:
        trade_fee_amount = info['trade_fee_amount']
        db.session.add(
            UserTradeFeeSummary(
                report_date=start_date,
                user_id=user_id,
                system=system,
                trade_fee_amount=trade_fee_amount,
                rank=rank,
            )
        )
        rank += 1
    db.session.commit()


def update_trade_asset_summary(
        start_date: date,
        system: str,
        force_update: bool = False
):
    if system == 'spot':
        _db = TradeSummaryDB
        _model = SpotAssetTradeSummary
    else:
        _db = PerpetualSummaryDB
        _model = PerpetualAssetTradeSummary
    first_record = _model.query.filter(
        _model.report_date == start_date,
    ).first()
    if first_record and not force_update:
        return
    records = _db.group_by_date_market_asset(start_date)
    if not records:
        return

    balance_dict = defaultdict(lambda: defaultdict(Decimal))

    coin_rate = AssetPrice.get_close_price_map(start_date)

    if system == 'spot':
        for item in records:
            _rate = coin_rate[item['money_asset']]
            info = balance_dict[item['market']]
            info['trade_amount'] += item['deal_volume'] * _rate
            info['money_asset'] = item['money_asset']
            info['stock_asset'] = item['stock_asset']
    else:
        for item in records:
            info = balance_dict[item['market']]
            info['trade_amount'] += item['deal_amount']
            info['money_asset'] = item['money_asset']
            info['stock_asset'] = item['stock_asset']

    if force_update:
        _model.query.filter(
            _model.report_date == start_date,
        ).delete(synchronize_session=False)
    for market, info in balance_dict.items():
        trade_amount = info['trade_amount']
        if trade_amount <= Decimal():
            continue
        trade_amount = quantize_amount(trade_amount, PrecisionEnum.CASH_PLACES)
        db.session.add(
            _model(
                report_date=start_date,
                market=market,
                money_asset=info['money_asset'],
                stock_asset=info['stock_asset'],
                trade_amount=trade_amount,
            )
        )
    db.session.commit()


@scheduled(crontab(minute="10,30,50", hour=0))
@lock_call()
def update_spot_user_trade_summary_schedule():
    """
    币币-用户成交统计
    """
    today = date.today()

    last_record: UserTradeSummary = UserTradeSummary.query.filter(
        UserTradeSummary.system == UserTradeSummary.System.SPOT
    ).order_by(
        UserTradeSummary.report_date.desc()
    ).first()

    if last_record:
        start_date = last_record.report_date + timedelta(days=1)
    else:
        start_date = today + timedelta(days=-1)

    while start_date < today:
        update_user_trade_summary(
            start_date, UserTradeSummary.System.SPOT)
        start_date += timedelta(days=1)


@scheduled(crontab(minute="40,55", hour=0))
def update_perpetual_user_trade_summary_schedule():
    """
    永续-用户成交统计
    """
    today = date.today()

    last_record: UserTradeSummary = UserTradeSummary.query.filter(
        UserTradeSummary.system == UserTradeSummary.System.PERPETUAL
    ).order_by(
        UserTradeSummary.report_date.desc()
    ).first()

    if last_record:
        start_date = last_record.report_date + timedelta(days=1)
    else:
        start_date = today + timedelta(days=-1)

    while start_date < today:
        update_user_trade_summary(
            start_date, UserTradeSummary.System.PERPETUAL)
        start_date += timedelta(days=1)


@scheduled(crontab(minute='*/1'), queue=CeleryQueues.REAL_TIME)
@lock_call()
def check_asset_lock_schedule():
    model = LockedAssetBalance

    locked_configs: List[model] = model.query.filter(
        model.status.in_([model.Status.CREATED, model.Status.LOCK_FAIL]),
        model.business != model.Business.GIFT,  # gift 的业务有自己的重试机制
        or_(
            model.locked_at <= now(),
            model.locked_at.isnot(None)
        )
    ).order_by(
        model.locked_at,
        model.id
    ).all()
    for locked_config in locked_configs:
        # noinspection PyBroadException

        try:
            LockAssetHelper.lock(locked_config)
        except Exception as e:
            if e.code == ServerResponseCode.INSUFFICIENT_BALANCE:
                locked_config.status = model.Status.FAILED
            else:
                current_app.logger.error(f"lock asset error: {e}")

    unlocked_configs = model.query.filter(
        model.status.in_([
            model.Status.CREATED, model.Status.LOCKED, model.Status.UNLOCK_FAIL
        ]),
        model.unlocked_at <= now(),
    ).order_by(
        model.unlocked_at,
        model.id
    ).all()
    for unlocked_config in unlocked_configs:
        # noinspection PyBroadException
        try:
            LockAssetHelper.unlock(unlocked_config, BalanceBusiness.SYSTEM)
        except Exception as e:
            if e.code == ServerResponseCode.INSUFFICIENT_BALANCE:
                unlocked_config.status = model.Status.FAILED
            else:
                current_app.logger.error(f"unlock asset error: {traceback.format_exc()}")
    db.session.commit()


@scheduled(crontab(minute=30, hour='0-2'))
def update_spot_user_trade_fee_summary_schedule():
    """
    币币-用户成交手续费统计
    """
    today = date.today()

    last_record: UserTradeFeeSummary = UserTradeFeeSummary.query.filter(
        UserTradeFeeSummary.system == UserTradeFeeSummary.System.SPOT
    ).order_by(
        UserTradeFeeSummary.report_date.desc()
    ).first()

    if last_record:
        start_date = last_record.report_date + timedelta(days=1)
    else:
        start_date = today + timedelta(days=-365)

    while start_date < today:
        update_user_trade_fee_summary(
            start_date, UserTradeFeeSummary.System.SPOT)
        start_date += timedelta(days=1)


@scheduled(crontab(minute=30, hour='0-2'))
def update_perpetual_user_trade_fee_summary_schedule():
    """
    永续-用户成交手续费统计
    """
    today = date.today()

    last_record: UserTradeFeeSummary = UserTradeFeeSummary.query.filter(
        UserTradeFeeSummary.system == UserTradeFeeSummary.System.PERPETUAL
    ).order_by(
        UserTradeFeeSummary.report_date.desc()
    ).first()

    if last_record:
        start_date = last_record.report_date + timedelta(days=1)
    else:
        start_date = today + timedelta(days=-365)

    while start_date < today:
        update_user_trade_fee_summary(
            start_date, UserTradeFeeSummary.System.PERPETUAL)
        start_date += timedelta(days=1)


@scheduled(crontab(minute=30, hour='0-2'))
def update_spot_trade_asset_summary_schedule():
    today = date.today()

    last_record: SpotAssetTradeSummary = SpotAssetTradeSummary.query.order_by(
        SpotAssetTradeSummary.report_date.desc()
    ).first()

    if last_record:
        start_date = last_record.report_date + timedelta(days=1)
    else:
        start_date = today + timedelta(days=-365)

    while start_date < today:
        update_trade_asset_summary(
            start_date, 'spot')
        start_date += timedelta(days=1)


@scheduled(crontab(minute=30, hour='0-2'))
def update_perpetual_trade_asset_summary_schedule():
    today = date.today()

    last_record: PerpetualAssetTradeSummary = PerpetualAssetTradeSummary.query.order_by(
        PerpetualAssetTradeSummary.report_date.desc()
    ).first()

    if last_record:
        start_date = last_record.report_date + timedelta(days=1)
    else:
        start_date = today + timedelta(days=-365)

    while start_date < today:
        update_trade_asset_summary(
            start_date, 'perpetual')
        start_date += timedelta(days=1)


@scheduled(crontab(hour='*/2', minute="27"), queue=CeleryQueues.REAL_TIME)
@lock_call()
def check_tax_export_task_schedule():
    """报税数据导出超时的定时任务重新执行及过期任务的状态变更"""
    _check_expired_task()
    _check_pending_task()


def _check_expired_task():
    now_ = now()
    deadline = now_ - datetime.timedelta(days=TaxExportHistory.EXPIRE_DAYS)
    TaxExportHistory.query.filter(or_(
        and_(TaxExportHistory.status == TaxExportHistory.Status.FINISHED,
             TaxExportHistory.finished_at < deadline),
        and_(TaxExportHistory.status == TaxExportHistory.Status.PENDING,
             TaxExportHistory.created_at < deadline)
    )).update(
        {'status': TaxExportHistory.Status.EXPIRED},
        synchronize_session=False)
    db.session.commit()


def _check_pending_task():
    now_ = now()
    deadline = now_ - datetime.timedelta(hours=TaxExportHistory.TTL_HOUR)
    tasks = TaxExportHistory.query.filter(
        TaxExportHistory.created_at < deadline,
        TaxExportHistory.status == TaxExportHistory.Status.PENDING
    ).with_entities(
        TaxExportHistory.id
    ).all()
    for task in tasks:
        id_ = task.id
        current_app.logger.info(f'报税数据导出记录id:{id_} 超过2小时仍未生成，重新生成。。。')
        export_tax_data.delay(id_)


@scheduled(crontab(minute='*/16'), queue=CeleryQueues.REAL_TIME)
@lock_call(ttl=7200)
def scan_and_gen_share_pop_window():
    """扫描用户实时的弹窗"""
    for pop_cls in real_time_update_share_window():
        pop_cls: SharePopWindowRepository
        pop_cls.gen_pop_window()


@scheduled(crontab(minute='*/16'), queue=CeleryQueues.REAL_TIME)
@lock_call(ttl=7200)
def scan_daily_share_pop_window():
    """扫描每日数据的分享弹窗"""
    for pop_cls in daily_update_share_window():
        pop_cls: SharePopWindowRepository
        pop_cls.gen_pop_window()


@scheduled(crontab(minute='*/15'), queue=CeleryQueues.REAL_TIME)
@lock_call()
def expire_pop_windows_schedule():
    expire_time = now() - datetime.timedelta(days=1)
    model = UserShareWindowRecord
    rows = model.query.filter(
        model.status == model.Status.PENDING,
        model.created_at < expire_time
    ).all()
    for row in rows:
        row.status = model.Status.POPPED
    db.session.commit()


# @scheduled(crontab(minute=0, hour=0, day_of_month='13-18', month_of_year=12))
def make_merkle_balance_snapshot():
    """该任务在指定时间执行一次，执行完成后应去掉计划任务。"""
    snapshot_id = MerkleBusiness.make_balance_snapshot()
    MerkleBusiness.make_merkle_tree(snapshot_id)


@scheduled(crontab(minute="10,30,50", hour=0))
@lock_call()
def update_user_exchange_summary_schedule():
    """
    兑换交易统计
    """
    today = date.today()

    model = UserExchangeSummary
    last_record: UserExchangeSummary = model.query.order_by(
        model.report_date.desc()
    ).first()

    if last_record:
        start_date = last_record.report_date + timedelta(days=1)
    else:
        start_date = today + timedelta(days=-1)

    while start_date < today:
        update_user_exchange_summary(start_date)
        start_date += timedelta(days=1)


def update_user_exchange_summary(start_date):
    end_date = start_date + timedelta(days=1)
    rates = AssetPrice.get_close_price_map(start_date)
    sub_users = SubAccount.query.with_entities(SubAccount.user_id, SubAccount.main_user_id).all()
    sub_users = dict(sub_users)

    model = AssetExchangeOrder
    rows = model.query.with_entities(
        model.target_asset_exchanged_amount,
        model.target_asset,
        model.user_id,
        model.id,
    ).filter(
        model.updated_at >= start_date,
        model.updated_at < end_date,
        model.status == model.Status.FINISHED,
    ).all()
    ids = []
    mapping = defaultdict(lambda: defaultdict(Decimal))
    for row in rows:
        ids.append(row.id)
        user_id = sub_users.get(row.user_id, row.user_id)
        info = mapping[user_id]
        info['trade_amount'] += row.target_asset_exchanged_amount * rates.get(row.target_asset, 0)

    fee_model = AssetExchangeOrderFee
    for chunk_ids in batch_iter(ids, 5000):
        fee_rows = fee_model.query.with_entities(
            fee_model.user_id,
            fee_model.asset,
            fee_model.amount,
            fee_model.type,
        ).filter(
            fee_model.exchange_order_id.in_(chunk_ids)
        ).all()
        for row in fee_rows:
            user_id = sub_users.get(row.user_id, row.user_id)
            info = mapping[user_id]
            fee = row.amount * rates.get(row.asset, 0)
            if row.type is ExchangeFeeType.WEB:
                info['web_fee'] += fee
            else:
                info['server_fee'] += fee

    for user_id, info in mapping.items():
        db.session.add(
            UserExchangeSummary(
                report_date=start_date,
                user_id=user_id,
                trade_amount=info['trade_amount'],
                web_fee_amount=info['web_fee'],
                server_fee_amount=info['server_fee'],
            )
        )
    db.session.commit()


@scheduled(crontab(minute='15,35,55', hour='0'))
@lock_call()
def update_user_perpetual_profit_real_summary_schedule():
    """
    用户合约盈亏统计
    """
    today_ = date.today()

    last_record: UserPerpetualProfitRealSummary = UserPerpetualProfitRealSummary.query.order_by(
        UserPerpetualProfitRealSummary.report_date.desc()
    ).first()

    if last_record:
        start_date = last_record.report_date + timedelta(days=1)
    else:
        start_date = today_ + timedelta(days=-1)

    while start_date < today_:
        update_user_perpetual_profit_real_summary(start_date)
        start_date += timedelta(days=1)


def update_user_perpetual_profit_real_summary(start_date: date, force_update: bool = False):
    first_record = UserPerpetualProfitRealSummary.query.filter(
        UserPerpetualProfitRealSummary.report_date == start_date,
    ).first()
    if first_record and not force_update:
        return

    end_date = start_date + timedelta(days=1)

    direct_user_profit_real_usd_map, inverse_user_profit_real_usd_map = get_user_profit_real_usd_map(
        int(date_to_datetime(start_date).timestamp()),
        int(date_to_datetime(end_date).timestamp()),
        AssetPrice.get_close_price_map(start_date),
    )

    if force_update:
        UserPerpetualProfitRealSummary.query.filter(
            UserPerpetualProfitRealSummary.report_date == start_date,
        ).delete(synchronize_session=False)

    direct_data = sorted(list(direct_user_profit_real_usd_map.items()), key=lambda x: x[1], reverse=True)
    rank = 1
    for user_id, profit_real_usd in direct_data:
        if user_id == config['PROP_TRADING_USER_ID']:
            continue
        db.session.add(
            UserPerpetualProfitRealSummary(
                report_date=start_date,
                market_type=PerpetualMarketType.DIRECT,
                user_id=user_id,
                profit_real_usd=quantize_amount(profit_real_usd, PrecisionEnum.COIN_PLACES),
                rank=rank,
            )
        )
        rank += 1

    inverse_data = sorted(list(inverse_user_profit_real_usd_map.items()), key=lambda x: x[1], reverse=True)
    rank = 1
    for user_id, profit_real_usd in inverse_data:
        if user_id == config['PROP_TRADING_USER_ID']:
            continue
        db.session.add(
            UserPerpetualProfitRealSummary(
                report_date=start_date,
                market_type=PerpetualMarketType.INVERSE,
                user_id=user_id,
                profit_real_usd=quantize_amount(profit_real_usd, PrecisionEnum.COIN_PLACES),
                rank=rank,
            )
        )
        rank += 1

    db.session.commit()
