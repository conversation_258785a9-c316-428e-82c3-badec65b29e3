# -*- coding: utf-8 -*-
from collections import defaultdict
from dataclasses import dataclass
from decimal import Decimal
import json
import time
from typing import Dict, Any, Iterable, List

from click import Tuple
from kafka import KafkaConsumer
from flask import current_app
from traceback import format_exc

from app import config
from app.business.clients.server import MAX_ORDER_ACCOUNT_ID, SPOT_ACCOUNT_ID
from app.business.lock import CacheLock, LockKeys
from app.business.prices import PriceManager
from app.common.constants import AccountBalanceType, BalanceBusiness, ProducerTopics
from app.models.amm import LiquidityHistory
from app.models.asset_cost import AssetCost
from app.models.base import db
from app.models.copy_trading import CopyTransferHistory
from app.models.investment import InvestmentAccount
from app.models.pledge import MAX_PLEDGE_ACCOUNT_ID, MIN_PLEDGE_ACCOUNT_ID
from app.models.staking import StakingAccount
from app.models.strategy import StrategyTransferHistory
from app.models.user import SubAccountAssetTransfer
from app.utils.amount import quantize_amount
from app.utils.date_ import current_timestamp
from app.utils.iterable import batch_iter


"""
message = \
dict(
    event_data=event_data,
    biz_type=business,
    biz_id=business_id,
    timestamp=current_timestamp(to_int=True),
    user_id=user_id
)

event_data = \
{
    'user_id': user_id,
    'account_id': account_id,
    'asset': asset,
    'business': business,
    'business_id': business_id,
    'amount': str(amount),
    'detail': detail or {},
}
"""

"""
1. 账户带价转入的业务类型
对此类业务类型，使用源账户的该币种的均价来更新目标账户的均价
主要包含了账户间转账的场景
"""
TRANSFER_WITH_PRICE_BUSINESS_TYPES = (
    BalanceBusiness.MARGIN_TRANSFER,
    BalanceBusiness.INVESTMENT_TRANSFER,
    BalanceBusiness.PERPETUAL_TRANSFER_IN,
    BalanceBusiness.PERPETUAL_TRANSFER_OUT,
    BalanceBusiness.PERPETUAL_TRANSFER,
    BalanceBusiness.SPOT_GRID_TRANSFER,
    BalanceBusiness.ADD_LIQUIDITY,
    BalanceBusiness.REMOVE_LIQUIDITY,
    # BalanceBusiness.DIBS_ACTIVITY_SUBSCRIBE,
    # BalanceBusiness.DIBS_ACTIVITY_CANCEL,
    BalanceBusiness.PLEDGE_ASSET_LOCK,
    BalanceBusiness.PLEDGE_ASSET_RELEASE,
    BalanceBusiness.COPY_TRADING_TRANSFER,
    BalanceBusiness.STAKING_ADD,
    BalanceBusiness.STAKING_REMOVE,
    BalanceBusiness.SUB_ACCOUNT_TRANSFER
)

# 账户划转业务类型 -> 账户类型映射
ACCOUNT_TRANSFER_BUSINESS_TYPE_MAP = {
    BalanceBusiness.MARGIN_TRANSFER: AccountBalanceType.MARGIN,
    BalanceBusiness.INVESTMENT_TRANSFER: AccountBalanceType.INVESTMENT,
    BalanceBusiness.PERPETUAL_TRANSFER_IN: AccountBalanceType.PERPETUAL,
    BalanceBusiness.PERPETUAL_TRANSFER_OUT: AccountBalanceType.PERPETUAL,
    BalanceBusiness.PERPETUAL_TRANSFER: AccountBalanceType.PERPETUAL,
    BalanceBusiness.ADD_LIQUIDITY: AccountBalanceType.AMM,
    BalanceBusiness.REMOVE_LIQUIDITY: AccountBalanceType.AMM,
    BalanceBusiness.PLEDGE_ASSET_LOCK: AccountBalanceType.PLEDGE,
    BalanceBusiness.PLEDGE_ASSET_RELEASE: AccountBalanceType.PLEDGE,
    BalanceBusiness.STAKING_ADD: AccountBalanceType.INVESTMENT,
    BalanceBusiness.STAKING_REMOVE: AccountBalanceType.INVESTMENT
}

SUB_ACCOUNT_BUSINESS_TYPES = (
    BalanceBusiness.SUB_ACCOUNT_TRANSFER,
    BalanceBusiness.COPY_TRADING_TRANSFER,
    BalanceBusiness.SPOT_GRID_TRANSFER
)

"""
2. 按0成本更新的业务类型
对此类业务类型，按均价为0来更新目标账户的均价
主要包含了奖励、返佣、红包、理财收益等场景
"""
ZERO_COST_BUSINESS_TYPES = (
    BalanceBusiness.GIFT,
    BalanceBusiness.GIFT_REVOKE,
    BalanceBusiness.COUPON,
    BalanceBusiness.COUPON_RECYCLE,
    BalanceBusiness.TRADE_GIFT_COUPON,
    BalanceBusiness.CASHBACK_FEE,
    BalanceBusiness.PERPETUAL_SUBSIDY_COUPON,
    BalanceBusiness.COPY_TRADING_EXPERIENCE_FEE,
    BalanceBusiness.COPY_TRADING_EXPERIENCE_FEE_RECYCLE,
    BalanceBusiness.REFERRAL,
    BalanceBusiness.MAKER_CASH_BACK,
    BalanceBusiness.BROKER_REFERRAL,
    BalanceBusiness.NORMAL_REFERRAL,
    BalanceBusiness.AMBASSADOR_REFERRAL,
    BalanceBusiness.IEO_ACTIVITY_LOTTERY,
    BalanceBusiness.DIBS_ACTIVITY_LOTTERY,
    BalanceBusiness.BUS_USER_REFER,
    BalanceBusiness.INVESTMENT_INTEREST,
    BalanceBusiness.INVESTMENT_INC_INTEREST,
    BalanceBusiness.STAKING_INCOME
)

"""
3. 按市场价更新的业务类型
对此类业务类型，按市场价来更新目标账户的均价
主要包含了充值提现、站内转账、借币等场景
"""
MARKET_PRICE_BUSINESS_TYPES = (
    BalanceBusiness.DEPOSIT, 
    BalanceBusiness.WITHDRAWAL,
    BalanceBusiness.SYSTEM,
    BalanceBusiness.RED_PACKET,
    BalanceBusiness.RED_PACKET_GRABBING,
    BalanceBusiness.SIGNED_OFF_USER_TO_ADMIN_TRANSFER,
    BalanceBusiness.CLEANED_BALANCE_TO_ADMIN_TRANSFER,
    BalanceBusiness.EXCHANGE_ORDER_TRANSFER,
    BalanceBusiness.AUTO_INVEST_TRANSFER,
    BalanceBusiness.AMM_FEE_TRANSFER,
    BalanceBusiness.ABNORMAL_DEPOSIT_APPLICATION,
    BalanceBusiness.IEO_ACTIVITY_SUBSCRIBE,
    BalanceBusiness.IEO_ACTIVITY_CANCEL,
    BalanceBusiness.PRE_TRADING_ISSUE,
    BalanceBusiness.PRE_TRADING_REDEMPTION,
    BalanceBusiness.PRE_TRADING_POS_SETTLE,
    BalanceBusiness.PRE_TRADING_ISSUE_SETTLE,
    BalanceBusiness.COPY_PROFIT_SETTLEMENT,
    BalanceBusiness.BUS_AMB_LOAN,
    BalanceBusiness.BUS_AMB_LOAN_REPAY,
    BalanceBusiness.P2P_SUB,
    BalanceBusiness.P2P_ADD,
    BalanceBusiness.P2P_MARGIN_PAYMENT,
    BalanceBusiness.P2P_MARGIN_REFUND,
    BalanceBusiness.P2P_MARGIN_SYS_ADD,
    BalanceBusiness.P2P_MARGIN_SYS_SUB,
    BalanceBusiness.COMMENT_TIP_IN,
    BalanceBusiness.COMMENT_TIP_OUT,
    BalanceBusiness.CREDIT,
    BalanceBusiness.CREDIT_REPAYMENT,
    BalanceBusiness.PLEDGE_LOAN_ASSET_ADD,
    BalanceBusiness.PLEDGE_REPAY,
    BalanceBusiness.PLEDGE_LIQ,
    BalanceBusiness.MARGIN_LOAN,
    BalanceBusiness.MARGIN_REPAYMENT
)


"""
4. 不更新的业务类型
对此类业务类型，不更新均价，仅更新持仓数量
"""
NO_PRICE_UPDATE_BUSINESS_TYPES = (
    BalanceBusiness.DEPOSIT_CANCELLATION,
    BalanceBusiness.WITHDRAWAL_CANCELLATION,
    BalanceBusiness.WITHDRAWAL_FEE_CANCELLATION,
    BalanceBusiness.RED_PACKET_REFUND
)

AMM_BUSINESS_TYPES = (
    BalanceBusiness.ADD_LIQUIDITY,
    BalanceBusiness.REMOVE_LIQUIDITY
)
@dataclass
class BalanceUpdateRecord:
    user_id: int
    account_id: int
    asset: str
    business: str
    business_id: int
    price: Decimal
    amount: Decimal
    timestamp: int
    detail: Dict[str, Any] | None = None


class BaseHandler:

    @staticmethod
    def _get_account_type(record: BalanceUpdateRecord) -> AccountBalanceType:
        if record.detail and record.detail.get('system') == 'perpetual':
            return AccountBalanceType.PERPETUAL
        business = record.business
        account_id = record.account_id
        if business in (BalanceBusiness.PERPETUAL_TRANSFER_IN, BalanceBusiness.PERPETUAL_TRANSFER_OUT):
            return AccountBalanceType.PERPETUAL
        if account_id == SPOT_ACCOUNT_ID:
            return AccountBalanceType.SPOT
        if SPOT_ACCOUNT_ID < account_id < MAX_ORDER_ACCOUNT_ID:
            return AccountBalanceType.MARGIN
        if account_id in (StakingAccount.ACCOUNT_ID, InvestmentAccount.ACCOUNT_ID):
            return AccountBalanceType.INVESTMENT
        if MIN_PLEDGE_ACCOUNT_ID <= account_id <= MAX_PLEDGE_ACCOUNT_ID:
            return AccountBalanceType.PLEDGE
        if business and business in AMM_BUSINESS_TYPES:
            return AccountBalanceType.AMM
        raise ValueError("No account type found!")

    @classmethod
    def _update_result_map(cls, records: List[BalanceUpdateRecord], result_map):
        for record in records:
            account_type = cls._get_account_type(record)
            # 更新持仓均价 = (增量 * 价格 + （当前持仓量 * 持仓均价）) / (当前持仓量 + 增量)
            result = result_map[record.user_id][(account_type, record.asset)]
            
            prev_amount = result['amount']
            result['amount'] += record.amount
            if result['amount'] <= Decimal():
                result['amount'] = Decimal()
                result['price'] = Decimal()
            # 只有增量才更新均价
            if record.business not in NO_PRICE_UPDATE_BUSINESS_TYPES and result['amount'] > Decimal() and record.amount > Decimal():
                price = (record.amount * record.price + prev_amount * result['price']) / result['amount']
                result['price'] = quantize_amount(price, 8)

    @classmethod
    def _get_result_map(cls, user_ids: Iterable[int]):
        res = defaultdict(lambda: defaultdict(lambda: {'price': Decimal(), 'amount': Decimal()}))
        record_map = dict()
        for ids in batch_iter(user_ids, 5000):
            records = AssetCost.query.filter(
                AssetCost.user_id.in_(ids)
            ).all()
            for item in records:
                item: AssetCost
                res[item.user_id][(item.account_type, item.asset)]['price'] = item.price
                res[item.user_id][(item.account_type, item.asset)]['amount'] = item.amount

                record_map[(item.user_id, item.account_type, item.asset)] = item
        return dict(
            result_map=res,
            record_map=record_map
        )

    @classmethod
    def _update_asset_cost(cls, result_map, record_map):
        for user_id, account_map in result_map.items():
            for (account_type, asset), data in account_map.items():
                record = record_map.get((user_id, account_type, asset))
                if record:
                    if data['price'] != record.price or data['amount'] != record.amount:
                        # TODO: remove this log for production
                        current_app.logger.warning(f"Updating EXISTING asset cost for user: {user_id}, account_type: {account_type.name}, "
                                                   f"asset: {asset}, price: {data['price']}, amount: {data['amount']}")
                        record.price = data['price']
                        record.amount = data['amount']
                else:
                    # TODO: remove this log for production
                    current_app.logger.warning(f"Creating NEW asset cost for user: {user_id}, account_type: {account_type.name}, "
                                               f"asset: {asset}, price: {data['price']}, amount: {data['amount']}")
                    record = AssetCost(
                        user_id=user_id,
                        account_type=account_type,
                        asset=asset,
                        price=data['price'],
                        amount=data['amount']
                    )
                    db.session.add(record)
        db.session.commit()
    
    @classmethod
    def handle(cls, messages: List[Dict[str, Any]]) -> None:
        """
        处理消息，更新持仓均价和持仓量
        :param messages: 消息列表
        """
        raise NotImplementedError("This method should be implemented by subclasses.")

class BalanceUpdateHandler(BaseHandler):

    
    @classmethod
    def _handle_amm_records(cls, records: List[BalanceUpdateRecord]) -> None:
        """
        AMM注入提取流动性时，实际注入/提取到系统用户ID，需要根据business_id取到普通用户ID
        """
        history_ids = []
        amm_records = []
        for item in records:
            if item.business in AMM_BUSINESS_TYPES:
                history_ids.append(item.business_id) # this is liquidity history id
                amm_records.append(records)
        if not history_ids:
            return
        liquidity_map = {}
        for ids in batch_iter(history_ids, 1000):
            histories = LiquidityHistory.query.filter(LiquidityHistory.id.in_(ids)).with_entities(
                LiquidityHistory.id,
                LiquidityHistory.user_id,
            ).all()
            liquidity_map.update(dict(histories))
        
        for item in amm_records:
            real_user_id = liquidity_map.get(item.business_id)
            if not real_user_id:
                current_app.logger.error(f"_handle_amm_msgs liquidity user not found for: {item.business_id}")
                continue
            item.user_id = real_user_id

    @classmethod
    def _update_price(cls, records: List[BalanceUpdateRecord], result_map: Dict[Tuple, Dict], record_map: Dict[Tuple, Dict]) -> None:
        price_map = PriceManager.assets_to_usd()        
        
        def _filter_msgs(_types):
            return [item for item in records if item.business in _types]
        
        def _handle_price_for_zero_cost_records(records) -> None:
            for item in records:
                item.price = Decimal()
        
        def _handle_price_for_market_price_records(records) -> None:
            for item in records:
                item.price = price_map.get(item.asset, Decimal())
        
        def _handle_price_for_transfer_with_price_records(records) -> None:
            """
            带价划转
            """
            sub_transfer_ids, copy_transfer_ids, strategy_transfer_ids = [], [], []
            involved_user_ids = set()

            def _update_sub_transfer_business_ids(business, business_id):
                if business == BalanceBusiness.SUB_ACCOUNT_TRANSFER:
                    sub_transfer_ids.append(business_id)
                elif business == BalanceBusiness.COPY_TRADING_TRANSFER:
                    copy_transfer_ids.append(business_id)
                elif business == BalanceBusiness.SPOT_GRID_TRANSFER:
                    strategy_transfer_ids.append(business_id)
                else:
                    raise ValueError(f"Unknown business type: {business} for business_id: {business_id}")

            def _get_sub_transfer_info():
                res = dict()
                for ids in batch_iter(sub_transfer_ids, 1000):
                    records = SubAccountAssetTransfer.query.filter(
                        SubAccountAssetTransfer.id.in_(ids)
                    ).with_entities(
                        SubAccountAssetTransfer.id,
                        SubAccountAssetTransfer.source,
                        SubAccountAssetTransfer.target,
                        SubAccountAssetTransfer.source_account_type,
                        SubAccountAssetTransfer.target_account_type,
                    ).all()
                    for item in records:
                        assert item.source_account_type in (SubAccountAssetTransfer.AccountType.SPOT, 
                                                            SubAccountAssetTransfer.AccountType.PERPETUAL) \
                            and item.target_account_type in (SubAccountAssetTransfer.AccountType.SPOT, 
                                                            SubAccountAssetTransfer.AccountType.PERPETUAL)

                        from_account = AccountBalanceType.SPOT if item.source_account_type == SubAccountAssetTransfer.AccountType.SPOT\
                            else AccountBalanceType.PERPETUAL
                        to_account = AccountBalanceType.SPOT if item.target_account_type == SubAccountAssetTransfer.AccountType.SPOT\
                            else AccountBalanceType.PERPETUAL
                        res[(BalanceBusiness.SUB_ACCOUNT_TRANSFER, item.id)] = {
                            'from_user_id': item.source,
                            'to_user_id': item.target,
                            'from_account': from_account,
                            'to_account': to_account
                        }

                for ids in batch_iter(copy_transfer_ids, 1000):
                    records = CopyTransferHistory.query.filter(
                        CopyTransferHistory.id.in_(ids)
                    ).with_entities(
                        CopyTransferHistory.id,
                        CopyTransferHistory.from_user_id,
                        CopyTransferHistory.to_user_id,
                        CopyTransferHistory.from_account_type,
                        CopyTransferHistory.to_account_type,
                    ).all()
                    for item in records:
                        assert item.from_account_type in (CopyTransferHistory.AccountType.SPOT, 
                                                            CopyTransferHistory.AccountType.PERPETUAL) \
                            and item.to_account_type in (CopyTransferHistory.AccountType.SPOT, 
                                                            CopyTransferHistory.AccountType.PERPETUAL)
                        from_account = AccountBalanceType.SPOT if item.from_account_type == CopyTransferHistory.AccountType.SPOT\
                            else AccountBalanceType.PERPETUAL
                        to_account = AccountBalanceType.SPOT if item.to_account_type == CopyTransferHistory.AccountType.SPOT\
                            else AccountBalanceType.PERPETUAL
                        res[(BalanceBusiness.COPY_TRADING_TRANSFER, item.id)] = {
                            'from_user_id': item.from_user_id,
                            'to_user_id': item.to_user_id,
                            'from_account': from_account,
                            'to_account': to_account
                        }

                for ids in batch_iter(strategy_transfer_ids, 1000):
                    records = StrategyTransferHistory.query.filter(
                        StrategyTransferHistory.id.in_(ids)
                    ).with_entities(
                        StrategyTransferHistory.id,
                        StrategyTransferHistory.from_user_id,
                        StrategyTransferHistory.to_user_id,
                    ).all()
                    for item in records:
                        res[(BalanceBusiness.SPOT_GRID_TRANSFER, item.id)] = {
                            'from_user_id': item.from_user_id,
                            'to_user_id': item.to_user_id,
                            'from_account': AccountBalanceType.SPOT,
                            'to_account': AccountBalanceType.SPOT
                        }
                for v in res.values():
                    involved_user_ids.add(v['from_user_id'])
                    involved_user_ids.add(v['to_user_id'])
                return res

            
            for item in records:
                item: BalanceUpdateRecord
                business = item.business
                business_id = item.business_id
                if business in SUB_ACCOUNT_BUSINESS_TYPES:
                    _update_sub_transfer_business_ids(business, business_id)
                
            sub_transfer_info = _get_sub_transfer_info()

            left_user_ids = involved_user_ids - set(result_map)
            if left_user_ids:
                r = cls._get_result_map(left_user_ids)
                left_result_map = r['result_map']
                left_record_map = r['record_map']
                result_map.update(left_result_map)
                record_map.update(left_record_map)
            
            for item in records:
                item: BalanceUpdateRecord
                business = item.business
                business_id = item.business_id


                account_type = cls._get_account_type(item)
                if type_:= ACCOUNT_TRANSFER_BUSINESS_TYPE_MAP.get(business):
                    if account_type == type_:
                        other_account_type = AccountBalanceType.SPOT
                    else:
                        other_account_type = type_
                    other_result = result_map.get(item.user_id, {}).get((other_account_type, item.asset), {})
                elif business in SUB_ACCOUNT_BUSINESS_TYPES:
                    info = sub_transfer_info.get((business, business_id))
                    if not info:
                        current_app.logger.error(f"get sub transfer info failed, business: {business}, "
                                                f"business_id: {business_id}")
                        continue
                    from_user_id = info['from_user_id']
                    account_type = info['from_account']
                    other_result = result_map.get(from_user_id, {}).get((account_type, item.asset), {})
                price = other_result.get('price', Decimal())
                
                if not price:
                    price = price_map.get(item.asset, Decimal())
                    current_app.logger.error(
                        f"get price for transfer with price record failed, user: {item.user_id}, "
                        f"business: {business}, account: {account_type}, asset: {item.asset} using price: {price}"
                    )
                item.price = price


        def _handle_price_for_no_price_update_records(messages) -> None:
            # 对于不需要更新价格的业务类型，不做任何处理
            pass

        _handle_price_for_zero_cost_records(_filter_msgs(ZERO_COST_BUSINESS_TYPES))
        _handle_price_for_market_price_records(_filter_msgs(MARKET_PRICE_BUSINESS_TYPES))
        _handle_price_for_transfer_with_price_records(_filter_msgs(TRANSFER_WITH_PRICE_BUSINESS_TYPES))
        _handle_price_for_no_price_update_records(_filter_msgs(NO_PRICE_UPDATE_BUSINESS_TYPES))

    @classmethod
    def _process_raw_msgs(cls, messages: List[Dict[str, Any]]) -> List[BalanceUpdateRecord]:
        """
        处理原始消息，转为 BalanceUpdateRecord 列表
        """
        result = []
        current_ts = current_timestamp(to_int=True)
        all_balance_business_values = {item.value for item in BalanceBusiness}
        for item in messages:
            # current_app.logger.warning(f"Processing raw message: {item['event_data']}")
            event = item['event_data']
            ts = item['timestamp']
            if current_ts - ts > 300:
                current_app.logger.warning(f"Balance update event too old, ts: {ts}, current_ts: {current_ts}, "
                                        f"business: {item['biz_type']}")
            business = event['business']
            if business not in all_balance_business_values:
                current_app.logger.error(f"_process_raw_msgs: Unknown business type: {business} in message: {item}")
                continue
            business = BalanceBusiness(business)
            record = BalanceUpdateRecord(
                user_id=event['user_id'],
                account_id=event['account_id'],
                asset=event['asset'],
                business=business,
                business_id=event['business_id'],
                price=Decimal(event.get('price', 0)),
                amount=Decimal(event['amount']),
                timestamp=ts,
                detail=event.get('detail', None)
            )
            result.append(record)
        return result


    @classmethod
    def _process_messages(cls, messages: List[Dict[str, Any]]) -> List[BalanceUpdateRecord]:        
        records = cls._process_raw_msgs(messages)
        if not records:
            return
        cls._handle_amm_records(records)
        return records
    

    @classmethod
    def handle(cls, messages: List[Dict[str, Any]]) -> None:
        with CacheLock(LockKeys.balance_cost_update(), wait=30):
            db.session.rollback()

            records = cls._process_messages(messages)
            r = cls._get_result_map(list({item.user_id for item in records}))
            result_map = r['result_map']
            record_map = r['record_map']

            cls._update_price(records, result_map, record_map)
            cls._update_result_map(records, result_map)
            cls._update_asset_cost(result_map, record_map)


class SpotDealHandler(BaseHandler):

    @classmethod
    def _process_messages(cls, messages: List[Dict[str, Any]]) -> List[BalanceUpdateRecord]:
        result = []
        current_ts = current_timestamp(to_int=True)
        price_map = PriceManager.assets_to_usd()
        # 每笔成交包含买卖双方的数据，转成6条update记录，分别是买卖方的支出币种，获得币种以及手续费币种
        for event in messages:
            ts = event['timestamp']
            if current_ts - ts > 300:
                current_app.logger.warning(f"Spot deal event too old, ts: {ts}, current_ts: {current_ts}")
            sell_receive_record = BalanceUpdateRecord(
                user_id=event['ask_user_id'],
                account_id=event['ask_account'],
                asset=event['money'],
                business=BalanceBusiness.TRADING,
                business_id=event['ask_id'],
                price=price_map.get(event['money'], Decimal()),
                amount=Decimal(event['deal']),
                timestamp=event['timestamp']
            )
            sell_offer_record = BalanceUpdateRecord(
                user_id=event['ask_user_id'],
                account_id=event['ask_account'],
                asset=event['stock'],
                business=BalanceBusiness.TRADING,
                business_id=event['ask_id'],
                price=Decimal(event['price']) * price_map.get(event['money'], Decimal()),
                amount=-Decimal(event['amount']),
                timestamp=event['timestamp']
            )
            sell_fee_record = BalanceUpdateRecord(
                user_id=event['ask_user_id'],
                account_id=event['ask_account'],
                asset=event['ask_fee_asset'],
                business=BalanceBusiness.TRADING,
                business_id=event['ask_id'],
                price=price_map.get(event['ask_fee_asset'], Decimal()),
                amount=-Decimal(event['ask_fee']),
                timestamp=event['timestamp']
            )
            buy_receive_record = BalanceUpdateRecord(
                user_id=event['bid_user_id'],
                account_id=event['bid_account'],
                asset=event['stock'],
                business=BalanceBusiness.TRADING,
                business_id=event['bid_id'],
                price=Decimal(event['price']) * price_map.get(event['money'], Decimal()),
                amount=Decimal(event['amount']),
                timestamp=event['timestamp']
            )
            buy_offer_record = BalanceUpdateRecord(
                user_id=event['bid_user_id'],
                account_id=event['bid_account'],
                asset=event['money'],
                business=BalanceBusiness.TRADING,
                business_id=event['bid_id'],
                price=price_map.get(event['money'], Decimal()),
                amount=-Decimal(event['deal']),
                timestamp=event['timestamp']
            )
            buy_fee_record = BalanceUpdateRecord(
                user_id=event['bid_user_id'],
                account_id=event['bid_account'],
                asset=event['bid_fee_asset'],
                business=BalanceBusiness.TRADING,
                business_id=event['bid_id'],
                price=price_map.get(event['bid_fee_asset'], Decimal()),
                amount=-Decimal(event['bid_fee']),
                timestamp=event['timestamp']
            )
            result.extend([
                sell_receive_record,
                sell_offer_record,
                sell_fee_record,
                buy_receive_record,
                buy_offer_record,
                buy_fee_record,
            ])
        return result
    
    @classmethod
    def handle(cls, messages: List[Dict[str, Any]]) -> None:
        with CacheLock(LockKeys.balance_cost_update(), wait=30):
            db.session.rollback()

            records = cls._process_messages(messages)
            r = cls._get_result_map(list({item.user_id for item in records}))
            result_map = r['result_map']
            record_map = r['record_map']
            
            cls._update_result_map(records, result_map)
            cls._update_asset_cost(result_map, record_map)


RETRY_TIMEOUT = 20

def run_balance_update_kafka_consumer():
    kafka_config = config['BALANCE_UPDATE_KAFKA_CONFIG']
    handler = BalanceUpdateHandler()
    consumer = None
    while True:
        try:
            consumer = KafkaConsumer(
                ProducerTopics.BALANCE_UPDATE.value,
                bootstrap_servers=kafka_config['KAFKA_SERVERS'],
                group_id=kafka_config['KAFKA_GROUP_ID'],
                max_poll_records=5000,
                max_partition_fetch_bytes=20 * 1024 * 1024,
            )
            while True:
                result = consumer.poll(timeout_ms=1000)
                for _, messages in result.items():
                    messages = [json.loads(msg.value) for msg in messages]
                    handler.handle(messages)
                time.sleep(1)
        except Exception as e:
            current_app.logger.error(f"Consumer error: {e!r}. {format_exc()} Retrying after {RETRY_TIMEOUT}s...")
            db.session.rollback()
            if consumer:
                consumer.close()
            time.sleep(RETRY_TIMEOUT)


def run_spot_deals_kafka_consumer():
    kafka_config = config['BALANCE_UPDATE_KAFKA_CONFIG']
    
    handler = SpotDealHandler()
    consumer = None
    while True:
        try:
            consumer = KafkaConsumer(
                'deals',
                bootstrap_servers=config['KAFKA_SPOT_CONFIG']['KAFKA_SERVERS'],
                group_id=kafka_config['KAFKA_GROUP_ID'],
                max_poll_records=5000,
                max_partition_fetch_bytes=20 * 1024 * 1024,
            )
            while True:
                result = consumer.poll(timeout_ms=1000)
                for _, messages in result.items():
                    messages = [json.loads(msg.value) for msg in messages]
                    handler.handle(messages)
                time.sleep(1)
        except Exception as e:
            current_app.logger.error(f"Consumer error: {e!r}. {format_exc()} Retrying after {RETRY_TIMEOUT}s...")
            db.session.rollback()
            if consumer:
                consumer.close()
            time.sleep(RETRY_TIMEOUT)
