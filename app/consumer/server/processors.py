import json
import time
from collections import defaultdict
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Set

from flask import current_app
from kafka.consumer.fetcher import ConsumerRecord

from app.business.equity_center.cashback import Cashback<PERSON><PERSON><PERSON><PERSON>el<PERSON>, CashbackTradeFeeCollector
from app.caches import PerpetualMarketCache, PerpetualOfflineMarketCache
from app.common import ProducerTopics
from app.consumer.server.base import BusinessProcessor, DealOrder
from app.models import db
from app.models.amm import AmmMarket
from app.models.equity_center import UserCashbackTradeFeeHistory
from app.models.kafka import ConsumerMaxOffset
from app.producer.kafka_mission import mission_producer
from app.utils import spawn_greenlet
from app.utils.amount import quantize_amount
from app.utils.date_ import timestamp_to_datetime


class MissionMessageProcessor(BusinessProcessor):
    """Mission消息处理器"""
    send_topic: ProducerTopics

    def process_messages(self, msgs: List[ConsumerRecord]) -> None:
        mission_messages = []
        topic_partition_offset_mapper = defaultdict(list)
        offset_mapper = ConsumerMaxOffset.query_max_offset_mapper()
        for msg in msgs:
            current_app.logger.debug(f"Received message: {self.send_topic}  {msg}")
            if msg.offset <= offset_mapper.get((msg.topic, msg.partition), -1):
                continue
            mission_messages.extend(self._process_message(msg))
            topic_partition_offset_mapper[(msg.topic, msg.partition)].append(msg.offset)

        if mission_messages:
            mission_producer.send_multiple_messages(self.send_topic, mission_messages)
            ConsumerMaxOffset.save_multiple_offset(topic_partition_offset_mapper)
            db.session.commit()

    def _process_message(self, msg: ConsumerRecord) -> List[dict]:
        raise NotImplementedError

    @classmethod
    def is_first_run(cls, topics):
        has_row = ConsumerMaxOffset.query_has_topic_row(topics)
        return not has_row


class SpotDealsMissionProcessor(MissionMessageProcessor):
    send_topic = ProducerTopics.SPOT_DEALS

    def _process_message(self, msg: ConsumerRecord) -> List[dict]:
        value = json.loads(msg.value)
        ask_order = DealOrder(
            time=int(value["timestamp"]),
            market=value["market"],
            user_id=int(value["ask_user_id"]),
            amount=Decimal(value["deal"]),
            amount_asset=value['money']
        )
        bid_order = DealOrder(
            time=int(value["timestamp"]),
            market=value["market"],
            user_id=int(value["bid_user_id"]),
            amount=Decimal(value["deal"]),
            amount_asset=value['money']
        )
        send_order = (ask_order, bid_order)
        if ask_order.user_id == bid_order.user_id:
            merge_order = DealOrder(
                time=int(value["timestamp"]),
                market=value["market"],
                user_id=ask_order.user_id,
                amount_asset=ask_order.amount_asset,
                amount=ask_order.amount + bid_order.amount
            )
            send_order = (merge_order,)
        return [
            dict(
                event_data=order.to_dict(),
                user_id=order.user_id,
                timestamp=order.time,
                biz_type="server_spot_kafka",
                biz_id=value['id']
            ) for order in send_order
        ]


class PerpetualDealsMissionProcessor(MissionMessageProcessor):
    send_topic = ProducerTopics.PERPETUAL_DEALS
    market_amount_asset_mapper = {}

    def get_amount_asset(self, market: str):
        if market not in self.market_amount_asset_mapper:
            self.market_amount_asset_mapper[market] = PerpetualMarketCache.get_balance_asset(market)
        return self.market_amount_asset_mapper[market]

    def _process_message(self, msg: ConsumerRecord) -> List[dict]:
        value = json.loads(msg.value)
        market = value["market"]
        amount_asset = self.get_amount_asset(market)
        ask_order = DealOrder(
            time=int(value["timestamp"]),
            market=market,
            user_id=int(value["ask_user_id"]),
            amount=Decimal(value["bid_stock"]),
            amount_asset=amount_asset
        )
        bid_order = DealOrder(
            time=int(value["timestamp"]),
            market=market,
            user_id=int(value["bid_user_id"]),
            amount=Decimal(value["ask_stock"]),
            amount_asset=amount_asset
        )
        send_order = (ask_order, bid_order)
        if ask_order.user_id == bid_order.user_id:
            merge_order = DealOrder(
                time=int(value["timestamp"]),
                market=market,
                user_id=ask_order.user_id,
                amount_asset=ask_order.amount_asset,
                amount=ask_order.amount + bid_order.amount
            )
            send_order = (merge_order,)
        return [
            dict(
                event_data=order.to_dict(),
                user_id=order.user_id,
                timestamp=order.time,
                biz_type="server_perpetual_kafka",
                biz_id=value['id']
            ) for order in send_order
        ]


class CashbackMessageProcessor(BusinessProcessor):
    """返现消息处理器基类"""

    trade_type: UserCashbackTradeFeeHistory.TradeType

    def __init__(self):
        self.target_user_map: Dict[int, datetime] = {}
        self.refresh_data()

    def refresh_data(self) -> None:
        """刷新数据"""
        current_app.logger.info(
            f'CashbackMessageProcessor refresh_data start for {self.trade_type}')
        db.session.rollback()
        self.target_user_map = self.get_target_users()
        self._refresh_market_data()
        current_app.logger.info(
            f'CashbackMessageProcessor refresh_data end for {self.trade_type}')

    def _refresh_market_data(self) -> None:
        """刷新市场数据，由子类实现"""
        raise NotImplementedError

    def process_messages(self, msgs: List[ConsumerRecord]) -> None:
        """处理消息"""
        fee_info = []
        for msg in msgs:
            data = json.loads(msg.value)
            market = data['market']

            if not self._should_process_market(market):
                continue

            fee_info.extend(self._process_message(data))

        if fee_info:
            self._save_fee_info(fee_info)

    def _should_process_market(self, market: str) -> bool:
        """判断是否应该处理该市场，由子类实现"""
        raise NotImplementedError

    def _process_message(self, data: dict) -> List[dict]:
        """处理消息"""
        result_map = dict()
        deal_time = timestamp_to_datetime(data['timestamp'])
        deal_id = data['id']
        market = data['market']

        # 处理ask费用
        if self._should_process_fee(data, 'ask', deal_time):
            try:
                fee_amount, fee_asset = self._calculate_fee(
                    data, 'ask', market)
                if fee_amount > 0:
                    res_key = (data['ask_user_id'], deal_id)
                    sell_fee = [market, fee_asset, fee_amount]
                    sell_info = {
                        'trade_user_id': data['ask_user_id'],
                        'trade_business_id': deal_id,
                        'trade_time': deal_time,
                        'fees': [sell_fee],
                    }
                    result_map[res_key] = sell_info
            except Exception as e:
                current_app.logger.error(
                    f'{self.trade_type}_ask get_fee_info_from_message {data} error: {e!r}')

        # 处理bid费用
        if self._should_process_fee(data, 'bid', deal_time):
            try:
                fee_amount, fee_asset = self._calculate_fee(
                    data, 'bid', market)
                if fee_amount > 0:
                    res_key = (data['bid_user_id'], deal_id)
                    buy_fee = [market, fee_asset, fee_amount]
                    if res_key not in result_map:
                        buy_info = {
                            'trade_user_id': data['bid_user_id'],
                            'trade_business_id': deal_id,
                            'trade_time': deal_time,
                            'fees': [buy_fee],
                        }
                        result_map[res_key] = buy_info
                    else:
                        result_map[res_key]["fees"].append(buy_fee)
            except Exception as e:
                current_app.logger.error(
                    f'{self.trade_type}_bid get_fee_info_from_message {data} error: {e!r}')

        return list(result_map.values())

    def _should_process_fee(self, data: dict, side: str, deal_time: datetime) -> bool:
        """判断是否应该处理费用"""
        user_id = data[f'{side}_user_id']
        return user_id in self.target_user_map and deal_time <= self.target_user_map[user_id]

    def _calculate_fee(self, data: dict, side: str, market: str) -> tuple[Decimal, str]:
        """计算费用，由子类实现"""
        raise NotImplementedError

    def _save_fee_info(self, fee_info_list: List[dict]) -> None:
        """保存费用信息"""
        miss_deal_fees = CashbackTradeFeeCollector.filter_by_user_trade_type_bus_id(
            fee_info_list,
            self.trade_type
        )
        if miss_deal_fees:
            current_app.logger.info(
                f'CashbackMessageProcessor save_fee_rows {len(miss_deal_fees)} {miss_deal_fees[:5]}')
            CashbackTradeFeeCollector.save_trade_fees(
                miss_deal_fees, self.trade_type)

    def get_target_users(self) -> Dict[int, datetime]:
        """获取目标用户"""
        spot_user_map, per_user_map = CashbackSettleHelper.get_need_collect_fee_users()
        return spot_user_map if self.trade_type == UserCashbackTradeFeeHistory.TradeType.SPOT else per_user_map

    def listen_for_update(self):
        """监听更新"""
        while True:
            time.sleep(300)
            try:
                self.refresh_data()
            except Exception as e:
                current_app.logger.error(
                    f"Processor for cashback-{self.trade_type}-related deals update failed: {e}")

    def start_loop(self):
        spawn_greenlet(self.listen_for_update)

    def is_first_run(self, topics) -> bool:
        """判断是否首次运行"""
        if not UserCashbackTradeFeeHistory.query.with_entities(UserCashbackTradeFeeHistory.id).first():
            return True
        return False


class SpotDealsCashbackProcessor(CashbackMessageProcessor):
    """现货返现消息处理器"""

    trade_type = UserCashbackTradeFeeHistory.TradeType.SPOT

    def __init__(self):
        super().__init__()
        self.amm_markets: Set[str] = set()

    def _refresh_market_data(self) -> None:
        """刷新市场数据"""
        self.amm_markets = self.get_amm_markets()

    def _should_process_market(self, market: str) -> bool:
        """判断是否应该处理该市场"""
        return market not in self.amm_markets

    def _calculate_fee(self, data: dict, side: str, market: str) -> tuple[Decimal, str]:
        """计算费用"""
        fee = data[f'{side}_fee']
        fee_asset = data[f'{side}_fee_asset']
        return quantize_amount(Decimal(fee), 8), fee_asset

    @classmethod
    def get_amm_markets(cls) -> Set[str]:
        """获取AMM市场列表"""
        amm_market_rows = AmmMarket.query.filter(
            AmmMarket.status == AmmMarket.Status.ONLINE,
        ).with_entities(
            AmmMarket.name,
        ).all()
        return {i.name for i in amm_market_rows}


class PerpetualDealsCashbackProcessor(CashbackMessageProcessor):
    """永续合约返现消息处理器"""

    trade_type = UserCashbackTradeFeeHistory.TradeType.PERPETUAL

    def __init__(self):
        super().__init__()
        self.online_market_info_map: Dict = {}
        self.offline_market_info_map: Dict = {}

    def _refresh_market_data(self) -> None:
        """刷新市场数据"""
        self.online_market_info_map = PerpetualMarketCache().read_aside()
        self.offline_market_info_map = PerpetualOfflineMarketCache().read_aside()

    def _should_process_market(self, market: str) -> bool:
        """判断是否应该处理该市场"""
        return bool(self.online_market_info_map.get(market) or self.offline_market_info_map.get(market))

    def _calculate_fee(self, data: dict, side: str, market: str) -> tuple[Decimal, str]:
        """计算费用"""
        fee = data[f'{side}_fee']
        fee_asset = data.get(f'{side}_fee_asset')
        market_info = self._get_market_info(market)
        return quantize_amount(Decimal(fee), 8), fee_asset or PerpetualMarketCache.get_fee_asset(market_info)

    def _get_market_info(self, market: str) -> dict:
        """获取市场信息"""
        return self.online_market_info_map.get(market) or self.offline_market_info_map.get(market) or {}
