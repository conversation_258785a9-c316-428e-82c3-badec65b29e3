# -*- coding: utf-8 -*-
from decimal import Decimal
from enum import Enum, auto, unique, IntEnum
from types import MappingProxyType
from typing import NamedTuple, Mapping, Union

from flask_babel import gettext as _

from app import config

# move these to configs???
LOGIN_TOKEN_SIZE = 64
OPERATION_TOKEN_SIZE = 64

LOGIN_FAILURE_LIMIT = 10, 3600
LOGIN_IP_LIMIT = 20, 600
LOGIN_STATE_DEFAULT_TTL = 86400 * 30
LOGIN_TOKEN_SECURITY_DURATION = 3600
WITHDRAWAL_SUSPENSION_AFTER_SECURITY_RESET = 86400
WITHDRAW_PASSWORD_FAILURE_LIMIT = 10, 86400
TRADE_PASSWORD_FAILURE_LIMIT = 10, 86400

EMAIL_TOKEN_TTL = 86400
EMAIL_TOKEN_SIZE = 64
EMAIL_TOKEN_COOLDOWN = 60

API_SECRET_KEY_SIZE = 48

CAPTCHA_SEND_LIMIT = 10, 600
SMS_MOBILE_BINDING_SEND_LIMIT = 10, 24 * 3600
CAPTCHA_VALIDATION_LIMIT = 20, 600

DEFAULT_DEPTH_LIMIT = 20

TRADE_PASSWORD_CHECK_TIME = 60 * 60 * 2

LOCAL_TRANSFER_MAX_PRECISION = 8

ASSET_CONVERSION_FEE_RATE = Decimal('0.05')

SERVER_ASSET_PRECISION_SAVED = 24
SERVER_ASSET_PRECISION_SHOWN = 8

MARKET_PRICE_DIGITS = 5

SUB_ACCOUNT_NUM_LIMIT = 20
SUB_ACCOUNT_MANAGER_NUM_LIMIT = 5
C_BOX_CODE_FAILURE_TIMES_LIMIT = 10
C_BOX_CODE_FAILURE_INTERVAL = 60 * 60 * 24
ADMIN_EXPORT_LIMIT = 10000 * 5

FUNDING_INTERVAL_TRIGGER_DYNAMIC_MINUTES = 4 * 60
FUNDING_INTERVAL_SILENT_PERIOD = 8 * 60 * 60
FUNDING_INTERVAL_HOLDING_PERIOD = 24 * 60 * 60
WITHDRAWAL_WIDE_LIMIT_MULTI = Decimal('1.01')

CAN_BIND_REFERRER_DAYS = 7


class Language(Enum):
    EN_US = DEFAULT = 'en_US'
    ZH_HANS_CN = 'zh_Hans_CN'
    ZH_HANT_HK = 'zh_Hant_HK'
    JA_JP = 'ja_JP'
    RU_KZ = 'ru_KZ'
    KO_KP = 'ko_KP'
    ID_ID = 'id_ID'
    ES_ES = 'es_ES'
    FA_IR = 'fa_IR'
    TR_TR = 'tr_TR'
    VI_VN = 'vi_VN'
    AR_AE = 'ar_AE'
    FR_FR = 'fr_FR'
    PT_PT = 'pt_PT'
    DE_DE = 'de_DE'
    TH_TH = 'th_TH'
    IT_IT = 'it_IT'
    PL_PL = 'pl_PL'


_LN = NamedTuple('LanguageNames',
                 [('english', str),
                  ('chinese', str),
                  ('native', str),
                  ("i18", str)], )
# https://en.wikipedia.org/wiki/List_of_language_names
LANGUAGE_NAMES: Mapping[Language, _LN] = MappingProxyType({
    Language.EN_US: _LN('English', '英语', 'English', _('英语')),
    Language.ZH_HANS_CN: _LN('Chinese Simplified', '简体中文', '简体中文', _('简体中文')),
    Language.ZH_HANT_HK: _LN('Chinese Traditional', '繁体中文', '繁體中文', _('繁体中文')),
    Language.JA_JP: _LN('Japanese', '日语', '日本語', _('日语')),
    Language.RU_KZ: _LN('Russian', '俄语', 'Русский', _('俄语')),
    Language.KO_KP: _LN('Korean', '韩语', '한국어', _('韩语')),
    Language.ID_ID: _LN('Indonesian', '印尼语', 'Bahasa Indonesia', _('印尼语')),
    Language.ES_ES: _LN('Spanish', '西班牙语', 'Español', _('西班牙语')),
    Language.FA_IR: _LN('Persian', '波斯语', 'فارسی', _('波斯语')),
    Language.TR_TR: _LN('Turkish', '土耳其语', 'Türk', _('土耳其语')),
    Language.VI_VN: _LN('Vietnamese', '越南语', 'Tiếng Việt', _('越南语')),
    Language.AR_AE: _LN('Arabic', '阿拉伯语', 'عربى', _('阿拉伯语')),
    Language.FR_FR: _LN('French', '法语', 'Français', _('法语')),
    Language.PT_PT: _LN('Portuguese', '葡萄牙语', 'Português', _('葡萄牙语')),
    Language.DE_DE: _LN('German', '德语', 'Deutsch', _('德语')),
    Language.TH_TH: _LN('Thai', '泰语', 'ไทย', _('泰语')),
    Language.IT_IT: _LN('Italian', '意大利语', 'Italiano', _('意大利语')),
    Language.PL_PL: _LN('Polish', '波兰语', 'Polski', _('波兰语')),
})


def language_en_names():
    return {lang: ln.english for lang, ln in LANGUAGE_NAMES.items()}


def language_cn_names():
    return {lang: ln.chinese for lang, ln in LANGUAGE_NAMES.items()}


def language_name_cn_names():
    return {lang.name: ln.chinese for lang, ln in LANGUAGE_NAMES.items()}


def language_native_names():
    return {lang: ln.native for lang, ln in LANGUAGE_NAMES.items()}


class Media(Enum):
    EMAIL = 'Email'
    TELEGRAM = 'Telegram'
    TWITTER = 'Twitter'
    FACEBOOK = 'Facebook'
    MEDIUM = 'Medium'
    REDDIT = 'Reddit'
    INSTAGRAM = 'Instagram'
    YOUTUBE = 'YouTube'
    NAVERBLOG = 'Naver Blog'
    LINE = 'Line'
    TIKTOK = 'Tiktok'
    VK = 'VK'
    NOTE = 'note'
    DISCORD = 'Discord'
    RUTUBE = 'Rutube'


class ImageType(str, Enum):
    PNG = 'png'
    JPEG = 'jpeg'
    BMP = 'bmp'
    GIF = 'gif'
    WEBP = 'webp'


_ImgInf = NamedTuple('ImageInfo',
                     [('mime_type', str),
                      ('ext', str)])
IMAGE_INFO: Mapping[Union[ImageType, str], _ImgInf] = MappingProxyType({
    ImageType.PNG: _ImgInf('image/png', 'png'),
    ImageType.JPEG: _ImgInf('image/jpeg', 'jpg'),
    ImageType.BMP: _ImgInf('image/bmp', 'bmp'),
    ImageType.GIF: _ImgInf('image/gif', 'gif'),
    ImageType.WEBP: _ImgInf('image/webp', 'webp'),
})


@unique
class CeleryQueues(str, Enum):
    EMAIL = 'email'
    PUSH = 'push'
    SMS = 'sms'
    PRICES = 'prices'
    EMAIL_PUSH = 'email_push'
    MARGIN = 'margin'
    CREDIT = 'credit'
    INVESTMENT = 'investment'
    PERPETUAL = 'perpetual'
    MARKET_MAKER = 'market_maker'
    PROFIT_LOSS = 'profit_loss'
    REPORT = 'report'
    DAILY = 'daily'
    ACTIVITY = 'activity'
    GIFT = 'gift'
    RISK_CONTROL = 'risk_control'
    REAL_TIME = 'real_time'
    STATISTIC = 'statistic'
    REALTIME_INCOME = 'realtime_income'
    VIABTC_POOL = 'viabtc_pool'
    WALLET = 'wallet'
    EXCHANGE = "exchange"
    STRATEGY = "strategy"
    USER_TAG = "user_tag"
    PRICE_NOTICE = "price_notice"
    PUSH_TAG = 'push_tag'
    PLEDGE = "pledge"
    P2P = "p2p"
    COPY_TRADING = "copy_trading"
    AI = "ai"
    TRANSLATION = "translation"
    VIP = "vip"
    THIRD_EXCHANGE = "third_exchange"
    REWARD_CENTER = "reward_center"

    def __str__(self):
        return self.value


class Platform(Enum):
    WEB = 'web'
    ANDROID = 'android'
    IOS = 'ios'


@unique
class PrecisionEnum(IntEnum):
    PRICE_PLACES = 12
    COIN_PLACES = 8
    RATE_PLACES = 4
    CASH_PLACES = 2


@unique
class ByteLengthEnum(IntEnum):
    BYTE_8 = 8
    BYTE_16 = 16
    BYTE_32 = 32
    BYTE_64 = 64
    BYTE_128 = 128
    BYTE_256 = 256
    BYTE_512 = 512
    BYTE_65536 = 65536


class LoginPwdLevel(Enum):
    LOW = auto()
    MIDDLE = auto()
    HIGH = auto()


SMALL_CURRENCY_SET = {
    "KRW", "COP", "LBP", "TZS", "IDR", "VND", "IQD", "BYN", "UAH", "NGN", "GHS", "MMK", "BND", "AMD", "MNT"
}


@unique
class CommonCurrency(Enum):
    """系统常用货币"""
    CNY = auto()
    USD = auto()
    EUR = auto()
    JPY = auto()
    KRW = auto()
    AED = auto()
    ARS = auto()
    AUD = auto()
    BDT = auto()
    BGN = auto()
    BHD = auto()
    BOB = auto()
    BRL = auto()
    BWP = auto()
    CAD = auto()
    CHF = auto()
    CLP = auto()
    COP = auto()
    CUP = auto()
    CZK = auto()
    DKK = auto()
    DZD = auto()
    GBP = auto()
    GTQ = auto()
    HKD = auto()
    HRK = auto()
    HUF = auto()
    IDR = auto()
    ILS = auto()
    INR = auto()
    ISK = auto()
    JMD = auto()
    JOD = auto()
    KES = auto()
    KWD = auto()
    KZT = auto()
    LAK = auto()
    LBP = auto()
    LKR = auto()
    MAD = auto()
    MNT = auto()
    MOP = auto()
    MUR = auto()
    MXN = auto()
    MYR = auto()
    NOK = auto()
    NZD = auto()
    OMR = auto()
    PAB = auto()
    PEN = auto()
    PHP = auto()
    PKR = auto()
    PLN = auto()
    PYG = auto()
    QAR = auto()
    RON = auto()
    RUB = auto()
    SAR = auto()
    SEK = auto()
    SGD = auto()
    THB = auto()
    TND = auto()
    TRY = auto()
    TWD = auto()
    TZS = auto()
    VND = auto()
    ZAR = auto()
    IQD = auto()
    BYN = auto()
    UAH = auto()
    NGN = auto()
    GHS = auto()
    MMK = auto()
    BND = auto()
    EGP = auto()
    AMD = auto()


class FiatPriceCurrency(Enum):
    """需要的汇率法币"""
    AED = auto()
    ALL = auto()
    ARE = auto()
    ARS = auto()
    AUD = auto()
    AUN = auto()
    AWG = auto()
    BAM = auto()
    BBD = auto()
    BDT = auto()
    BGN = auto()
    BHD = auto()
    BIF = auto()
    BND = auto()
    BOB = auto()
    BRI = auto()
    BRL = auto()
    BSD = auto()
    BWP = auto()
    BZD = auto()
    CAD = auto()
    CDF = auto()
    CHF = auto()
    CLP = auto()
    CNH = auto()
    CNY = auto()
    COP = auto()
    CRC = auto()
    CUP = auto()
    CZK = auto()
    DKK = auto()
    DOE = auto()
    DOP = auto()
    DZD = auto()
    EGP = auto()
    ETB = auto()
    EUR = auto()
    FJD = auto()
    GBP = auto()
    GHS = auto()
    GNF = auto()
    GTQ = auto()
    GYD = auto()
    HKD = auto()
    HNL = auto()
    HRK = auto()
    HTG = auto()
    HUF = auto()
    IDR = auto()
    ILS = auto()
    INR = auto()
    IQD = auto()
    ISK = auto()
    JMD = auto()
    JOD = auto()
    JPY = auto()
    KES = auto()
    KMF = auto()
    KRU = auto()
    KRW = auto()
    KWD = auto()
    KYD = auto()
    KZT = auto()
    LBP = auto()
    LKR = auto()
    LRD = auto()
    LSL = auto()
    LYD = auto()
    M5P = auto()
    MAD = auto()
    MAL = auto()
    MDL = auto()
    MGA = auto()
    MKD = auto()
    MMK = auto()
    MOP = auto()
    MRO = auto()
    MUR = auto()
    MVR = auto()
    MWK = auto()
    MXN = auto()
    MYR = auto()
    MZN = auto()
    NAD = auto()
    NBL = auto()
    NGN = auto()
    NIO = auto()
    NOK = auto()
    NPR = auto()
    NSO = auto()
    NZD = auto()
    OMR = auto()
    OSO = auto()
    PAB = auto()
    PEN = auto()
    PGK = auto()
    PHP = auto()
    PKR = auto()
    PLN = auto()
    PYG = auto()
    QAR = auto()
    RON = auto()
    RSD = auto()
    RUB = auto()
    RWF = auto()
    SAR = auto()
    SCR = auto()
    SDD = auto()
    SDG = auto()
    SEK = auto()
    AMD = auto()
    BYN = auto()
    LAK = auto()
    MNT = auto()
    SGD = auto()
    THB = auto()
    TND = auto()
    TRY = auto()
    TWD = auto()
    TZS = auto()
    UAH = auto()
    USD = auto()
    VND = auto()
    ZAR = auto()
    UYU = auto()


# 货币集合
Currency = Enum('Currency', list({e.name for c in [CommonCurrency, FiatPriceCurrency] for e in c}))


@unique
class EmailCodeType(Enum):
    # each value corresponds to a template and to maintain compatibility we
    # are currently using old names
    REGISTRATION = 'sign_up'
    SIGN_IN = 'sign_in'
    EMAIL_BINDING = 'add_email'
    EMAIL_EDIT = 'edit_old_email'
    EMAIL_RESET = 'edit_new_email'
    MOBILE_BINDING = 'add_mobile'
    MOBILE_EDIT = 'edit_mobile'
    MOBILE_UNBIND = 'unbind_mobile'
    TOTP_BINDING = 'add_totp'
    TOTP_EDIT = 'edit_totp'
    TOTP_UNBIND = 'unbind_totp'
    NON_LOGIN_PASSWORD_RESET = 'non_login_reset_login_password'
    NON_LOGIN_BIND_THIRD_PARTY_ACCOUNT = 'non_login_bind_third_party_account'
    BIND_THIRD_PARTY_ACCOUNT = 'bind_third_party_account'

    LOGIN_PASSWORD_RESET = 'reset_login_password'
    LOGIN_PASSWORD_SET = 'set_login_password'
    API_WITHDRAWAL_ADDRESS_ADDITION = 'add_api_withdraw_address'
    ANTI_PHISHING_CODE_ADD = 'add_anti_phishing_code'
    ANTI_PHISHING_CODE_EDIT = 'edit_anti_phishing_code'
    RESET_2FA = 'reset_2fa'
    SIGN_OFF = 'sign_off'
    NON_LOGIN_EMAIL_RESET = 'non_login_edit_new_email'
    SEND_C_BOX = 'send_c_box'
    UNFREEZE_ACCOUNT = 'unfreeze_account'

    BINDING_ADMIN_WEBAUTHN = 'bind_admin_webauthn'

    ADD_WITHDRAW_PASSWORD = 'add_withdraw_password'
    EDIT_WITHDRAW_PASSWORD = 'edit_withdraw_password'
    RESET_WITHDRAW_PASSWORD = 'reset_withdraw_password'
    RESET_WEBAUTHN = 'reset_webauthn'
    BIND_WEBAUTHN = 'bind_webauthn'
    UNBIND_WEBAUTHN = 'unbind_webauthn'

    TRADING_PASSWORD_ADDITION = 'add_trade_password'
    TRADING_PASSWORD_EDIT = 'edit_trade_password'
    TRADING_PASSWORD_REMOVE = 'remove_trade_password'


@unique
class MobileCodeType(Enum):
    LOGIN_WITH_OPERATION_TOKEN = 'sign_in_by_operate_token'

    LOGIN_PASSWORD_EDIT = 'edit_login_password'
    LOGIN_PASSWORD_SET = 'set_login_password'  # 针对第三方登录账号首次设置密码的情况

    NON_LOGIN_PASSWORD_RESET = 'non_login_edit_login_password'

    NON_LOGIN_BIND_THIRD_PARTY_ACCOUNT = 'non_login_bind_third_party_account'

    BIND_THIRD_PARTY_ACCOUNT = 'bind_third_party_account'

    MOBILE_BINDING = 'add_mobile'
    MOBILE_EDIT = 'edit_old_mobile'
    MOBILE_RESET = 'edit_new_mobile'
    MOBILE_UNBIND = 'unbind_mobile'
    TOTP_BINDING = 'add_totp'
    TOTP_EDIT = 'edit_totp'
    TOTP_UNBIND = 'unbind_totp'
    SIGN_OFF = 'sign_off'

    SECURITY_RESET = 'security_reset'

    EMAIL_BINDING = 'add_email'
    EMAIL_EDIT = 'edit_email'

    FLAT_COIN = 'flat_coin'

    WITHDRAWAL_ADDRESS_ADDITION = 'add_withdraw_address'
    API_WITHDRAWAL_ADDRESS_ADDITION = 'add_api_withdraw_address'
    WITHDRAWAL_APPLICATION = 'add_withdraw'

    ADD_WITHDRAW_PASSWORD = 'add_withdraw_password'
    EDIT_WITHDRAW_PASSWORD = 'edit_withdraw_password'
    RESET_WITHDRAW_PASSWORD = 'reset_withdraw_password'

    API_AUTH_ADDITION = 'add_user_auth'
    API_AUTH_EDIT = 'edit_user_auth'
    API_AUTH_QUERY = 'get_user_auth'
    API_AUTH_DELETION = 'delete_user_auth'
    API_AUTH_EXTEND = 'extend_user_auth'

    TRADING_PASSWORD_ADDITION = 'add_trade_password'
    TRADING_PASSWORD_EDIT = 'edit_trade_password'
    TRADING_PASSWORD_REMOVE = 'remove_trade_password'
    ORDER_PLACEMENT = 'placing_order'

    VIP_PURCHASE = 'purchase_vip'

    SEND_C_BOX = 'send_c_box'
    SUB_ACCOUNT_REGISTRATION = 'sub_account_register'
    SUB_ACCOUNT_PASSWORD_RESET = 'sub_account_reset_password'
    SUB_ACCOUNT_DELETION = 'sub_account_delete'
    SUB_ACCOUNT_BIND_MANAGER = 'sub_account_bind_manager'

    ADD_WITHDRAWAL_APPROVER = 'add_withdrawal_approver'
    DELETE_WITHDRAWAL_APPROVER = 'delete_withdrawal_approver'

    BIND_WEBAUTHN = 'bind_webauthn'
    UNBIND_WEBAUTHN = 'unbind_webauthn'
    RESET_WEBAUTHN = 'reset_webauthn'

    P2P_FINISH = "p2p_finish"
    P2P_ADD_USER_PAY_CHANNEL = "p2p_add_user_pay_channel"
    P2P_EDIT_USER_PAY_CHANNEL = "p2p_edit_user_pay_channel"
    P2P_DELETE_USER_PAY_CHANNEL = "p2p_delete_user_pay_channel"


@unique
class TwoFAType(Enum):
    NONE = ''
    MOBILE = 'mobile'
    TOTP = 'totp'
    WEBAUTHN = 'webauthn'


@unique
class MarketStatusType(IntEnum):
    MARKET_STATUS_START = 0
    MARKET_STATUS_STOP = 1
    MARKET_STATUS_PROTECTION = 2
    MARKET_STATUS_CAUTION_START = 3
    MARKET_STATUS_CAUTION_STOP_CANCEL = 4


@unique
class OrderSideType(IntEnum):
    SELL = 1
    BUY = 2


class OrderType(Enum):
    # 市价单
    MARKET_ORDER_TYPE = 'market'
    # 计划市价单
    STOP_MARKET_ORDER_TYPE = 'stop_market'
    # 限价单
    LIMIT_ORDER_TYPE = 'limit'
    # 计划委托单
    STOP_LIMIT_ORDER_TYPE = 'stop_limit'


@unique
class StopOrderType(IntEnum):
    LAST_PRICE = 1
    INDEX_PRICE = 2
    SIGN_PRICE = 3


@unique
class PricingBasisType(IntEnum):
    LAST_PRICE = 1
    SIGN_PRICE = 3


@unique
class OrderIntType(IntEnum):
    LIMIT = 1
    MARKET = 2


@unique
class StopOrderIntType(IntEnum):
    STOP_LIMIT = 1
    STOP_MARKET = 2


@unique
class OrderTopicEvent(IntEnum):
    ORDER_EVENT_PUT = 1
    ORDER_EVENT_UPDATE = 2
    ORDER_EVENT_FINISH = 3


@unique
class StopTopicEvent(IntEnum):
    STOP_EVENT_PUT = 1
    STOP_EVENT_ACTIVE = 2
    STOP_EVENT_CANCEL = 3


class OrderStatusType(Enum):
    PART_DEAL = 'part_deal'
    NOT_DEAL = 'not_deal'
    DONE = 'done'
    CANCEL = 'cancel'


class StopOrderStatusType(Enum):
    ACTIVE = 'active'
    CANCEL = 'cancel'
    FAIL = 'fail'


@unique
class StopOrderStatusIntType(IntEnum):
    ACTIVE = 1
    FAIL = 2
    CANCEL = 3


class OrderBusinessType(Enum):
    NORMAL_BUSINESS_TYPE = 'normal'
    MARGIN_BUSINESS_TYPE = 'margin'
    PERPETUAL_BUSINESS_TYPE = 'perpetual'


@unique
class WebPushChannelType(Enum):
    """1～1000是通知，1001～2000是数据，目前只涉及通知"""
    NORMAL = 1  # 普通全站通知
    MAINTAIN = 2  # 维护通知
    PLEDGE = 3  # 质押数据 单独通知，方便APP解析
    REWARD_CENTER = 4  # 奖励中心
    API_MAINTAIN = 101  # API用户临时维护通知


@unique
class WebPushMessageType(Enum):
    """用于特定业务场景前端根据类型做下一步操作"""
    EXCHANGE = 'exchange'  # 兑换，刷新页面
    MAINTAIN = 'maintain'  # 临时维护，查看维护详情
    PLEDGE = 'pledge'  # 质押，展示数据
    REWARD_CENTER = 'reward_center'  # 奖励中心


@unique
class BalanceBusiness(Enum):
    DEPOSIT = 'deposit'
    WITHDRAWAL = 'withdraw'
    WITHDRAWAL_FEE = 'withdraw_fee'
    DEPOSIT_CANCELLATION = 'cancel_deposit'
    WITHDRAWAL_CANCELLATION = 'cancel_withdraw'
    WITHDRAWAL_FEE_CANCELLATION = 'cancel_withdraw_fee'
    TRADING = 'trade'
    TRADING_FEE = 'trade_fee'
    SYSTEM = 'system'
    GIFT = 'gift'
    GIFT_REVOKE = 'gift_revoke'
    COUPON = 'coupon'
    COUPON_RECYCLE = 'coupon_recycle'
    TRADE_GIFT_COUPON = "trade_gift_coupon"
    CASHBACK_FEE = "cashback_fee"
    PERPETUAL_SUBSIDY_COUPON = "perpetual_subsidy_coupon"
    COPY_TRADING_EXPERIENCE_FEE = "ct_experience_fee"
    COPY_TRADING_EXPERIENCE_FEE_RECYCLE = "ct_experience_fee_recycle"  # 合约business不超过30字符
    REFERRAL = 'refer'
    OTC_TRANSFER = 'otc_transfer'
    MARGIN_TRANSFER = 'margin_transfer'
    MARGIN_LOAN = 'margin_loan'
    MARGIN_REPAYMENT = 'margin_flat'
    MARGIN_LIQUIDATION = 'burst'
    # 爆仓费
    MARGIN_LIQUIDATION_FEE = 'margin_liquidation_fee'
    # ----------------------------
    # 实时收支结算相关类型
    REALTIME_WALLET_TX_FEE = "realtime_wallet_tx_fee"
    REALTIME_ASSET_CONVERSION = "realtime_asset_conversion"
    REALTIME_CREDIT_INTEREST = "realtime_credit_interest"
    REALTIME_MARGIN_INTEREST = "realtime_margin_interest"
    REALTIME_WITHDRAW_FEE = "realtime_withdraw_fee"
    REALTIME_EXCHANGE_FEE = "realtime_exchange_fee"
    REALTIME_SIGN_OFF_USER_BALANCE = "realtime_sign_off_user_balance"
    REALTIME_CLEANED_BALANCE = "realtime_cleaned_balance"
    REALTIME_AMM_TRADE_PAY_FEE = 'realtime_amm_trade_pay_fee'
    REALTIME_PLEDGE_INTEREST = 'realtime_pledge_interest'
    REALTIME_PRE_TRADING_FEE = 'realtime_pre_trading_fee'  # 预测市场的赎回、交割手续费，不是预测市场的交易手续费
    REALTIME_P2P_ORDER_FEE = 'realtime_p2p_order_fee'

    REALTIME_TRANSFER = "realtime_transfer"
    # ----------------------------

    ASSET_CONVERSION = 'conversion'
    SUB_ACCOUNT_TRANSFER = 'sub_account_transfer'
    RED_PACKET = 'send_red_packet'
    RED_PACKET_GRABBING = 'grab_red_packet'
    RED_PACKET_REFUND = 'return_red_packet'

    MAKER_CASH_BACK = 'maker_cash_back'

    FUTURE_TRANSFER = 'future_transfer'
    FUTURE_LOAN = 'future_loan'
    FUTURE_REPAYMENT = 'future_flat'
    FUTURE_LIQUIDATION = 'future_burst'
    FUTURE_DELIVERY = 'future_delivery'
    FUTURE_DELIVERY_EXCHANGE = 'future_delivery_exchange'

    OPTION_ISSUANCE = 'option_issue'
    OPTION_REDEMPTION = 'option_redeem'
    OPTION_DELIVERY = 'option_delivery'

    INVESTMENT_TRANSFER = 'investment_transfer'             # 活期理财划转
    INVESTMENT_INTEREST = 'investment_interest'             # 理财收益
    INVESTMENT_INC_INTEREST = 'investment_inc_interest'     # 理财加息系统账号扣减

    DEX_NODE_VOTING = 'dex_node_vote'
    LOSS_BALANCE = 'loss_balance'

    PERPETUAL_TRANSFER_IN = 'contract_transfer_in'
    PERPETUAL_TRANSFER_OUT = 'contract_transfer_out'
    PERPETUAL_TRANSFER = 'contract_transfer'
    PERPETUAL_CLEARING_FUNDING = 'clearing_funding'
    PERPETUAL_CLEARING_CLOSE = 'clearing_close'

    CREDIT = 'credit'
    CREDIT_REPAYMENT = 'flat_credit'

    # 划转至小币账号
    SMALL_COIN_TRANSFER = 'small_coin_transfer'
    # 小币账户兑换之后划出
    INCOME_TOTAL_TRANSFER = 'income_total_transfer'
    # 回购划转
    BUYBACK_TRANSFER = 'buyback_transfer'
    # 收入划转, 回购之后
    INCOME_TO_ADMIN_TRANSFER = 'income_to_admin_transfer'
    # 回购兑换，指定账户回购CET
    BUYBACK_EXCHANGE = 'buyback_exchange'
    # 币种利息收入
    ASSET_AUTO_REWARD = 'asset_auto_reward'

    # 杠杆保险基金划转
    MARGIN_INSURANCE_TRANSFER = 'margin_insurance_transfer'
    # 合约保险基金划转
    PERPETUAL_INSURANCE_TRANSFER = 'perpetual_insurance_transfer'
    # 保险基金兑换账户划转到admin
    INSURANCE_TO_ADMIN_TRANSFER = "insurance_to_admin_transfer"
    # 注销账户划转到admin
    SIGNED_OFF_USER_TO_ADMIN_TRANSFER = "signed_off_user_to_admin"
    # 小额资产清理划转到admin
    CLEANED_BALANCE_TO_ADMIN_TRANSFER = "cleaned_balance_to_admin"
    # 兑换订单划转
    EXCHANGE_ORDER_TRANSFER = "exchange_order_transfer"
    # 定投划转
    AUTO_INVEST_TRANSFER = "auto_invest_transfer"
    # 经纪商返佣
    BROKER_REFERRAL = "broker_referral"
    # 普通返佣
    NORMAL_REFERRAL = "normal_referral"
    # 大使返佣
    AMBASSADOR_REFERRAL = "ambassador_referral"
    EXCHANGE_ORDER_TRANSFER_FEE = "exchange_order_transfer_fee"
    # 现货网格划转
    SPOT_GRID_TRANSFER = "spot_grid_transfer"

    # AMM
    ADD_LIQUIDITY = 'add_liquidity'
    REMOVE_LIQUIDITY = 'remove_liquidity'
    AMM_FEE_TRANSFER = 'amm_fee_transfer'

    # mining activity
    MINING_ACTIVITY = 'mining_activity'
    LAUNCH_POOL_MINING = 'launch_pool_mining'
    # 充值找回资产变更
    ABNORMAL_DEPOSIT_APPLICATION = 'abnormal_deposit_apply'
    IEO_ACTIVITY_SUBSCRIBE = 'ieo_activity_subscribe'
    IEO_ACTIVITY_CANCEL = 'ieo_activity_cancel'
    IEO_ACTIVITY_LOTTERY = 'ieo_activity_lottery'

    DIBS_ACTIVITY_SUBSCRIBE = 'dibs_activity_subscribe'
    DIBS_ACTIVITY_CANCEL = 'dibs_activity_cancel'
    DIBS_ACTIVITY_LOTTERY = 'dibs_activity_lottery'

    # 质押相关划转
    PLEDGE_LOAN_ASSET_ADD = "pledge_loan_asset_add"
    PLEDGE_ASSET_LOCK = "pledge_asset_lock"
    PLEDGE_ASSET_RELEASE = "pledge_asset_release"
    PLEDGE_REPAY = "pledge_repay"
    PLEDGE_LIQ = "pledge_liq"
    PLEDGE_LIQ_FEE = "pledge_liq_fee"

    PRE_TRADING_ISSUE = 'pre_trading_issue'
    PRE_TRADING_REDEMPTION = 'pre_trading_redemption'
    PRE_TRADING_POS_SETTLE = 'pre_trading_pos_settle'
    PRE_TRADING_ISSUE_SETTLE = 'pre_trading_issue_settle'

    # 跟单相关划转
    COPY_TRADING_TRANSFER = "copy_trading_transfer"
    COPY_PROFIT_SETTLEMENT = "copy_profit_settlement"

    # 链上质押相关划转
    STAKING_ADD = "staking_add"
    STAKING_REMOVE = "staking_remove"
    STAKING_INCOME = "staking_income"

    # 商务大使相关
    BUS_AMB_LOAN = "bus_amb_loan"
    BUS_AMB_LOAN_REPAY = "bus_amb_loan_repay"
    BUS_USER_REFER = "bus_user_refer"

    # 权益中心相关
    EQUITY_CASHBACK = "equity_cashback"
    EQUITY_AIRDROP = "equity_airdrop"

    # p2p
    P2P_LOCK = "p2p_lock"
    P2P_UNLOCK = "p2p_unlock"
    P2P_SUB = "p2p_sub"
    P2P_ADD = "p2p_add"

    P2P_MARGIN_PAYMENT = "p2p_margin_payment"
    P2P_MARGIN_REFUND = "p2p_margin_refund"
    P2P_MARGIN_SYS_ADD = "p2p_margin_sys_add"
    P2P_MARGIN_SYS_SUB = "p2p_margin_sys_sub"
    P2P_MARGIN_DEDUCT = "p2p_margin_deduct"

    # 大使激励包
    AMBASSADOR_PACKAGE_SETTLEMENT = "ambassador_package_settlement"

    # comment tip 打赏
    COMMENT_TIP_IN = "comment_tip_in"
    COMMENT_TIP_OUT = "comment_tip_out"

    SPOT_OPERATION = (
        DEPOSIT,
        WITHDRAWAL,
        WITHDRAWAL_FEE,
        DEPOSIT_CANCELLATION,
        WITHDRAWAL_CANCELLATION,
        WITHDRAWAL_FEE_CANCELLATION,
        TRADING,
        SYSTEM,
        GIFT,
        GIFT_REVOKE,
        REFERRAL,
        OTC_TRANSFER,
        MARGIN_TRANSFER,
        FUTURE_TRANSFER,
        ASSET_CONVERSION,
        SUB_ACCOUNT_TRANSFER,
        DEX_NODE_VOTING,
        INVESTMENT_TRANSFER,
        FUTURE_DELIVERY,
        PERPETUAL_TRANSFER,
        RED_PACKET,
        RED_PACKET_GRABBING,
        RED_PACKET_REFUND,
        ADD_LIQUIDITY,
        REMOVE_LIQUIDITY,
        AMM_FEE_TRANSFER,
        MAKER_CASH_BACK,
        ABNORMAL_DEPOSIT_APPLICATION,
        EXCHANGE_ORDER_TRANSFER,
        BROKER_REFERRAL,
        NORMAL_REFERRAL,
        AMBASSADOR_REFERRAL,
        EXCHANGE_ORDER_TRANSFER_FEE,
        SPOT_GRID_TRANSFER,
        TRADE_GIFT_COUPON,
        SIGNED_OFF_USER_TO_ADMIN_TRANSFER,
        INVESTMENT_INC_INTEREST,  # 理财加息系统账号扣减，
        CASHBACK_FEE,  # 手续费返现
        PERPETUAL_SUBSIDY_COUPON,
        PLEDGE_LOAN_ASSET_ADD,
        PLEDGE_ASSET_LOCK,
        PLEDGE_ASSET_RELEASE,
        PLEDGE_REPAY,
        COPY_TRADING_TRANSFER,
        COPY_PROFIT_SETTLEMENT,
        PRE_TRADING_ISSUE,
        PRE_TRADING_REDEMPTION,
        PRE_TRADING_POS_SETTLE,
        PRE_TRADING_ISSUE_SETTLE,
        LAUNCH_POOL_MINING,
        AUTO_INVEST_TRANSFER,
        STAKING_ADD,
        STAKING_REMOVE,
        STAKING_INCOME,
        P2P_SUB,
        P2P_ADD,
        COUPON,
        COUPON_RECYCLE,
        COPY_TRADING_EXPERIENCE_FEE,
        COPY_TRADING_EXPERIENCE_FEE_RECYCLE,
        BROKER_REFERRAL,
        INSURANCE_TO_ADMIN_TRANSFER
    )

    @classmethod
    def build_operation(cls, account_type: 'AccountBalanceType', trans_map: dict | None = None):
        margin_operation = {
            cls.MARGIN_TRANSFER,
            cls.TRADING,
            cls.MARGIN_LOAN,
            cls.MARGIN_REPAYMENT,
            cls.MARGIN_LIQUIDATION
        }
        investment_operation = {
            cls.INVESTMENT_INTEREST,
            cls.INVESTMENT_TRANSFER,
            cls.INVESTMENT_INC_INTEREST,
        }

        pledge_operation = {
            cls.TRADING,
            cls.PLEDGE_LOAN_ASSET_ADD,
            cls.PLEDGE_ASSET_LOCK,
            cls.PLEDGE_ASSET_RELEASE,
            cls.PLEDGE_REPAY,
            cls.PLEDGE_LIQ,
            cls.PLEDGE_LIQ_FEE,
        }

        staking_operation = {
            cls.STAKING_ADD,
            cls.STAKING_REMOVE,
        }

        _m = {
            AccountBalanceType.MARGIN: margin_operation,
            AccountBalanceType.INVESTMENT: investment_operation,
            AccountBalanceType.STAKING: staking_operation,
            AccountBalanceType.PLEDGE: pledge_operation,
        }

        if account_type not in (
            AccountBalanceType.SPOT,
            AccountBalanceType.STAKING,
            AccountBalanceType.PLEDGE,
            AccountBalanceType.INVESTMENT,
            AccountBalanceType.MARGIN,
        ):
            raise ValueError
        trans_m = trans_map or balance_business_trans_map
        spot_types = set(trans_m.keys()) - set(margin_operation) - \
            set(investment_operation) - \
            set(staking_operation) - \
            set(pledge_operation)
        # auto add
        spot_types -= {cls.TRADING_FEE}
        spot_types |= {
            cls.TRADING,
            cls.BROKER_REFERRAL,
            cls.COPY_TRADING_EXPERIENCE_FEE,
            cls.COPY_TRADING_EXPERIENCE_FEE_RECYCLE,
            cls.INVESTMENT_INC_INTEREST,
            cls.MARGIN_TRANSFER,
            cls.INVESTMENT_TRANSFER,
            cls.PLEDGE_ASSET_LOCK,
            cls.PLEDGE_ASSET_RELEASE,
            cls.PLEDGE_LOAN_ASSET_ADD,
            cls.PLEDGE_REPAY,
            cls.STAKING_ADD,
            cls.STAKING_REMOVE
        }


        _m[AccountBalanceType.SPOT] = spot_types
        return tuple(sorted(list(_m[account_type]), key=lambda x: list(cls.__members__.values()).index(x)))

    @classmethod
    def build_trans_map(cls, types, trans_map: dict | None = None) -> dict:
        trans_m = trans_map or balance_business_trans_map

        t_map = {
            _type.value: trans_m.get(_type, _type.value)
            for _type in types
        }
        c = dict()
        for key, value in t_map.items():
            c.setdefault(value, 0)
            c[value] += 1
        # 这里主要兼容重复翻译的类型，方便辨识
        return {
            k: f'{v}:{k}' if c[v] > 1 else v
            for k, v in t_map.items()
        }


class BalanceLockBusiness(Enum):
    """ 锁定业务类型 """
    ALL_LOCK = "0"
    P2P_ORDER = "1"


balance_business_trans_map = {
    BalanceBusiness.DEPOSIT: _('充值'),
    BalanceBusiness.WITHDRAWAL: _('提现'),
    BalanceBusiness.WITHDRAWAL_FEE: _('提现手续费'),
    BalanceBusiness.DEPOSIT_CANCELLATION: _('取消充值'),
    BalanceBusiness.WITHDRAWAL_CANCELLATION: _('取消提现'),
    BalanceBusiness.WITHDRAWAL_FEE_CANCELLATION: _('提现手续费退回'),
    BalanceBusiness.TRADING: _('交易'),
    BalanceBusiness.TRADING_FEE: _('交易费'),
    BalanceBusiness.SYSTEM: _('系统'),
    BalanceBusiness.ABNORMAL_DEPOSIT_APPLICATION: _('系统'),
    BalanceBusiness.GIFT: _('赠送'),
    BalanceBusiness.GIFT_REVOKE: _('奖励撤销'),
    BalanceBusiness.REFERRAL: _('返佣'),
    BalanceBusiness.OTC_TRANSFER: _('OTC划转'),
    BalanceBusiness.MARGIN_TRANSFER: _('杠杆划转'),
    BalanceBusiness.MARGIN_LOAN: _('杠杆借币'),
    BalanceBusiness.MARGIN_REPAYMENT: _('杠杆还币'),
    BalanceBusiness.MARGIN_LIQUIDATION: _('强制平仓'),
    BalanceBusiness.MARGIN_LIQUIDATION_FEE: _("强平清算费"),
    BalanceBusiness.ASSET_CONVERSION: _('小额兑换'),
    BalanceBusiness.SUB_ACCOUNT_TRANSFER: _('子账号划转'),
    BalanceBusiness.RED_PACKET: _('发C-Box'),
    BalanceBusiness.RED_PACKET_GRABBING: _('领C-Box'),
    BalanceBusiness.RED_PACKET_REFUND: _('C-Box退还'),
    BalanceBusiness.COUPON: _('体验金'),
    BalanceBusiness.COUPON_RECYCLE: _('回收体验金'),
    BalanceBusiness.TRADE_GIFT_COUPON: _("交易赠金券"),
    BalanceBusiness.CASHBACK_FEE: _("手续费返现券"),
    BalanceBusiness.PERPETUAL_SUBSIDY_COUPON: _("合约补贴金"),
    BalanceBusiness.MAKER_CASH_BACK: _('maker返现'),
    BalanceBusiness.FUTURE_TRANSFER: _('期货划转'),
    BalanceBusiness.FUTURE_LOAN: _('期货借币'),
    BalanceBusiness.FUTURE_REPAYMENT: _('期货还币'),
    BalanceBusiness.FUTURE_LIQUIDATION: _('期货强平'),
    BalanceBusiness.FUTURE_DELIVERY: _('期货交割'),
    BalanceBusiness.FUTURE_DELIVERY_EXCHANGE: _('交割兑换'),
    BalanceBusiness.OPTION_ISSUANCE: _('发行'),
    BalanceBusiness.OPTION_REDEMPTION: _('赎回'),
    BalanceBusiness.OPTION_DELIVERY: _('交割结算'),
    BalanceBusiness.INVESTMENT_TRANSFER: _('活期理财划转'),
    BalanceBusiness.INVESTMENT_INTEREST: _('利息收入'),
    BalanceBusiness.INVESTMENT_INC_INTEREST: _('加息收入'),
    BalanceBusiness.DEX_NODE_VOTING: _('dex_node_vote'),
    BalanceBusiness.LOSS_BALANCE: _('loss_balance'),
    BalanceBusiness.PERPETUAL_TRANSFER_IN: _('划入'),
    BalanceBusiness.PERPETUAL_TRANSFER_OUT: _('划出'),
    BalanceBusiness.PERPETUAL_TRANSFER: _('合约划转'),
    BalanceBusiness.PERPETUAL_CLEARING_FUNDING: _('结算盈亏'),
    BalanceBusiness.PERPETUAL_CLEARING_CLOSE: _('清仓盈亏'),
    BalanceBusiness.CREDIT: _('授信'),
    BalanceBusiness.CREDIT_REPAYMENT: _('授信还币'),
    # 划转至小币账号
    BalanceBusiness.SMALL_COIN_TRANSFER: _('small_coin_transfer'),
    # 小币账户兑换之后划出
    BalanceBusiness.INCOME_TOTAL_TRANSFER: _('income_total_transfer'),
    # 回购划转
    BalanceBusiness.BUYBACK_TRANSFER: _('buyback_transfer'),
    # 收入划转, 回购之后
    BalanceBusiness.INCOME_TO_ADMIN_TRANSFER: _('income_to_admin_transfer'),
    # cet兑换
    BalanceBusiness.BUYBACK_EXCHANGE: _("buyback_exchange"),
    # 划转
    BalanceBusiness.MARGIN_INSURANCE_TRANSFER: _("insurance_transfer"),
    BalanceBusiness.PERPETUAL_INSURANCE_TRANSFER: _("insurance_transfer"),
    # 保险基金兑换账户划转到admin
    BalanceBusiness.INSURANCE_TO_ADMIN_TRANSFER: _("insurance_to_admin_transfer"),
    # 注销账户划转到admin
    BalanceBusiness.SIGNED_OFF_USER_TO_ADMIN_TRANSFER: _("注销账号划转到admin"),
    # AMM
    BalanceBusiness.ADD_LIQUIDITY: _('增加流动性'),
    BalanceBusiness.REMOVE_LIQUIDITY: _('提取流动性'),
    BalanceBusiness.AMM_FEE_TRANSFER: _('AMM手续费注入'),
    # mining activity
    BalanceBusiness.MINING_ACTIVITY: _('mining_activity'),
    # ieo
    BalanceBusiness.IEO_ACTIVITY_SUBSCRIBE: _('申购'),
    BalanceBusiness.IEO_ACTIVITY_CANCEL: _('退款'),
    BalanceBusiness.IEO_ACTIVITY_LOTTERY: _('中签'),
    BalanceBusiness.EXCHANGE_ORDER_TRANSFER: _("兑换"),
    BalanceBusiness.AUTO_INVEST_TRANSFER: _("定投"),
    BalanceBusiness.BROKER_REFERRAL: _("经纪商返佣"),
    BalanceBusiness.NORMAL_REFERRAL: _("普通返佣"),
    BalanceBusiness.AMBASSADOR_REFERRAL: _("大使返佣"),
    BalanceBusiness.EXCHANGE_ORDER_TRANSFER_FEE: _("兑换手续费"),
    BalanceBusiness.SPOT_GRID_TRANSFER: _("现货网格"),
    # Dibs
    BalanceBusiness.DIBS_ACTIVITY_SUBSCRIBE: _('Dibs申购'),
    BalanceBusiness.DIBS_ACTIVITY_CANCEL: _('Dibs退款'),
    BalanceBusiness.DIBS_ACTIVITY_LOTTERY: _('Dibs中签'),
    # pledge
    BalanceBusiness.PLEDGE_LOAN_ASSET_ADD: _("借币(借贷)"),
    BalanceBusiness.PLEDGE_ASSET_LOCK: _("锁定质押资产"),
    BalanceBusiness.PLEDGE_ASSET_RELEASE: _("释放质押资产"),
    BalanceBusiness.PLEDGE_REPAY: _("还币(借贷)"),
    BalanceBusiness.PLEDGE_LIQ: _("强制平仓"),
    BalanceBusiness.PLEDGE_LIQ_FEE: _("强平清算费"),
    BalanceBusiness.BUS_AMB_LOAN: _("预付金"),
    BalanceBusiness.BUS_AMB_LOAN_REPAY: _("预付金还款"),
    BalanceBusiness.BUS_USER_REFER: _("返佣"),
    # copy trading
    BalanceBusiness.COPY_TRADING_TRANSFER: _("跟单资金划转"),
    BalanceBusiness.COPY_PROFIT_SETTLEMENT: _("跟单分润结算"),
    BalanceBusiness.PRE_TRADING_ISSUE: _("预测发行"),
    BalanceBusiness.PRE_TRADING_REDEMPTION: _("预测赎回"),
    BalanceBusiness.PRE_TRADING_ISSUE_SETTLE: _("发行交割"),
    BalanceBusiness.PRE_TRADING_POS_SETTLE: _("持仓交割"),
    # launch pool
    BalanceBusiness.LAUNCH_POOL_MINING: _("挖矿收益分发"),
    # reward center
    BalanceBusiness.EQUITY_AIRDROP: _("空投"),
    BalanceBusiness.EQUITY_CASHBACK: _("手续费返现"),
    # staking
    BalanceBusiness.STAKING_ADD: _("质押"),
    BalanceBusiness.STAKING_REMOVE: _("赎回"),
    BalanceBusiness.STAKING_INCOME: _("质押收益"),
    # p2p
    BalanceBusiness.P2P_LOCK: _("p2p锁定订单资产"),
    BalanceBusiness.P2P_UNLOCK: _("p2p取消订单"),
    BalanceBusiness.P2P_SUB: _("p2p卖出"),
    BalanceBusiness.P2P_ADD: _("p2p买入"),
    BalanceBusiness.P2P_MARGIN_PAYMENT: _("商家保证金缴纳"),
    BalanceBusiness.P2P_MARGIN_REFUND: _("商家保证金返还"),
    BalanceBusiness.P2P_MARGIN_SYS_ADD: _("商家保证金缴纳"),
    BalanceBusiness.P2P_MARGIN_SYS_SUB: _("商家保证金返还"),
    BalanceBusiness.P2P_MARGIN_DEDUCT: _("P2P交易赔付"),
    BalanceBusiness.AMBASSADOR_PACKAGE_SETTLEMENT: _("大使激励"),
    # comment_tip
    BalanceBusiness.COMMENT_TIP_IN: _("收到礼物(打赏)"),
    BalanceBusiness.COMMENT_TIP_OUT: _("发出礼物(打赏)"),
}


def get_balance_business_display(business: str):
    business_str_list = [v.value for v in BalanceBusiness]
    business_display = business
    if business in business_str_list:
        balance_enum = BalanceBusiness(business)
        if balance_enum in balance_business_trans_map:
            business_display = balance_business_trans_map[balance_enum]
    return business_display


FIXED_ASSET_PRICES = MappingProxyType({  # to USD
    'USDC': Decimal(1),
    'TUSD': Decimal(1),
    'GUSD': Decimal(1),
    'PAX': Decimal(1),
    'USDH': Decimal(1),
    'VIAX': Decimal('0.042')
})


class TradeType(Enum):
    MAKER = 'maker'
    TAKER = 'taker'


@unique
class TradeIntType(IntEnum):
    MAKER = 1
    TAKER = 2


class TradeBusinessType(Enum):
    SPOT = 'spot'
    PERPETUAL = 'perpetual'


@unique
class PositionSide(IntEnum):
    SHORT = 1
    LONG = 2


@unique
class FundingRateType(IntEnum):
    PAY = 1
    TAKE = 2


@unique
class PositionType(IntEnum):
    ISOLATED_MARGIN = 1
    CROSS_MARGIN = 2


_('逐仓')
_('全仓')


class PositionMarginType(Enum):
    MARGIN_INCREASE = _('追加保证金')
    MARGIN_DECREASE = _('减少保证金')


class PositionDealType(Enum):
    TYPE_POSITION_OPEN = _('开仓')
    TYPE_POSITION_INCREASE = _('加仓')
    TYPE_POSITION_DECREASE = _('减仓')
    TYPE_POSITION_CLOSE = _('平仓')
    TYPE_POSITION_SYSTEM_LIQ = _('强制减仓')
    TYPE_POSITION_LIQ = _('强平')
    TYPE_POSITION_ADL = _('自动减仓')


@unique
class PositionDealIntType(IntEnum):
    TYPE_POSITION_OPEN = 1
    TYPE_POSITION_INCREASE = 2
    TYPE_POSITION_DECREASE = 3
    TYPE_POSITION_CLOSE = 4
    TYPE_POSITION_SYSTEM_LIQ = 5
    TYPE_POSITION_LIQ = 6
    TYPE_POSITION_ADL = 7
    TYPE_TAKE_PROFIT = 8
    TYPE_STOP_LOSS = 9
    TYPE_MARKET_CLOSE_ALL = 10
    TYPE_CLOSE_ALL = 11


@unique
class PositionEventType(IntEnum):
    # kafka topic: perpetual_sys_positions
    STOP_LOSS = 2
    TAKE_PROFIT = 3
    MARKET_CLOSE_ALL = 4
    CLOSE_ALL = 5  # 强平


position_margin_type_map = {
    1: _('追加保证金'),
    2: _('减少保证金'),
    3: _('调整杠杆'),
    4: {
        0: _('模式调整'),
        1: _('逐仓'),
        2: _('全仓')
    },
    5: {
        1: _('逐仓结算'),
        2: _('全仓结算')
    }

}

position_deal_type_map = {
    1: _('开仓'),
    2: _('加仓'),
    3: _('减仓'),
    4: _('平仓'),
    5: _('减仓（强制减仓）'),
    6: _('强平'),
    7: _('自动减仓'),
    8: _('减仓（系统强平）'),
    9: _('减仓（自动减仓）'),
    10: _('减仓（止盈）'),
    11: _('减仓（止损）'),
    12: _('平仓（系统强平）'),
    13: _('平仓（自动减仓）'),
    14: _('平仓（止盈）'),
    15: _('平仓（止损）'),
    16: _('自动减仓开仓')
}

position_side_map = {
    1: _('做空'),
    2: _('做多')
}

perpetual_direction_map = {
    1: _('上涨'),
    2: _('下跌')
}

adl_order_side_map = {
    1: _('购买'),
    2: _('出售'),
}


class MessageTitle(Enum):
    DEPOSIT = _('充值成功')
    GIFT = _('赠送成功')
    WITHDRAW = _('提现审核通过')
    WITHDRAW_EMAIL_CHECK = _('提现需邮件确认')
    MODIFY_PASSWORD = _('修改密码')
    ADD_MOBILE = _('手机号绑定成功')
    ADD_WITHDRAW_PASSWORD = _('提现密码设置成功')
    EDIT_TRADE_PASSWORD = _('修改交易密码成功')
    ADD_EMAIL = _('绑定邮箱')
    ADD_TOTP_AUTH = _('TOTP绑定成功')
    EDIT_EMAIL = _('修改邮箱')
    EDIT_MOBILE = _('手机号更换成功')
    RESET_MOBILE = _('手机号解绑成功')
    EDIT_WITHDRAW_PASSWORD = _('提现密码修改成功')
    EDIT_TOTP_AUTH = _('修改谷歌验证器')
    OPERATE_PROTECT = _('二次验证')
    IDENTITY_STATUS = _('认证状态')
    PROMOTION_GIFT = _('恭喜')
    ACC_PROJECT_WIN = _('申购中签通知')
    MARGIN_LIQUIDATION_WARNING = _('强平预警')
    FUTURE_BURST_WARN = _('强平预警')
    MARGIN_LIQUIDATION = _('强平通知')
    FUTURE_BURST = _('强平通知')
    MARGIN_GIFT = _('margin gift')
    CONTRACT_LIQUIDATE = _('强平通知')
    PERPETUAL_LIQUIDATE = _('合约强制平仓通知')
    PERPETUAL_POSITION_REDUCE = _('合约降档减仓通知')
    LIQUIDATE_ALERT = _('强平预警通知')
    ADL_NOTE = _('自动减仓通知')
    PERPETUAL_ADL_NOTICE = _('合约自动减仓通知')
    LOAN_ORDER_EXPIRED = _('杠杆还币提醒')
    LOAN_ORDER_FORCE_FLAT = _('强制还币通知')
    LOAN_ORDER_RENEW_FAILED = _('续借失败')
    CET_NODE_LOTTERY_NOTICE = _('预选节点投票抽奖')
    RED_PACKET_TO_ACCOUNT = _('红包到账通知')
    C_BOX_TO_ACCOUNT = _('C-Box到账通知')
    RED_PACKET_REFUND = _('红包退还')
    RED_PACKET_REFUND_NOTICE = _('红包退还通知')
    C_BOX_REFUND_NOTICE = _('C-Box退还通知')
    ACTIVITY_REWORDS = _('奖励发放')
    ACTIVITY_AYA_TRADE = _('活动邀请')
    ACTIVITY_OKEX_REWORDS = _('奖励发放')
    AIRDROP_REWORDS_SUCCESS = _('空投中奖通知')
    AIRDROP_REWORDS_FAIL = _('空投未中奖通知')
    AIRDROP_REWORDS_RESULT = _('参与空投结果通知')
    DIBS_REWORDS_SUCCESS = _('Dibs中奖通知')
    DIBS_REWORDS_FAIL = _('Dibs未中奖通知')
    SOTOSHI_BID_SUCCESS = _('稀有聪竞标成功')
    IEO_REWORDS_SUCCESS = _('已中签！')
    IEO_REWORDS_FAIL = _('很遗憾，未中签')
    ACTIVITY_MINING_REWORDS = _('奖励发放')
    SECURITY_RESET_SUCCESS = _('重置安全项成功')
    SECURITY_RESET_FAIL = _('重置安全项失败')
    RESET_WITHDRAW_PASSWORD_RESET_SUCCESS = _('提现密码重置成功')
    RESET_WITHDRAW_PASSWORD_RESET_FAIL = _('提现密码重置失败')
    TAKE_PROFIT_NOTICE = _('止盈止损通知')
    TAKE_PROFIT_FAIL_NOTICE = _('合约止盈失败通知')
    STOP_LOSS_FAIL_NOTICE = _('合约止损失败通知')
    POSITION_CLOSE_FAIL_NOTICE = _('合约平仓失败通知')
    ABNORMAL_DEPOSIT_APPLICATION_ADDITIONAL_INFO = _("资料补充通知：充值未到账找回申请")
    ABNORMAL_DEPOSIT_APPLICATION_FEE = _("手续费补充通知：充值未到账找回申请")
    ABNORMAL_DEPOSIT_APPLICATION_REJECTED = _("充值未到账找回申请审核失败")
    ABNORMAL_DEPOSIT_APPLICATION_SUCCESS = _("充值未到账找回成功")
    CREDIT_RISK_WITHDRAW_CLOSE = _("提现功能关闭通知")
    CREDIT_RISK_TRADE_CLOSE = _("交易功能关闭通知")
    OPENING_MARGIN_FUNCTION = _("开通杠杆交易")
    OPENING_PERPETUAL_TRADING = _("开通合约交易")
    COUPON_QUALIFIED = _("卡券领取成功")
    COUPON_EXCHANGE = _("卡券兑换成功")
    COUPON_UNLOCK = _("卡券资金已满足赠送标准")
    TRADING_GIFT_COUPON_UNLOCK = _("卡券资金到账成功")
    COUPON_USED = _("卡券已使用")
    COPY_TRADING_EXPERIENCE_FEE_SEND_COUPON = _("合约跟单体验金券待领取")
    COPY_TRADING_EXPERIENCE_FEE_COUPON_QUALIFIED = _("合约跟单体验金券领取成功")
    COPY_TRADING_EXPERIENCE_FEE_COUPON_EXCHANGE = _("恭喜兑换合约跟单体验金券")
    VIP_UPGRADE_SEND_COUPON = _("VIP+%(value)s升级券待领取")
    VIP_UPGRADE_EXCHANGE = _("恭喜兑换VIP升级券")
    VIP_UPGRADE_USE = _("VIP升级券领取成功")
    VIP_LEVEL_CHANGE = _("VIP等级变更")
    SIGN_IN_UNUSUAL = _("异地登录提醒")
    KYC_RESULT = _("实名认证结果")
    KYC_PRO_PASS = _("KYC高级认证通过")
    KYC_PRO_FAIL = _("KYC高级认证不通过")
    RISK_SCREEN_FAIL = _("【重要】请尽快提币或处置资产")
    RESET_EMAIL_PASS = _("邮箱重置成功")
    RESET_TOTP_PASS = _("TOTP重置成功")
    RESET_WEBAUTHN_PASS = _("重置通行密钥成功")
    RESET_PASSWORD = _("密码重置提醒")
    POTENTIAL_USER = _("恭喜你！获得CoinEx白银大使资格！")
    AMBASSADOR_BUSINESS_TRACE = _("【CoinEx大使】50%永久返佣，USDT日结")
    TRADE_RANK_ACTIVITY_GIFT = _("交易排位赛奖励发放")
    TAX_EXPORT_SUCCESS = _("账户数据已生成")
    FIFTH_ULT_BOX_QUALIFY = _("恭喜获得终极盲盒开箱机会！")
    FIFTH_ANN_BOX_QUALIFY = _("恭喜获得周年盲盒开箱机会！")
    SPOT_GRID_TRIGGERED = _("%(market)s现货市场已达网格触发价")
    SPOT_GRID_TAKE_PROFIT_TRIGGERED = _("%(market)s现货市场已达网格止盈价")
    SPOT_GRID_STOP_LOSS_TRIGGERED = _("%(market)s现货市场已达网格止损价")
    SPOT_GRID_EXCEED_PRICE_RANGE = _("%(market)s现货市价超出网格价格区间")
    SPOT_GRID_EXCEED_RECOMMEND_RUN_DAYS = _("%(market)s现货网格策略已超时")
    TAKE_PROFIT_SUCCESS = _("合约止盈成功通知")
    TAKE_PROFIT_FAIL = _("合约止盈部分失败通知")
    STOP_LOSS_SUCCESS = _("合约止损成功通知")
    STOP_LOSS_FAIL = _("合约止损部分失败通知")
    SUB_ACCOUNT_BIND_MANAGER = _("子账号授权成功")
    SUB_ACCOUNT_CANCEL_MANAGER = _("子账号授权解除")
    AUTO_INVEST_DEAL_ALL = _("自动定投全部成交通知")
    AUTO_INVEST_DEAL_PARTIAL = _("自动定投部分成交通知")
    AUTO_INVEST_DEAL_FAILED = _("自动定投未成交通知")
    AUTO_INVEST_PROFIT_AMOUNT = _("自动定投到达盈利目标通知")
    AUTO_INVEST_PROFIT_RATE = _("自动定投到达盈利目标通知")
    AUTO_INVEST_FAILED = _("自动定投失败通知")
    AUTO_INVEST_PAUSED = _("自动定投暂停通知")
    AUTO_INVEST_CLOSED = _("自动定投关闭通知")
    PLEDGE_LIQ_WARNING = _("借贷补仓通知")
    PLEDGE_LIQ = _("借贷强平通知")
    ACTIVITY_REWARD_RECEIPT = _("活动奖励到账通知")
    PUSH_MSG_TEMPLATE = "_"  # 手动站内信推送template，占位符 无内容
    OPEN_POSITION_TP_SL_FAIL = _('开仓止盈止损设置失效提醒')
    # p2p
    P2P_REMIND_RECEIVE_ORDER = _("P2P接单提醒")
    P2P_MERCHANT_REJECT_ORDER = _("P2P拒绝接单提醒")
    P2P_RECEIVED_ORDER = _("P2P订单已确认")
    P2P_BUYER_PAYMENT_DEADLINE = _("P2P订单付款提醒")
    P2P_PAID_WAIT_RELEASE_ASSET = _("P2P订单放币提醒")
    P2P_FINISHED_ORDER = _("P2P订单已完成")
    P2P_ORDER_CANCELLED = _("P2P订单已取消")
    P2P_CREATED_COMPLAINT = _("P2P订单发起申诉")
    P2P_UPDATED_COMPLAINT = _("P2P申诉订单进度更新")
    P2P_OPERATION_COMPLAINT = _("P2P订单申诉已处理")
    P2P_CANCELED_COMPLAINT = _("P2P订单申诉已取消")
    P2P_FINISHED_COMPLAINT = _("P2P订单申诉已完成")
    P2P_RESTART_COMPLAINT = _("P2P订单申诉重新开启")
    P2P_AUTO_OFFLINE_ADVERTISING = _("P2P广告单被下架")
    P2P_ADD_PAYMENT_CHANNEL = _("P2P新增收款方式")
    P2P_PAYMENT_CHANNEL_INVALID = _("P2P支付渠道失效")
    # p2p商家活动
    P2P_MER_ACT_APPLY_SUCCESS = _("P2P商家活动：报名提交成功")
    P2P_MER_ACT_AUDIT_SUCCESS = _("P2P商家活动：报名成功")
    P2P_MER_ACT_AUDIT_FAIL = _("P2P商家活动：审核未通过")
    P2P_MER_ACT_AUDIT_CANCEL = _("P2P商家活动：参与资格取消")
    P2P_MER_ACT_REWARD_SUCCESS = _("P2P商家活动：得奖通知")
    P2P_MER_ACT_REWARD_FREEZE = _("P2P商家活动：奖励冻结通知")
    P2P_MER_ACT_REWARD_CANCEL = _("P2P商家活动：撤消活动奖励")
    P2P_MER_ACT_REWARD_FAIL = _("P2P商家活动：奖励发放失败")
    P2P_MER_ACT_POINT_CANCEL = _("P2P商家活动：排名取消通知")

    # p2p保证金
    P2P_MARGIN_PAYMENT = _("P2P商家保证金补充通知")
    P2P_MARGIN_SHORTFALL = _("P2P商家权限受限通知")
    P2P_MARGIN_CHANGE = _("P2P商家保证金调整通知")
    P2P_MER_CANCEL = _("P2P商家身份取消通知")

    P2P_MER_COMPENSATION = _("商家保证金扣除通知")
    P2P_MARGIN_PENALTY = _("商家违规扣款通知")
    P2P_MARGIN_EXCESS_REFUND = _("商家保证金返还通知")
    P2P_USER_COMPENSATION = _("P2P交易赔付到账通知")

    # staking
    STAKING_REMOVE_SUCCESS = _("成功赎回%(asset)s")
    STAKING_REMOVE_NOTICE = _("质押生效提醒")

    PRE_TRADING_SETTLEMENT = _("预测市场 %(asset)s已完成交割")
    # 跟单
    COPY_TRADER_APPLY_SUCCESS = _("恭喜成为跟单交易员")
    COPY_TRADER_OPEN_POSITION = _("%(market)s %(leverage)s 带单开仓成功")
    COPY_TRADER_CLOSE_POSITION = _("%(market)s %(leverage)s 带单平仓成功")
    COPY_TRADER_ADJUST_PROFIT_SHARE_RATE = _("交易员调整分润比例")
    COPY_TRADER_LIQUIDATION = _("%(market)s %(leverage)s 带单强平通知")
    COPY_TRADER_TAKE_PROFIT = _("%(market)s %(leverage)s 带单止盈触发")
    COPY_TRADER_STOP_LOSS = _("%(market)s %(leverage)s 带单止损触发")
    COPY_TRADER_TRADE_FINISHED = _("结束带单")
    COPY_TRADER_TRADE_SYSTEM_FINISHED = _("交易员身份取消通知")
    COPY_TRADER_TRADE_PRE_CANCEL = _("交易员身份取消提醒")
    COPY_FOLLOWER_OPEN_POSITION = _("%(market)s %(leverage)s 跟单开仓成功")
    COPY_FOLLOWER_OPEN_POSITION_FAILED = _("%(market)s %(leverage)s 跟单失败")
    COPY_FOLLOWER_CLOSE_POSITION = _("%(market)s %(leverage)s 跟单交易平仓成功")
    COPY_FOLLOWER_LIQUIDATION = _("%(market)s %(leverage)s 跟单强平通知")
    COPY_FOLLOWER_PROFIT_SHARE = _("分润结算")
    COPY_FOLLOWER_PROFIT_SHARE_RATE_CHANGED = _("交易员调整分润比例")
    COPY_FOLLOWER_SUGGEST_ADD_MARGIN = _("交易员转入保证金")
    COPY_FOLLOWER_TAKE_PROFIT = _("跟单止盈触发")
    COPY_FOLLOWER_STOP_LOSS = _("跟单止损触发")
    COPY_FOLLOWER_FOLLOW_FINISHED = _("结束跟单关系")
    #
    LAUNCH_MINING_REWARD = _("你的%(stake_assets)s挖矿币种奖励已到账")
    # 权益中心
    CASHBACK_EQUITY_EXPIRING = _("手续费返现权益即将到期")
    CASHBACK_EQUITY_DELIVERY = _("手续费返现权益发放")
    AIRDROP_EQUITY_DELIVERY = _("空投奖励发放")
    NEW_MISSION_NOTICE = _("请查收新任务")
    MISSION_REWARD_SENT = _("任务奖励发放")
    MISSION_EXPIRING = _("任务即将到期")

    # 币种评论
    COMMENT_BAN_FOR_DAYS = _("账号禁言%(count)s天")
    COMMENT_BAN_FOR_EVER = _("账号永久禁言")
    COMMENT_WARNING = "%(title)s"  # 直接展示admin填写的内容，无需翻译
    # 大使激励包
    MEET_REQUIREMENT_PACKAGE = _("大使激励包奖励到账提醒")
    NOT_MEET_REQUIREMENT_PACKAGE = _("大使激励包考核未达标提醒")

    COMMENT_TIPS = _("收到%(count)s份礼物")


class MessageContent(Enum):
    DEPOSIT = _('你于%(create_time)s充值的%(amount)s已到账。')
    ON_CHAIN_DEPOSIT = _('你于%(create_time)s充值的%(amount)s已到账。\n交易ID：%(tx_id)s')
    LOCAL_DEPOSIT = _('你于%(create_time)s充值的%(amount)s已到账。')
    ON_CHAIN_WITHDRAW = _(
        "你于%(create_time)s提交的提现申请已通过，提现金额为%(amount)s。\n交易ID：%(tx_id)s\n\n"
        "如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
    )
    LOCAL_WITHDRAW = _(
        "你于%(create_time)s提交的提现申请已通过，提现金额为%(amount)s。\n\n"
        "如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
    )
    COIN_WITHDRAW = _(
        '你于%(create_time)s提交了提现申请，金额为%(amount)s。现已审核汇出。')
    CASH_WITHDRAW = _(
        '你于%(create_time)s在CoinEx发生提现，金额为%(amount)s，现已审核，一个工作日内到账。')
    EDIT_LOGIN_PASSWORD = _(
        '你的登录密码已修改成功。')
    EDIT_TRADE_PASSWORD = _('你的资金密码已修改成功。')
    RESET_LOGIN_PASSWORD = _(
        '你的登录密码已重置成功。')
    RESET_TRADE_PASSWORD = _(
        '你的资金密码已重置成功。')

    ADD_MOBILE = _('你的手机号已绑定成功。')
    ADD_MOBILE_SUCCESS = _(
        "你已成功绑定手机号（%(mobile)s），后续可使用该手机号进行手机验证。\n\n"
        "如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
    )
    ADD_WITHDRAW_PASSWORD_SUCCESS = _(
        "你在CoinEx的提现密码设置成功。\n\n"
        "如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
    )
    EDIT_TRADE_PASSWORD_SUCCESS = _(
        "你在CoinEx的交易密码修改成功。\n\n"
        "如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
    )
    ADD_EMAIL = _('你的邮箱已绑定成功。')
    ADD_TOTP_AUTH = _('你的谷歌验证器已绑定成功。')
    ADD_TOTP_AUTH_SUCCESS = _(
        "你已成功绑定TOTP，后续可使用TOTP验证器进行验证。\n\n"
        "如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
    )
    RESET_2FA = _(
        'You had reset %(reset_type)s for your CoinEx account.')
    RESET_EMAIL = _(
        'You had reset email to your CoinEx account.')
    RESET_2FA_FAIL = _(
        'Your reset security application has been rejected. Reason is %(reason)s.')

    EDIT_MOBILE = _('你的手机号已修改成功。')
    EDIT_MOBILE_SUCCESS = _(
        "你已成功更换绑定的手机号，后续需使用新手机号（%(mobile)s）进行验证。为了保障你的账号安全，24小时内禁止提现。\n\n"
        "如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
    )
    RESET_MOBILE_SUCCESS = _(
        "你绑定的手机号已解绑，请尽快绑定新手机号或其他安全验证方式，保证账号安全。"
    )
    EDIT_WITHDRAW_PASSWORD_SUCCESS = _(
        "你在CoinEx的提现密码修改成功。为了保障你的账户安全，24小时内禁止提现。\n\n"
        "若非你本人操作，请立即重置密码或禁用账户，并尽快向CoinEx客服提交工单。"
    )
    EDIT_EMAIL = _('你的邮箱已修改成功。')
    EDIT_TOTP_AUTH = _(
        '你的谷歌验证器已修改成功。')
    EDIT_PROTECT_TYPE = _(
        '你的二次验证方式已修改成功。')

    JUNIOR_IDENTITY_PASS = _(
        '你的账户已通过实名认证。')
    SENIOR_IDENTITY_PASS = _(
        '你的账户已通过高级实名认证。')
    GIFT_CET = _(
        '恭喜你在CoinEx的「 1.5亿随机送 回馈老用户 」活动中获得%(amount)s枚CET，请在钱包中查看。')
    PROMOTION_GIFT = _(
        '你在CoinEx的活动中获得%(amount)s个%(coin_type)s，请在钱包中查看。')

    ACC_PROJECT_WIN = _(
        '你的申购编号（%(lottery_tickets)s）成功中签，%(amount)s %(coin_type)s 已到账。')
    MARGIN_LIQUIDATION_WARNING = _(
        '你的 %(market)s 杠杆交易风险率已低于 %(risk_limit)s，请注意仓位控制以免发生强平。')
    FUTURE_BURST_WARN = _(
        '你的 %(market)s 期货交易风险率已低于 %(risk_limit)s，请注意仓位控制以免发生强平。')

    MARGIN_LIQUIDATION = _('因行情剧烈波动，你的%(market)s 杠杆交易，触发强平')
    FUTURE_BURST = _('因行情剧烈波动，你的 %(market)s 期货交易，触发强平')
    MARGIN_GIFT = _(
        '恭喜你获得CoinEx送出的 %(amount)s BCH杠杆体验新手奖励，可到币币钱包中查收。')
    CONTRACT_LIQUIDATE = _(
        '你在CoinEx的账户(%(user_name)s)中的保证金余额低于所需维持保证金， '
        '你的%(market)s合约持仓已被强制平仓，触发强平价格为%(liq_price)s。')
    PERPETUAL_LIQUIDATE = _(
        "你的%(market_type)s%(market)s持仓已被强制平仓。\n强制平仓均价：%(liq_price)s\n强制平仓原因：保证金余额低于所需维持保证金\n\n"
        "请注意，如果你设置了止盈止损平仓，可能会由于行情剧烈波动而触发失败。\n"
        "风险提示：合约交易具有高风险性，请你密切关注市场动向。CoinEx对加密货币价格波动可能造成的任何损失不承担任何责任。"
    )
    LIQUIDATE_ALERT = _(
        '你在CoinEx的账户(%(user_name)s)中的%(market)s合约的爆仓风险率已达到%(risk)s%%，为避免强制平仓，请你立即补充保证金或降低你的持仓。')
    ADL_NOTE = _(
        "你在CoinEx的账户(%(user_name)s)触发自动减仓， 你的%(market)s合约持仓以%(liq_price)s价格平仓，平仓数量为%(liq_amount)s。")
    PERPETUAL_ADL_NOTICE = _(
        "你的%(market_type)s%(market)s持仓已触发自动减仓流程。\n"
        "减仓价格：%(liq_price)s\n减仓数量：%(liq_amount)s\n"
        "触发原因：对手盘发生穿仓，且保险基金余额不足\n\n"
        "风险提示：合约交易具有高风险性，请你密切关注市场动向。CoinEx对加密货币价格波动可能造成的任何损失不承担任何责任。"
    )
    POSITION_REDUCE = _(
        "很遗憾地通知你，由于你在CoinEx的账户(%(user_name)s)中的保证金余额低于所需维持保证金， "
        "你的%(market)s合约持仓触发了降档减仓，减仓数量为%(amount)s。"
    )
    PERPETUAL_POSITION_REDUCE = _(
        "你的%(market_type)s%(market)s持仓已触发降档减仓。\n"
        "触发时的标记价格：%(sign_price)s\n"
        "降档减仓数量：%(amount)s\n"
        "触发原因：保证金余额低于所需维持保证金\n\n"
        "风险提示：合约交易具有高风险性，请你密切关注市场动向。CoinEx对加密货币价格波动可能造成的任何损失不承担任何责任。"
    )
    LOAN_ORDER_EXPIRED = _(
        '你的%(coin_type)s借币订单将于%(expired_time)s到期，'
        '请及时归还借币或开启自动续期以免发生强制还币。')
    LOAN_ORDER_FORCE_FLAT = _(
        '你的%(coin_type)s借币订单已在%(expired_time)s到期，触发强制还币。')
    LOAN_ORDER_FORCE_FLAT_BY_LIQUIDATION = _(
        "你的%(market)s杠杆账户已触发强制还币。\n强制还币金额：%(amount)s\n强制还币原因：风险率已达%(risk_rate)s"
    )
    LOAN_ORDER_FORCE_FLAT_BY_NOT_ENOUGH = _(
        "你的%(market)s杠杆账户已触发强制还币。\n强制还币金额：%(amount)s\n强制还币原因：借币池余额不足"
    )
    LOAN_ORDER_FORCE_FLAT_BY_NOT_FLAT = _(
        "你的%(market)s杠杆账户已触发强制还币。\n强制还币金额：%(amount)s\n强制还币原因：到期未续借或续借失败"
    )
    LOAN_ORDER_RENEW_FAILED = _(
        '你的%(coin_type)s借币订单由于续借失败，请于%(expired_time)s前归还借币以免发生强制还币。')

    CET_NODE_LOTTERY_NOTICE = _(
        'CoinEx Chain启动节点预选计划，用CET投票，支持你心仪的节点，获得抽取大奖的机会。'
        'BTC数字货币大礼包，最新高配Iphone 11，CoinEx专属周边任你拿。'
        '机不可失，动动手指头就有机会抱走千万壕礼，快来参与吧！'
        '活动详情（www.coinex.com/activity/node-vote）')

    RED_PACKET_TO_ACCOUNT = _('恭喜你于%(time)s抢到红包，到账 %(amount)s %(coin_type)s。')
    C_BOX_TO_ACCOUNT = _('恭喜，手气不错，你已成功领取一个C-Box。\n领取时间：%(time)s\n到账金额：%(amount)s %(coin_type)s。')

    RED_PACKET_REFUND = _(
        '你发的红包48小时未领取完，已退还%(amount)s %(coin_type)s。')
    RED_PACKET_REFUND_NOTICE = _(
        "你发出的红包已退还到账 %(amount)s %(coin_type)s。\n红包发出时间：%(time)s\n红包退还原因：发出的红包未在48小时内被领取完"
    )
    C_BOX_EXPIRE_REFUND_NOTICE = _(
        "你发出的C-Box，数字货币已原路退回。\n发出时间：%(time)s\n"
        "退还到账：%(amount)s %(coin_type)s。\n退还原因：发出的C-Box未在48小时内被领取完"
    )  # 兼容历史数据保留，最新的文案使用C_BOX_EXPIRE_REFUND_NOTICE_NEW
    C_BOX_EXPIRE_REFUND_NOTICE_NEW = _(
        "你发出的C-Box，数字货币已原路退回。\n发出时间：%(time)s\n"
        "退还到账：%(amount)s %(coin_type)s。\n退还原因：发出的C-Box未在%(valid_days)s天内被领取完"
    )
    C_BOX_NOT_REGISTER_REFUND_NOTICE = _(
        "你发出的C-Box，数字货币已原路退回。\n发出时间：%(time)s\n"
        "退还到账：%(amount)s %(coin_type)s。\n退还原因：领取C-Box的用户未在领取7天内注册"
    )

    ACTIVITY_AYA_TRADE = _('“买入AYA，瓜分5000USDT大奖”活动正在火热进行中，'
                           'CoinEx邀请你参加此次活动。活动期间内，'
                           '净买入（总买入量-总卖出量）数量大于100枚AYA即可参与排名，'
                           '即有机会领取最高250USDT的现金大奖，高达300份奖励等你来领。'
                           '了解更多：https://announcement.coinex.com/hc/articles/360051182091')

    ACTIVITY_OKEX_NEWBIE_REWORDS = _(
        "恭喜你获得CoinEx送出的5 USDT OKEx用户专享福利奖励，可到现货账户中查看。")
    ACTIVITY_OKEX_REFERAL_REWORDS = _(
        "恭喜你获得CoinEx送出的200 CET OKEx用户专享福利奖励（额外返佣），可到现货账户中查看。")

    ACTIVITY_AYA_NEWBIE_REWORDS = _(
        "恭喜你获得CoinEx送出的5 USDT AYA活动奖励，可到现货账户中查看。")

    ACTIVITY_AYA_REFERAL_REWORDS = _(
        "恭喜你获得CoinEx送出的100 CET AYA活动奖励（额外返佣），可到现货账户中查看。")
    ACTIVITY_AYA_PUSH = _(
        "CoinEx新人礼，注册瓜分10000 "
        "USDT”活动正在火热进行中，CoinEx邀请你参加此次活动。"
        "11月6日至11月26日（UTC+8）期间，CoinEx新注册用户只要完成规定的任务，"
        "将立即获得5 USDT的奖励，先到先得，送完为止。"
        "同时平台所有用户都可以邀请新用户来参与本次活动，除了获取邀请返佣之外，"
        "每邀请一名成功完成任务并获得奖励的新用户，将额外获得CET奖励。"
        "更多活动详情，请前往公告查看。")

    ACTIVITY_CFX_DEPOSIT = _(
        "“CoinEx上线Conflux（CFX），充值瓜分5000 "
        "USDT”活动正在火热进行中，CoinEx邀请你参加此次活动。"
        "CoinEx将于2020年11月9日（UTC+8）正式开放CFX充值，"
        "并于11月10日（UTC+8）支持CFX/BTC、CFX/USDT交易对。"
        "为了庆祝CFX上线，CoinEx将开启充值交易瓜分活动。"
        "更多活动详情，请前往公告查看。")
    ACTIVITY_PERPETUAL_REWORDS = _(
        "恭喜你获得CoinEx送出的%(amount)s %(asset)s 合约体验奖励，可到现货账户中查看"
    )
    TAKE_PROFIT_NOTICE = _(
        "你的账号%(account)s在%(market)s合约的止盈设置已被触发，且平仓成功。请知晓。"
    )
    TAKE_PROFIT_SUB_ACCOUNT_NOTICE = _(
        "你的子账号%(account)s在%(market)s合约的止盈设置已被触发，且平仓成功。请知晓。"
    )
    STOP_LOSS_NOTICE = _(
        "你的账号%(account)s在%(market)s合约的止损设置已被触发，且平仓成功。请知晓。"
    )
    STOP_LOSS_SUB_ACCOUNT_NOTICE = _(
        "你的子账号%(account)s在%(market)s合约的止损设置已被触发，且平仓成功。请知晓。"
    )
    TAKE_PROFIT_FAIL_NOTICE = _(
        "你的账号%(account)s在合约%(market)s中的止盈设置未能全部平仓，剩余仓位%(amount)s "
        "%(asset)s，且原止盈设置已失效。请知晓。"
    )
    TAKE_PROFIT_FAIL_SUB_ACCOUNT_NOTICE = _(
        "你的子账号%(account)s在%(market)s合约中的止盈设置未能全部平仓，剩余仓位%(amount)s "
        "%(asset)s，且原止盈设置已失效。请知晓。"
    )
    STOP_LOSS_FAIL_NOTICE = _(
        "你的账号%(account)s在合约%(market)s止损设置未能全部平仓，剩余仓位%(amount)s "
        "%(asset)s，且原止损设置已失效。请知晓"
    )
    STOP_LOSS_FAIL_SUB_ACCOUNT_NOTICE = _(
        "你的子账号%(account)s在%(market)s合约中的止损设置未能全部平仓，剩余仓位%(amount)s "
        "%(asset)s，且原止损设置已失效。请知晓。"
    )

    POSITION_CLOSE_FAIL_NOTICE = _(
        "你的账号%(account)s在合约%(market)s中的一键全平操作未能全部平仓，剩余仓位%(amount)s %(asset)s。请知晓。"
    )

    POSITION_CLOSE_FAIL_SUB_ACCOUNT_NOTICE = _(
        "你的子账号%(account)s在%(market)s合约中的一键全平操作未能全部平仓，剩余仓位%(amount)s %(asset)s。请知晓。"
    )

    POSITION_CLOSE_ALL_FAIL_NOTICE = _(
        "你的账号%(account)s在合约%(market)s中的一键平仓操作未能全部平仓，剩余仓位%(amount)s %(asset)s。请知晓。"
    )

    POSITION_CLOSE_ALL_FAIL_SUB_ACCOUNT_NOTICE = _(
        "你的子账号%(account)s在%(market)s合约中的一键平仓操作未能全部平仓，剩余仓位%(amount)s %(asset)s。请知晓。"
    )
    ACTIVITY_PERPETUAL_TRADING = _(
        "CoinEx合约交易活动火热进行中，首次体验合约交易将获得CET奖励，"
        "参与交易比赛，更有机会瓜分20000USDT的大奖！更多活动详情，请前往官网活动主页查看。"
    )
    ACTIVITY_PERPETUAL_RANK_REWORDS = _(
        "恭喜你获得CoinEx送出的%(amount)s %(asset)s 合约交易大赛奖励，可到现货账户中查看。"
    )
    ACTIVITY_VIABTC_REWARDS = _(
        "恭喜你获得ViaBTC & CoinEx送出的%(amount)s %(asset)s 2021矿年狂欢奖励，可到现货账户中查看。"
    )
    ACTIVITY_MINING_REWARDS = _("恭喜你收到 %(amount)s %(asset)s %(activity_name)s奖励。")
    PLEDGE_AMM_ACTIVITY_MINING_REWARDS = _("恭喜你收到 %(amount)s %(asset)s %(activity_name)s奖励。")

    ACTIVITY_RUSSIAN_BOXES_ULTIMATE = _(
        "恭喜你，在CoinEx Mart幸运盲盒活动中瓜分到%(amount)s USDT奖励，奖励已发放，可到现货账户中查看"
    )

    ACTIVITY_RUSSIAN_BOXES_CET = _(
        "恭喜你，在CoinEx Mart幸运盲盒活动中收集的套娃最多，获得%(amount)s CET奖励，"
        "奖励已发放，可到现货账户中查看"
    )

    SECURITY_RESET_FAIL = _(
        "你在CoinEx安全项重置审核不通过，原因是%(reason)s."
    )

    SECURITY_RESET_EMAIL_SUCCESS = _(
        "你在CoinEx的绑定邮箱重置成功。完成重置后只能通过新邮箱登录。为了保障你的账号安全，24小时内禁止提现。"
    )

    SECURITY_RESET_WITHDRAW_PASSWORD_SUCCESS = _(
        "你已成功重置提现密码。 为了你的账户安全，24小时内将禁止提现。\n\n"
        "若非你本人操作，请立即重置密码或禁用账户，并尽快向CoinEx客服提交工单。"
    )

    SECURITY_RESET_WITHDRAW_PASSWORD_FAIL = _(
        "你在CoinEx的提现密码重置审核不通过。\n\n"
        "原因是%(reason)s，请按照示例重新拍照上传。"
    )

    SECURITY_RESET_2FA_SUCCESS = _(
        "你已成功解绑%(reset_type)s验证。请及时重新绑定%(reset_type)s，以免造成损失。\n\n"
        "为了保障你的账号安全，24小时内禁止提现。\n\n"
        "如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
    )

    ACTIVITY_AIRDROP_REWARDS_SUCCESS = _(
        "恭喜你，在CoinEx的“%(asset)s空投”活动中，获得%(amount)s%(asset)s的空投奖励。奖励已发放，将于%(lock_day)s天后解冻。")

    ACTIVITY_AIRDROP_LOTTERY_SUCCESS = _(
        "恭喜你，在CoinEx的“%(title)s空投”活动中，获得%(amount)s%(asset)s的空投奖励。奖励已发放，%(unlocked_at)s后支持交易/提现。")

    ACTIVITY_AIRDROP_LOTTERY_SUCCESS_MULTI = _(
        "恭喜您，在CoinEx的“%(title)s空投”活动中，获得%(rewards)s的空投奖励。")

    ACTIVITY_AIRDROP_REWARDS_FAIL = _(
        "抱歉，很遗憾，在CoinEx的“%(title)s空投”活动中，您的抽奖签号未中奖。"
    )
    ACTIVITY_AIRDROP_REWARDS_RESULT = _(
        "抱歉，您在CoinEx的“%(title)s空投”活动中，%(rewards)s领取失败。\n"
        "原因：当前账号已有使用中的合约体验金或交易赠金券，无法领取更多同类型卡券。"
    )

    ACTIVITY_DIBS_LOTTERY_SUCCESS = _(
        "“%(title)s”活动中，获得%(amount)s%(asset)s，申购已发放。")

    ACTIVITY_DIBS_REWARDS_FAIL = _(
        "抱歉，很遗憾，“%(title)s”活动中，未中奖，期待下次参与。"
    )
    SOTOSHI_BID_SUCCESS = _(
        "恭喜！您在稀有聪（Sat 1,968,750,000,000,000）的拍卖中，赢得竞拍，请通过当前账户的邮箱联系"
        "（<EMAIL>），并提供验证码：CoinExRareSatWinner \n\n "
        "我们将有专员为您处理该稀有聪的提取事宜。")

    ABNORMAL_DEPOSIT_APPLICATION_ADDITIONAL_INFO = _(
        "你的%(amount)s %(asset)s充值未到账找回申请已受理，还需补充相关资料，请尽快补充。\n"
        "申请时间：%(time)s"
    )
    ABNORMAL_DEPOSIT_APPLICATION_FEE = _(
        "你的%(amount)s %(asset)s充值未到账找回申请已受理，"
        "请确保你的现货账户资产有%(fee_amount)s %(fee_asset)s作为手续费，找回资产时平台将自动扣除手续费。\n\n"
        "当前你的余额不足，请尽快补充，否则申请将被驳回。"
    )
    ABNORMAL_DEPOSIT_APPLICATION_REJECTED = _(
        "你的%(amount)s %(asset)s充值未到账找回申请审核失败。\n"
        "申请时间：%(time)s\n"
        "失败原因：%(rejection_reason)s"
    )
    ABNORMAL_DEPOSIT_APPLICATION_SUCCESS_BY_SPOT = _(
        "你的%(amount)s %(asset)s充值未到账找回成功，已入账至你的现货账户。\n"
        "申请时间：%(time)s"
    )
    ABNORMAL_DEP_APPLY_SUCCESS_BY_SPOT_WITH_FEE = _(
        "你的%(amount)s %(asset)s充值未到账找回成功，已入账至你的现货账户。\n"
        "申请时间：%(time)s\n"
        "由于需要额外的人力和技术投入以帮您找回资产，我们已从您的充值资产里收取一定的手续费，"
        "手续费金额为：%(fee_amount)s %(fee_asset)s。手续费收取规则可点击下列按钮查看。"
    )
    ABNORMAL_DEPOSIT_APPLICATION_SUCCESS_BY_REFUND = _(
        "你的%(amount)s %(asset)s充值未到账找回成功，已退回至你提供的地址。\n"
        "申请时间：%(time)s\n"
        "退回的交易ID：%(refund_tx_id)s"
    )
    ABNORMAL_DEP_APPLY_SUCCESS_BY_REFUND_WITH_FEE = _(
        "你的%(amount)s %(asset)s充值未到账找回成功，已退回至你提供的地址。\n"
        "申请时间：%(time)s\n"
        "退回的交易ID：%(refund_tx_id)s\n"
        "由于需要额外的人力和技术投入以帮您找回资产，我们已从您的充值资产里收取一定的手续费，"
        "手续费金额为：%(fee_amount)s %(fee_asset)s。手续费收取规则可点击下列按钮查看。"
    )
    ACTIVITY_IEO_REWARDS_SUCCESS = _(
        "恭喜你在CoinEx Dock成功登船！"
        "你在%(project_name)s（%(asset)s）项目的申购中，中签%(count)s份包含%(amount)s %(asset)s，代币已发放。"
        "将于%(unlocked_at)s 解冻。"
    )
    ACTIVITY_IEO_REWARDS_SUCCESS_WITH_RULE = _(
        "恭喜你在CoinEx Dock成功登船！"
        "你在%(project_name)s（%(asset)s）项目的申购中，中签%(count)s份包含%(amount)s %(asset)s，代币已发放。将于%(unlocked_at)s 解冻。"
        "该项目代币有特定释放规则详情见申购详情；"
    )
    ACTIVITY_IEO_REWARDS_FAIL = _(
        "很遗憾，你在%(project_name)s（%(asset)s）项目的申购中未中签，期待您的下次参与。"
    )
    CREDIT_RISK_WITHDRAW_CLOSE = _(
        "你的授信账户当前风险率已低于可提现风险率，系统已关闭提现功能, 请尽快补充非授信资产。\n\n"
        "当前风险率：%(warn_rate)s\n"
        "可提现风险率：%(withdraw_rate)s\n\n"
        "如你的当前风险率大于可提现风险率时，提现功能将自动开启。"
    )
    CREDIT_RISK_TRADE_CLOSE = _(
        "你的授信账户当前风险率已低于可交易风险率，系统已关闭现货交易功能，杠杆及合约交易仍可正常使用，请尽快补充非授信资产。\n\n"
        "当前风险率：%(warn_rate)s\n"
        "可提现风险率：%(withdraw_rate)s\n"
        "可交易风险率：%(trade_rate)s\n\n"
        "如你的当前风险率大于可交易风险率时，现货交易功能将自动开启。"
    )
    OPENING_MARGIN_FUNCTION = _("你已成功开通杠杆交易，请注意风险。为你奉上杠杆交易操作手册，祝你交易愉快。")
    OPENING_PERPETUAL_TRADING = _("你已成功开通合约交易，请注意风险。为你奉上合约交易操作手册，祝你交易愉快。")
    COUPON_QUALIFIED = _("恭喜你获得一张 %(amount)s %(coupon_type)s，领取后即可使用，先到先得。")

    EXPERIENCE_FEE_COUPON_QUALIFIED = _(
        "恭喜你成功领取%(value)s %(value_type)s合约体验金，请尽快使用。如在使用有效期内正向合约交易额达标，则体验金到期后不回收。 \n\n"
        "使用截止日期：%(expired_at)s \n"
        "正向合约交易达标额：%(qualified_trade_amount)s %(value_type)s"
    )
    TRADING_GIFT_COUPON_QUALIFIED = _(
        "恭喜获得一张%(value)s %(value_type)s交易赠金券！\n\n"
        "有效期内，%(trade_type)s交易额累计达到%(qualified_trade_amount)s %(value_type)s即可使用。快去交易吧～ \n"
        "使用截止日期：%(expired_at)s"
    )
    INVESTMENT_INCREASE_RATE_COUPON_QUALIFIED = _(
        "恭喜你成功领取%(value)s%(value_type)s理财加息券一张。激活并划转资产到指定理财账户，即可享受额外加息收益。\n"
        "激活截止日期：%(expired_at)s"
    )
    CASHBACK_FEE_COUPON_QUALIFIED = _(
        "恭喜你成功领取%(value)s %(value_type)s手续费返现券-%(trade_type)s。\n"
        "即刻去交易，符合条件的交易订单手续费将于次日返现。"
    )
    PERPETUAL_SUBSIDY_COUPON_QUALIFIED = _(
        "恭喜你成功领取一张%(value)s %(value_type)s合约补贴金，使用有效期为：%(usable_days)s天。"
    )
    EXPERIENCE_FEE_COUPON_EXCHANGE = _(
        "恭喜你成功兑换%(value)s %(value_type)s合约体验金，请尽快使用。如在使用有效期内正向合约交易额达标，则体验金到期后不回收。 \n\n"
        "使用截止日期：%(expired_at)s \n"
        "正向合约交易达标额：%(qualified_trade_amount)s %(value_type)s"
    )
    INVESTMENT_INCREASE_RATE_COUPON_EXCHANGE = _(
        "恭喜你成功兑换%(value)s%(value_type)s理财加息券一张。激活并划转资产到指定理财账户，即可享受额外加息收益。\n"
        "激活截止日期：%(expired_at)s"
    )
    CASHBACK_FEE_COUPON_EXCHANGE = _(
        "恭喜你成功兑换%(value)s %(value_type)s手续费返现券-%(trade_type)s。\n"
        "即刻去交易，符合条件的交易订单手续费将于次日返现。"
    )
    INVESTMENT_INCREASE_RATE_COUPON_USED = _(
        "你的理财加息券已使用完毕。\n"
        "本次累计加息收益：%(value)s %(value_type)s"
    )
    CASHBACK_FEE_COUPON_USED = _(
        "你的手续费返现券已使用完毕。\n"
        "本次共为你返现 %(value)s %(value_type)s"
    )
    TRADING_GIFT_COUPON_EXCHANGE = _(
        "恭喜成功兑换一张%(value)s%(value_type)s交易赠金券！\n\n"
        "有效期内，%(trade_type)s交易额累计达到%(qualified_trade_amount)s %(value_type)s即可使用。快去交易吧～\n"
        "使用截止日期：%(expired_at)s"
    )
    PERPETUAL_SUBSIDY_COUPON_EXCHANGE = _(
        "恭喜你成功兑换一张%(value)s%(value_type)s合约补贴金，使用有效期为：%(usable_days)s天。"
    )
    EXPERIENCE_FEE_COUPON_UNLOCK = _(
        "恭喜你，合约交易额已达标。\n\n"
        "%(value)s %(value_type)s 合约体验金已满足赠送标准，过期后不收回，你可前往CoinEx合约交易继续使用。"
    )
    TRADING_GIFT_COUPON_UNLOCK = _(
        "恭喜你成功激活一张%(value)s %(value_type)s 交易赠金券。现金已发放至你的现货账户，快去查看吧～"
    )

    COPY_TRADING_EXPERIENCE_FEE_SEND_COUPON = _(
        "恭喜你获得合约跟单体验金券，立即前往卡券中心领取。"
    )
    COPY_TRADING_EXPERIENCE_FEE_COUPON_QUALIFIED = _(
        "合约跟单体验金券领取成功！\n"
        "卡券有效期内，你可以在卡券指定的适用范围内，在跟单或带单时使用卡券。"
    )
    COPY_TRADING_EXPERIENCE_FEE_COUPON_EXCHANGE = _(
        "恭喜兑换合约跟单体验金券！\n"
        "卡券有效期内，你可以在卡券指定的适用范围内，在跟单或带单时使用卡券。\n"
        "权益到期时间：%(expired_at)s。"
    )

    VIP_UPGRADE_SEND_COUPON = _(
        "恭喜你获得VIP+%(value)s 升级券，立即前往卡券中心领取。"
    )

    VIP_UPGRADE_EXCHANGE = _(
        "恭喜兑换VIP升级券，立刻体验更多特权。\n\n"
        "卡券有效期内，你的VIP等级将被提升%(value)s级，最高可提升到VIP5。\n\n"
        "权益到期时间：%(expired_at)s"
    )

    VIP_UPGRADE_USE = _(
        "VIP升级券(+%(value)s级)领取成功！\n\n"
        "卡券有效期内，你的VIP等级将被提升%(value)s级，最高可提升到VIP5。\n\n"
        "了解更多VIP特权。"
    )

    VIP_LEVEL_UP = _(
        "恭喜，你的VIP等级已升级为%(new_level)s，相关权益调整如下：\n\n"
        "现货费率：%(spot_taker_fee_rate)s\n"
        "现货费率 (开启CET抵扣)：%(spot_taker_discount_fee_rate)s\n"
        "合约费率：Maker %(perpetual_maker_fee_rate)s，Taker %(perpetual_taker_fee_rate)s\n"
        "杠杆日息：以%(new_level)s等级日息为准（详情请点击下方“前往查看”）\n"
        "返佣比例：%(referral_rate)s"
    )
    VIP_LEVEL_DOWN = _(
        "抱歉，你的VIP等级已降级为%(new_level)s，相关权益调整如下：\n\n"
        "现货费率：%(spot_taker_fee_rate)s\n"
        "现货费率 (开启CET抵扣)：%(spot_taker_discount_fee_rate)s\n"
        "合约费率：Maker %(perpetual_maker_fee_rate)s，Taker %(perpetual_taker_fee_rate)s\n"
        "杠杆日息：以%(new_level)s等级日息为准（详情请点击下方“前往查看”）\n"
        "返佣比例：%(referral_rate)s"
    )
    SIGN_IN_UNUSUAL = _(
        "系统检测到你的登录地点与上次不一致。\n\n"
        "时间：%(time)s\n"
        "IP: %(ip)s\n"
        "地点: %(location)s\n\n"
        "如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
    )
    KYC_PASS = _("恭喜，你的实名认证已通过审核，系统已为你开启相关权益。")
    KYC_FAIL = _("抱歉，你的实名认证未通过审核。\n原因：%(reject_reason)s\n\n你可以点击重新提交认证资料。")
    KYC_PRO_PASS = _("恭喜，您的KYC高级认证已通过审核，系统已为您开启对应权益。")
    KYC_PRO_FAIL = _("抱歉，您的KYC高级认证未通过审核。\n原因：%(reject_reason)s。\n\n")
    RISK_SCREEN_FAIL = _(
        "基于监管规定及要求，非常抱歉我们无法继续为你提供服务。\n\n"
        "我们将于7个工作日后对你的账户进行清退。届时你的账户将进入“仅提现模式”，即关闭充值、交易等功能，仅支持提现服务。请尽快处理正在进行中的交易和资产提现。\n\n"
        "感谢你的理解与配合。"
    )
    RESET_EMAIL_PASS = _(
        "你已成功重置邮箱，后续需使用新邮箱（%(email)s）登录。为了保障你的账号安全，24小时内禁止提现。\n\n"
        "如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
    )
    RESET_TOTP_PASS = _(
        "你已成功重置TOTP验证器。为了保障你的账号安全，24小时内禁止提现。\n\n"
        "如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
    )
    RESET_WEBAUTHN_PASS = _(
        "您的通行密钥解绑成功，为了保障您的账号安全，24小时内禁止提现。您的账号已失去通行密钥的保护，请及时重新绑定，以免造成损失。\n\n"
        "如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
    )
    RESET_PASSWORD = _(
        "你已成功重置登录密码。为了保障你的账号安全，24小时内禁止提现。\n\n"
        "如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
    )
    POTENTIAL_USER = _("每月仅需邀请3个用户，即可成为CoinEx大使，享受最高50%永久返佣。点击“查看详情”立即申请！")
    AMBASSADOR_BUSINESS_TRACE = _("成为CoinEx大使，每月仅需邀请3个用户。点击“查看详情”，立即享受最高50%永久返佣。")
    TRADE_RANK_ACTIVITY_GIFT = _(
        "恭喜你，在CoinEx的%(title)s活动中，获得%(amount)s %(asset)s的奖励，奖励已发放至现货账户。")
    TAX_EXPORT_SUCCESS = _(
        "你的账户数据已生成，请前往CoinEx资金流水页面进行下载，下载链接 7 天内有效，请注意文件隐私安全。")
    FIFTH_ULT_BOX_QUALIFY = _(
        "恭喜你在CoinEx五周年感恩庆典的「大挑战」成功超越自我，获得终极盲盒的开箱机会！\n\n"
        "所有盲盒开箱时间：2023年1月1日 00:00（UTC+0），先到先得哦～\n\n"
        "注：终极盲盒和周年盲盒仅可选择一个盲盒开箱。"
    )
    FIFTH_ANN_BOX_QUALIFY = _(
        "恭喜你在CoinEx五周年感恩庆典的「大挑战」获得周年盲盒的开箱机会。\n\n"
        "继续挑战，完成所有任务，即可冲击终极盲盒，$2000只有一步之遥！\n\n"
        "所有盲盒开箱时间：2023年1月1日 00:00（UTC+0），先到先得哦～\n\n"
        "注：终极盲盒和周年盲盒仅可选择一个盲盒开箱。"
    )
    SPOT_GRID_TRIGGERED = _(
        "%(market)s市场已达设定的网格触发价，网格策略开始运行。\n\n"
        "网格触发价：%(trigger_price)s %(quote_asset)s\n\n"
        "策略ID：%(strategy_id)s"
    )
    SPOT_GRID_TAKE_PROFIT_TRIGGERED = _(
        "%(market)s市场已达设定的网格止盈价，系统将自动平仓，及时止盈，为你锁定利润。\n\n"
        "网格止盈价：%(take_profit_price)s %(quote_asset)s\n\n"
        "策略ID：%(strategy_id)s"
    )
    SPOT_GRID_STOP_LOSS_TRIGGERED = _(
        "%(market)s市场已达设定的网格止损价，系统将自动平仓，及时止损，降低单边行情损失。\n\n"
        "网格止损价：%(stop_loss_price)s %(quote_asset)s\n\n"
        "策略ID：%(strategy_id)s"
    )
    SPOT_GRID_EXCEED_PRICE_RANGE = _(
        "%(market)s市场的现货价格已超过网格策略的价格区间，你可手动终止策略或修改止盈止损价格。\n\n"
        "%(base_asset)s市价：%(last_price)s %(quote_asset)s\n\n"
        "网格价格区间：%(lowest_price)s - %(highest_price)s %(quote_asset)s\n\n"
        "策略ID：%(strategy_id)s"
    )
    SPOT_GRID_EXCEED_RECOMMEND_RUN_DAYS = _(
        "%(market)s市场的现货网格设置已达到推荐的运行时间，你可手动终止或继续运行当前策略。\n\n"
        "策略ID：%(strategy_id)s\n\n"
        "时间区间：%(recommend_days)sD"
    )
    TAKE_PROFIT_SUCCESS = _(
        "你在%(market_type)s %(market)s中的止盈设置已被触发，并且全部平仓成功。\n\n"
        "风险提示：合约交易具有高风险性，请你密切关注市场动向。CoinEx对加密货币价格波动可能造成的任何损失不承担任何责任。")
    TAKE_PROFIT_FAIL = _(
        "你在%(market_type)s %(market)s中的止盈设置已被触发并且部分平仓，但由于行情波动剧烈、触发自动减仓等原因，导致未能完全平仓。\n\n"
        "剩余未平仓位：%(amount)s%(asset)s。\n\n"
        "请注意，止盈止损设置对剩余未平仓位已失效。\n\n"
        "风险警告：合约交易具有高风险性，请你密切关注市场动向。CoinEx对加密货币价格波动可能造成的任何损失不承担任何责任。"
    )
    STOP_LOSS_SUCCESS = _(
        "你在%(market_type)s %(market)s中的止损设置已被触发，并且全部平仓成功。\n\n"
        "风险提示：合约交易具有高风险性，请你密切关注市场动向。CoinEx对加密货币价格波动可能造成的任何损失不承担任何责任。")
    STOP_LOSS_FAIL = _(
        "你在%(market_type)s %(market)s中的止损设置已被触发并且部分平仓，但由于行情波动剧烈、触发自动减仓等原因，导致未能完全平仓。\n\n"
        "剩余未平仓位：%(amount)s%(asset)s。\n\n"
        "请注意，止盈止损设置对剩余未平仓位已失效。\n\n"
        "风险警告：合约交易具有高风险性，请你密切关注市场动向。CoinEx对加密货币价格波动可能造成的任何损失不承担任何责任。"
    )
    SUB_ACCOUNT_BIND_MANAGER = _(
        "你已与子账号 %(sub_user_name)s 建立授权关系，可访问并管理该子账号，如需解除授权，请联系授权人操作解除。\n\n"
        "授权人：%(main_user_email)s\n"
        "被授权人：%(manager_email)s （你）\n"
        "授权子账号： %(sub_user_name)s\n"
        "账号权限：%(permissions)s\n"
    )
    SUB_ACCOUNT_CANCEL_MANAGER = _(
        "你已与子账号 %(sub_user_name)s 解除授权关系，将无法访问和管理该子账号。\n\n"
        "授权人：%(main_user_email)s\n"
        "被授权人：%(manager_email)s （你）\n"
        "授权子账号： %(sub_user_name)s\n"
        "账号权限：%(permissions)s\n"
    )
    AUTO_INVEST_DEAL_ALL = _(
        "你的%(target_asset)s自动定投计划，本次定投已全部成交。\n"
        "定投时间：%(time)s\n"
        "定投金额：%(source_asset_traded_amount)s %(source_asset)s\n"
        "买入数量：%(target_asset_traded_amount)s %(target_asset)s\n"
    )
    AUTO_INVEST_DEAL_PARTIAL = _(
        "因市场深度原因，你的%(target_asset)s自动定投计划，本次定投部分成交。\n"
        "定投时间：%(time)s\n"
        "计划定投金额：%(source_asset_amount)s %(source_asset)s\n"
        "实际成交金额：%(source_asset_traded_amount)s %(source_asset)s\n"
        "买入数量：%(target_asset_traded_amount)s %(target_asset)s\n"
    )
    AUTO_INVEST_DEAL_FAILED = _(
        "因市场深度原因，你的%(target_asset)s自动定投计划，本次定投未成交。\n"
        "定投时间：%(time)s\n"
        "计划定投金额：%(source_asset_amount)s %(source_asset)s\n"
        "实际成交金额：%(source_asset_traded_amount)s %(source_asset)s\n"
        "买入数量：%(target_asset_traded_amount)s %(target_asset)s\n"
    )
    AUTO_INVEST_PROFIT_AMOUNT = _(
        "你的%(target_asset)s自动定投计划已到达设置的盈利目标。\n"
        "收益额：%(profit_amount)s %(source_asset)s"
    )
    AUTO_INVEST_PROFIT_RATE = _(
        "你的%(target_asset)s自动定投计划已到达设置的盈利目标。\n"
        "收益率：%(profit_rate)s%%"
    )
    AUTO_INVEST_FAILED = _("由于现货账户余额不足，你的%(target_asset)s自动定投计划，本次定投失败，请充值资产后再继续定投。")
    AUTO_INVEST_PAUSED = _(
        "由于连续5次现货账户余额不足导致定投失败，你的%(target_asset)s自动定投计划已暂停，可充值资产后再手动开启定投。")
    AUTO_INVEST_CLOSED = _(
        "你的%(target_asset)s自动定投计划已到达设置的定投总额，定投计划已自动关闭。\n"
        "定投总额：%(total_source_amount)s %(source_asset)s"
    )
    ACTIVITY_REWARD_RECEIPT = _("你在CoinEx的%(activity)s活动中，获得了%(reward)s的奖励，奖励已到账")
    WITHDRAW_EMAIL_CHECK = _("前往%(email)s邮箱确认你的提现信息。提现申请24小时内未确认将自动取消。")
    PLEDGE_LIQ_WARNING = _(
        "您的%(loan_asset)s借贷持仓当前质押率已达到 %(ltv)s%%，为避免强制平仓，请及时补充质押资产或主动还币。"
    )
    PLEDGE_LIQ = _(
        "您的%(loan_asset)s借贷持仓当前质押率已达到 %(ltv)s%%，超过强平质押率 %(liq_ltv)s%%，您的仓位已被强制平仓。"
    )
    PUSH_MSG_TEMPLATE = "_"  # 手动站内信推送template，占位符 无内容
    OPEN_POSITION_STOP_LOSS_FAIL = _(
        '由于市场波动和您设置的参数间隔过小，合约市场%(market)s的开仓止损未生效，请及时关注。')
    OPEN_POSITION_TAKE_PROFIT_FAIL = _(
        '由于市场波动和您设置的参数间隔过小，合约市场%(market)s的开仓止盈未生效，请及时关注。')

    # p2p
    P2P_REMIND_RECEIVE_ORDER = _(
        "您发布的广告已有用户下单，请前往确认订单。\n"
        "订单编号：%(order_id)s。\n"
        "下单方向：%(side)s。\n"
        "数字货币：%(base_amount)s %(base)s；法币：%(quote_amount)s%(quote)s。\n"
    )
    P2P_MERCHANT_REJECT_ORDER = _(
        "您发起的P2P订单商家拒绝接单。\n"
        "订单编号：%(order_id)s。\n"
    )
    P2P_RECEIVED_ORDER_TO_PAYMENT = _(
        "您的P2P买币订单商家已确认接单，请查看订单并尽快完成付款。\n"
        "订单编号：%(order_id)s\n"
        "您需支付：%(quote_amount)s %(quote)s\n"
        "请使用本人实名认证的支付方式，确认完成付款后再点击“我已付款”按钮。\n"
    )
    P2P_RECEIVED_ORDER_WAIT_PAYMENT = _(
        "您发起的卖币订单商家已确认。\n"
        "订单编号：%(order_id)s。\n"
        "您将收到：%(quote_amount)s %(quote)s。\n"
        "收到后需放币：%(from_amount)s %(base)s。\n"
    )
    P2P_BUYER_PAYMENT_DEADLINE = _(
        "您的P2P买币订单将在5分钟后超时，超时后将自动取消订单，请查看订单并尽快完成付款。\n"
        "订单编号：%(order_id)s\n"
        "您需支付：%(quote_amount)s %(quote)s\n"
        "请使用本人实名认证的支付方式，确认完成付款后再点击“我已付款”按钮。\n"
    )
    P2P_PAID_WAIT_RELEASE_ASSET = _(
        "您的P2P卖币订单商家已点击完成付款，请尽快前往收款账号核实款项。\n"
        "收款时，请确保资金来自商家实名认证的支付渠道，金额正确，再点击“确认放币”按钮。\n"
        "如需帮助，请联系客服。\n"
        "订单编号：%(order_id)s\n"
        "您将收到：%(quote_amount)s %(quote)s。\n"
    )
    P2P_FINISHED_ORDER = _(
        "您的P2P订单卖家已放币，订单已完成。\n"
        "订单编号：%(order_id)s。\n"
        "您已收到：%(to_amount)s%(base)s。\n"
        "数字货币已经划转到您的现货账户。\n"
    )
    P2P_ORDER_AUTO_CANCELLED = _(
        "您的P2P订单已取消，原因是：%(cancel_reason)s \n"
        "订单编号：%(order_id)s。"
    )
    P2P_BUYER_ORDER_CANCELLED = _(
        "您的P2P订单已被买家取消，原因是：%(cancel_reason)s \n"
        "订单编号：%(order_id)s。"
    )
    P2P_CREATED_COMPLAINT = _(
        "您的P2P订单%(user_type)s已发起申诉，原因是：%(reason)s \n"
        "订单编号：%(order_id)s。"
    )
    P2P_UPDATED_COMPLAINT = _(
        "您的P2P订单申诉有新的通知。\n"
        "订单编号：%(order_id)s。"
    )
    P2P_OPERATION_COMPLAINT_FOR_CANCEL_TO_BUYER = _(
        "您的P2P订单申诉客服已处理，数字货币已经释放给卖家。\n"
        "订单编号：%(order_id)s。\n"
        "释放数字货币：%(to_amount)s%(base)s。"
    )
    P2P_OPERATION_COMPLAINT_FOR_CANCEL_TO_SELLER = _(
        "您的P2P订单客服已处理，冻结的数字货币已经释放，请前往现货账户查看记录。\n"
        "订单编号：%(order_id)s。\n"
        "释放数字货币：%(from_amount)s%(base)s。"
        "详情请点击查看。"
    )
    P2P_OPERATION_COMPLAINT_FOR_RELEASE_TO_BUYER = _(
        "您的P2P订单申诉客服已处理，数字货币已经划转到您的现货账户。\n"
        "订单编号：%(order_id)s。\n"
        "您已收到：%(to_amount)s%(base)s。"
    )
    P2P_OPERATION_COMPLAINT_FOR_RELEASE_TO_SELLER = _(
        "您的P2P订单申诉客服已处理，您的数字货币已经划转给买家。\n"
        "订单编号：%(order_id)s。\n"
        "划转数字货币：%(from_amount)s%(base)s。"
    )
    P2P_CANCELED_COMPLAINT = _(
        "您的P2P订单申诉已被%(user_type)s取消申诉。\n"
        "订单编号：%(order_id)s。"
    )
    P2P_FINISHED_COMPLAINT = _(
        "您的P2P订单申诉已完成。\n"
        "订单编号：%(order_id)s。"
    )
    P2P_RESTART_COMPLAINT = _(
        "您的P2P订单申诉被重新开启。\n"
        "订单编号：%(order_id)s。"
    )
    P2P_ADD_PAYMENT_CHANNEL = _(
        "你已成功增加P2P收款方式：%(pay_channel)s"
    )
    P2P_AUTO_OFFLINE_ADVERTISING = _(
        "您的P2P广告单被自动下架。\n"
        "原因是：%(reason)s。\n"
        "广告单编号：%(adv_number)s。"
    )
    P2P_PAYMENT_CHANNEL_INVALID = _(
        "你设置的支付渠道【%(pay_channel)s】已失效，请重新设置。"
    )
    # p2p 商家活动
    P2P_MER_ACT_APPLY_SUCCESS = _(
        "您已成功提交 %(act_name)s 报名申请，CoinEx将通过邮件及站内信通知审核结果，请耐心等待。"
    )
    P2P_MER_ACT_AUDIT_SUCCESS = _(
        "恭喜您已通过 %(act_name)s 报名审核，请前往商家后台发布广告单，赢取活动奖励。"
    )
    P2P_MER_ACT_AUDIT_FAIL = _(
        "很遗憾地通知，你未能通过 %(act_name)s 的报名审核。\n"
        "感谢报名，如对审核结果有疑问，请联系CoinEx客服。"
    )
    P2P_MER_ACT_AUDIT_CANCEL = _(
        "很遗憾地通知，你在 %(act_name)s 的参与资格已被取消。\n"
        "感谢参与，如对审核结果有疑问，请联系CoinEx客服。"
    )
    P2P_MER_ACT_REWARD_SUCCESS = _(
        "恭喜你获得P2P商家活动的奖励瓜分资格。\n"
        "获奖日期：%(reward_date)s\n"
        "活动名称：%(act_name)s\n"
        "活动奖金已发放至现货账户，请前往活动主页查看详情。"
    )
    P2P_MER_ACT_REWARD_FREEZE = _(
        "很遗憾地通知，你在 %(act_name)s 的活动奖励已被冻结。\n"
        "请立即联系CoinEx客服处理异常。"
    )
    P2P_MER_ACT_REWARD_CANCEL = _(
        "很遗憾地通知，你在 %(act_name)s 的活动奖励已被撤销。\n"
        "感谢参与，如对处理结果有疑问，请联系CoinEx客服。"
    )
    P2P_MER_ACT_REWARD_FAIL = _(
        "由于你的账户异常， %(act_name)s 奖励发放失败，请立即联系客服处理。"
    )
    P2P_MER_ACT_POINT_CANCEL = _(
        "很遗憾地通知，由于你已连续3次接单超时，今日的活动排名已被取消。\n"
        "如对处理结果有疑问，请联系CoinEx客服。"
    )

    P2P_MARGIN_PAYMENT = _(
        "为保障P2P交易安全、维护商家服务信誉，CoinEx已于2025-03-07（UTC）起实施商家保证金制度。\n"
        "我们发现您当前的保证金余额低于P2P商家保证金最低要求，请在 %(grace_deadline)s（UTC）前补充保证金，以免P2P交易受到限制。\n\n"
        "补充方式：前往商家后台 > 总览 > 去补充，按页面提示完成操作"
    )
    P2P_MARGIN_SHORTFALL = _(
        "为保障P2P交易安全、维护商家服务信誉，CoinEx已于2025-03-07（UTC）起实施商家保证金制度。\n"
        "由于您未在 %(grace_deadline)s（UTC）前补充保证金，目前已被限制发布P2P广告单。\n"
        "补充保证金后即可恢复广告发布权益，请尽快补充。\n\n"
        "补充方式：前往商家后台 > 总览 > 去补充，按页面提示完成操作"
    )
    P2P_MARGIN_CHANGE = _(
        "基于商家所在地区和账户风险评估，系统已将您的保证金要求调整为 %(amount)s USDT。请及时补充保证金，以免广告发布受到限制。\n\n"
        "补充方式：前往商家后台 > 总览 > 去补充，按页面提示完成操作"
    )
    P2P_MARGIN_CHANGE_ZERO = _(
        "基于商家所在地区和账户风险评估，系统已将您的保证金要求调整为 %(amount)s USDT。\n\n"
    )
    P2P_MER_CANCEL = _(
        "P2P商家身份已取消，商家保证金将于48小时内自动退回现货账户，如需查看历史交易记录，请登录商家后台。\n\n"
        "感谢您对CoinEx P2P的支持和信任，期待未来再次合作。"
    )

    P2P_USER_COMPENSATION = _(
        "尊敬的客户，您的P2P交易赔付已完成，赔付金额 %(amount)s USDT，已划转至您的现货账户，资金可随时用于交易或提现。\n\n"
        "如有疑问请提交工单联系客服，感谢您的理解与支持！"
    )

    P2P_MER_COMPENSATION = _(
        "尊敬的商家，平台已根据《平台商家行为规范》处理P2P交易赔付，赔付金额 %(amount)s USDT 已从您的保证金内扣除并转至用户现货账户。"
        "请及时登录商家后台查看保证金余额，确保后续交易正常进行。\n\n"
        "如有疑问请提交工单联系客服，感谢您的理解与支持！"
    )

    P2P_MARGIN_EXCESS_REFUND = _(
        "尊敬的商家，您超额缴纳的P2P保证金已完成返还，金额 %(amount)s USDT 已划转至您的现货账户。"
        "请及时登录商家后台查看保证金余额，确保后续交易正常进行。\n\n"
        "如有疑问请提交工单联系客服，感谢您的理解与支持！"
    )

    P2P_MARGIN_PENALTY = _(
        "尊敬的商家，经核实您近期存在违反《平台商家行为规范》的行为，现依据规则扣除保证金 %(amount)s USDT。"
        "款项已从您的商家保证金余额内划扣。请及时登录商家后台查看保证金余额，确保后续交易正常进行。\n\n"
        "如有疑问请提交工单联系客服，感谢您的理解与支持！"
    )


    # staking
    STAKING_REMOVE_NOTICE = _(
        "你质押的%(amount)s%(asset)s已经生效，次日08:30 （UTC+8）左右，产生的收益将发放至你的现货账户。")
    STAKING_REMOVE_SUCCESS = _("你质押的%(amount)s%(asset)s成功赎回 ，已存入到你的现货账户中。")

    PRE_TRADING_SETTLEMENT = _("%(asset)s已于%(settle_at)s 完成交割，交割价格%(settle_price)s %(pledge_asset)s")
    # 跟单
    COPY_TRADER_APPLY_SUCCESS = _("你已成为CoinEx合约跟单交易员，请在交易员主页完善你的个人简介。")
    COPY_TRADER_OPEN_POSITION = _("开仓均价 %(price)s %(asset)s，请在官网带单数据-当前带单中查看详情。")
    COPY_TRADER_CLOSE_POSITION = _("平仓均价 %(price)s %(asset)s，请在官网带单数据-历史带单中查看详情。")
    COPY_TRADER_ADJUST_PROFIT_SHARE_RATE = _(
        "你的分润比例已从%(old_rate)s调整为%(new_rate)s，新的分润比例从下个周期开始生效。"
    )
    COPY_TRADER_TAKE_PROFIT = _(
        "你的%(market)s跟单合约市场止盈设置已触发平仓，同时，跟单员仓位已全部平仓成功。"
    )
    COPY_TRADER_STOP_LOSS = _(
        "你的%(market)s跟单合约市场止损设置已触发平仓，同时，跟单员仓位已全部平仓成功。"
    )
    COPY_TRADER_LIQUIDATION = _(
        "很遗憾地通知你，你的跟单交易正向合约%(market)s持仓已被强制平仓。请在带单数据-历史带单中查看详情。")
    COPY_TRADER_TRADE_FINISHED = _("你已结束带单和仓位会以市价平仓，你获得的分润金额将会转入到现货账户。")
    COPY_TRADER_TRADE_SYSTEM_FINISHED = _(
        "由于你已超过%(days)s天未进行合约带单交易，系统已取消你的交易员身份。\n"
        "如需重新成为合约交易员，请重新申请带单，通过申请后才可发起带单交易。"
    )
    COPY_TRADER_TRADE_PRE_CANCEL = _(
        "请注意，你已超过%(days)s天未进行合约带单交易。如连续%(stop_days)s天未带单，系统将自动取消你的交易员身份。"
    )
    COPY_FOLLOWER_OPEN_POSITION = _("你已跟随交易员@%(trader_nickname)s开仓成功 ，均价 %(price)s %(asset)s。")
    COPY_FOLLOWER_CLOSE_POSITION = _("你已跟随交易员@%(trader_nickname)s平仓成功 ，均价 %(price)s %(asset)s。")
    COPY_FOLLOWER_TAKE_PROFIT = _(
        "你对交易员@%(trader_nickname)s 止盈设置已成功触发平仓。同时，你的跟单仓位已全部平仓成功和结束跟单"
    )
    COPY_FOLLOWER_STOP_LOSS = _(
        "你对交易员@%(trader_nickname)s 止损设置已成功触发平仓。同时，你的跟单仓位已全部平仓成功和结束跟单"
    )
    COPY_FOLLOWER_LIQUIDATION = _(
        "很遗憾地通知你，你的跟单交易正向合约%(market)s持仓已被强制平仓。"
    )
    COPY_FOLLOWER_PROFIT_SHARE = _(
        "你的交易员@%(trader_nickname)s 获得本次分润结算金额 %(amount)s %(asset)s，本结算周期为 %(start_time)s - %(end_time)s"
    )
    COPY_FOLLOWER_PROFIT_SHARE_RATE_CHANGED = _(
        "你的交易员@%(trader_nickname)s分润比例已从%(old_rate)s调整为%(new_rate)s，新的分润比例从下个周期开始生效。"
    )
    COPY_FOLLOWER_SUGGEST_ADD_MARGIN = _(
        "你的交易员@%(trader_nickname)s 已转入%(amount)s %(asset)s保证金，建议同步增加同等 数量的保证金，并注意控制风险。"
    )
    COPY_FOLLOWER_OPEN_POSITION_FAILED = _(
        "由于跟单保证金余额不足，你未能跟随交易员@%(trader_nickname)s成功开仓/加仓，请追加跟单金额。"
    )
    COPY_FOLLOWER_FOLLOW_FINISHED = _(
        "你已与交易员@%(trader_nickname)s 结束跟单关系，仓位以市价全平成功。扣除分润金额后，剩余资金将会转入到现货账户。"
    )
    LAUNCH_MINING_REWARD = _(
        "感谢参与%(stake_assets)s挖矿活动，%(reward_amount)s %(reward_asset)s奖励已发放至你的现货账户。"
    )
    CASHBACK_EQUITY_EXPIRING = _(
        "你的%(amount)s %(asset)s 手续费返现权益即将到期，请尽快使用。\n"
        "返现到期时间：%(expired_at)s\n\n"
        "请前往「我的奖励」查看详情。"
    )
    CASHBACK_EQUITY_DELIVERY = _(
        "恭喜你获得 %(amount)s %(asset)s 手续费返现权益，请尽快使用。返现到期时间：%(expired_at)s 。请前往「我的奖励」查看详情。"
    )
    AIRDROP_EQUITY_DELIVERY = _(
        "恭喜你获得 %(amount)s %(asset)s 空投奖励，请前往「我的奖励」查看详情。"
    )
    NEW_MISSION_NOTICE = _(
        '“%(title)s”已上线，快来奖励中心接受挑战吧！\n'
        "请前往「奖励中心」查看详情。"
    )
    MUL_NEW_MISSION_NOTICE = _(
        '“%(title)s”等多个任务已上线，快来奖励中心接受挑战吧！\n'
        '请前往「奖励中心」查看详情。'
    )
    MISSION_REWARD_SENT = _(
        "恭喜，你已完成新用户专属任务！\n"
        "任务名称：%(title)s\n"
        "请前往「我的奖励」查看详情。"
    )
    MISSION_EXPIRING = _(
        "你的新用户专属任务即将到期，完成即可获得%(value)s %(value_type)s%(reward_type)s奖励！\n\n"
        "请前往「奖励中心」完成任务。"
    )
    MUL_MISSION_EXPIRING = _(
        "你有多个新用户专属任务即将到期，请前往「奖励中心」查看详情。"
    )
    COMMENT_BAN_FOR_DAYS = _("由于您的账号币种评论内容涉及“%(reason)s”，您的账号%(count)s天无法发表币种评论和回复。")
    COMMENT_BAN_FOR_EVER = _("由于您的账号币种评论内容涉及“%(reason)s”，您的账号永久无法发表币种评论和回复。")
    COMMENT_WARNING = "%(content)s"  # 直接展示admin填写的内容，无需翻译
    # 大使激励包
    MEET_REQUIREMENT_PACKAGE = _("恭喜通过本期大使激励包考核，%(settled_amount)s %(asset)s奖励已成功发放。")
    MEET_REQUIREMENT_PACKAGE_LAST_PERIOD = _("恭喜通过本期大使激励包考核，%(settled_amount)s %(asset)s奖励已成功发放。"
                                             "感谢你一直以来对CoinEx大使项目的支持，你的所有%(asset)s激励包奖励已经全部发放完毕！")
    NOT_MEET_REQUIREMENT_PACKAGE = _("很遗憾，你在本期大使激励包考核中未能达标，%(period_amount)s %(asset)s奖励未能正常发放。")
    NOT_MEET_REQUIREMENT_PACKAGE_LAST_PERIOD = _("很遗憾，你的大使激励包考核未达标，%(period_amount)s %(asset)s奖励未能正常发放。"
                                                 "感谢你一直以来对CoinEx大使项目的支持，本次%(asset)s激励包考核已全部结束。")
    PACKAGE_NOT_SETTLED_USER_NOT_AMBASSADOR = _("很遗憾，你的大使身份已失效，本期大使激励包%(period_amount)s %(asset)s未能正常发放。")
    COMMENT_TIPS = _("您发表的内容收到%(count)s份礼物(打赏)共%(amount)s")


class MessageWebLink(Enum):
    """ 站内信-web-落地页 """
    DEPOSIT_PAGE = "/asset/deposit"  # 充值页面
    DEPOSIT_RECORD_PAGE = "/asset/deposit/record"  # 充值记录
    WITHDRAWAL_RECORD_PAGE = "/asset/withdraw/record"  # 提现记录
    MARGIN_LOAN_RECORD_PAGE = "/asset/history/margin"  # 杠杆-借币记录列表
    MARGIN_GUIDE_ARTICLE_URL = "https://support.coinex.com/hc/zh-cn/articles/4411638043417"  # 帮助中心-杠杆指南文章
    PERPETUAL_GUIDE_ARTICLE_URL = "https://support.coinex.com/hc/zh-cn/articles/4411645149081"  # 帮助中心-合约指南文章
    PERPETUAL_DEAL_RECORD_PAGE = "/futures/order/deal-record"  # 合约订单-成交记录
    PERPETUAL_CURRENT_POSITION_PAGE = "/futures/order/current-position"  # 合约订单-当前持仓
    SPOT_ASSET_HISTORY_PAGE = "/asset/history/spot"  # 现货-资产流水记录
    SPOT_ASSET_PAGE = "/asset/spot"  # 现货资产页面
    COUPON_CENTER_PAGE = "/my/info/coupon"  # 卡券中心
    REWARD_CENTER_PAGE = "/reward-center"  # 奖励中心专题页
    PERPETUAL_TRADE_PAGE = "/futures"  # 合约交易页
    SPOT_TRADE_PAGE = "/exchange"  # 现货交易页
    VIP_LEVEL_PAGE = "/vip"  # VIP等级页面
    ACCOUNT_SETTING_PAGE = "/my/info/basic"  # 账户设置页面
    ACCOUNT_KYC_PAGE = "/my/info/kyc/result"  # KYC页面
    ACCOUNT_KYC_PRO_PAGE = "/my/info/kyc/senior"  # 高级KYC页面
    ASSET_DASHBOARD_PAGE = "/asset/total"  # 资产总览页面
    ACCOUNT_SECURITY_PAGE = "/my/info/basic"  # 账户总览页面
    ACCOUNT_SECURITY_SETTING_PAGE = "/my/info/security"  # 账户安全页面
    DEPOSIT_RECOVERY_PAGE = "/asset/deposit/recovery"  # 充值未到账找回页面
    APPLY_AMBASSADOR = "/activity/apply?type=1"  # 大使申请页面
    TAX_EXPORT_PAGE = '/asset/history/spot?export=1'  # 报税数据导出页面
    SUB_ACCOUNT_AUTHORIZE_PAGE = "/account/sub/authorize"  # 子账号授权页面
    AUTO_INVEST_PLAN_PAGE = "/strategy/auto-invest/{plan_id}"  # 定投策略详情页面
    SPOT_GRID_STRATEGY_DETAIL_PAGE = "/strategy/spot-grid/{strategy_id}"  # 现货网格详情页面
    DIBS_ACTIVITY_DETAIL_PAGE = "/activity/dibs/{activity_id}"  # Dibs详情页面
    AIRDROP_ACTIVITY_DETAIL_PAGE = "/activity/airdrop/{activity_id}"  # 空投详情页面
    NOVICE_PAGE = "/newbie"  # 新手专区页面
    PLEDGE_ACCOUNT_PAGE = "/asset/loans"  # 借贷账户页面
    PERPETUAL_MARKET_TRADE_PAGE = "/futures/{market}"  # 合约市场交易页
    INVESTMENT_RECORD_PAGE = "/asset/finance/record"  # 理财记录页
    P2P_MERCHANT_ADV_PAGE = "/p2p-merchant/advertisement"  # p2p商家广告管理
    P2P_USER_ORDER_INFO = "/p2p/order-detail"  # 订单详情页  /p2p/order-detail?orderID=xxxx
    P2P_USER_PAY_CHANNEL_PAGE = "/p2p/user/center"  # p2p 收款方式
    P2P_MERCHANT_PAY_CHANNEL_PAGE = "/p2p-merchant/payment"  # p2p 商家收款方式
    PRE_TRADING_DETAIL_PAGE = "/pre-token-trading/{asset}"  # 盘前项目详情页
    COPY_TRADER_LEAD_PAGE = "/copy-trading/lead"  # 我的带单-主页
    COPY_TRADER_INDEX_PAGE = "/copy-trading/futures"  # 跟单主页
    DEMO_TRADING_PAGE = "/demo-trading"
    COPY_FOLLOWER_CUR_POSITION_PAGE = "/copy-trading/follow-mine/futures?traderId={trader_id}"  # 当前跟单－当前仓位
    COPY_FOLLOWER_FIN_POSITION_PAGE = "/copy-trading/follow-mine/futures?activeTab=history&traderId={trader_id}"  # 当前跟单-历史仓位
    COPY_FOLLOWER_PROFIT_SHARE_PAGE = "/copy-trading/follow-mine/futures?activeTab=profitShare&traderId={trader_id}"  # 当前跟单-分润结算
    COPY_FOLLOWER_FIN_FOLLOW_PAGE = "/copy-trading/follow-mine/futures?followTab=history"  # 历史跟单
    P2P_MER_ACT_PAGE = "/activity/p2p/{act_id}"  # p2p商家活动主页
    P2P_MERCHANT_PAGE = '/p2p-merchant'  # p2p后台主页
    P2P_HELP_PAGE = '/help/sections/articles/39518281578265'  # p2p帮助主页
    ABNORMAL_DEP_APPLY_FEE_RULE_PAGE = '/help/sections/articles/47528831562777'  # 充值找回手续费规则页
    INTERACTIVE_MESSAGE = "/interactive-message"  # 评论互动消息
    MESSAGE = "/messages" # 消息中心
    P2P_COMPLAINT_PAGE = "/p2p/order/appeal/{order_id}"  # p2p申诉页面
    AMBASSADOR_PACKAGE_PAGE = '/ambassador-panel/incentive-package?status=RUNNING'   # 大使激励包落地页
    AMBASSADOR_PACKAGE_FINISHED_PAGE = '/ambassador-panel/incentive-package?status=FINISHED'   # 大使激励包已发完落地页
    P2P_MER_STANDARD_PAGE = '/help/sections/articles/**************'  # 商家行为规范


class MessageAndroidLink(Enum):
    """ 站内信-android-落地页 """
    pass


class MessageIosLink(Enum):
    """ 站内信-ios-落地页 """
    pass


@unique
class ServerBalanceType(IntEnum):
    AVAILABLE = 1
    FROZEN = 2
    LOCK = 3


class ReportType(Enum):
    DAILY = 'daily'
    WEEKLY = 'weekly'
    MONTHLY = 'monthly'
    QUARTERLY = 'quarterly'


class AccountBalanceType(Enum):
    SPOT = 'spot'
    MARGIN = 'margin'
    INVESTMENT = 'investment'
    PERPETUAL = 'perpetual'
    AMM = 'amm'
    PLEDGE = 'pledge'
    STAKING = 'staking'


class OrderOption(IntEnum):
    # 普通
    NORMAL = 0
    # 优先收取stock作为手续费
    USE_STOCK_FEE = 0x1
    # 优先收取money作为手续费
    USE_MONEY_FEE = 0x2
    # 不限制最小下单数量
    WITHOUT_ORDER_MIN_AMOUNT = 0x4
    # 立即成交或取消
    IOC = 0x8
    # 全部成交或取消
    FOK = 0x10
    # 隐藏委托
    HIDE = 0x20
    # 只做maker
    MAKER_ONLY = 0x80
    # 系统订单标识
    SYSTEM = 0x100
    # amm order
    AMM = 0x200
    # 切换交易币种
    REVERSE_AMOUNT = 0x400

    @property
    def effect_type_name(self):
        match self:
            case self.NORMAL:
                return 'AL'
            case self.FOK:
                return self.FOK.name
            case self.IOC:
                return self.IOC.name
        return 'AL'


# 允许用户下单传入的option
USER_ORDER_OPTIONS = (
    OrderOption.NORMAL, OrderOption.IOC, OrderOption.FOK, OrderOption.HIDE, OrderOption.MAKER_ONLY,
    OrderOption.REVERSE_AMOUNT
)


class PerpetualOrderOption(IntEnum):
    # 只做maker
    MAKER_ONLY = 0x1
    # 隐藏委托
    HIDDEN = 0x2
    # 只减仓
    REDUCE_ONLY = 0x4


PERPETUAL_HIDE_VALUE = 2
PERPETUAL_ONLY_MAKER_VALUE = 1

PERPETUAL_ALL_MARKET_TYPE = 0


class PerpetualMarketType(IntEnum):
    DIRECT = 1
    INVERSE = 2


PERPETUAL_MARKET_TYPE_MAP = {
    1: _('正向合约'),
    2: _('反向合约')
}


class NoticePushType(IntEnum):
    SUCCESS = 1
    FAIL = 2
    NOTICE = 3


@unique
class SubAccountPermission(Enum):
    PERPETUAL = _("合约交易")
    MARGIN = _("杠杆交易")
    AMM = _("参与做市")
    API = _("API管理")


BIG_COINS = {'BTC', 'ETH', 'USDT', 'USDC', 'CET'}

ORDERED_BIG_COINS = ("USDT", "USDC", "BTC", "ETH", "CET")

# 兼容报表数据获取
HISTORY_BIG_COINS = {'BTC', 'ETH', 'BCH', 'USDT', 'USDC', 'CET'}


class DepositWithdrawalDisableReason(Enum):
    """ 充提、提现 关闭原因 """
    # 充提关闭原因
    DEPOSIT_SUSPENDED_WALLET_MAINTENANCE = _("钱包维护中，暂停充值")
    DEPOSIT_SUSPENDED_MAINNET_UPGRADE = _("主网升级中，暂停充值")
    DEPOSIT_SUSPENDED_SMART_CONTRACT_UPDATE = _("合约更新中，暂停充值")
    DEPOSIT_SUSPENDED = _("暂停充值")
    DEPOSIT_SUSPENDED_OFFLINE_ASSET = _("%(asset)s-%(chain)s已下架，停止充值")
    # 提现关闭原因
    WITHDRAWAL_SUSPENDED_WALLET_MAINTENANCE = _("钱包维护中，暂停提现")
    WITHDRAWAL_SUSPENDED_MAINNET_UPGRADE = _("主网升级中，暂停提现")
    WITHDRAWAL_SUSPENDED_SMART_CONTRACT_UPDATE = _("合约更新中，暂停提现")
    WITHDRAWAL_SUSPENDED = _("暂停提现")
    WITHDRAWAL_SUSPENDED_OFFLINE_ASSET = _("%(asset)s-%(chain)s已下架，停止提现")


PREVENT_RISK_CONTROL_REASONS = {"RENAME_OR_IDENTITY_CHANGE", "DELISTING"}


class GuideType(Enum):
    NO_TRADE_USER = 1  # 用户画像选择 "我是新手"
    HAS_TRADE_USER = 2  # 用户画像选择 "我有经验"


class SettleSwitch(IntEnum):
    OPEN = 1
    CLOSE = 2


del _


class NoviceTradeType(Enum):
    SPOT = '币币交易'
    PERPETUAL = '合约交易'
    DEPOSIT = '链上充值'
    SPOT_PERPETUAL = '不限制'  # 币币+合约(不包含链上充值)


class WhiteListType(Enum):
    WITHDRAWAL_WHITELIST = '免风控提现'
    NO_DELAY_WHITELIST = '免延迟提现'


class ReferralType(Enum):
    REFERRAL = '普通'
    AMBASSADOR = '大使'


class LastPushReadType(Enum):
    APP = 'APP'
    EMAIL = 'email'


class CouponApplyBusinessParty(Enum):
    ALL = "全部"
    OPERATION = "运营"
    GROWTH = "增长"
    RISK_CONTROL = "风控"
    BUSINESS = "商务"
    CHANNEL_EN_US = "渠道-英语"
    CHANNEL_ZH_HANT_HK = "渠道-繁体中文"
    CHANNEL_FA_IR = "渠道-波斯语"
    CHANNEL_AR_AE = "渠道-阿拉伯语"
    CHANNEL_RU_KZ = "渠道-俄语"
    CHANNEL_TR_TR = "渠道-土耳其语"
    CHANNEL_JA_JP = "渠道-日语"
    CHANNEL_KO_KP = "渠道-韩语"
    CHANNEL_ID_ID = "渠道-印尼语"
    CHANNEL_ES_ES = "渠道-西语"
    CHANNEL_PT_PT = "渠道-葡语"
    CHANNEL_FR_FR = "渠道-法语"
    CHANNEL_DE_DE = "渠道-德语"
    CHANNEL_TH_TH = "渠道-泰语"
    CHANNEL_VI_VN = "渠道-越南语"
    CHANNEL_IT_IT = "渠道-意大利语"
    CHANNEL_PL_PL = "渠道-波兰语"
    OTHER = "其他"


class BusinessParty(Enum):
    """业务方"""
    SOUTHEAST_ASIA = "东南亚"
    EAST_ASIA = "东亚"
    SOUTH_ASIA = "南亚"
    AFRICA = "非洲"
    MIDDLE_EAST = "中东"
    CIS = "CIS"
    LATIN_AMERICA = "拉美"
    EUROPE = "欧洲"
    CUSTOMER_SERVICE = "客服"
    RISK_CONTROL = "风控"
    GROWTH = "增长"
    BRAND = "品牌"
    OTHERS = "其他"


# 自定义提现手续费币种
CUSTOM_WITHDRAWAL_FEE_ASSETS = {'CET', 'USDT', 'USDC', 'BTC', 'ETH'}


class P2pBusinessType(Enum):
    SELL = "sell"
    BUY = "buy"

    @classmethod
    def reverse(cls, _type):
        return cls.BUY if _type == cls.SELL else cls.SELL


class P2pOrderUserType(Enum):
    CUSTOMER = "发起方"  # 用户
    MERCHANT = "接单方"  # 商家


class P2pAmountType(Enum):
    QUOTE = "定价货币"  # 法币
    BASE = "交易货币"  # 数字货币


class ReportPlatform(Enum):
    WEB = 'web'
    IOS = 'iOS'
    ANDROID = 'Android'


class P2pMerActRewardType(Enum):
    COIN = "代币"


channel_field = NamedTuple('ShareChannel', [('value', str), ('desc', str)])


class ShareUserTag(Enum):
    """
        根据产品文档中 channel参数 定义
        https://app.clickup.com/9008230771/v/dc/8cexcbk-10718/8cexcbk-267838
    """
    first_spot = channel_field("firstspot", "首次现货买入成交弹窗")
    first_2000_cet = channel_field("first2000cet", "用户首次现货账户资产≥2000CET弹窗")
    first_financial = channel_field("firstfinancial", "首次理财弹窗")
    spot_daily_yield = channel_field("spotdailyyield", "现货账户-盈亏分析页面“每日收益率”首次突破弹窗")
    spot_float_yield = channel_field("spotfloatyield", "现货（浮盈）收益率首次突破弹窗")
    spot_actual_yield = channel_field("spotactualyield", "现货（实际盈利）收益率首次突破弹窗")
    financial_yield1 = channel_field("financialyield1", "理财收益额突破（500/3000）弹窗")
    financial_yield2 = channel_field("financialyield2", "理财收益率首次突破（10000）弹窗")
    staking_yield = channel_field("stakingyield", "staking收益额突破弹窗")
    first_vip = channel_field("firstvip", "首次VIP弹窗")
    anniversary = channel_field("anniversary", "用户注册X周年弹窗")
    mining_reward = channel_field("miningreward", "用户获得挖矿收益弹窗")

    first_future = channel_field("firstfuture", "用户首次合约完全平仓弹窗")
    yield_ = channel_field("yield", "用户合约平仓收益率首次突破弹窗")
    future_current_position = channel_field("future-Current-Position", "用户当前仓位-合约仓位总盈亏分享弹窗")
    future_position_history = channel_field("future-Position-History", "用户历史仓位-合约仓位总盈亏分享弹窗")
    first_swap = channel_field("firstswap", "用户首次兑换弹窗")
    first_margin = channel_field("firstmargin", "用户首次杠杆交易弹窗")
    first_spot_grid = channel_field("firstspotgrid", "用户首次现货网格交易弹窗")
    first_auto_invest = channel_field("firstautoinvest", "用户首次定投弹窗")
    first_amm = channel_field("firstamm", "用户首次AMM弹窗")
    first_loans = channel_field("firstloans", "用户首次借贷弹窗")

    dock = channel_field("Dock", "Dock 页面")
    airdrop = channel_field("Airdrop", "空投活动")
    dibs = channel_field("Dibs", "打折购")
    auto_invest = channel_field("Auto-Invest", "定投")
    spot_grid = channel_field("Spot-Grid", "现货网格")

    trade_board = channel_field("Tradeboard", "交易赛活动")
    ambassador_bonus = channel_field("Ambassador-Bonus", "大使活动")
    mining = channel_field("Mining", "挖矿")

    newcomer_offer = channel_field("Newcomer-Offer", "新手礼包")
    referral = channel_field("Referral", "推荐返佣")

    c_box = channel_field("C-Box", "C-Box 分享")
    position_pnl = channel_field("Position-PNL", "持仓盈亏分析")
    tx_statistics = channel_field("Tx-Statistics", "成交统计")

    academy = channel_field("Academy", "学院文章")
    flash = channel_field("Flash", "快讯文章")
    news = channel_field("News", "要闻文章")
    insight = channel_field("Insight", "洞见文章")
    blog = channel_field("Blog", "博客文章")
    announcements = channel_field("Announcements", "公告文章")

    app_share_btn = channel_field("AppShareBtn", "分享app按钮")
    coin_info = channel_field("Coininfo", "币种资料页")
    candlestick = channel_field("Candlestick", "K线页分享")
    market_data = channel_field("Marketdata", "行情页分享")
    market_data_feed = channel_field("Marketdata-Feed", "币种详情页-资讯分享")
    swap = channel_field("Swap", "兑换")
    pnl_analysis = channel_field("PNL-Analysis", "盈亏分析")

    @classmethod
    def get_share_tags(cls):
        return [tag.value.value for tag in cls]

    @classmethod
    def get_channel_name_map(cls):
        return {tag.value.value: f"{tag.value.desc} - {tag.value.value}" for tag in cls}


class InsuranceType(Enum):
    ADD = 1  # 增加
    DECREASE = 2  # 减少


class ProducerTopics(Enum):
    def __new__(cls, suffix):
        prefix = config["PRODUCER_TOPIC_PREFIX"]
        obj = object.__new__(cls)
        obj._value_ = f"{prefix}.{suffix}"
        return obj

    SPOT_DEALS = "spot_deals"
    EXCHANGE_DEALS = "exchange_deals"
    PERPETUAL_DEALS = "perpetual_deals"
    DEPOSIT_REWARD = "deposit_reward"
    COPY_TRADING = "copy_trading"
    DEMO_TRADING = "demo_trading"
    BALANCE_UPDATE ="balance_update"

P2P_ASSET_SORT = ["USDT", "USDC", "BTC", "ETH"]

