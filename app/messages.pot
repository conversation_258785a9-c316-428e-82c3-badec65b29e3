# Translations template for PROJECT.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-07-10 11:27+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.13.1\n"

#: app/api/admin/coupon.py:107
msgid "级"
msgstr ""

#: app/api/admin/system.py:840
msgid "系统升级保护期生效中"
msgstr ""

#: app/api/admin/system.py:842 app/api/admin/system.py:887
#, python-format
msgid "预计于 %(end_at_str)s（UTC）结束，当前支持撤单、下单（仅Maker Only限价单）。"
msgstr ""

#: app/api/admin/system.py:846 app/api/admin/system.py:895
#, python-format
msgid "预计于 %(end_at_str)s（UTC）结束，当前支持撤单、下单（仅Maker Only限价单）、增加或减少保证金。"
msgstr ""

#: app/api/admin/system.py:939
#, python-format
msgid "升级结束后，系统将自动进入保护期，持续时间%(protect_duration)s分钟，期间支持撤单、下单（仅Maker Only限价单）。"
msgstr ""

#: app/api/admin/system.py:942
#, python-format
msgid ""
"升级结束后，系统将自动进入保护期，持续时间%(protect_duration)s分钟，期间支持撤单、下单（仅Maker "
"Only限价单）、增加或减少保证金。"
msgstr ""

#: app/api/admin/system.py:948
msgid "币币停服升级即将开始"
msgstr ""

#: app/api/admin/system.py:949
#, python-format
msgid "币币系统升级将于%(started_at_str)s（UTC）开始，时长约%(minute_delta)s分钟，届时暂停现货、杠杆交易与兑换服务，请提前做好风险控制。"
msgstr ""

#: app/api/admin/system.py:952
#, python-format
msgid "币币系统升级即将开始，时长约%(minute_delta)s分钟，届时暂停现货、杠杆交易与兑换服务，请提前做好风险控制。"
msgstr ""

#: app/api/admin/system.py:956
msgid "合约停服升级即将开始"
msgstr ""

#: app/api/admin/system.py:957
#, python-format
msgid "合约系统升级将于%(started_at_str)s（UTC）开始，时长约%(minute_delta)s分钟，届时暂停合约交易，请提前做好风险控制。"
msgstr ""

#: app/api/admin/system.py:960
#, python-format
msgid "合约系统升级即将开始，时长约%(minute_delta)s分钟，届时暂停合约交易，请提前做好风险控制。"
msgstr ""

#: app/api/admin/system.py:964
msgid "币币及合约停服升级即将开始"
msgstr ""

#: app/api/admin/system.py:965
#, python-format
msgid "币币及合约系统升级将于%(started_at_str)s（UTC）开始，时长约%(minute_delta)s分钟，届时暂停现货、杠杆、合约交易与资产划转及兑换服务，请提前做好风险控制。"
msgstr ""

#: app/api/admin/system.py:969
#, python-format
msgid "币币及合约系统升级即将开始，时长约%(minute_delta)s分钟，届时暂停现货、杠杆、合约交易与资产划转及兑换服务，请提前做好风险控制。"
msgstr ""

#: app/api/admin/system.py:973
msgid "全站停服升级即将开始"
msgstr ""

#: app/api/admin/system.py:974
#, python-format
msgid "CoinEx将于%(started_at_str)s（UTC）进行全站升级，届时所有服务暂停%(minute_delta)s分钟，请提前做好风险控制。给你带来的不便，敬请谅解。"
msgstr ""

#: app/api/admin/system.py:978
#, python-format
msgid "CoinEx即将进行全站升级，届时所有服务暂停%(minute_delta)s分钟，请提前做好风险控制。给你带来的不便，敬请谅解。"
msgstr ""

#: app/api/admin/system.py:1406
msgid "该市场当前为保护期，仅支持撤单、下单（仅Maker Only限价单）。"
msgstr ""

#: app/api/admin/system.py:1409
#, python-format
msgid "预计恢复时间为：%(maintain_expect_end_at_str)s (UTC)"
msgstr ""

#: app/api/admin/p2p/config.py:510
msgid "姓名"
msgstr ""

#: app/api/common/responses.py:18 app/api/common/responses.py:36
#: app/api/common/responses.py:46 app/api/common/responses.py:48
msgid "成功"
msgstr ""

#: app/api/common/responses.py:30
msgid "失败"
msgstr ""

#: app/api/common/validators.py:148
#, python-format
msgid "验证码：%(code)s，你正在使用本手机号登录，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:150 app/api/common/validators.py:154
#, python-format
msgid "验证码：%(code)s，你正在修改登录密码，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:152
#, python-format
msgid "验证码：%(code)s，你正在设置登录密码，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:156
#, python-format
msgid "验证码：%(code)s，你正在绑定手机，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:158 app/api/common/validators.py:160
#, python-format
msgid "验证码：%(code)s，你正在修改绑定手机，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:162
#, python-format
msgid "验证码：%(code)s，你正在解绑手机，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:164
#, python-format
msgid "验证码：%(code)s，你正在绑定谷歌验证器，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:166
#, python-format
msgid "验证码：%(code)s，你正在修改谷歌验证器，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:168
#, python-format
msgid "验证码：%(code)s，你正在解绑谷歌验证器，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:170
#, python-format
msgid "验证码：%(code)s，你正在绑定邮箱，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:172
#, python-format
msgid "验证码：%(code)s，你正在修改邮箱，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:174
#, python-format
msgid "验证码：%(code)s，你正在重置安全项，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:176
#, python-format
msgid "验证码：%(code)s，你正在进行还币操作，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:178
#, python-format
msgid "验证码：%(code)s，你正在申请增加提现钱包地址，该码30分钟有效。"
msgstr ""

#: app/api/common/validators.py:180
#, python-format
msgid "验证码：%(code)s，你正在新建白名单地址，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:182
#, python-format
msgid "验证码：%(code)s，你正在申请提现，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:184
#, python-format
msgid "验证码：%(code)s，你正在申请API密钥，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:186
#, python-format
msgid "验证码：%(code)s，你正在修改API密钥，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:188
#, python-format
msgid "验证码：%(code)s，你正在查看API密钥，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:190
#, python-format
msgid "验证码：%(code)s，你正在删除API密钥，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:192
#, python-format
msgid "验证码：%(code)s，你正在续期API密钥，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:194
#, python-format
msgid "验证码：%(code)s，你正在设置交易密码，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:196
#, python-format
msgid "验证码：%(code)s，你正在修改交易密码，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:198
#, python-format
msgid "验证码：%(code)s，你正在关闭交易密码，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:200
#, python-format
msgid "验证码：%(code)s，你正在进行下单操作，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:202
#, python-format
msgid "验证码：%(code)s，你正在购买VIP，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:204
#, python-format
msgid "验证码： %(code)s，你正在注册子账号，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:206
#, python-format
msgid "验证码： %(code)s，你正在修改子账号密码，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:208
#, python-format
msgid "验证码： %(code)s，你正在删除子账号，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:210
#, python-format
msgid "验证码： %(code)s，你正在添加子账号授权，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:212
#, python-format
msgid "验证码： %(code)s，你正在发C-Box，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:215
#, python-format
msgid "验证码： %(code)s, 你正在添加提现审核人，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:218
#, python-format
msgid "验证码： %(code)s, 您正在删除提现审核人，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:221
#, python-format
msgid "验证码：%(code)s，你正在注销账号，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:224
#, python-format
msgid "验证码： %(code)s，你正在添加提现密码，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:226
#, python-format
msgid "验证码： %(code)s，你正在修改提现密码，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:228
#, python-format
msgid "验证码： %(code)s，你正在重置提现密码，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:230
#, python-format
msgid "验证码： %(code)s，你正在创建通行密钥，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:232
#, python-format
msgid "验证码： %(code)s，你正在删除通行密钥，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:234
#, python-format
msgid "验证码： %(code)s，你正在重置通行密钥，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:236
#, python-format
msgid "验证码： %(code)s，你正在进行p2p放币，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:238
#, python-format
msgid "验证码： %(code)s，你正在增加收款方式，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:241
#, python-format
msgid "验证码： %(code)s，你正在修改收款方式，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:244
#, python-format
msgid "验证码： %(code)s，你正在删除收款方式，该码30分钟内有效。"
msgstr ""

#: app/api/common/validators.py:247 app/api/common/validators.py:250
#, python-format
msgid "验证码： %(code)s，你正在绑定第三方账号，该码30分钟内有效。"
msgstr ""

#: app/api/frontend/abnornal_deposit.py:57
msgid "你填写的地址不是你的CoinEx地址，请确认是否输入正确。"
msgstr ""

#: app/api/frontend/abnornal_deposit.py:58
msgid "你填写的交易ID（TxID）与填写充值接收地址不一致，请确认是否输入正确。"
msgstr ""

#: app/api/frontend/abnornal_deposit.py:59
msgid "无法在区块上找到该笔充值记录，请检查交易ID（TxID）或公链类型后重新提交。"
msgstr ""

#: app/api/frontend/abnornal_deposit.py:60
msgid "该笔交易已经申请过，可在下方 申请记录 中查看，请勿重复申请。"
msgstr ""

#: app/api/frontend/abnornal_deposit.py:61
msgid "该笔交易已经申请过，请勿重复申请"
msgstr ""

#: app/api/frontend/abnornal_deposit.py:63
msgid "无需找回，可在充值记录中查看该笔充值。"
msgstr ""

#: app/api/frontend/abnornal_deposit.py:64
msgid "该笔充值不是你本人的充值。"
msgstr ""

#: app/api/frontend/abnornal_deposit.py:65
msgid "该币种/公链暂停充值，请恢复充值后再提交。"
msgstr ""

#: app/api/frontend/abnornal_deposit.py:66
msgid "该笔充值在区块链上还未转账成功，请耐心等待。"
msgstr ""

#: app/api/frontend/abnornal_deposit.py:67
#: app/api/frontend/abnornal_deposit.py:68
msgid "该笔充值无法入账，请联系客服。"
msgstr ""

#: app/api/frontend/abnornal_deposit.py:385
msgid "补充资料不全"
msgstr ""

#: app/api/frontend/abnornal_deposit.py:543
msgid "该币种/公链暂停提现，请恢复后再提交资料"
msgstr ""

#: app/api/frontend/abnornal_deposit.py:547
msgid "不能提交CoinEx地址"
msgstr ""

#: app/api/frontend/account.py:241
#, python-format
msgid "%(offline_assets)s已下架交易市场，不支持兑换"
msgstr ""

#: app/api/frontend/account.py:721 app/models/user.py:3602
msgid "现货账户"
msgstr ""

#: app/api/frontend/account.py:723 app/models/user.py:3605
msgid "理财账户"
msgstr ""

#: app/api/frontend/account.py:725
msgid "借贷账户"
msgstr ""

#: app/api/frontend/account.py:727
#, python-format
msgid "%(market)s-杠杆账户"
msgstr ""

#: app/api/frontend/activity.py:669 app/api/frontend/activity.py:2049
#: app/api/frontend/activity.py:5190 app/api/frontend/activity.py:5888
#: app/api/frontend/activity.py:6153
msgid "你的账户暂被限制参与活动，如有疑问请联系客服。"
msgstr ""

#: app/api/frontend/activity.py:1562 app/api/frontend/activity.py:1567
#: app/api/frontend/activity.py:1577 app/api/frontend/activity.py:1594
#: app/api/frontend/activity.py:1610 app/api/frontend/activity.py:1639
#: app/api/frontend/activity.py:1644 app/api/frontend/activity.py:1651
#: app/api/frontend/activity.py:1834 app/api/frontend/activity.py:2631
msgid "你未满足条件或者未到活动时间"
msgstr ""

#: app/api/frontend/activity.py:1819 app/api/frontend/activity.py:2398
#: app/api/frontend/activity.py:5899
msgid "你未满足条件或不在活动时间内，请刷新后重试"
msgstr ""

#: app/api/frontend/activity.py:1823 app/api/frontend/activity.py:1857
msgid "领取失败，数量已被瓜分完"
msgstr ""

#: app/api/frontend/activity.py:1830
msgid "你已经领取过"
msgstr ""

#: app/api/frontend/activity.py:1850
msgid "不符合领奖要求，领取失败"
msgstr ""

#: app/api/frontend/activity.py:2406 app/api/frontend/activity.py:2423
msgid "你已领取过抽签号"
msgstr ""

#: app/api/frontend/activity.py:2611
msgid "很遗憾！申购已结束"
msgstr ""

#: app/api/frontend/activity.py:2628
msgid "你的KYC国家不支持申购"
msgstr ""

#: app/api/frontend/activity.py:2649
msgid "累计申购超出最大上限"
msgstr ""

#: app/api/frontend/activity.py:2733
msgid "申购已结束，不允许撤销"
msgstr ""

#: app/api/frontend/activity.py:2745
msgid "撤销失败，已达最大撤销上限"
msgstr ""

#: app/api/frontend/activity.py:3158 app/business/coupon/pool.py:130
msgid "value_type"
msgstr ""

#: app/api/frontend/activity.py:3323 app/api/frontend/activity.py:3338
#: app/api/frontend/activity.py:3346
msgid "待激活"
msgstr ""

#: app/api/frontend/activity.py:3324 app/api/frontend/activity.py:3331
#: app/api/frontend/activity.py:3347
msgid "使用中"
msgstr ""

#: app/api/frontend/activity.py:3325 app/api/frontend/activity.py:3332
#: app/api/frontend/activity.py:3340 app/api/frontend/activity.py:3348
msgid "已使用"
msgstr ""

#: app/api/frontend/activity.py:3326 app/api/frontend/activity.py:3333
#: app/api/frontend/activity.py:3341 app/api/frontend/activity.py:3349
msgid "已过期"
msgstr ""

#: app/api/frontend/activity.py:3327 app/api/frontend/activity.py:3334
#: app/api/frontend/activity.py:3342 app/api/frontend/activity.py:3350
#: app/api/frontend/referral.py:2359
msgid "已失效"
msgstr ""

#: app/api/frontend/activity.py:3339
msgid "加息中"
msgstr ""

#: app/api/frontend/activity.py:3618
msgid "空投活动"
msgstr ""

#: app/api/frontend/activity.py:3619
msgid "交易排位赛"
msgstr ""

#: app/api/frontend/activity.py:3717 app/api/frontend/activity.py:3725
#: app/api/frontend/activity.py:4106 app/api/frontend/activity.py:4389
#: app/api/frontend/activity.py:5429
msgid "活动未开始"
msgstr ""

#: app/api/frontend/activity.py:3719 app/api/frontend/activity.py:3727
#: app/api/frontend/activity.py:4108 app/api/frontend/activity.py:4391
#: app/api/frontend/activity.py:5431
msgid "活动已结束"
msgstr ""

#: app/api/frontend/activity.py:3895 app/api/frontend/activity.py:3984
msgid "请先解锁周年账单，领取门票。"
msgstr ""

#: app/api/frontend/activity.py:3993
msgid "未完成对应任务，暂无法开启盲盒"
msgstr ""

#: app/api/frontend/activity.py:4034
msgid "请求繁忙，请稍后再试"
msgstr ""

#: app/api/frontend/amm.py:203 app/api/v2/assets/amm.py:91
#, python-format
msgid "%(market)s市场暂停交易中，无法增加流动性"
msgstr ""

#: app/api/frontend/auth.py:220 app/api/frontend/user.py:1453
#: app/api/frontend/user.py:1779
msgid "由于你绑定了通行密钥，APP端暂不支持验证，请到WEB端设置其他2FA"
msgstr ""

#: app/api/frontend/auth.py:352
msgid "该域名无支持的通行密钥，请选择其他2FA验证或使用支持通行密钥的域名进行验证。"
msgstr ""

#: app/api/frontend/auth.py:354
msgid "该域名无支持的通行密钥，请使用支持通行密钥的域名进行验证。"
msgstr ""

#: app/api/frontend/auto_invest.py:36
msgid "暂不支持定投"
msgstr ""

#: app/api/frontend/auto_invest.py:37
msgid "该市场暂不支持定投"
msgstr ""

#: app/api/frontend/auto_invest.py:38
msgid "该币对深度不足，暂不支持定投"
msgstr ""

#: app/api/frontend/auto_invest.py:39
msgid "该币对定投金额不支持定投"
msgstr ""

#: app/api/frontend/auto_invest.py:380 app/api/frontend/auto_invest.py:461
#: app/api/frontend/auto_invest.py:548
msgid "本次定投正在购买，请稍后再试"
msgstr ""

#: app/api/frontend/auto_invest.py:537
msgid "策略已终止，无法立即定投"
msgstr ""

#: app/api/frontend/blog.py:387
msgid "CoinEx 博客"
msgstr ""

#: app/api/frontend/blog.py:389
msgid ""
"CoinEx "
"博客为你提供比特币、以太坊、莱特币、比特币现金等数字货币科普知识、区块链行业动态分析、热门技术见解等，在这里你能了解到最专业全面的数字货币信息。"
msgstr ""

#: app/api/frontend/broker.py:66
msgid "您已经是经纪商"
msgstr ""

#: app/api/frontend/broker.py:242
#, python-format
msgid "已超过每日导出次数%(num)s次"
msgstr ""

#: app/api/frontend/broker.py:252 app/api/frontend/sub_accounts.py:528
msgid "时间"
msgstr ""

#: app/api/frontend/broker.py:252
msgid "总交易用户"
msgstr ""

#: app/api/frontend/broker.py:252 app/api/frontend/referral.py:697
#: app/api/frontend/referral.py:704
msgid "现货交易额"
msgstr ""

#: app/api/frontend/broker.py:252 app/api/frontend/referral.py:698
#: app/api/frontend/referral.py:704 app/business/user_group.py:209
msgid "合约交易额"
msgstr ""

#: app/api/frontend/broker.py:253
msgid "总交易额"
msgstr ""

#: app/api/frontend/broker.py:253 app/api/frontend/referral.py:1076
msgid "现货手续费"
msgstr ""

#: app/api/frontend/broker.py:253 app/api/frontend/referral.py:1076
#: app/schedules/reports/ambassador_report.py:2565
msgid "合约手续费"
msgstr ""

#: app/api/frontend/broker.py:253 app/api/frontend/referral.py:1076
msgid "总手续费"
msgstr ""

#: app/api/frontend/broker.py:253 app/api/frontend/referral.py:1076
msgid "返佣金额"
msgstr ""

#: app/api/frontend/copy_trading.py:332
msgid "卡券已经被激活使用，不可重复激活使用。"
msgstr ""

#: app/api/frontend/copy_trading.py:1144
msgid "请调整跟单金额后重新提交"
msgstr ""

#: app/api/frontend/copy_trading.py:1187
#: app/business/copy_trading/trader.py:661
msgid "系统正处理你的结束带单请求，禁止操作"
msgstr ""

#: app/api/frontend/copy_trading.py:1301
msgid "超过最大可跟单金额划入"
msgstr ""

#: app/api/frontend/copy_trading.py:1771 app/api/frontend/copy_trading.py:2001
msgid "仅支持跟随交易员保证金模式，请重试"
msgstr ""

#: app/api/frontend/copy_trading.py:1773 app/api/frontend/copy_trading.py:2003
msgid "仅支持跟随交易员杠杆倍数，请重试"
msgstr ""

#: app/api/frontend/copy_trading.py:1775 app/api/frontend/copy_trading.py:2005
msgid "当前不支持设置止盈或止损，请清空后重试"
msgstr ""

#: app/api/frontend/copy_trading.py:1779 app/api/frontend/copy_trading.py:1790
msgid "交易员不支持跟单"
msgstr ""

#: app/api/frontend/exchange.py:307
msgid "暂不支持兑换"
msgstr ""

#: app/api/frontend/exchange.py:308
msgid "该币对暂不支持兑换"
msgstr ""

#: app/api/frontend/exchange.py:309
msgid "该币对深度不足，暂不支持兑换"
msgstr ""

#: app/api/frontend/exchange.py:407
#, python-format
msgid "%(market)s市场暂停交易中，无法发起兑换"
msgstr ""

#: app/api/frontend/exchange.py:420
#, python-format
msgid "超过最大兑换数量 %(max_exchange_amount)s %(asset)s"
msgstr ""

#: app/api/frontend/fiat.py:194
msgid "输入的资产数量过大或过小"
msgstr ""

#: app/api/frontend/fiat.py:199
#, python-format
msgid "%(partner)s异常，请稍后重试"
msgstr ""

#: app/api/frontend/insight.py:309
msgid "CoinEx 洞见"
msgstr ""

#: app/api/frontend/insight.py:311
msgid "深度解读，发现独到观点，把握投资机会。"
msgstr ""

#: app/api/frontend/investment.py:122
#, python-format
msgid "低于最小划转数量 %(min_amount)s %(asset)s"
msgstr ""

#: app/api/frontend/investment.py:136 app/api/frontend/margin.py:160
#: app/api/frontend/perpetual/balance.py:79
#: app/api/frontend/perpetual/balance.py:131
msgid "划转成功"
msgstr ""

#: app/api/frontend/kyc.py:70
msgid "证件号码已被使用"
msgstr ""

#: app/api/frontend/kyc.py:87
msgid "高级认证已通过或待审核时不允许发起初级认证重新认证。"
msgstr ""

#: app/api/frontend/kyc.py:101
msgid "你已完成机构认证，无法重新提交。"
msgstr ""

#: app/api/frontend/kyc.py:208 app/api/frontend/kyc.py:381
msgid "不正确的证件号码"
msgstr ""

#: app/api/frontend/kyc.py:456
#, python-format
msgid "目前CoinEx 实名认证不支持%(countries)s的用户；"
msgstr ""

#: app/api/frontend/launch_pool.py:278
msgid "你的账号处于清退状态，无法参与挖矿。如需更多帮助，可提交工单咨询。"
msgstr ""

#: app/api/frontend/launch_pool.py:280
msgid "该账号异常，无法参与挖矿，请提交工单处理。"
msgstr ""

#: app/api/frontend/margin.py:606 app/api/frontend/pledge.py:428
#, python-format
msgid "还币后剩余待还数不能低于%(min_amount)s%(coin_type)s,请调整还币数量."
msgstr ""

#: app/api/frontend/market_maker.py:269
msgid "该邮箱为本人账号"
msgstr ""

#: app/api/frontend/market_maker.py:277
#, python-format
msgid "最多增加%(num)s个邮箱。"
msgstr ""

#: app/api/frontend/market_maker.py:284
msgid "该邮箱已添加"
msgstr ""

#: app/api/frontend/market_maker.py:381
msgid "您已经是做市商"
msgstr ""

#: app/api/frontend/market_maker.py:394 app/api/frontend/referral.py:1264
msgid "你已提交申请，请等待审核"
msgstr ""

#: app/api/frontend/mission.py:107
#, python-format
msgid ""
"注册 %(deadline_days)s 天内完成%(mission_type)s，且累积金额 ≥ %(total)s %(unit)s，获得 "
"%(value)s %(value_type)s %(reward_type)s奖励。\n"
"请注意：\n"
"1. 请务必在规定时间内完成任务，超出活动时间的入金将无法计入有效金额；\n"
"2. 链上充值、P2P买币、第三方买币均为有效入金，不包括站内转账和C-Box转账。"
msgstr ""

#: app/api/frontend/mission.py:122
#, python-format
msgid ""
"注册 %(deadline_days)s 天内完成%(mission_type)s，且累积金额 ≥ %(total)s %(unit)s，获得 "
"%(value)s %(value_type)s %(reward_type)s奖励。\n"
"请注意：\n"
"1. 请务必在规定时间内完成任务，超出活动时间的币币交易将无法计入有效交易额；\n"
"2. 兑换交易、现货交易、杠杆交易、策略交易均为有效币币交易；\n"
"3. 子账户无法独立参与任务，交易金额将合并计入主账户。"
msgstr ""

#: app/api/frontend/mission.py:138
#, python-format
msgid ""
"注册 %(deadline_days)s 天内完成%(mission_type)s，且累积金额 ≥ %(total)s %(unit)s，获得 "
"%(value)s %(value_type)s %(reward_type)s奖励。\n"
"请注意：\n"
"1. 请务必在规定时间内完成任务，超出活动时间的合约交易将无法计入有效交易额；\n"
"2. 正向合约和反向合约均为有效合约交易；\n"
"3. 子账户无法独立参与任务，交易金额将合并计入主账户。"
msgstr ""

#: app/api/frontend/mission.py:154
#, python-format
msgid ""
"注册 %(deadline_days)s 天内完成一次%(mission_type)s，获得 %(value)s %(value_type)s "
"%(reward_type)s奖励。\n"
"请注意：\n"
"请务必在规定时间内完成任务，超出活动时间的跟单交易将不再统计。"
msgstr ""

#: app/api/frontend/mission.py:166
#, python-format
msgid ""
"注册 %(deadline_days)s 天内完成一次%(mission_type)s，获得 %(value)s %(value_type)s "
"%(reward_type)s奖励。\n"
"请注意：\n"
"请务必在规定时间内完成任务，超出活动时间的模拟交易将不再统计。"
msgstr ""

#: app/api/frontend/mission.py:182
msgid ""
"手续费返现奖励将在任务达标后发放并立即生效。\n"
"请在有效期内根据规则使用权益，并获得手续费返现。\n"
"请在「我的奖励」页面查看详情。\n"
"\n"
"CoinEx禁止作弊行为，如批量注册小号等，一经发现将取消其获奖资格。"
msgstr ""

#: app/api/frontend/mission.py:189
msgid ""
"空投奖励将在任务达标后发放至现货账户。\n"
"请在「我的奖励」页面查看详情。\n"
"\n"
"CoinEx禁止作弊行为，如批量注册小号等，一经发现将取消其获奖资格。"
msgstr ""

#: app/api/frontend/operation.py:187
msgid "请注意：该资产符合[r]CoinEx ST规则[/r]，已进入观察期，可能存在下架风险，请注意防范风险"
msgstr ""

#: app/api/frontend/operation.py:189
msgid "请注意：CoinEx已启动该资产的下架流程，[r]请参考公告了解详情[/r] >>"
msgstr ""

#: app/api/frontend/operation.py:191
msgid "请注意：CoinEx已启动该资产的维护流程，[r]请参考公告了解详情[/r] >>"
msgstr ""

#: app/api/frontend/operation.py:193
msgid "风险提示：该资产近期存在负面新闻或价格异常波动，请谨慎评估投资风险。"
msgstr ""

#: app/api/frontend/order.py:99
msgid "部分订单不可撤销"
msgstr ""

#: app/api/frontend/order.py:370 app/api/frontend/order.py:441
#: app/api/frontend/order.py:521 app/api/frontend/order.py:640
#: app/api/frontend/order.py:754 app/api/frontend/order.py:845
#: app/api/frontend/perpetual/order.py:270
#: app/api/frontend/perpetual/order.py:458
#: app/api/frontend/perpetual/order.py:531
#: app/api/frontend/perpetual/order.py:570
#: app/api/frontend/perpetual/order.py:626
#: app/api/frontend/perpetual/order.py:665
#: app/api/frontend/perpetual/order.py:783
#: app/api/frontend/perpetual/order.py:859
#: app/api/frontend/perpetual/order.py:1011
#: app/api/frontend/perpetual/order.py:1080
#: app/api/frontend/perpetual/position.py:869
#: app/api/frontend/perpetual/position.py:932
#: app/api/frontend/perpetual/position.py:976
msgid "下单成功"
msgstr ""

#: app/api/frontend/order.py:406 app/api/frontend/order.py:482
msgid "集合竞价第二阶段，暂不支持修改"
msgstr ""

#: app/api/frontend/order.py:803
msgid "预测市场暂不支持计划市价的订单"
msgstr ""

#: app/api/frontend/pledge.py:254
#, python-format
msgid "你使用的借币币种%(asset)s已下架，无法操作"
msgstr ""

#: app/api/frontend/pledge.py:268 app/exceptions/denied.py:21
msgid "禁止操作"
msgstr ""

#: app/api/frontend/pledge.py:274 app/api/frontend/pledge.py:305
#: app/api/frontend/pledge.py:470
#, python-format
msgid "你使用的质押币%(asset)s临时下架，无法操作"
msgstr ""

#: app/api/frontend/pledge.py:308 app/api/frontend/pledge.py:473
#, python-format
msgid "你使用的质押币%(asset)s已下架，无法操作"
msgstr ""

#: app/api/frontend/pre_trading.py:258 app/business/pre_trading.py:270
msgid "请求频繁，请稍后再试"
msgstr ""

#: app/api/frontend/pre_trading.py:300
msgid "赎回数量需小于等于发行数量"
msgstr ""

#: app/api/frontend/questionnaire.py:90 app/api/frontend/questionnaire.py:95
msgid "问卷已结束，无法提交"
msgstr ""

#: app/api/frontend/questionnaire.py:93
msgid "问卷未开始，无法提交"
msgstr ""

#: app/api/frontend/questionnaire.py:99
msgid "问卷已提交，请勿重复操作"
msgstr ""

#: app/api/frontend/quotes.py:123
msgid "集合竞价或倒计时中，不支持收藏"
msgstr ""

#: app/api/frontend/quotes.py:127
msgid "新币还未上线，暂不支持收藏"
msgstr ""

#: app/api/frontend/referral.py:697 app/api/frontend/referral.py:703
#: app/api/frontend/referral.py:1076
msgid "账户"
msgstr ""

#: app/api/frontend/referral.py:697 app/api/frontend/referral.py:703
#: app/schedules/reports/ambassador_report.py:2558
msgid "注册时间"
msgstr ""

#: app/api/frontend/referral.py:697 app/api/frontend/referral.py:703
msgid "是否充值"
msgstr ""

#: app/api/frontend/referral.py:697 app/api/frontend/referral.py:703
msgid "是否交易"
msgstr ""

#: app/api/frontend/referral.py:697 app/api/frontend/referral.py:704
msgid "现货返佣"
msgstr ""

#: app/api/frontend/referral.py:698 app/api/frontend/referral.py:704
#: app/schedules/reports/ambassador_report.py:2567
msgid "合约返佣"
msgstr ""

#: app/api/frontend/referral.py:698 app/api/frontend/referral.py:704
#: app/api/frontend/referral.py:2341
msgid "状态"
msgstr ""

#: app/api/frontend/referral.py:703
msgid "领取时间"
msgstr ""

#: app/api/frontend/referral.py:703
msgid "卡券名称"
msgstr ""

#: app/api/frontend/referral.py:1076
msgid "发放时间"
msgstr ""

#: app/api/frontend/referral.py:1076
msgid "累计邀请交易量"
msgstr ""

#: app/api/frontend/referral.py:1256
msgid "你已经是CoinEx大使"
msgstr ""

#: app/api/frontend/referral.py:2338 app/api/frontend/referral.py:2580
#: app/schedules/reports/ambassador_report.py:2557
msgid "账户名"
msgstr ""

#: app/api/frontend/referral.py:2338
msgid "绑定时间"
msgstr ""

#: app/api/frontend/referral.py:2338
msgid "首次入金时间"
msgstr ""

#: app/api/frontend/referral.py:2338
msgid "首次入金金额（USD）"
msgstr ""

#: app/api/frontend/referral.py:2338
msgid "总入金金额（USD）"
msgstr ""

#: app/api/frontend/referral.py:2338
#: app/schedules/reports/ambassador_report.py:2560
msgid "首次交易时间"
msgstr ""

#: app/api/frontend/referral.py:2339
msgid "首次交易金额（USD）"
msgstr ""

#: app/api/frontend/referral.py:2339
msgid "总现货交易金额（USD）"
msgstr ""

#: app/api/frontend/referral.py:2339
msgid "总合约交易金额（USD）"
msgstr ""

#: app/api/frontend/referral.py:2340
msgid "总现货手续费（USD）"
msgstr ""

#: app/api/frontend/referral.py:2340
msgid "总合约手续费（USD）"
msgstr ""

#: app/api/frontend/referral.py:2341
msgid "总现货返佣"
msgstr ""

#: app/api/frontend/referral.py:2341
msgid "总合约返佣"
msgstr ""

#: app/api/frontend/referral.py:2359
msgid "生效中"
msgstr ""

#: app/api/frontend/referral.py:2448 app/api/frontend/referral.py:2580
#: app/schedules/reports/daily_market_maker_trade_report.py:31
#: app/schedules/reports/daily_market_maker_trade_report.py:41
#: app/schedules/reports/daily_market_maker_trade_report.py:55
#: app/schedules/reports/daily_market_maker_trade_report.py:65
#: app/schedules/reports/daily_market_maker_trade_report.py:79
#: app/templates/email/notice/user_trade_summary.j2:69
#: app/templates/email/notice/user_trade_summary.j2:99
#: app/templates/email/notice/user_trade_summary.j2:127
#: app/templates/email/notice/user_trade_summary.j2:164
#: app/templates/email/notice/user_trade_summary.j2:193
#: app/templates/email/notice/user_trade_summary.j2:230
msgid "日期"
msgstr ""

#: app/api/frontend/referral.py:2448 app/api/frontend/referral.py:2580
msgid "总返佣金额"
msgstr ""

#: app/api/frontend/referral.py:2448
msgid "交易用户数"
msgstr ""

#: app/api/frontend/referral.py:2448 app/api/frontend/referral.py:2580
msgid "现货交易额（USD）"
msgstr ""

#: app/api/frontend/referral.py:2448 app/api/frontend/referral.py:2580
msgid "合约交易额（USD）"
msgstr ""

#: app/api/frontend/referral.py:2448 app/api/frontend/referral.py:2580
msgid "总交易额（USD）"
msgstr ""

#: app/api/frontend/referral.py:2449 app/api/frontend/referral.py:2581
msgid "现货手续费（USD）"
msgstr ""

#: app/api/frontend/referral.py:2449 app/api/frontend/referral.py:2581
msgid "合约手续费（USD）"
msgstr ""

#: app/api/frontend/referral.py:2449 app/api/frontend/referral.py:2581
msgid "总手续费（USD）"
msgstr ""

#: app/api/frontend/referral.py:2799
msgid "激励包已过期，无法领取"
msgstr ""

#: app/api/frontend/reward_center.py:187
msgid "币币市场"
msgstr ""

#: app/api/frontend/reward_center.py:188
msgid "合约市场"
msgstr ""

#: app/api/frontend/reward_center.py:189
msgid "所有市场"
msgstr ""

#: app/api/frontend/reward_center.py:195
#, python-format
msgid ""
"1. 返现范围：%(cashback_scope)s；\n"
"2. 返现时间：符合使用条件的交易订单将在1-3小时内返还手续费；\n"
"3. 返还形式：以 %(cashback_asset)s 形式发放到现货账户；\n"
"4. 注意事项：\n"
"（1）返现额度用完或返现权益到期后，无法继续使用该返现奖励；\n"
"（2）如获得多张相同范围的返现奖励，系统会按一定顺序依次消耗额度；\n"
"（3）手续费返现奖励适用于VIP等级、CET费率折扣；\n"
"（4）手续费返现奖励不适用于AMM市场。\n"
"5. CoinEx保留手续费返现奖励相关规则的最终解释权。\n"
msgstr ""

#: app/api/frontend/reward_center.py:209
msgid "空投奖励已发放到现货账户，可划转或交易。请前往现货账户查看。"
msgstr ""

#: app/api/frontend/strategy.py:342 app/exceptions/user.py:590
msgid "暂不支持"
msgstr ""

#: app/api/frontend/sub_accounts.py:407 app/api/v2/account.py:672
#, python-format
msgid "%(name)s无合约交易权限，不可向合约账户转入资金"
msgstr ""

#: app/api/frontend/sub_accounts.py:528
#: app/templates/email/confirmation/add_api_address.j2:13
msgid "币种"
msgstr ""

#: app/api/frontend/sub_accounts.py:528
msgid "数量"
msgstr ""

#: app/api/frontend/sub_accounts.py:528
msgid "转出"
msgstr ""

#: app/api/frontend/sub_accounts.py:528
msgid "转入"
msgstr ""

#: app/api/frontend/sub_accounts.py:645
msgid "关闭杠杆功能时杠杆账户资产需为0 USD，功能关闭后不允许资金转入"
msgstr ""

#: app/api/frontend/sub_accounts.py:660
msgid "关闭合约功能时合约账户资产需为0 USD，功能关闭后不允许资金转入"
msgstr ""

#: app/api/frontend/sub_accounts.py:678
msgid "关闭AMM时AMM账户资产需为0 USD，功能关闭后不允许增加流动性"
msgstr ""

#: app/api/frontend/sub_accounts.py:767
msgid "被授权人不存在，请重新确认账号。"
msgstr ""

#: app/api/frontend/sub_accounts.py:771
msgid "当前子账号为本人创建，请授权其他主账号管理"
msgstr ""

#: app/api/frontend/sub_accounts.py:788
msgid "已存在授权关系，请勿重复授权"
msgstr ""

#: app/api/frontend/tutorial.py:13
msgid "以下哪个不是正向合约与反向合约的差异？"
msgstr ""

#: app/api/frontend/tutorial.py:15
msgid "保证金资产种类"
msgstr ""

#: app/api/frontend/tutorial.py:16
msgid "计价单位"
msgstr ""

#: app/api/frontend/tutorial.py:17
msgid "杠杆倍数"
msgstr ""

#: app/api/frontend/tutorial.py:21
msgid "Tips：正向合约的保证金资产为定价货币，反向合约的保证金资产为交易货币。比如正向合约BTCUSDT的保证金为USDT，计价单位为USDT；反向合约BTCUSD的保证金为BTC，计价单位为USD。正向合约和反向合约，均支持最高100倍杠杆。"
msgstr ""

#: app/api/frontend/tutorial.py:29
msgid "关于“全仓”和“逐仓“保证金模式，以下哪个描述不正确？"
msgstr ""

#: app/api/frontend/tutorial.py:31
msgid "全仓模式下，合约账户中所有可用余额均可用作当前仓位的保证金"
msgstr ""

#: app/api/frontend/tutorial.py:32
msgid "逐仓模式下，只有当前仓位的保证金用于维持仓位，或自行手动追加"
msgstr ""

#: app/api/frontend/tutorial.py:33
msgid "当前委托存在订单记录时，可以随意切换全仓或者逐仓模式"
msgstr ""

#: app/api/frontend/tutorial.py:36
msgid "Tips：若当前交易市场存在委托订单时，不可以切换全仓逐仓，也不可以进行杠杆倍数更改。"
msgstr ""

#: app/api/frontend/tutorial.py:39
msgid "CoinEx合约采用哪种价格来决定强平价格？"
msgstr ""

#: app/api/frontend/tutorial.py:41
msgid "指数价格"
msgstr ""

#: app/api/frontend/tutorial.py:42
msgid "标记价格"
msgstr ""

#: app/api/frontend/tutorial.py:43
msgid "最新价格"
msgstr ""

#: app/api/frontend/tutorial.py:47
msgid "Tips：为了减少合约交易过程中价格操纵和流动性缺失导致的损失，CoinEx会引用多种价格指标供用户参考。"
msgstr ""

#: app/api/frontend/tutorial.py:48
msgid "最新价格：指CoinEx平台中，当前合约市场的最新成交价格。"
msgstr ""

#: app/api/frontend/tutorial.py:49
msgid "指数价格：取多家主流交易所现货市场价格的加权平均值，每5s更新一次。"
msgstr ""

#: app/api/frontend/tutorial.py:50
msgid "标记价格：基于指数价格和资金费率计算得出，主要作为当前持仓强平处理的参考价格。"
msgstr ""

#: app/api/frontend/tutorial.py:54
msgid "关于“爆仓风险率”以下描述不正确的是？"
msgstr ""

#: app/api/frontend/tutorial.py:56
msgid "爆仓风险率根据仓位保证金及当前仓位所需的维持保证金计算，爆仓风险率数值越高表示风险越高"
msgstr ""

#: app/api/frontend/tutorial.py:57
msgid "爆仓风险率达到100%时，平台会发出强平预警通知"
msgstr ""

#: app/api/frontend/tutorial.py:58
msgid "爆仓风险率达到100%时，触发强制平仓流程"
msgstr ""

#: app/api/frontend/tutorial.py:61
msgid "Tips：爆仓风险率数值达到70%时，平台会发出强平预警通知。"
msgstr ""

#: app/api/frontend/tutorial.py:64
msgid "合约交易最大的亏损风险为？"
msgstr ""

#: app/api/frontend/tutorial.py:66
msgid "合约账户的全部余额"
msgstr ""

#: app/api/frontend/tutorial.py:67
msgid "合约账户余额的50%"
msgstr ""

#: app/api/frontend/tutorial.py:68
msgid "合约交易不会亏损"
msgstr ""

#: app/api/frontend/tutorial.py:72
msgid "Tips：合约交易具有高杠杆性和高风险性，当仓位保证金无法满足维持保证金的要求时将被强制平仓，损失全部保证金，因此合约交易有风险，请合理控制仓位。"
msgstr ""

#: app/api/frontend/tutorial.py:357
msgid "question"
msgstr ""

#: app/api/frontend/user.py:723
#, python-format
msgid "登录已锁定，请%(minutes)s分钟后再尝试。"
msgstr ""

#: app/api/frontend/user.py:2004 app/api/frontend/user.py:2072
msgid "IP地址错误"
msgstr ""

#: app/api/frontend/user.py:2070
msgid "超出最大上限，IP最多可配置50个"
msgstr ""

#: app/api/frontend/user.py:2672 app/api/frontend/user.py:2723
#: app/api/frontend/user.py:3084
msgid "24H内只允许提交5次"
msgstr ""

#: app/api/frontend/user.py:2719 app/api/frontend/user.py:2729
msgid "用户名错误或权限受限"
msgstr ""

#: app/api/frontend/user.py:2769 app/api/frontend/user.py:2807
msgid "Sorry, you are not qualified to answer questions"
msgstr ""

#: app/api/frontend/user.py:3055
msgid "该账号未被自助冻结，无法提交"
msgstr ""

#: app/api/frontend/user.py:3180 app/api/frontend/user.py:3465
#: app/api/frontend/user.py:3725
msgid "有资料未提交，请补充"
msgstr ""

#: app/api/frontend/user.py:4171
#, python-format
msgid "%(source)s邮箱已注册，暂无法绑定"
msgstr ""

#: app/api/frontend/user.py:4174
#, python-format
msgid "%(source)s账号已绑定其他CoinEx账号，无法重复绑定"
msgstr ""

#: app/api/frontend/user.py:4267
msgid "邮箱已存在，无法重复注册"
msgstr ""

#: app/api/frontend/wallet.py:691
msgid "该账号已失效，不支持内部转账"
msgstr ""

#: app/api/frontend/wallet.py:1913
msgid "请勿上传空表格"
msgstr ""

#: app/api/frontend/wallet.py:1917
#, python-format
msgid "超过上传地址限制条数:%(count)s"
msgstr ""

#: app/api/frontend/wallet.py:1975 app/exceptions/basic.py:209
msgid "提现地址错误。"
msgstr ""

#: app/api/frontend/wallet.py:2287 app/exceptions/p2p.py:109
msgid "用户名不得包含特殊字符和空格"
msgstr ""

#: app/api/frontend/p2p/mer_act.py:150 app/api/frontend/p2p/mer_act.py:410
msgid "活动不存在"
msgstr ""

#: app/api/frontend/p2p/mer_act.py:404
msgid "已报名"
msgstr ""

#: app/api/frontend/p2p/mer_act.py:412
msgid "不在活动报名时间内"
msgstr ""

#: app/api/frontend/p2p/mer_act.py:417
msgid "没有符合活动的支付渠道"
msgstr ""

#: app/api/frontend/perpetual/market.py:770
#: app/api/frontend/perpetual/position.py:532
msgid "设置成功"
msgstr ""

#: app/api/frontend/perpetual/market.py:779
#: app/api/frontend/perpetual/market.py:800
#: app/api/frontend/perpetual/position.py:314
msgid "deal_type"
msgstr ""

#: app/api/frontend/perpetual/order.py:624
#: app/api/frontend/perpetual/position.py:930
msgid "市场波动剧烈，无法执行市价平仓，请稍后再试"
msgstr ""

#: app/api/frontend/perpetual/order.py:1110
msgid "市价"
msgstr ""

#: app/api/frontend/perpetual/order.py:1111
msgid "限价"
msgstr ""

#: app/api/frontend/perpetual/order.py:1251
#: app/api/frontend/perpetual/order.py:1270
msgid "target"
msgstr ""

#: app/api/frontend/perpetual/order.py:1252
#: app/api/frontend/perpetual/order.py:1271
msgid "计划市价"
msgstr ""

#: app/api/frontend/perpetual/order.py:1252
#: app/api/frontend/perpetual/order.py:1271
msgid "计划限价"
msgstr ""

#: app/api/frontend/perpetual/position.py:371
#: app/api/frontend/perpetual/position.py:984
msgid "typeposition_type"
msgstr ""

#: app/api/frontend/perpetual/position.py:373
#: app/business/perpetual/position.py:186
msgid "type"
msgstr ""

#: app/api/frontend/perpetual/position.py:530
msgid "余额不足，调整失败"
msgstr ""

#: app/api/frontend/perpetual/position.py:595
msgid "止损价格应低于当前价格"
msgstr ""

#: app/api/frontend/perpetual/position.py:598
msgid "止损价格应高于当前价格"
msgstr ""

#: app/api/frontend/perpetual/position.py:610
msgid "止损设置成功"
msgstr ""

#: app/api/frontend/perpetual/position.py:637
msgid "止损撤销成功"
msgstr ""

#: app/api/frontend/perpetual/position.py:713
#: app/api/frontend/perpetual/position.py:752 app/exceptions/perpetual.py:98
msgid "该仓位不存在"
msgstr ""

#: app/api/frontend/perpetual/position.py:723
msgid "止盈价格应高于当前价格"
msgstr ""

#: app/api/frontend/perpetual/position.py:726
msgid "止盈价格应低于当前价格"
msgstr ""

#: app/api/frontend/perpetual/position.py:737
msgid "止盈设置成功"
msgstr ""

#: app/api/frontend/perpetual/position.py:762
msgid "止盈撤销成功"
msgstr ""

#: app/business/ambassador.py:2127
msgid "你的专属激励包已就位"
msgstr ""

#: app/business/ambassador.py:2128
#, python-format
msgid "你的专属%(package_amount)s %(asset)s大使激励包待领取"
msgstr ""

#: app/business/comment.py:62
msgid "不支持赠送自己礼物"
msgstr ""

#: app/business/comment.py:68 app/business/comment.py:75
msgid "当前账户不支持此功能"
msgstr ""

#: app/business/comment.py:82
msgid "你设置了提现多人审核，当前无法使用此功能"
msgstr ""

#: app/business/comment.py:91
msgid "可用余额不足"
msgstr ""

#: app/business/comment.py:93
#, python-format
msgid "超出可提现数量，当日剩余可赠送%(amount)s %(asset)s"
msgstr ""

#: app/business/comment.py:95 app/exceptions/comment.py:66
#, python-format
msgid "超出当日限额，当日剩余可赠送%(amount)s %(asset)s"
msgstr ""

#: app/business/comment.py:138
msgid "无法打赏"
msgstr ""

#: app/business/demo_trading.py:4
msgid "【模拟交易】合约成交提醒"
msgstr ""

#: app/business/demo_trading.py:5
msgid "【模拟交易】合约委托提醒"
msgstr ""

#: app/business/demo_trading.py:6
msgid "【模拟交易】合约止盈成功通知"
msgstr ""

#: app/business/demo_trading.py:7
msgid "【模拟交易】合约止盈部分失败通知"
msgstr ""

#: app/business/demo_trading.py:8
msgid "【模拟交易】合约止损成功通知"
msgstr ""

#: app/business/demo_trading.py:9
msgid "【模拟交易】合约止损部分失败通知"
msgstr ""

#: app/business/demo_trading.py:10
msgid "【模拟交易】合约平仓提醒"
msgstr ""

#: app/business/demo_trading.py:11
msgid "【模拟交易】合约强平预警"
msgstr ""

#: app/business/demo_trading.py:12
msgid "【模拟交易】合约降档减仓通知"
msgstr ""

#: app/business/demo_trading.py:13
msgid "【模拟交易】合约自动减仓"
msgstr ""

#: app/business/demo_trading.py:14
msgid "【模拟交易】合约强平通知"
msgstr ""

#: app/business/demo_trading.py:15
msgid "领取失败，存在当前持仓或委托"
msgstr ""

#: app/business/demo_trading.py:16
msgid "重置失败，存在当前持仓或委托"
msgstr ""

#: app/business/demo_trading.py:17
msgid "当日重置已达上限"
msgstr ""

#: app/business/email.py:99 app/business/email.py:441
msgid "【CoinEx】欢迎注册"
msgstr ""

#: app/business/email.py:100
msgid "【CoinEx】登录提醒"
msgstr ""

#: app/business/email.py:101
msgid "【CoinEx】账号注销提醒"
msgstr ""

#: app/business/email.py:102 app/business/email.py:150
#: app/templates/email/notice/withdrawal_cancelled_notice.j2:3
#: app/templates/email/notice/withdrawal_expired_notice.j2:3
msgid "【CoinEx】提现撤销"
msgstr ""

#: app/business/email.py:104
msgid "【CoinEx】合约强制平仓通知"
msgstr ""

#: app/business/email.py:106
msgid "【CoinEx】多次尝试登录提醒"
msgstr ""

#: app/business/email.py:108
msgid "【CoinEx】重置安全项失败"
msgstr ""

#: app/business/email.py:110
msgid "【CoinEx】修改交易密码成功"
msgstr ""

#: app/business/email.py:111
msgid "【CoinEx】你的实名认证已通过审核"
msgstr ""

#: app/business/email.py:112
msgid "【CoinEx】你的实名认证未通过审核"
msgstr ""

#: app/business/email.py:113
msgid "【CoinEx】KYC高级认证通过"
msgstr ""

#: app/business/email.py:114
msgid "【CoinEx】KYC高级认证不通过"
msgstr ""

#: app/business/email.py:115
msgid "【CoinEx】请提供充值证明文件"
msgstr ""

#: app/business/email.py:116
msgid "【CoinEx】请重新提供充值证明文件"
msgstr ""

#: app/business/email.py:117 app/business/email.py:121
msgid "【CoinEx】充值证明审核不通过"
msgstr ""

#: app/business/email.py:118
msgid "【CoinEx】帐户只支持提现资产"
msgstr ""

#: app/business/email.py:119
msgid "【CoinEx】请提供充值证明"
msgstr ""

#: app/business/email.py:120
msgid "【CoinEx】请提供充值证明补充资料"
msgstr ""

#: app/business/email.py:122
msgid "【CoinEx】请提供增强尽职调查证明"
msgstr ""

#: app/business/email.py:123
msgid "【CoinEx】请提供增强尽职调查证明补充资料"
msgstr ""

#: app/business/email.py:124
msgid "【CoinEx】增强尽职调查证明审核通过"
msgstr ""

#: app/business/email.py:125
msgid "【CoinEx】请尽快提币或处置资产"
msgstr ""

#: app/business/email.py:126
msgid "【CoinEx】充值成功"
msgstr ""

#: app/business/email.py:127
msgid "【CoinEx】充值隐私币请完成实名认证"
msgstr ""

#: app/business/email.py:128
#: app/templates/email/notice/kyc_institution_pass.j2:3
msgid "机构认证通过"
msgstr ""

#: app/business/email.py:129
#: app/templates/email/notice/kyc_institution_fail.j2:2
msgid "机构认证失败"
msgstr ""

#: app/business/email.py:130
msgid "【CoinEx】充值低于最小金额"
msgstr ""

#: app/business/email.py:132
msgid "【CoinEx】强平通知"
msgstr ""

#: app/business/email.py:133
#: app/templates/email/notice/perpetual_market_maker_level_appraisal.j2:3
msgid "【CoinEx】合约做市商月度考核"
msgstr ""

#: app/business/email.py:135
msgid "【CoinEx】异地登录提醒"
msgstr ""

#: app/business/email.py:137
msgid "【CoinEx】重置手机成功"
msgstr ""

#: app/business/email.py:138
#: app/templates/email/notice/ambassador_agent_appraisal.j2:4
msgid "【CoinEx】大使推荐官月度考核"
msgstr ""

#: app/business/email.py:140
msgid "【CoinEx】资金密码修改提醒"
msgstr ""

#: app/business/email.py:142
msgid "【CoinEx】合约自动减仓通知"
msgstr ""

#: app/business/email.py:143
msgid "【CoinEx】合约降档减仓通知"
msgstr ""

#: app/business/email.py:144
msgid "【CoinEx】杠杆续借成功通知"
msgstr ""

#: app/business/email.py:145 app/business/push.py:696 app/business/push.py:706
#: app/templates/email/notice/margin_renew_failed.j2:2
msgid "续借失败通知"
msgstr ""

#: app/business/email.py:147 app/business/email.py:278
#: app/templates/email/notice/ambassador_level_appraisal.j2:3
#: app/templates/email/notice/potential_invalid_ambassador.j2:3
msgid "【CoinEx】大使月度考核"
msgstr ""

#: app/business/email.py:148
#: app/templates/email/notice/ambassador_level_change.j2:3
msgid "【CoinEx】大使等级变更"
msgstr ""

#: app/business/email.py:149
#: app/templates/email/notice/ambassador_level_change_pre_notify.j2:3
msgid "【CoinEx】大使预降级提醒"
msgstr ""

#: app/business/email.py:151 app/business/email.py:153
msgid "CoinEx大使培训手册"
msgstr ""

#: app/business/email.py:155
msgid "【CoinEx】大使预淘汰提醒"
msgstr ""

#: app/business/email.py:156
msgid "【CoinEx】大使淘汰提醒"
msgstr ""

#: app/business/email.py:157
#: app/templates/email/notice/coin_application_rejected.j2:2
msgid "【CoinEx】上币申请反馈"
msgstr ""

#: app/business/email.py:158
#: app/templates/email/notice/ieo_application_rejected.j2:2
msgid "【CoinEx Dock】合作申请反馈"
msgstr ""

#: app/business/email.py:159
msgid "【CoinEx】充值待入账"
msgstr ""

#: app/business/email.py:160
#: app/templates/email/notice/maker_cashback_level_appraisal.j2:3
msgid "【CoinEx】现货Maker返现机制月度考核"
msgstr ""

#: app/business/email.py:162
#: app/templates/email/notice/maker_cashback_level_change.j2:3
msgid "【CoinEx】现货Maker返现等级变更"
msgstr ""

#: app/business/email.py:164
msgid "【CoinEx】新建子账号"
msgstr ""

#: app/business/email.py:166
msgid "【CoinEx】活动奖励通知"
msgstr ""

#: app/business/email.py:168
msgid "【CoinEx】提现审核通过"
msgstr ""

#: app/business/email.py:170
msgid "【CoinEx】亲，恭喜你抢到{{ amount }} {{ coin_type }}的C-Box，注册后即可到账！"
msgstr ""

#: app/business/email.py:172
msgid "【CoinEx】C-Box即将失效"
msgstr ""

#: app/business/email.py:173
msgid "【CoinEx】注册成功"
msgstr ""

#: app/business/email.py:174
msgid "【CoinEx】设置密码成功"
msgstr ""

#: app/business/email.py:175
msgid "【CoinEx】恭喜成为VIP{{ new_level }}，多项专属权益已解锁！"
msgstr ""

#: app/business/email.py:176
msgid "【CoinEx】恭喜晋升VIP{{ new_level }}，专属权益已升级！"
msgstr ""

#: app/business/email.py:177
msgid "【CoinEx】VIP等级变动提醒：VIP{{ old_level }} >> VIP{{ new_level }}，点击查看调整后权益"
msgstr ""

#: app/business/email.py:178
msgid "【CoinEx】你的VIP权益已失效，重新升级，享更多专属福利！"
msgstr ""

#: app/business/email.py:179
msgid "【CoinEx】充值恢复通知"
msgstr ""

#: app/business/email.py:181
#: app/templates/email/notice/maker_cashback_user_daily_report.j2:2
msgid "【CoinEx】现货Maker返现"
msgstr ""

#: app/business/email.py:183
#: app/templates/email/notice/withdrawal_resumed_notice.j2:3
msgid "【CoinEx】提现恢复通知"
msgstr ""

#: app/business/email.py:185 app/business/email.py:200
#: app/business/email.py:201
msgid "【CoinEx】安全设置修改提醒"
msgstr ""

#: app/business/email.py:187 app/common/constants.py:1346
#: app/templates/email/notice/margin_loan_order_expired.j2:2
msgid "杠杆还币提醒"
msgstr ""

#: app/business/email.py:188
msgid "【CoinEx】挖矿奖励发放"
msgstr ""

#: app/business/email.py:189 app/business/email.py:199
msgid "【CoinEx】强平预警通知"
msgstr ""

#: app/business/email.py:191
msgid "【CoinEx】重置邮箱成功"
msgstr ""

#: app/business/email.py:192
msgid "【CoinEx】邮箱已注册"
msgstr ""

#: app/business/email.py:193 app/business/push.py:596 app/business/push.py:606
#: app/business/push.py:727 app/business/push.py:741 app/business/push.py:767
#: app/business/push.py:781 app/common/constants.py:1347
#: app/templates/email/notice/margin_loan_order_force_flat.j2:2
msgid "强制还币通知"
msgstr ""

#: app/business/email.py:195
#: app/templates/email/notice/spot_market_maker_level_appraisal.j2:3
msgid "【CoinEx】现货做市商月度考核"
msgstr ""

#: app/business/email.py:197
msgid "【CoinEx】重置TOTP成功"
msgstr ""

#: app/business/email.py:198
msgid "【CoinEx】重置通行密钥成功"
msgstr ""

#: app/business/email.py:202
#: app/templates/email/notice/perpetual_position_close_result_notice.j2:2
msgid "【CoinEx】合约平仓失败通知"
msgstr ""

#: app/business/email.py:203
msgid "【CoinEx】止盈止损通知"
msgstr ""

#: app/business/email.py:205
#: app/templates/email/notice/credit_risk_withdraw_close_pre_warning.j2:3
msgid "提现功能—预警通知"
msgstr ""

#: app/business/email.py:206
#: app/templates/email/notice/credit_risk_withdraw_close.j2:3
msgid "提现功能—关闭通知"
msgstr ""

#: app/business/email.py:207
msgid "【CoinEx】空投中奖通知"
msgstr ""

#: app/business/email.py:208
msgid "【CoinEx】空投未中奖通知"
msgstr ""

#: app/business/email.py:209
msgid "【CoinEx】参与空投结果通知"
msgstr ""

#: app/business/email.py:210
msgid "【CoinEx】Dibs中奖通知"
msgstr ""

#: app/business/email.py:211
msgid "【CoinEx】Dibs未中奖通知"
msgstr ""

#: app/business/email.py:212
msgid "【CoinEx】稀有聪竞标成功！"
msgstr ""

#: app/business/email.py:213 app/common/constants.py:1364
msgid "已中签！"
msgstr ""

#: app/business/email.py:214 app/common/constants.py:1365
msgid "很遗憾，未中签"
msgstr ""

#: app/business/email.py:215
#: app/templates/email/notice/abnormal_deposit_application_additional_info.j2:3
msgid "【CoinEx】充值未到账找回申请资料补充"
msgstr ""

#: app/business/email.py:216
#: app/templates/email/notice/abnormal_deposit_application_fee.j2:3
msgid "【CoinEx】充值未到账找回申请手续费补充"
msgstr ""

#: app/business/email.py:217
#: app/templates/email/notice/abnormal_deposit_application_finished.j2:3
msgid "【CoinEx】充值未到账找回成功"
msgstr ""

#: app/business/email.py:218
#: app/templates/email/notice/abnormal_deposit_application_rejected.j2:3
msgid "【CoinEx】充值未到账找回申请审核失败"
msgstr ""

#: app/business/email.py:219
#: app/templates/email/notice/abnormal_deposit_application_passed.j2:3
msgid "【CoinEx】充值自助找回审核通过"
msgstr ""

#: app/business/email.py:220
msgid "【CoinEx】部分兑换成功通知"
msgstr ""

#: app/business/email.py:221
msgid "【CoinEx】兑换失败通知"
msgstr ""

#: app/business/email.py:222
msgid "【CoinEx】完全兑换成功通知"
msgstr ""

#: app/business/email.py:223
msgid "【CoinEx】自动定投部分成交通知"
msgstr ""

#: app/business/email.py:224
msgid "【CoinEx】自动定投全部成交通知"
msgstr ""

#: app/business/email.py:225
msgid "【CoinEx】自动定投未成交通知"
msgstr ""

#: app/business/email.py:226 app/business/email.py:227
msgid "【CoinEx】自动定投到达盈利目标通知"
msgstr ""

#: app/business/email.py:228
msgid "【CoinEx】自动定投失败通知"
msgstr ""

#: app/business/email.py:229
msgid "【CoinEx】自动定投暂停通知"
msgstr ""

#: app/business/email.py:230
msgid "【CoinEx】自动定投关闭通知"
msgstr ""

#: app/business/email.py:231
msgid "【CoinEx】恭喜获得{{ value }} {{ value_type }}合约体验金，快来领取吧～"
msgstr ""

#: app/business/email.py:232
msgid "【CoinEx】合约体验金即将到期，请尽快领取！"
msgstr ""

#: app/business/email.py:233
msgid "【CoinEx】{{ value }} {{ value_type }}合约体验金即将过期"
msgstr ""

#: app/business/email.py:234
msgid "【CoinEx】{{ value }} {{ value_type }}合约体验金已满足赠送标准"
msgstr ""

#: app/business/email.py:235
msgid "CoinEx送你{{ value }} {{ value_type }} 现金福利了！"
msgstr ""

#: app/business/email.py:236
#: app/templates/email/notice/trading_gift/random_send_coupon_notice.j2:3
msgid "CoinEx喊你开盲盒，领取现金福利！"
msgstr ""

#: app/business/email.py:237
msgid "【CoinEx】{{ value }} {{ value_type }} 交易赠金券，即将过期"
msgstr ""

#: app/business/email.py:238
msgid "【CoinEx】交易赠金券盲盒即将过期"
msgstr ""

#: app/business/email.py:239
msgid "【CoinEx】{{ value }} {{ value_type }}交易赠金券，即将过期"
msgstr ""

#: app/business/email.py:240
msgid "【CoinEx】恭喜你，交易赠金券已激活"
msgstr ""

#: app/business/email.py:241
msgid "【CoinEx】{{ value }}{{ value_type }} 加息券即将到期，请尽快领取！"
msgstr ""

#: app/business/email.py:244
msgid "【CoinEx】恭喜获得 {{ value }}{{ value_type }} 加息券，快来领取吧～"
msgstr ""

#: app/business/email.py:247
msgid "【CoinEx】恭喜获得 {{ value }}{{ value_type }} 加息券，请尽快激活使用"
msgstr ""

#: app/business/email.py:250
msgid "【CoinEx】{{ value }}{{ value_type }} 加息券即将失效，请尽快使用！"
msgstr ""

#: app/business/email.py:253
msgid "【CoinEx】{{value}} {{value_type}} 返现券即将到期，请尽快领取！"
msgstr ""

#: app/business/email.py:254
msgid "【CoinEx】{{value}} {{value_type}} 返现券待激活"
msgstr ""

#: app/business/email.py:255
msgid "【CoinEx】恭喜获得{{value}} {{value_type}} 返现券，快来领取吧～"
msgstr ""

#: app/business/email.py:256
msgid "【CoinEx】返现券即将过期"
msgstr ""

#: app/business/email.py:257
msgid "【CoinEx】{{value}} {{value_type}} 返现券即将截止激活"
msgstr ""

#: app/business/email.py:258 app/business/email.py:259
msgid "【CoinEx】送你 {{value}} {{value_type}} 合约补贴金！"
msgstr ""

#: app/business/email.py:260 app/business/email.py:261
msgid "【CoinEx】{{value}} {{value_type}} 合约补贴金，即将过期"
msgstr ""

#: app/business/email.py:262
msgid "【CoinEx】CoinEx给你送VIP升级券了！"
msgstr ""

#: app/business/email.py:263 app/business/email.py:264
msgid "【CoinEx】VIP升级券即将过期"
msgstr ""

#: app/business/email.py:265
msgid "【CoinEx】CoinEx给你送合约跟单体验金券了！"
msgstr ""

#: app/business/email.py:266 app/business/email.py:267
msgid "【CoinEx】合约跟单体验金券即将过期"
msgstr ""

#: app/business/email.py:268
msgid "【CoinEx】手续费返现权益即将到期"
msgstr ""

#: app/business/email.py:269
msgid "【CoinEx】手续费返现权益发放"
msgstr ""

#: app/business/email.py:270
msgid "【CoinEx】空投奖励发放"
msgstr ""

#: app/business/email.py:271
#: app/templates/email/notice/mission_center/new_mission_notice.j2:2
msgid "【CoinEx】请查收新任务"
msgstr ""

#: app/business/email.py:272
#: app/templates/email/notice/mission_center/mission_expiring.j2:2
msgid "【CoinEx】任务即将到期"
msgstr ""

#: app/business/email.py:273
#: app/templates/email/notice/mission_center/mission_reward_sent.j2:2
msgid "【CoinEx】任务奖励发放"
msgstr ""

#: app/business/email.py:274
msgid "【CoinEx】 大使提交申请提醒"
msgstr ""

#: app/business/email.py:275
#: app/templates/email/notice/ambassador_application_approve.j2:3
msgid "【CoinEx】 大使申请审核通过提醒"
msgstr ""

#: app/business/email.py:276
#: app/templates/email/notice/ambassador_application_reject.j2:3
msgid "【CoinEx】大使申请审核未通过提醒"
msgstr ""

#: app/business/email.py:277
#: app/templates/email/notice/potential_ambassador.j2:3
msgid "【CoinEx大使邀请函】50%永久返佣，USDT日结"
msgstr ""

#: app/business/email.py:279
msgid "【CoinEx】成员加入提现审核通知"
msgstr ""

#: app/business/email.py:280
msgid "【CoinEx】提现审核邀请失败"
msgstr ""

#: app/business/email.py:281
msgid "【CoinEx】提现审核移除成功"
msgstr ""

#: app/business/email.py:282
msgid "【CoinEx】添加API提现白名单地址取消"
msgstr ""

#: app/business/email.py:283
msgid "【CoinEx】交易排位赛奖励发放"
msgstr ""

#: app/business/email.py:284 app/common/constants.py:1407
#: app/templates/email/notice/tax_export_success.j2:2
msgid "账户数据已生成"
msgstr ""

#: app/business/email.py:285
msgid "【CoinEx】解冻账号成功"
msgstr ""

#: app/business/email.py:286
msgid "【CoinEx】解冻账号失败"
msgstr ""

#: app/business/email.py:287
msgid "【CoinEx】{{market}}现货市场已达网格触发价"
msgstr ""

#: app/business/email.py:288
msgid "【CoinEx】{{market}}现货市场已达网格止盈价"
msgstr ""

#: app/business/email.py:289
msgid "【CoinEx】{{market}}现货市场已达网格止损价"
msgstr ""

#: app/business/email.py:290
msgid "【CoinEx】{{market}}现货市价超出网格价格区间"
msgstr ""

#: app/business/email.py:291
msgid "【CoinEx】{{market}}现货网格策略已超时"
msgstr ""

#: app/business/email.py:292
msgid "【CoinEx】借贷补仓通知"
msgstr ""

#: app/business/email.py:293
msgid "【CoinEx】借贷强平通知"
msgstr ""

#: app/business/email.py:294
msgid "【CoinEx】借贷订单即将到期提醒"
msgstr ""

#: app/business/email.py:295
msgid "【CoinEx】借贷订单即将到期还币提醒"
msgstr ""

#: app/business/email.py:296
msgid "【CoinEx】借贷订单续借成功通知"
msgstr ""

#: app/business/email.py:297
msgid "【CoinEx】借贷订单进入宽限期通知"
msgstr ""

#: app/business/email.py:298
msgid "【CoinEx】借贷订单续借状态通知"
msgstr ""

#: app/business/email.py:299
msgid "【CoinEx】借贷订单强平通知"
msgstr ""

#: app/business/email.py:300
msgid "【CoinEx】借贷订单自动还币通知"
msgstr ""

#: app/business/email.py:301
msgid "【CoinEx】预测市场 {{asset}}已完成交割"
msgstr ""

#: app/business/email.py:302
msgid "【CoinEx】子账号授权成功"
msgstr ""

#: app/business/email.py:303
msgid "【CoinEx】子账号授权解除"
msgstr ""

#: app/business/email.py:304
msgid "【CoinEx】交易排位赛未达标提醒"
msgstr ""

#: app/business/email.py:305
msgid "【CoinEx】恭喜你成功开通CoinEx杠杆功能"
msgstr ""

#: app/business/email.py:306
msgid "【CoinEx】恭喜你成功开通CoinEx合约功能"
msgstr ""

#: app/business/email.py:307
msgid "【CoinEx】恭喜成为CoinEx经纪商"
msgstr ""

#: app/business/email.py:308
msgid "【CoinEx】成功开启API授权"
msgstr ""

#: app/business/email.py:309
msgid "【CoinEx】提现密码重置成功"
msgstr ""

#: app/business/email.py:310
msgid "【CoinEx】提现密码重置失败"
msgstr ""

#: app/business/email.py:311
msgid "【CoinEx】提现密码修改成功"
msgstr ""

#: app/business/email.py:312
msgid "【CoinEx】{{market}}市场资金费用收取周期调整"
msgstr ""

#: app/business/email.py:313
msgid "【CoinEx】{{activate_title}}，新用户好礼活动即将结束！"
msgstr ""

#: app/business/email.py:314 app/business/email.py:315
msgid "【CoinEx】开仓止盈止损设置失效提醒"
msgstr ""

#: app/business/email.py:316
msgid "【CoinEx】成功赎回{{ asset }}"
msgstr ""

#: app/business/email.py:317
msgid "【CoinEx】质押生效提醒"
msgstr ""

#: app/business/email.py:318 app/business/email.py:319
#: app/business/email.py:320 app/business/email.py:321
#: app/business/email.py:322
msgid "【CoinEx】您加入CoinEx已经{{ years }}年啦！"
msgstr ""

#: app/business/email.py:323
#: app/templates/email/notice/copy_trading/follower_close_position.j2:2
msgid "【CoinEx】跟单交易平仓成功"
msgstr ""

#: app/business/email.py:324
#: app/templates/email/notice/copy_trading/follower_position_liquidation.j2:2
msgid "【CoinEx】跟单员强平通知"
msgstr ""

#: app/business/email.py:325
#: app/templates/email/notice/copy_trading/follower_open_position.j2:2
msgid "【CoinEx】跟单成功通知"
msgstr ""

#: app/business/email.py:326
#: app/templates/email/notice/copy_trading/follower_open_position_failed.j2:2
msgid "【CoinEx】跟单开仓失败"
msgstr ""

#: app/business/email.py:327
#: app/templates/email/notice/copy_trading/follower_stop_loss.j2:2
msgid "【CoinEx】跟单止损触达成功"
msgstr ""

#: app/business/email.py:328
#: app/templates/email/notice/copy_trading/follower_follow_finished.j2:2
msgid "【CoinEx】结束跟单通知"
msgstr ""

#: app/business/email.py:329
#: app/templates/email/notice/copy_trading/follower_take_profit.j2:2
msgid "【CoinEx】跟单止盈触发成功"
msgstr ""

#: app/business/email.py:330
#: app/templates/email/notice/copy_trading/follower_suggest_add_margin.j2:2
msgid "【CoinEx】交易员转入保证金"
msgstr ""

#: app/business/email.py:331
#: app/templates/email/notice/copy_trading/follower_profit_share.j2:2
msgid "【CoinEx】分润结算通知"
msgstr ""

#: app/business/email.py:332
#: app/templates/email/notice/copy_trading/follower_profit_share_rate_changed.j2:2
msgid "【CoinEx】分润比例调整通知"
msgstr ""

#: app/business/email.py:333
#: app/templates/email/notice/copy_trading/trader_apply_success.j2:2
msgid "【CoinEx】恭喜成为跟单交易员"
msgstr ""

#: app/business/email.py:334
#: app/templates/email/notice/copy_trading/trader_adjust_profit_share_rate.j2:2
msgid "【CoinEx】分润比例调整成功"
msgstr ""

#: app/business/email.py:335
#: app/templates/email/notice/copy_trading/trader_open_position.j2:2
msgid "【CoinEx】交易员开仓成功"
msgstr ""

#: app/business/email.py:336
#: app/templates/email/notice/copy_trading/trader_close_position.j2:2
msgid "【CoinEx】交易员平仓成功"
msgstr ""

#: app/business/email.py:337
#: app/templates/email/notice/copy_trading/trader_liquidation.j2:2
msgid "【CoinEx】交易员强平通知"
msgstr ""

#: app/business/email.py:338
#: app/templates/email/notice/copy_trading/trader_stop_loss.j2:2
msgid "【CoinEx】止损触发成功"
msgstr ""

#: app/business/email.py:339
#: app/templates/email/notice/copy_trading/trader_trade_finished.j2:2
msgid "【CoinEx】结束带单通知"
msgstr ""

#: app/business/email.py:340
#: app/templates/email/notice/copy_trading/trader_trade_system_finished.j2:2
msgid "【CoinEx】交易员身份取消通知"
msgstr ""

#: app/business/email.py:341
#: app/templates/email/notice/copy_trading/trader_trade_pre_cancel.j2:2
msgid "【CoinEx】交易员身份取消提醒"
msgstr ""

#: app/business/email.py:342
#: app/templates/email/notice/copy_trading/trader_take_profit.j2:2
msgid "【CoinEx】止盈触发成功"
msgstr ""

#: app/business/email.py:343
#: app/templates/email/notice/p2p/add_payment_channel.j2:2
msgid "【CoinEx】P2P新增收款方式"
msgstr ""

#: app/business/email.py:344
#: app/templates/email/notice/p2p/auto_offline_advertising.j2:2
msgid "【CoinEx】P2P广告单被下架"
msgstr ""

#: app/business/email.py:345 app/business/email.py:356
#: app/templates/email/notice/p2p/buyer_cancelled_order.j2:2
#: app/templates/email/notice/p2p/order_auto_cancelled.j2:2
msgid "【CoinEx】P2P订单已取消"
msgstr ""

#: app/business/email.py:346
#: app/templates/email/notice/p2p/buyer_payment_deadline.j2:2
msgid "【CoinEx】P2P订单付款提醒"
msgstr ""

#: app/business/email.py:347
#: app/templates/email/notice/p2p/canceled_complaint.j2:2
msgid "【CoinEx】P2P订单申诉已取消"
msgstr ""

#: app/business/email.py:348
#: app/templates/email/notice/p2p/created_complaint.j2:2
msgid "【CoinEx】P2P订单发起申诉"
msgstr ""

#: app/business/email.py:349
#: app/templates/email/notice/p2p/finished_complaint.j2:2
msgid "【CoinEx】P2P订单申诉已完成"
msgstr ""

#: app/business/email.py:350 app/templates/email/notice/p2p/finished_order.j2:2
msgid "【CoinEx】P2P订单已完成"
msgstr ""

#: app/business/email.py:351
#: app/templates/email/notice/p2p/merchant_reject_order.j2:2
msgid "【CoinEx】P2P拒绝接单提醒"
msgstr ""

#: app/business/email.py:352 app/business/email.py:353
#: app/business/email.py:354 app/business/email.py:355
#: app/templates/email/notice/p2p/operation_complaint_for_cancel_to_buyer.j2:2
#: app/templates/email/notice/p2p/operation_complaint_for_cancel_to_seller.j2:2
#: app/templates/email/notice/p2p/operation_complaint_for_release_to_buyer.j2:2
#: app/templates/email/notice/p2p/operation_complaint_for_release_to_seller.j2:2
msgid "【CoinEx】P2P订单申诉已处理"
msgstr ""

#: app/business/email.py:357
#: app/templates/email/notice/p2p/paid_wait_release_asset.j2:2
msgid "【CoinEx】P2P订单放币提醒"
msgstr ""

#: app/business/email.py:358
#: app/templates/email/notice/p2p/payment_channel_invalid.j2:2
msgid "【CoinEx】P2P支付渠道失效"
msgstr ""

#: app/business/email.py:359 app/business/email.py:360
#: app/templates/email/notice/p2p/received_order_to_payment.j2:2
#: app/templates/email/notice/p2p/received_order_wait_payment.j2:2
msgid "【CoinEx】P2P订单已确认"
msgstr ""

#: app/business/email.py:361
#: app/templates/email/notice/p2p/remind_receive_order.j2:2
msgid "【CoinEx】P2P接单提醒"
msgstr ""

#: app/business/email.py:362
#: app/templates/email/notice/p2p/restart_complaint.j2:2
msgid "【CoinEx】P2P订单申诉重新开启"
msgstr ""

#: app/business/email.py:363
#: app/templates/email/notice/p2p/updated_complaint.j2:2
msgid "【CoinEx】P2P申诉订单进度更新"
msgstr ""

#: app/business/email.py:364 app/business/email.py:365
#: app/templates/email/notice/p2p/become_merchant.j2:2
#: app/templates/email/notice/p2p/become_merchant_not_2fa.j2:2
msgid "【CoinEx】商家身份开通成功"
msgstr ""

#: app/business/email.py:366 app/business/email.py:368
#: app/business/email.py:370 app/business/email.py:371
msgid "【CoinEx】{{ title }}"
msgstr ""

#: app/business/email.py:367
msgid "【CoinEx】恭喜！您具备合约交易赛报名资格"
msgstr ""

#: app/business/email.py:369
msgid "【CoinEx】参与{{ asset }}空投活动，赢取{{ amount }} {{ asset }}"
msgstr ""

#: app/business/email.py:372
msgid "【CoinEx】卡券账户资金不足，请补充资金"
msgstr ""

#: app/business/email.py:373
msgid "【CoinEx】请完成身份验证"
msgstr ""

#: app/business/email.py:375
msgid "【CoinEx】P2P{{ act_name }}报名成功通知"
msgstr ""

#: app/business/email.py:376
msgid "【CoinEx】P2P{{ act_name }}报名结果通知"
msgstr ""

#: app/business/email.py:377
msgid "【CoinEx】P2P{{ act_name }}奖励发放通知"
msgstr ""

#: app/business/email.py:378
msgid "【CoinEx】P2P{{ act_name }}奖励发放失败通知"
msgstr ""

#: app/business/email.py:380
msgid "【CoinEx】P2P商家保证金补充通知"
msgstr ""

#: app/business/email.py:381
msgid "【CoinEx】P2P商家权限受限通知"
msgstr ""

#: app/business/email.py:382
msgid "【CoinEx】P2P商家保证金调整通知"
msgstr ""

#: app/business/email.py:383
msgid "【CoinEx】P2P商家身份取消通知"
msgstr ""

#: app/business/email.py:384
msgid "【CoinEx】商家保证金扣除通知"
msgstr ""

#: app/business/email.py:385
msgid "【CoinEx】商家违规扣款通知"
msgstr ""

#: app/business/email.py:386
msgid "【CoinEx】商家保证金返还通知"
msgstr ""

#: app/business/email.py:387
msgid "【CoinEx】P2P交易赔付到账通知"
msgstr ""

#: app/business/email.py:389
msgid "【CoinEx】充值福利：奖励发放通知"
msgstr ""

#: app/business/email.py:390
msgid "【CoinEx】【IMPORTANT】Further information request for CoinEx KYC verification"
msgstr ""

#: app/business/email.py:391
msgid "【CoinEx】内容违规警告"
msgstr ""

#: app/business/email.py:395
#: app/templates/email/notice/ambassador_package_suspend.j2:3
msgid "【CoinEx】大使激励包停止发放提醒"
msgstr ""

#: app/business/email.py:396 app/business/email.py:397
#: app/templates/email/notice/ambassador_package_fail_assessment.j2:3
#: app/templates/email/notice/ambassador_package_fail_final.j2:3
msgid "【CoinEx】大使激励包未达标提醒"
msgstr ""

#: app/business/email.py:398 app/business/email.py:399
#: app/templates/email/notice/ambassador_package_success.j2:3
#: app/templates/email/notice/ambassador_package_success_final.j2:3
msgid "【CoinEx】大使激励包发放提醒"
msgstr ""

#: app/business/email.py:400
#: app/templates/email/notice/ambassador_package_stop.j2:3
msgid "【CoinEx】大使激励包暂停发放提醒"
msgstr ""

#: app/business/email.py:401
msgid "【CoinEx】你的专属{{package_amount}} {{asset}}激励包已就位，立即领取！"
msgstr ""

#: app/business/email.py:402
msgid "【CoinEx】立即成为大使，持续解锁{{package_amount}} {{asset}}激励包"
msgstr ""

#: app/business/email.py:405
msgid "【CoinEx】红包确认"
msgstr ""

#: app/business/email.py:406
msgid "【CoinEx】提现确认"
msgstr ""

#: app/business/email.py:407
msgid "【CoinEx】提现确认(补发)"
msgstr ""

#: app/business/email.py:408
msgid "【CoinEx】API到期提醒"
msgstr ""

#: app/business/email.py:409
msgid "【CoinEx】多人审核提现确认"
msgstr ""

#: app/business/email.py:410
msgid "【CoinEx】邀请加入提现审核"
msgstr ""

#: app/business/email.py:411
msgid "【CoinEx】退出提现审核确认"
msgstr ""

#: app/business/email.py:412
msgid "【CoinEx】添加API提现白名单地址确认"
msgstr ""

#: app/business/email.py:415 app/business/email.py:416
#: app/business/email.py:417 app/business/email.py:418
#: app/business/email.py:419 app/business/email.py:420
#: app/business/email.py:421 app/business/email.py:422
#: app/business/email.py:423 app/business/email.py:424
#: app/business/email.py:425 app/business/email.py:426
#: app/business/email.py:427 app/business/email.py:428
#: app/business/email.py:429 app/business/email.py:430
#: app/business/email.py:431 app/business/email.py:432
#: app/business/email.py:433 app/business/email.py:434
#: app/business/email.py:435 app/business/email.py:436
#: app/business/email.py:442 app/business/email.py:443
#: app/business/email.py:444 app/business/email.py:445
#: app/business/email.py:446 app/business/email.py:447
#: app/business/email.py:448
msgid "【CoinEx】邮箱验证"
msgstr ""

#: app/business/email.py:437
msgid "【CoinEx】设置交易密码"
msgstr ""

#: app/business/email.py:438
msgid "【CoinEx】修改交易密码"
msgstr ""

#: app/business/email.py:439
msgid "【CoinEx】关闭交易密码"
msgstr ""

#: app/business/email.py:440
msgid "【CoinEx】授权新设备"
msgstr ""

#: app/business/email.py:952
msgid "为保障账号安全，该链接30分钟内有效。提现申请24小时内未确认将自动取消。"
msgstr ""

#: app/business/email.py:960
msgid "为保障账号安全，该链接1小时内有效。提现申请24小时内未确认将自动取消。"
msgstr ""

#: app/business/email.py:968
msgid "confirmationwithdraw_coin_resend"
msgstr ""

#: app/business/email.py:1102
#: app/templates/email/confirmation/multi_approve_confirm.j2:27
msgid "为保障账号安全，该链接30分钟内有效。24小时内未确认将自动取消。"
msgstr ""

#: app/business/email.py:1110
#: app/templates/email/confirmation/multi_approve_join.j2:16
#: app/templates/email/confirmation/multi_approve_leave.j2:14
msgid "为保障账号安全，该链接1小时内有效。24小时内未确认将自动取消。"
msgstr ""

#: app/business/email.py:1583 app/business/email.py:1617
#: app/models/activity.py:1233 app/models/operation.py:2722
msgid "现货交易"
msgstr ""

#: app/business/email.py:1583 app/business/email.py:1617
msgid "理财服务"
msgstr ""

#: app/business/email.py:2091 app/business/push.py:1661
#: app/business/push.py:1676 app/common/constants.py:1417
#: app/templates/email/notice/perpetual_profit_loss_notice.j2:5
msgid "合约止损成功通知"
msgstr ""

#: app/business/email.py:2093 app/business/push.py:1616
#: app/business/push.py:1631 app/common/constants.py:1415
#: app/templates/email/notice/perpetual_profit_loss_notice.j2:7
msgid "合约止盈成功通知"
msgstr ""

#: app/business/email.py:2096 app/business/push.py:1667
#: app/business/push.py:1683 app/common/constants.py:1418
#: app/templates/email/notice/perpetual_profit_loss_notice.j2:11
msgid "合约止损部分失败通知"
msgstr ""

#: app/business/email.py:2098 app/business/push.py:1622
#: app/business/push.py:1638 app/common/constants.py:1416
#: app/templates/email/notice/perpetual_profit_loss_notice.j2:13
msgid "合约止盈部分失败通知"
msgstr ""

#: app/business/email.py:2512
msgid "new_level"
msgstr ""

#: app/business/email.py:2513
msgid "old_level"
msgstr ""

#: app/business/email.py:4327 app/business/push.py:2026
#: app/business/push.py:2036 app/common/constants.py:1517
#, python-format
msgid "收到%(count)s份礼物"
msgstr ""

#: app/business/email.py:4328
#, python-format
msgid "您发表的内容收到%(count)s份礼物(打赏)共%(amount)s，"
msgstr ""

#: app/business/gift.py:378 app/schedules/airdrop.py:480
msgid "coupon_type"
msgstr ""

#: app/business/investment.py:776
msgid "授信用户不允许划转资产到理财账户。"
msgstr ""

#: app/business/kyc.py:179
msgid "证件号码不能为纯数字，请重新填写"
msgstr ""

#: app/business/kyc.py:181
msgid "证件号码不能为纯字母，请重新填写"
msgstr ""

#: app/business/kyc.py:1685 app/business/kyc.py:1692 app/business/kyc.py:1696
#: app/business/kyc.py:1718 app/business/kyc.py:1742 app/business/kyc.py:1747
#: app/models/user.py:1467
msgid "证件号码有误，请重新填写"
msgstr ""

#: app/business/market_maker.py:33 app/business/market_maker.py:56
msgid "末尾30%"
msgstr ""

#: app/business/operation.py:80
#, python-format
msgid "大使新福利：好友交易你拿奖，瓜分最高%(amount)s%(asset)s"
msgstr ""

#: app/business/operation.py:82
#, python-format
msgid "CoinEx大使活动火热进行中，现在申请成为大使即可参与瓜分最高%(amount)s%(asset)s奖励。具体活动如下："
msgstr ""

#: app/business/operation.py:86
msgid "1. 全部用户手续费贡献奖"
msgstr ""

#: app/business/operation.py:88
#, python-format
msgid "活动期间，报名成为大使并邀请好友交易，加入好友手续费排名，即可瓜分最高%(amount)s%(asset)s奖池"
msgstr ""

#: app/business/operation.py:92
#, python-format
msgid "第一名：最高%(amount)s%(asset)s"
msgstr ""

#: app/business/operation.py:94
#, python-format
msgid "第二名：最高%(amount)s%(asset)s"
msgstr ""

#: app/business/operation.py:96
#, python-format
msgid "第三名：最高%(amount)s%(asset)s"
msgstr ""

#: app/business/operation.py:98
#, python-format
msgid "第四名及以下：按排名瓜分%(amount)s%(asset)s奖池"
msgstr ""

#: app/business/operation.py:103
#, python-format
msgid "活动时间：%(begin)s ~ %(end)s（UTC）"
msgstr ""

#: app/business/operation.py:106
msgid "2. 大使推荐奖励"
msgstr ""

#: app/business/operation.py:108
#, python-format
msgid "推荐好友成为CoinEx大使，还可获得好友邀请用户产生手续费总额 %(percent)s 的返佣奖励。"
msgstr ""

#: app/business/operation.py:112
msgid "立即申请>>"
msgstr ""

#: app/business/operation.py:119 app/business/operation.py:145
msgid "本活动一切解释权归CoinEx所有。"
msgstr ""

#: app/business/operation.py:124
#, python-format
msgid "🏆 最后冲刺：邀请好友交易，赢取最高%(amount)s%(asset)s"
msgstr ""

#: app/business/operation.py:125
#, python-format
msgid "大使活动倒计时%(days)s天，你的排名更新了吗？"
msgstr ""

#: app/business/operation.py:127
#, python-format
msgid "抓住最后机会，冲刺最高%(amount)s%(asset)s奖励！"
msgstr ""

#: app/business/operation.py:129
#: app/templates/email/notice/trade_rank_activity_notice.j2:6
#: app/templates/email/notice/trade_rank_activity_notice.j2:10
#: app/templates/email/notice/trade_rank_activity_notice.j2:15
#: app/templates/email/notice/trade_rank_activity_notice.j2:19
msgid "查看当前排名>>"
msgstr ""

#: app/business/operation.py:135
msgid "💡友情提示：好友交易越多，你的奖励越多。持续邀请新朋友加入CoinEx，可以快速增加你的返佣奖励！"
msgstr ""

#: app/business/operation.py:138
msgid "立即邀请>>"
msgstr ""

#: app/business/operation.py:149
msgid "大使活动报名开始啦！"
msgstr ""

#: app/business/operation.py:152
#, python-format
msgid "【大使活动：最高瓜分%(amount)s%(asset)s】报名开始啦！"
msgstr ""

#: app/business/operation.py:155
#, python-format
msgid "邀请好友，享最高%(percent)s%%大使返佣，还能瓜分%(amount)s%(asset)s大奖！"
msgstr ""

#: app/business/operation.py:160
#: app/templates/email/notice/novice_package.j2:13
msgid "立即参与"
msgstr ""

#: app/business/operation.py:168
msgid "大使活动报名即将结束啦！"
msgstr ""

#: app/business/operation.py:169
#, python-format
msgid "【大使活动：最高瓜分%(amount)s%(asset)s】报名即将结束啦！"
msgstr ""

#: app/business/operation.py:293
msgid "立即申请成为大使>>"
msgstr ""

#: app/business/operation.py:295
#, python-format
msgid "好友交易你拿奖，瓜分最高%(amount)s%(asset)s"
msgstr ""

#: app/business/operation.py:296
msgid "好友只要交易就能助你瓜分奖励，立即报名>>"
msgstr ""

#: app/business/operation.py:297
#, python-format
msgid "%(amount)s%(asset)s大使福利即将失效"
msgstr ""

#: app/business/operation.py:298
msgid "立即领取>>"
msgstr ""

#: app/business/operation.py:676
msgid "注意：该币种波动性较高，请自行调研后谨慎投资。"
msgstr ""

#: app/business/operation.py:759
msgid "该币种是新上线的币种，请注意行情波动，谨慎投资。"
msgstr ""

#: app/business/order.py:409
#: app/schedules/reports/daily_market_maker_trade_report.py:57
#: app/schedules/reports/daily_market_maker_trade_report.py:81
#: app/templates/email/notice/user_trade_summary.j2:166
#: app/templates/email/notice/user_trade_summary.j2:232
msgid "交易量"
msgstr ""

#: app/business/push.py:279
#, python-format
msgid "%(asset)s价格上涨至%(price)s。去查看>>"
msgstr ""

#: app/business/push.py:281
#, python-format
msgid "%(asset)s价格下跌至%(price)s。去查看>>"
msgstr ""

#: app/business/push.py:283
#, python-format
msgid "%(asset)s日涨幅达%(rate)s%%，最新价格：%(price)s。去查看>>"
msgstr ""

#: app/business/push.py:288
#, python-format
msgid "%(asset)s日跌幅达%(rate)s%%，最新价格：%(price)s。去查看>>"
msgstr ""

#: app/business/push.py:292
msgid "订阅价格提醒"
msgstr ""

#: app/business/push.py:324
#, python-format
msgid "%(asset)s 5分钟内上涨%(rate)s%%"
msgstr ""

#: app/business/push.py:325
#, python-format
msgid "%(market)s最新价格：%(price)s，上涨%(rate)s%%。请密切关注行情走势。"
msgstr ""

#: app/business/push.py:328
#, python-format
msgid "%(asset)s 5分钟内下跌%(rate)s%%"
msgstr ""

#: app/business/push.py:329
#, python-format
msgid "%(market)s最新价格：%(price)s，下跌%(rate)s%%。请密切关注行情走势。"
msgstr ""

#: app/business/push.py:366
#, python-format
msgid "%(asset)s突破%(level)sUSDT📈"
msgstr ""

#: app/business/push.py:369 app/business/push.py:376
#, python-format
msgid "%(asset)s最新价格：%(price)sUSDT。去查看>>"
msgstr ""

#: app/business/push.py:373
#, python-format
msgid "%(asset)s跌破%(level)sUSDT📉"
msgstr ""

#: app/business/push.py:406
msgid "新币上涨提醒"
msgstr ""

#: app/business/push.py:407
#, python-format
msgid "新币%(asset)s上线涨幅达到%(change_rate_percent)s，去查看>>"
msgstr ""

#: app/business/push.py:433 app/business/push_strategy.py:280
#, python-format
msgid "%(asset)s%(interval)s价格涨幅达到%(change_rate_percent)s"
msgstr ""

#: app/business/push.py:436 app/business/push_strategy.py:283
#, python-format
msgid "%(asset)s%(interval)s价格跌幅达到%(change_rate_percent)s"
msgstr ""

#: app/business/push.py:440 app/business/push_strategy.py:287
#, python-format
msgid "最新价格%(price)sUSDT，立即查看>>"
msgstr ""

#: app/business/push.py:469
#, python-format
msgid "%(asset)s 24小时内上涨%(rate)s%%"
msgstr ""

#: app/business/push.py:470
#, python-format
msgid "%(market)s最新价格：%(price)s，24小时内上涨%(rate)s%%。请密切关注行情走势。"
msgstr ""

#: app/business/push.py:475
#, python-format
msgid "%(asset)s 24小时下跌%(rate)s%%"
msgstr ""

#: app/business/push.py:476
#, python-format
msgid "%(market)s最新价格：%(price)s，24小时内下跌%(rate)s%%。请密切关注行情走势。"
msgstr ""

#: app/business/push.py:510
msgid "24H热搜榜TOP 5"
msgstr ""

#: app/business/push.py:536
msgid "持仓币种提醒"
msgstr ""

#: app/business/push.py:538
msgid "自选币种提醒"
msgstr ""

#: app/business/push.py:543
#, python-format
msgid "%(asset)s 当日上涨%(rate)s%%，最新价格：%(price)sUSDT。去查看>>"
msgstr ""

#: app/business/push.py:548
#, python-format
msgid "%(asset)s 当日下跌%(rate)s%%，最新价格：%(price)sUSDT。去查看>>"
msgstr ""

#: app/business/push.py:573
msgid "充值恢复"
msgstr ""

#: app/business/push.py:574
#, python-format
msgid "%(asset)s已恢复充值功能。"
msgstr ""

#: app/business/push.py:577
msgid "提现恢复"
msgstr ""

#: app/business/push.py:578
#, python-format
msgid "%(asset)s已恢复提现功能。"
msgstr ""

#: app/business/push.py:597 app/business/push.py:607
#, python-format
msgid "你在%(market)s杠杆账户风险率已触发强制还币流程。"
msgstr ""

#: app/business/push.py:629 app/business/push.py:642
msgid "杠杆强平预警"
msgstr ""

#: app/business/push.py:631 app/business/push.py:644
#, python-format
msgid "你的%(market)s杠杆账户借币风险率已达 %(risk_rate)s%%。"
msgstr ""

#: app/business/push.py:665 app/business/push.py:675
msgid "借币到期提醒"
msgstr ""

#: app/business/push.py:666 app/business/push.py:676
#, python-format
msgid "你在%(market)s杠杆市场的%(asset)s借币订单即将到期。"
msgstr ""

#: app/business/push.py:697 app/business/push.py:707
#, python-format
msgid "你在%(market)s杠杆市场的%(asset)s借币订单续借失败。"
msgstr ""

#: app/business/push.py:730 app/business/push.py:744
#, python-format
msgid "你在%(market)s杠杆市场的%(asset)s借币订单已到期，并触发强制还币流程。"
msgstr ""

#: app/business/push.py:770 app/business/push.py:784
#, python-format
msgid "你在%(market)s杠杆市场的%(asset)s借币订单，因借币池余额不足触发强制还币流程。"
msgstr ""

#: app/business/push.py:805 app/business/push.py:818
msgid "合约自动减仓"
msgstr ""

#: app/business/push.py:808 app/business/push.py:821
#, python-format
msgid "你的%(market_type_name)s%(market)s已触发自动减仓流程。"
msgstr ""

#: app/business/push.py:845 app/business/push.py:858
msgid "合约强平通知"
msgstr ""

#: app/business/push.py:848 app/business/push.py:861
#, python-format
msgid "你的%(market_type_name)s%(market)s已触发强平流程。"
msgstr ""

#: app/business/push.py:886 app/business/push.py:895
#: app/common/constants.py:1342
#: app/templates/email/notice/perpetual_position_reduce_notice.j2:2
msgid "合约降档减仓通知"
msgstr ""

#: app/business/push.py:889 app/business/push.py:898
#, python-format
msgid "你的%(market_type_name)s%(market)s持仓已触发降档减仓。"
msgstr ""

#: app/business/push.py:918 app/business/push.py:931
msgid "合约强平预警"
msgstr ""

#: app/business/push.py:921 app/business/push.py:934
#, python-format
msgid "你的%(market_type_name)s%(market)s仓位风险率已达到 %(risk_rate)s%%。"
msgstr ""

#: app/business/push.py:949 app/common/constants.py:2605
msgid "正向合约"
msgstr ""

#: app/business/push.py:951 app/common/constants.py:2606
msgid "反向合约"
msgstr ""

#: app/business/push.py:960
msgid "张"
msgstr ""

#: app/business/push.py:966
msgid "卖出"
msgstr ""

#: app/business/push.py:968
msgid "买入"
msgstr ""

#: app/business/push.py:974
msgid "卖出/做空"
msgstr ""

#: app/business/push.py:976
msgid "买入/做多"
msgstr ""

#: app/business/push.py:991 app/business/push.py:998
msgid "兑换成功"
msgstr ""

#: app/business/push.py:994 app/business/push.py:1001
#, python-format
msgid "你已成功兑换获得 %(target_asset_exchanged_amount)s %(target_asset)s。"
msgstr ""

#: app/business/push.py:1012 app/business/push.py:1019
msgid "兑换失败"
msgstr ""

#: app/business/push.py:1015 app/business/push.py:1022
#, python-format
msgid "你的兑换交易失败，%(source_asset_amount)s %(source_asset)s 已返还。"
msgstr ""

#: app/business/push.py:1030 app/business/push.py:1040
msgid "部分兑换成功"
msgstr ""

#: app/business/push.py:1036 app/business/push.py:1046
#, python-format
msgid ""
"你兑换获得 %(target_asset_exchanged_amount)s %(target_asset)s，剩余未兑换的 "
"%(source_asset_remain_amount)s %(source_asset)s 已返还。"
msgstr ""

#: app/business/push.py:1074 app/business/push.py:1084
#: app/common/constants.py:1316
#: app/templates/email/notice/deposit_pass_notice.j2:2
msgid "充值成功"
msgstr ""

#: app/business/push.py:1075 app/business/push.py:1085
#, python-format
msgid "你充值的%(amount)s 已到账。"
msgstr ""

#: app/business/push.py:1107 app/business/push.py:1115
msgid "提现成功"
msgstr ""

#: app/business/push.py:1108 app/business/push.py:1116
#, python-format
msgid "你已成功提出%(amount)s。"
msgstr ""

#: app/business/push.py:1135 app/business/push.py:1148
msgid "中签通知"
msgstr ""

#: app/business/push.py:1137 app/business/push.py:1150
#, python-format
msgid "恭喜！你在CoinEx Dock参与的%(project_name)s项目已成功中签。"
msgstr ""

#: app/business/push.py:1142 app/business/push.py:1155
#, python-format
msgid "很遗憾，你在CoinEx Dock参与的%(project_name)s项目申购未中签。"
msgstr ""

#: app/business/push.py:1178 app/business/push.py:1192
#: app/business/push.py:1224 app/business/push.py:1237
msgid "币币订单成交提醒"
msgstr ""

#: app/business/push.py:1183 app/business/push.py:1196
#, python-format
msgid ""
"你的%(market)s%(side)s委托已部分成交，成交数量%(amount)s "
"%(asset)s，成交均价%(counter_amount)s %(counter_asset)s。"
msgstr ""

#: app/business/push.py:1228 app/business/push.py:1241
#, python-format
msgid ""
"你的%(market)s%(side)s委托已全部成交，成交数量%(amount)s "
"%(asset)s，成交均价%(counter_amount)s %(counter_asset)s。"
msgstr ""

#: app/business/push.py:1262 app/business/push.py:1271
#: app/business/push.py:1298 app/business/push.py:1311
#: app/business/push.py:1340 app/business/push.py:1360
msgid "币币订单委托提醒"
msgstr ""

#: app/business/push.py:1266 app/business/push.py:1275
#, python-format
msgid "你的%(market)s%(side)s委托失败。"
msgstr ""

#: app/business/push.py:1302 app/business/push.py:1315
#, python-format
msgid ""
"你的%(market)s计划限价%(side)s委托已触发，委托数量%(amount)s "
"%(asset)s，委托价格%(counter_amount)s %(counter_asset)s。"
msgstr ""

#: app/business/push.py:1345 app/business/push.py:1365
#, python-format
msgid "你的%(market)s计划市价%(side)s委托已触发，委托数量%(amount)s %(asset)s。"
msgstr ""

#: app/business/push.py:1353 app/business/push.py:1373
#, python-format
msgid "你的%(market)s计划市价%(side)s委托已触发，交易额%(amount)s %(asset)s。"
msgstr ""

#: app/business/push.py:1400 app/business/push.py:1415
#: app/business/push.py:1449 app/business/push.py:1464
msgid "合约成交提醒"
msgstr ""

#: app/business/push.py:1408 app/business/push.py:1423
#, python-format
msgid ""
"你的%(market_type_name)s%(market)s%(side)s委托已部分成交，成交数量%(amount)s "
"%(asset)s，成交均价%(counter_amount)s %(counter_asset)s。"
msgstr ""

#: app/business/push.py:1457 app/business/push.py:1472
#, python-format
msgid ""
"你的%(market_type_name)s%(market)s%(side)s委托已完全成交，成交数量%(amount)s "
"%(asset)s，成交均价%(counter_amount)s %(counter_asset)s。"
msgstr ""

#: app/business/push.py:1497 app/business/push.py:1512
#: app/business/push.py:1545 app/business/push.py:1557
msgid "合约委托提醒"
msgstr ""

#: app/business/push.py:1505 app/business/push.py:1520
#, python-format
msgid ""
"你的%(market_type_name)s%(market)s计划限价%(side)s委托已触发，委托数量%(amount)s "
"%(asset)s，委托价格%(counter_amount)s %(counter_asset)s。"
msgstr ""

#: app/business/push.py:1552 app/business/push.py:1564
#, python-format
msgid ""
"你的%(market_type_name)s%(market)s计划市价%(side)s委托已触发，委托数量%(amount)s "
"%(asset)s。"
msgstr ""

#: app/business/push.py:1582 app/business/push.py:1592
msgid "合约委托失败"
msgstr ""

#: app/business/push.py:1587 app/business/push.py:1597
#, python-format
msgid "你的%(market_type_name)s%(market)s%(side)s委托失败。"
msgstr ""

#: app/business/push.py:1617 app/business/push.py:1632
#: app/templates/email/notice/perpetual_profit_loss_notice.j2:33
#, python-format
msgid "你在%(market_type)s %(market)s中的止盈设置已被触发，并且全部平仓成功。"
msgstr ""

#: app/business/push.py:1623 app/business/push.py:1639
#, python-format
msgid "你在%(market_type)s %(market)s中的止盈设置已部分平仓，剩余未平仓位%(amount)s%(asset)s。"
msgstr ""

#: app/business/push.py:1662 app/business/push.py:1677
#: app/templates/email/notice/perpetual_profit_loss_notice.j2:23
#, python-format
msgid "你在%(market_type)s %(market)s中的止损设置已被触发，并且全部平仓成功。"
msgstr ""

#: app/business/push.py:1668 app/business/push.py:1684
#, python-format
msgid "你在%(market_type)s %(market)s中的止损设置已部分平仓，剩余未平仓位%(amount)s%(asset)s。"
msgstr ""

#: app/business/push.py:1705 app/business/push.py:1720
msgid "合约平仓提醒"
msgstr ""

#: app/business/push.py:1709 app/business/push.py:1724
#, python-format
msgid "你的%(market_type_name)s%(market)s做空仓位已平。"
msgstr ""

#: app/business/push.py:1714 app/business/push.py:1729
#, python-format
msgid "你的%(market_type_name)s%(market)s做多仓位已平。"
msgstr ""

#: app/business/push.py:1911
msgid "赎回申请提交成功"
msgstr ""

#: app/business/push.py:1912
msgid "查看赎回记录"
msgstr ""

#: app/business/push.py:1939
msgid "欺诈/诈骗"
msgstr ""

#: app/business/push.py:1940
msgid "恶意/消极"
msgstr ""

#: app/business/push.py:1941
msgid "垃圾消息"
msgstr ""

#: app/business/push.py:1942
msgid "虚假互动"
msgstr ""

#: app/business/push.py:1972 app/business/push.py:1976
#, python-format
msgid "%(count)s位用户对您发表的内容点赞，"
msgstr ""

#: app/business/push.py:1980 app/business/push.py:1984
#, python-format
msgid "%(count)s位用户回复了您，"
msgstr ""

#: app/business/push.py:1988 app/business/push.py:1992
#, python-format
msgid "%(count)s位用户@了您，"
msgstr ""

#: app/business/push.py:1998 app/business/push.py:2005
msgid "您发表的内容"
msgstr ""

#: app/business/push.py:1999 app/business/push.py:2006
#, python-format
msgid "收到%(count)s份礼物(打赏)共%(amount)s，"
msgstr ""

#: app/business/push.py:2020 app/business/push.py:2030
msgid "点击查看详情"
msgstr ""

#: app/business/push.py:2022 app/business/push.py:2032
#, python-format
msgid "收到%(count)s条互动消息"
msgstr ""

#: app/business/push.py:2024 app/business/push.py:2034
#, python-format
msgid "+%(count)s份礼物"
msgstr ""

#: app/business/push.py:2043 app/business/push.py:2047
#: app/common/constants.py:1510
#, python-format
msgid "账号禁言%(count)s天"
msgstr ""

#: app/business/push.py:2044 app/business/push.py:2048
#, python-format
msgid "由于您的账号币种评论内容违规，您的账号%(count)s天无法发表币种评论和回复。"
msgstr ""

#: app/business/push.py:2052 app/business/push.py:2055
#: app/common/constants.py:1511
msgid "账号永久禁言"
msgstr ""

#: app/business/push.py:2053 app/business/push.py:2056
msgid "由于您的账号币种评论内容违规，您的账号永久无法发表币种评论和回复。"
msgstr ""

#: app/business/push.py:2135
msgid "title"
msgstr ""

#: app/business/push.py:2136
msgid "content"
msgstr ""

#: app/business/security.py:51
msgid "以下哪些现货市场你曾经交易过？"
msgstr ""

#: app/business/security.py:52
msgid "以下哪些币种你曾经充值过？"
msgstr ""

#: app/business/security.py:53
msgid "以下哪些币种你曾经提现过？"
msgstr ""

#: app/business/security.py:54
msgid "以下哪些币种你曾经借过（杠杆借币）？"
msgstr ""

#: app/business/security.py:55
msgid "以下哪些币种，是你当前持有（价值在1USD以上）币种？"
msgstr ""

#: app/business/security.py:56
msgid "以下哪些币种，是你当前的自选币种？"
msgstr ""

#: app/business/security.py:57
msgid "你的CoinEx注册时间（UTC+8）是？"
msgstr ""

#: app/business/security.py:888
msgid "提现密码错误次数超过限制，请24小时后重试"
msgstr ""

#: app/business/security.py:938 app/business/security.py:943
msgid "交易密码错误次数超过限制，请24小时后重试"
msgstr ""

#: app/business/staking.py:92
msgid "可质押数量不足"
msgstr ""

#: app/business/staking.py:112
msgid "质押失败，请重试"
msgstr ""

#: app/business/staking.py:123
msgid "可赎回余额不足"
msgstr ""

#: app/business/user.py:1387
msgid "客服"
msgstr ""

#: app/business/user.py:1387
msgid "官方"
msgstr ""

#: app/business/user.py:1387
msgid "支持"
msgstr ""

#: app/business/user.py:1387
msgid "申诉"
msgstr ""

#: app/business/user.py:1436
msgid "用户名不得包含特殊字符"
msgstr ""

#: app/business/user.py:1438
msgid "只允许在字符之间设置一个空格"
msgstr ""

#: app/business/user.py:1440
msgid "只允许一个空格，不得包含特殊字符"
msgstr ""

#: app/business/user.py:1442
msgid "该用户名无法设置"
msgstr ""

#: app/business/user.py:1504 app/business/user.py:1548
msgid "该账户名无法设置"
msgstr ""

#: app/business/user.py:1542
msgid "账户名只支持数字和字母"
msgstr ""

#: app/business/user.py:1544
msgid "账户名只允许修改一次"
msgstr ""

#: app/business/user_group.py:99
msgid "首次充值"
msgstr ""

#: app/business/user_group.py:100
msgid "首次币币交易"
msgstr ""

#: app/business/user_group.py:101
msgid "首次现货交易"
msgstr ""

#: app/business/user_group.py:102
msgid "首次合约交易"
msgstr ""

#: app/business/user_group.py:103
msgid "首次兑换交易"
msgstr ""

#: app/business/user_group.py:104
msgid "首次策略交易"
msgstr ""

#: app/business/user_group.py:105 app/models/activity.py:1231
msgid "注册"
msgstr ""

#: app/business/user_group.py:178
msgid "活动期间完成币币交易（包含现货、杠杆、兑换、策略）"
msgstr ""

#: app/business/user_group.py:179
#, python-format
msgid "活动期间完成币币交易（包含现货、杠杆、兑换、策略），累计金额>%(amount)s USD"
msgstr ""

#: app/business/user_group.py:180
msgid "活动期间完成合约交易"
msgstr ""

#: app/business/user_group.py:181
#, python-format
msgid "活动期间完成合约交易，累计金额>%(amount)s USD"
msgstr ""

#: app/business/user_group.py:182
msgid "活动期间完成链上充值"
msgstr ""

#: app/business/user_group.py:183
#, python-format
msgid "活动期间完成链上充值，累计金额>%(amount)s USD"
msgstr ""

#: app/business/user_group.py:184
msgid "活动期间完成交易（包含币币、合约、兑换、策略）"
msgstr ""

#: app/business/user_group.py:185
#, python-format
msgid "活动期间完成交易（包含币币、合约、兑换、策略），累计金额>%(amount)s USD"
msgstr ""

#: app/business/user_group.py:189
msgid "需完成实名认证"
msgstr ""

#: app/business/user_group.py:190
#, python-format
msgid "VIP等级 %(op)s %(level)s"
msgstr ""

#: app/business/user_group.py:191
#, python-format
msgid "注册时间 %(op)s %(time)s"
msgstr ""

#: app/business/user_group.py:192
#, python-format
msgid "账户邀请人为%(type)s"
msgstr ""

#: app/business/user_group.py:193
msgid "账户邀请人为指定邀请人"
msgstr ""

#: app/business/user_group.py:194
#, python-format
msgid "近%(count)s天内存在%(type)s交易"
msgstr ""

#: app/business/user_group.py:195
msgid "账户在指定名单内"
msgstr ""

#: app/business/user_group.py:196
#, python-format
msgid "不存在%(type)s交易行为用户"
msgstr ""

#: app/business/user_group.py:197
#, python-format
msgid "存在%(type)s历史交易行为"
msgstr ""

#: app/business/user_group.py:198
#, python-format
msgid "需为%(type)s做市商"
msgstr ""

#: app/business/user_group.py:199
msgid "需完成链上充值"
msgstr ""

#: app/business/user_group.py:200
#, python-format
msgid "%(account_type)s资产需 %(op)s %(amount)s %(asset)s"
msgstr ""

#: app/business/user_group.py:201
#, python-format
msgid "本券仅限%(type)s语区用户兑换"
msgstr ""

#: app/business/user_group.py:202
msgid "活动期间完成注册"
msgstr ""

#: app/business/user_group.py:207
msgid "充值金额"
msgstr ""

#: app/business/user_group.py:208
msgid "币币交易额"
msgstr ""

#: app/business/user_group.py:212
#, python-format
msgid "活动上线%(value)s天内"
msgstr ""

#: app/business/user_group.py:213
#, python-format
msgid "注册%(value)s天内"
msgstr ""

#: app/business/user_group.py:216
msgid "当前充值交易额"
msgstr ""

#: app/business/user_group.py:217
msgid "当前币币交易额"
msgstr ""

#: app/business/user_group.py:218
msgid "当前合约交易额"
msgstr ""

#: app/business/user_group.py:304
msgid "大使"
msgstr ""

#: app/business/user_group.py:306
#: app/templates/email/notice/vip_level_gain.j2:14
#: app/templates/email/notice/vip_level_lose.j2:13
msgid "普通用户"
msgstr ""

#: app/business/user_group.py:308
msgid "账户无邀请人"
msgstr ""

#: app/business/user_group.py:344 app/business/user_group.py:540
#: app/business/user_group.py:606
msgid "现货"
msgstr ""

#: app/business/coupon/message.py:440 app/business/coupon/message.py:443
#: app/business/user_group.py:542 app/business/user_group.py:610
msgid "合约"
msgstr ""

#: app/business/user_group.py:544
msgid "任意"
msgstr ""

#: app/business/user_group.py:546 app/models/operation.py:2729
msgid "理财"
msgstr ""

#: app/business/user_group.py:595
msgid "不是做市商"
msgstr ""

#: app/business/user_group.py:1131
#, python-format
msgid "完成%(task)s获取奖励"
msgstr ""

#: app/business/user_group.py:1274
#, python-format
msgid "完成%(operate)s即可领取奖励"
msgstr ""

#: app/business/user_group.py:1285
#, python-format
msgid "累计%(cumulative)s >= %(amount)s USDT，即可领取奖励"
msgstr ""

#: app/business/user_group.py:1286
#, python-format
msgid "完成%(operation)s，且累计%(cumulative)s >= %(amount)s USDT，即可领取奖励"
msgstr ""

#: app/business/user_group.py:1288
#, python-format
msgid "活动期内，完成%(operation)s，即可领取奖励"
msgstr ""

#: app/business/user_group.py:1289
#, python-format
msgid "活动上线%(value)s天内，完成%(operation)s，即可领取奖励"
msgstr ""

#: app/business/user_group.py:1290
#, python-format
msgid "注册%(value)s天内，完成%(operation)s，即可领取奖励"
msgstr ""

#: app/business/user_group.py:1293
#, python-format
msgid "活动期内，累计%(cumulative)s >= %(amount)s USDT，即可领取奖励"
msgstr ""

#: app/business/user_group.py:1294
#, python-format
msgid "活动上线%(value)s天内，累计%(cumulative)s >= %(amount)s USDT，即可领取奖励"
msgstr ""

#: app/business/user_group.py:1295
#, python-format
msgid "注册%(value)s天内，累计%(cumulative)s >= %(amount)s USDT，即可领取奖励"
msgstr ""

#: app/business/user_group.py:1298
#, python-format
msgid "活动期内，完成%(operation)s，且累计%(cumulative)s >= %(amount)s USDT，即可领取奖励"
msgstr ""

#: app/business/user_group.py:1299
#, python-format
msgid ""
"活动上线%(value)s天内，完成%(operation)s，且累计%(cumulative)s >= %(amount)s "
"USDT，即可领取奖励"
msgstr ""

#: app/business/user_group.py:1300
#, python-format
msgid "注册%(value)s天内，完成%(operation)s，且累计%(cumulative)s >= %(amount)s USDT，即可领取奖励"
msgstr ""

#: app/business/wallet.py:383
msgid "链号"
msgstr ""

#: app/business/wallet.py:384
msgid ""
"Kadena 目前支持20条链运行，并以「Chain 0」-「Chain "
"19」进行命名，不同链号之间资产相互隔离，因此同一笔转账金额不能同时来源于多条链。提现时需按照收款方对链号的要求进行选择，若无要求可选择默认链号。"
msgstr ""

#: app/business/wallet.py:403
msgid "name"
msgstr ""

#: app/business/wallet.py:404
msgid "description"
msgstr ""

#: app/business/wallet.py:532 app/business/wallet.py:656
msgid "此地址不在API提现白名单内，请前往添加"
msgstr ""

#: app/business/wallet.py:676
#, python-format
msgid "手续费币种%(fee_asset)s可用资产不足"
msgstr ""

#: app/business/wallet.py:821
msgid "提现不能撤销，如有疑问请联系客服"
msgstr ""

#: app/business/activity/fifth.py:884
msgid "周年盲盒"
msgstr ""

#: app/business/activity/fifth.py:884
msgid "终极盲盒"
msgstr ""

#: app/business/activity/fifth.py:885
#, python-format
msgid "获得%(box_type)s开箱机会"
msgstr ""

#: app/business/activity/fifth.py:960 app/business/activity/fifth.py:1023
msgid "获得VIP特权"
msgstr ""

#: app/business/activity/fifth.py:961 app/business/activity/fifth.py:1024
msgid "获得随机卡券"
msgstr ""

#: app/business/activity/fifth.py:962 app/business/activity/fifth.py:1025
#, python-format
msgid "获得%(amount)s %(asset)s"
msgstr ""

#: app/business/activity/fifth.py:1323
msgid "你的终极盲盒可以开箱啦！"
msgstr ""

#: app/business/activity/fifth.py:1324
msgid "1月1日0点(UTC)准时开箱，最高2000 USDT，数量有限，立即抢 >>"
msgstr ""

#: app/business/activity/fifth.py:1326
msgid "你的周年盲盒可以开箱啦！"
msgstr ""

#: app/business/activity/fifth.py:1327
msgid "1月1日0点(UTC)准时开箱，最高200 USDT，数量有限，立即抢 >>"
msgstr ""

#: app/business/activity/launch_pool.py:71
#, python-format
msgid "使用已达到每人存入上限 %(amount)s%(asset)s"
msgstr ""

#: app/business/activity/mining.py:763
#, python-format
msgid "每次质押最少需质押 %(amount)s%(asset)s"
msgstr ""

#: app/business/activity/mining.py:783
#, python-format
msgid "超出最多质押量，每人最多可质押 %(amount)s%(asset)s"
msgstr ""

#: app/business/activity/notice.py:358
#, python-format
msgid "挖矿预告：提前存入%(stake_asset)s，赚取%(reward_asset)s收益"
msgstr ""

#: app/business/activity/notice.py:363 app/business/activity/notice.py:371
#, python-format
msgid "活动开始即享%(reward_asset)s小时收益>>"
msgstr ""

#: app/business/activity/notice.py:365
#, python-format
msgid "挖矿预告：提前存入%(stake_asset1)s 或 %(stake_asset2)s，赚取%(reward_asset)s收益"
msgstr ""

#: app/business/activity/notice.py:411
#, python-format
msgid "存入%(stake_asset)s，赚取%(reward_asset)s收益"
msgstr ""

#: app/business/activity/notice.py:416 app/business/activity/notice.py:437
#, python-format
msgid "参与挖矿，瓜分%(reward_total_amount)s %(reward_asset)s奖池"
msgstr ""

#: app/business/activity/notice.py:424
#, python-format
msgid ""
"现在前往活动页面，存入最低%(min_stake_amount)s %(stake_asset)s，即可在%(start_time)s - "
"%(end_time)s（UTC）期间赚取每小时%(reward_asset)s收益！"
msgstr ""

#: app/business/activity/notice.py:431
#, python-format
msgid "存入%(stake_asset1)s 或 %(stake_asset2)s，赚取%(reward_asset)s收益"
msgstr ""

#: app/business/activity/notice.py:447
#, python-format
msgid ""
"现在前往活动页面，存入最低%(min_stake_amount1)s %(stake_asset1)s 或 "
"%(min_stake_amount2)s %(stake_asset2)s，即可在%(start_time)s - "
"%(end_time)s（UTC）期间赚取每小时%(reward_asset)s收益！"
msgstr ""

#: app/business/activity/notice.py:515
msgid "挖矿开启：存入{{ stake_asset }}，瓜分{{ reward_total_amount }} {{ reward_asset }}"
msgstr ""

#: app/business/activity/notice.py:518
msgid ""
"挖矿开启：存入{{ stake_asset1 }} 或 {{ stake_asset2 }}，瓜分{{ reward_total_amount "
"}} {{ reward_asset }}"
msgstr ""

#: app/business/activity/notice.py:532
msgid "完成初级KYC认证"
msgstr ""

#: app/business/activity/notice.py:581
#, python-format
msgid "挖矿开启：存入%(stake_asset)s，赚%(reward_asset)s小时收益"
msgstr ""

#: app/business/activity/notice.py:588
#, python-format
msgid ""
"最低存入%(min_stake_amount)s %(stake_asset)s，即可参与瓜分%(reward_total_amount)s "
"%(reward_asset)s奖池>>"
msgstr ""

#: app/business/activity/notice.py:594
#, python-format
msgid "挖矿开启：存入%(stake_asset1)s 或 %(stake_asset2)s，赚%(reward_asset)s小时收益"
msgstr ""

#: app/business/activity/notice.py:605
#, python-format
msgid ""
"最低存入%(min_stake_amount1)s %(stake_asset1)s 或 %(min_stake_amount2)s "
"%(stake_asset2)s，即可参与瓜分%(reward_total_amount)s %(reward_asset)s奖池>>"
msgstr ""

#: app/business/activity/notice.py:650
#, python-format
msgid "挖矿即将结束：仅需%(min_stake_amount)s %(stake_asset)s即可参与"
msgstr ""

#: app/business/activity/notice.py:655
#, python-format
msgid "赚取免费%(reward_asset)s，预估APR %(apr)s%% >>"
msgstr ""

#: app/business/activity/notice.py:661
msgid "挖矿即将结束"
msgstr ""

#: app/business/activity/notice.py:664
#, python-format
msgid "仅需%(min_stake_amount)s %(stake_asset)s即可参与，立即赚取免费%(reward_asset)s >>"
msgstr ""

#: app/business/activity/notice.py:671 app/business/activity/notice.py:681
#, python-format
msgid "挖矿即将结束：赚取免费%(reward_asset)s"
msgstr ""

#: app/business/activity/notice.py:676
#, python-format
msgid "预估APR %(apr)s%% >>"
msgstr ""

#: app/business/activity/notice.py:690
#, python-format
msgid ""
"最低存入%(min_stake_amount1)s %(stake_asset1)s 或 %(min_stake_amount2)s "
"%(stake_asset2)s，即可赚取免费%(reward_asset)s >>"
msgstr ""

#: app/business/activity/notice.py:734
msgid "全部币币市场"
msgstr ""

#: app/business/activity/notice.py:772 app/business/activity/notice.py:813
msgid "恭喜！您具备合约交易赛报名资格"
msgstr ""

#: app/business/activity/notice.py:815
#, python-format
msgid "报名有机会瓜分%(amount)s %(asset)s"
msgstr ""

#: app/business/activity/notice.py:875
#, python-format
msgid "%(amount)s %(asset)s奖池即将失效"
msgstr ""

#: app/business/activity/notice.py:876
msgid "点击领取 >>"
msgstr ""

#: app/business/activity/notice.py:907
msgid "温馨提示：合约交易赛活动进度已过半"
msgstr ""

#: app/business/activity/notice.py:909
#, python-format
msgid "提升排名最高可得%(amount)s %(asset)s，立即查看 >>"
msgstr ""

#: app/business/activity/notice.py:973
msgid "参与{{ asset }}空投活动，赢取{{ amount }} {{ asset }}"
msgstr ""

#: app/business/activity/notice.py:1051
msgid "参与CoinEx空投站，最后12小时！"
msgstr ""

#: app/business/activity/notice.py:1052
msgid "答题赢取空投奖励 >>"
msgstr ""

#: app/business/activity/notice.py:1151
msgid "CoinEx打折购即将结束，最后12小时，速来！"
msgstr ""

#: app/business/activity/notice.py:1152
#, python-format
msgid "参与%(asset)s打折购，立减%(discount)s%%，立即查看 >>"
msgstr ""

#: app/business/clients/server.py:167 app/exceptions/perpetual.py:96
msgid "触发价不可等于最新成交价"
msgstr ""

#: app/business/copy_trading/base.py:148
#, python-format
msgid "已超过%(max_count)s个跟单人数上限，请稍后再试"
msgstr ""

#: app/business/copy_trading/trader.py:199
msgid "完成初级实名认证"
msgstr ""

#: app/business/copy_trading/trader.py:204
#, python-format
msgid "账户资产等值≥%(balance_usd)s USDT"
msgstr ""

#: app/business/copy_trading/trader.py:209
#, python-format
msgid "近30天已平仓合约交易次数≥%(trade_count)s笔"
msgstr ""

#: app/business/copy_trading/trader.py:214
msgid "没有生效中的跟单"
msgstr ""

#: app/business/copy_trading/trader.py:449
#, python-format
msgid "由于你近期已修改分润比例，请于%(days)s天后再试"
msgstr ""

#: app/business/copy_trading/trader.py:632
msgid "合约跟单交易暂不支持10x以上杠杆"
msgstr ""

#: app/business/copy_trading/trader.py:663
msgid "请重新申请带单，通过申请后才可发起带单交易"
msgstr ""

#: app/business/copy_trading/trader.py:672
msgid "跟单交易不支持该合约市场"
msgstr ""

#: app/business/coupon/base.py:333
msgid "触发风控，无法领取"
msgstr ""

#: app/business/coupon/message.py:136
#, python-format
msgid "%(value)s %(value_type)s合约体验金待领取"
msgstr ""

#: app/business/coupon/message.py:137
msgid "免费体验合约交易，立即前往卡券中心领取使用，先到先得"
msgstr ""

#: app/business/coupon/message.py:140 app/business/coupon/message.py:144
msgid "你有1张交易赠金券待领取"
msgstr ""

#: app/business/coupon/message.py:141
#, python-format
msgid "恭喜获得1张%(value)s %(value_type)s交易赠金券。点击前往卡券中心领取"
msgstr ""

#: app/business/coupon/message.py:145
msgid "交易额达标后，交易赠金券将免费赠送！"
msgstr ""

#: app/business/coupon/message.py:148 app/business/coupon/message.py:152
#, python-format
msgid "恭喜获得%(value)s%(value_type)s理财加息券"
msgstr ""

#: app/business/coupon/message.py:149
msgid "立即前往卡券中心领取使用，让你的闲置资产“赚”起来！先到先得"
msgstr ""

#: app/business/coupon/message.py:153
msgid "立即前往卡券中心激活使用，让你的闲置资产“赚”起来"
msgstr ""

#: app/business/coupon/message.py:156
#, python-format
msgid "%(value)s%(value_type)s加息券即将截止领取"
msgstr ""

#: app/business/coupon/message.py:157
msgid "点击即刻领取，手慢无"
msgstr ""

#: app/business/coupon/message.py:160
#, python-format
msgid "你的%(value)s%(value_type)s加息券即将失效"
msgstr ""

#: app/business/coupon/message.py:161
msgid "使用加息券享更高理财收益，立即激活"
msgstr ""

#: app/business/coupon/message.py:164 app/business/coupon/message.py:172
#, python-format
msgid "%(value)s %(value_type)s交易赠金券，即将过期"
msgstr ""

#: app/business/coupon/message.py:165
#, python-format
msgid "你的%(value)s %(value_type)s交易赠金券即将到期，立即前往卡券中心领取"
msgstr ""

#: app/business/coupon/message.py:168
#: app/templates/email/notice/trading_gift/random_active_deadline_notice.j2:3
msgid "交易赠金券盲盒即将过期"
msgstr ""

#: app/business/coupon/message.py:169
msgid "你有一个交易赠金券盲盒即将到期，立即前往卡券中心领取"
msgstr ""

#: app/business/coupon/message.py:173
#, python-format
msgid "你的%(value)s %(value_type)s交易赠金券尚未激活，立即前往卡券中心查看"
msgstr ""

#: app/business/coupon/message.py:176
#, python-format
msgid "%(value)s %(value_type)s合约体验金即将截止领取"
msgstr ""

#: app/business/coupon/message.py:177
msgid "免费体验合约交易，先到先得"
msgstr ""

#: app/business/coupon/message.py:180
#, python-format
msgid "%(value)s %(value_type)s合约体验金即将到期"
msgstr ""

#: app/business/coupon/message.py:181
msgid "使用有效期内交易额达标则不回收，请尽快交易"
msgstr ""

#: app/business/coupon/message.py:184
#, python-format
msgid "%(value)s %(value_type)s返现券待领取"
msgstr ""

#: app/business/coupon/message.py:185
msgid "立即前往卡券中心领取使用，先到先得"
msgstr ""

#: app/business/coupon/message.py:188
#: app/templates/email/notice/cashback_fee/delivery_coupon_notice.j2:3
#, python-format
msgid "%(value)s %(value_type)s返现券待激活"
msgstr ""

#: app/business/coupon/message.py:189
msgid "即刻交易，符合条件的订单手续费将于次日返还。请尽快激活使用"
msgstr ""

#: app/business/coupon/message.py:192
#, python-format
msgid "%(value)s %(value_type)s返现券即将截止领取"
msgstr ""

#: app/business/coupon/message.py:193
msgid "领取成功后，符合条件的订单手续费将于次日返还。先到先得"
msgstr ""

#: app/business/coupon/message.py:196
#, python-format
msgid "%(value)s %(value_type)s返现券即将截止激活"
msgstr ""

#: app/business/coupon/message.py:197
msgid "激活后，符合条件的订单手续费将于次日返还。请尽快激活使用"
msgstr ""

#: app/business/coupon/message.py:200
#, python-format
msgid "%(value)s %(value_type)s返现券即将过期"
msgstr ""

#: app/business/coupon/message.py:201
msgid "请尽快前往交易使用"
msgstr ""

#: app/business/coupon/message.py:204
#, python-format
msgid "%(value)s %(value_type)s合约补贴金待领取"
msgstr ""

#: app/business/coupon/message.py:205
msgid "随心交易，亏损由CoinEx补贴！立即前往卡券中心领取"
msgstr ""

#: app/business/coupon/message.py:208
#, python-format
msgid "%(value)s %(value_type)s合约补贴金待激活"
msgstr ""

#: app/business/coupon/message.py:209
msgid "随心交易，亏损由CoinEx补贴！立即前往卡券中心激活"
msgstr ""

#: app/business/coupon/message.py:212 app/business/coupon/message.py:216
#: app/templates/email/notice/perpetual_subsidy/active_deadline_notice.j2:3
#: app/templates/email/notice/perpetual_subsidy/used_deadline_notice.j2:3
#, python-format
msgid "%(value)s %(value_type)s合约补贴金，即将过期"
msgstr ""

#: app/business/coupon/message.py:213
#, python-format
msgid "你的%(value)s %(value_type)s合约补贴金即将到期，立即前往领取"
msgstr ""

#: app/business/coupon/message.py:217
#, python-format
msgid "你的%(value)s %(value_type)s合约补贴金暂未使用完毕，立即前往使用"
msgstr ""

#: app/business/coupon/message.py:220
#, python-format
msgid "VIP+%(value)s 升级券待领取"
msgstr ""

#: app/business/coupon/message.py:221 app/common/constants.py:1971
#, python-format
msgid "恭喜你获得VIP+%(value)s 升级券，立即前往卡券中心领取。"
msgstr ""

#: app/business/coupon/message.py:224 app/business/coupon/message.py:228
#: app/business/coupon/message.py:232
#: app/templates/email/notice/vip_upgrade/active_deadline_notice.j2:3
#: app/templates/email/notice/vip_upgrade/used_deadline_notice.j2:3
msgid "VIP升级券即将过期"
msgstr ""

#: app/business/coupon/message.py:225
#, python-format
msgid "你的VIP+%(value)s 升级券即将到期，立即前往卡券中心领取。"
msgstr ""

#: app/business/coupon/message.py:229 app/business/coupon/message.py:233
#, python-format
msgid "你的VIP+%(value)s 升级券即将到期。权益到期时间：%(expired_at)s"
msgstr ""

#: app/business/coupon/message.py:236 app/common/constants.py:1388
msgid "合约跟单体验金券待领取"
msgstr ""

#: app/business/coupon/message.py:237 app/common/constants.py:1958
msgid "恭喜你获得合约跟单体验金券，立即前往卡券中心领取。"
msgstr ""

#: app/business/coupon/message.py:240 app/business/coupon/message.py:244
#: app/templates/email/notice/copy_trading_experience_fee/active_deadline_notice.j2:3
#: app/templates/email/notice/copy_trading_experience_fee/using_deadline_notice.j2:3
msgid "合约跟单体验金券即将过期"
msgstr ""

#: app/business/coupon/message.py:241
msgid "你的合约跟单体验金券即将到期，立即前往卡券中心领取。"
msgstr ""

#: app/business/coupon/message.py:245
#, python-format
msgid "你的合约跟单体验金券即将到期，立即前往卡券中心查看。权益到期时间：%(expired_at)s"
msgstr ""

#: app/business/coupon/message.py:439
msgid "币币(不含AMM)、兑换"
msgstr ""

#: app/business/coupon/message.py:441
msgid "币币(不含AMM)、兑换、合约"
msgstr ""

#: app/business/coupon/message.py:442
msgid "币币"
msgstr ""

#: app/business/coupon/message.py:444
#: app/templates/email/notice/sign_up_success.j2:57
msgid "永续合约交易"
msgstr ""

#: app/business/coupon/pool.py:279 app/business/coupon/pool.py:330
#: app/business/coupon/pool.py:440
msgid "不具备资格领取"
msgstr ""

#: app/business/fiat/base.py:272
#, python-format
msgid "%(partner)s 新用户专享减免%(activity_value)s手续费，具体费用以实际支付为准。"
msgstr ""

#: app/business/fiat/base.py:273
#, python-format
msgid "%(partner)s 用户专享减免%(activity_value)s手续费，具体费用以实际支付为准。"
msgstr ""

#: app/business/mission_center/message.py:184
#: app/business/mission_center/message.py:229 app/common/constants.py:1505
msgid "请查收新任务"
msgstr ""

#: app/business/mission_center/message.py:185
#, python-format
msgid "“%(title)s”已上线，快来奖励中心接受挑战吧！请前往「奖励中心」查看详情。"
msgstr ""

#: app/business/mission_center/message.py:230
#, python-format
msgid "“%(title)s”等多个任务已上线，快来奖励中心接受挑战吧！请前往「奖励中心」查看详情。"
msgstr ""

#: app/business/mission_center/message.py:267 app/common/constants.py:1506
msgid "任务奖励发放"
msgstr ""

#: app/business/mission_center/message.py:268
#, python-format
msgid "恭喜，你已完成新用户专属任务！任务名称：%(title)s 请前往「我的奖励」查看详情。"
msgstr ""

#: app/business/mission_center/message.py:319
#: app/business/mission_center/message.py:359 app/common/constants.py:1507
msgid "任务即将到期"
msgstr ""

#: app/business/mission_center/message.py:320
#, python-format
msgid ""
"你的新用户专属任务即将到期，完成即可获得 %(value)s "
"%(value_type)s%(reward_type)s奖励！请前往「奖励中心」完成任务。"
msgstr ""

#: app/business/mission_center/message.py:360 app/common/constants.py:2431
#: app/templates/email/notice/mission_center/mission_expiring.j2:5
msgid "你有多个新用户专属任务即将到期，请前往「奖励中心」查看详情。"
msgstr ""

#: app/business/mission_center/mission.py:171
#, python-format
msgid ""
"完成%(mission_type)s, 且累计金额≥%(amount)s %(asset)s，获得%(value)s "
"%(value_type)s%(reward_type)s奖励"
msgstr ""

#: app/business/mission_center/mission.py:175
#, python-format
msgid "完成一次%(mission_type)s，获得%(value)s %(value_type)s%(reward_type)s奖励"
msgstr ""

#: app/business/p2p/advertising.py:1147
msgid "您的注册时间未满足广告商要求"
msgstr ""

#: app/business/p2p/advertising.py:1148
msgid "您的认证地区未满足广告商要求"
msgstr ""

#: app/business/p2p/advertising.py:1149
msgid "您的完单率未满足广告商要求"
msgstr ""

#: app/business/p2p/advertising.py:1150
msgid "您未满足广告方要求"
msgstr ""

#: app/business/p2p/message.py:44
msgid "用户买币"
msgstr ""

#: app/business/p2p/message.py:45
msgid "用户卖币"
msgstr ""

#: app/business/p2p/message.py:50
msgid "买家"
msgstr ""

#: app/business/p2p/message.py:51
msgid "卖家"
msgstr ""

#: app/business/p2p/message.py:160 app/common/constants.py:1435
msgid "P2P接单提醒"
msgstr ""

#: app/business/p2p/message.py:160
msgid "您发布的广告已有用户下单，请前往确认订单。"
msgstr ""

#: app/business/p2p/message.py:161 app/common/constants.py:1436
msgid "P2P拒绝接单提醒"
msgstr ""

#: app/business/p2p/message.py:161
msgid "您发起的P2P订单商家拒绝接单，请重新下单。"
msgstr ""

#: app/business/p2p/message.py:162 app/business/p2p/message.py:163
#: app/common/constants.py:1437
msgid "P2P订单已确认"
msgstr ""

#: app/business/p2p/message.py:162
msgid "您发起的买币订单商家已确认，请前往付款。"
msgstr ""

#: app/business/p2p/message.py:163
msgid "您发起的卖币订单商家已确认，请等待商家付款。"
msgstr ""

#: app/business/p2p/message.py:164 app/common/constants.py:1438
msgid "P2P订单付款提醒"
msgstr ""

#: app/business/p2p/message.py:164
msgid "您的P2P买币订单5分钟后将超时取消，请前往付款。"
msgstr ""

#: app/business/p2p/message.py:165 app/common/constants.py:1439
msgid "P2P订单放币提醒"
msgstr ""

#: app/business/p2p/message.py:165
msgid "您的P2P订单买家已付款，请前往放币。"
msgstr ""

#: app/business/p2p/message.py:266
msgid "您有一笔订单待确认，请与对方沟通并确认是否接单，在您未确认接单前，对方的收款方式将不展示给您。"
msgstr ""

#: app/business/p2p/message.py:271
msgid "您有一笔订单待确认，请与对方沟通并确认是否接单，在未确认接单前，您的收款详情不会展示给对方。"
msgstr ""

#: app/business/p2p/message.py:278 app/business/p2p/message.py:363
msgid "对方已取消订单。"
msgstr ""

#: app/business/p2p/message.py:282 app/business/p2p/message.py:359
msgid "您已取消订单。"
msgstr ""

#: app/business/p2p/message.py:288
msgid "您有一笔订单待支付，付款后，请点击“已付款”按钮。"
msgstr ""

#: app/business/p2p/message.py:292
msgid "您有一笔订单待对方支付，请及时沟通并等待对方付款。"
msgstr ""

#: app/business/p2p/message.py:298
msgid "您的订单还有五分钟即将超时，超时后将自动取消，请及时付款。"
msgstr ""

#: app/business/p2p/message.py:302
msgid "您的订单还有五分钟即将付款超时，超时后将自动取消。"
msgstr ""

#: app/business/p2p/message.py:308
msgid "已付款，等待对方放币。"
msgstr ""

#: app/business/p2p/message.py:312
msgid "对方已付款，请查收后确认放币。"
msgstr ""

#: app/business/p2p/message.py:318
msgid "您已上传了付款凭证，<a>点击查看</a>"
msgstr ""

#: app/business/p2p/message.py:322
msgid "对方已上传了付款凭证，<a>点击查看</a>"
msgstr ""

#: app/business/p2p/message.py:328
msgid "已向对方发送放币提醒，请尝试沟通提醒，如长时间未放币，可点击订单申诉联系客服处理。"
msgstr ""

#: app/business/p2p/message.py:332
msgid "对方已付款，请及时放币。"
msgstr ""

#: app/business/p2p/message.py:338
msgid "对方已放币，订单完成。"
msgstr ""

#: app/business/p2p/message.py:342
msgid "已放币，订单完成。"
msgstr ""

#: app/business/p2p/message.py:348 app/business/p2p/message.py:352
msgid "商家拒绝接单，订单已取消。"
msgstr ""

#: app/business/p2p/message.py:369 app/business/p2p/message.py:373
msgid "确认超时，该订单已取消。"
msgstr ""

#: app/business/p2p/message.py:379
msgid "付款超时，该订单已取消。"
msgstr ""

#: app/business/p2p/message.py:383
msgid "付款超时，该订单已取消，如您已向对方转账，请及时联系对方退回或点击订单申诉联系客服处理。"
msgstr ""

#: app/business/p2p/message.py:390
msgid "客服介入, 该订单已取消"
msgstr ""

#: app/business/p2p/message.py:394
msgid "客服介入, 该订单已取消， 您的数字资产已解冻"
msgstr ""

#: app/business/p2p/message.py:400
msgid "订单已被系统取消。"
msgstr ""

#: app/business/p2p/message.py:404
msgid "该订单已被系统取消，您的数字资产已解冻。"
msgstr ""

#: app/business/p2p/message.py:411
msgid "您已提交申诉，请等待客服介入，在此期间也可以尝试联系对方沟通解决。"
msgstr ""

#: app/business/p2p/message.py:415
msgid "对方发起了一笔申诉，请与对方沟通并尝试解决。"
msgstr ""

#: app/business/p2p/message.py:421 app/business/p2p/message.py:425
msgid "申诉已完成，请点击申诉进度查看处理详情。"
msgstr ""

#: app/business/p2p/message.py:577
msgid "对方主动取消"
msgstr ""

#: app/business/perpetual/market.py:17
msgid "杠杆倍数过低，可用保证金不足"
msgstr ""

#: app/business/perpetual/market.py:19
msgid "杠杆倍数超过当前仓位允许的最大值"
msgstr ""

#: app/business/perpetual/position.py:38 app/business/perpetual/position.py:52
msgid "止盈"
msgstr ""

#: app/business/perpetual/position.py:39 app/business/perpetual/position.py:53
msgid "止损"
msgstr ""

#: app/business/perpetual/position.py:40
msgid "合约平仓"
msgstr ""

#: app/business/perpetual/position.py:41
msgid "合约一键平仓"
msgstr ""

#: app/business/perpetual/position.py:45 app/common/constants.py:1231
#: app/common/constants.py:1281
msgid "开仓"
msgstr ""

#: app/business/perpetual/position.py:46 app/common/constants.py:1232
#: app/common/constants.py:1282
msgid "加仓"
msgstr ""

#: app/business/perpetual/position.py:47 app/common/constants.py:1233
#: app/common/constants.py:1283
msgid "减仓"
msgstr ""

#: app/business/perpetual/position.py:48 app/common/constants.py:1234
#: app/common/constants.py:1284
msgid "平仓"
msgstr ""

#: app/business/perpetual/position.py:49 app/common/constants.py:1235
msgid "强制减仓"
msgstr ""

#: app/business/perpetual/position.py:50 app/common/constants.py:1236
#: app/common/constants.py:1286
msgid "强平"
msgstr ""

#: app/business/perpetual/position.py:51 app/common/constants.py:1237
#: app/common/constants.py:1287
msgid "自动减仓"
msgstr ""

#: app/business/perpetual/position.py:54
msgid "一键全平"
msgstr ""

#: app/business/perpetual/position.py:55
msgid "一键平仓"
msgstr ""

#: app/business/perpetual/position.py:191
msgid "cont"
msgstr ""

#: app/business/pledge/notice.py:113
msgid "还币成功"
msgstr ""

#: app/business/pledge/notice.py:118
#, python-format
msgid "你已成功还币 %(repay_amount)s %(loan_asset)s。实际使用质押币 %(used_pledge_assets)s。"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:6
msgid "我在P2P交易时，交易对手是："
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:8
msgid "CoinEx官方"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:9
msgid "发布P2P订单的广告商"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:10
msgid "随机分配的对手"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:11
msgid "我自己的钱包资金"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:16
msgid "频繁取消订单，我会受到什么影响？"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:18
msgid "如待接单状态取消订单，无影响"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:19
msgid "如商家接单后取消3次，会限制当日下单"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:20
msgid "会影响本人的完单率"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:21
#: app/business/question_bank/p2p_question_bank.py:51
msgid "以上都对"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:26
msgid "发现商家提供支付方式内容有误，我应该："
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:28
msgid "先抓紧时间转账"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:29
msgid "及时与商家沟通，要求提供正确的支付方式"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:30
msgid "立即申诉并举报商家"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:31
msgid "检查本人设置是否有误"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:36
msgid "买币向商家转账时，我应该："
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:38
msgid "使用自己选择的付款方式，并保证实名与平台认证一致"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:39
msgid "不一定要用本人实名的卡，转账金额正确即可"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:40
msgid "可以分批次多笔转账"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:41
msgid "使用来路不明的资金转账"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:46
msgid "确认放币前，我应该："
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:48
msgid "不能只看对方截图，需要在选择的收款账号中确认实际到账情况"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:49
msgid "检查转账人是否与商家实名一致"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:50
msgid "检查该笔转账有无异常（如冻结、延迟等）"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:56
msgid "确认放币后，我还能追回数字货币吗？"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:58
msgid "一旦点击确认放币，系统会自动解冻数字货币并划转至对方账户，无法追回"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:59
msgid "可通过平台协助追回"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:60
msgid "可通过申诉反馈追回"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:61
msgid "可与对方协商退回"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:66
msgid "交易过程中发现对方有异常行为，我应该："
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:68
msgid "立即停止交易并发起申诉"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:69
msgid "自行排查原因并继续交易"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:70
msgid "交易完成后再自行申诉"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:71
msgid "取消订单并不再理会"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:76
msgid "申诉过程中需要查看客服最新回复，我应该："
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:78
msgid "在聊天窗口和客服进行沟通"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:79
msgid "仅限申诉进度页面在客服进行沟通"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:80
msgid "提交工单询问最新消息"
msgstr ""

#: app/business/question_bank/p2p_question_bank.py:81
msgid "向交易对手方询问最新信息"
msgstr ""

#: app/business/red_packet/grab.py:498 app/business/red_packet/grab.py:571
msgid "领取成功"
msgstr ""

#: app/business/red_packet/grab.py:605 app/business/red_packet/grab.py:623
#: app/business/red_packet/grab.py:628
#, python-format
msgid "口令C-Box领取功能已锁定，请%(hours)s小时后再尝试。"
msgstr ""

#: app/business/strategy/base.py:79
#, python-format
msgid "运行中（包括已暂停）的策略不得超过%(max_count)s个"
msgstr ""

#: app/caches/assets.py:134
#, python-format
msgid "基于%(network)s主网的地址，充值通过%(network)s网络"
msgstr ""

#: app/caches/assets.py:135
#, python-format
msgid "基于%(network)s主网的地址，提现通过%(network)s网络"
msgstr ""

#: app/caches/assets.py:136
msgid "钱包维护，暂停充值。"
msgstr ""

#: app/caches/assets.py:137
msgid "钱包维护，暂停提现。"
msgstr ""

#: app/caches/mission.py:99
msgid "活动参与人数已满"
msgstr ""

#: app/common/constants.py:88
msgid "英语"
msgstr ""

#: app/common/constants.py:89
msgid "简体中文"
msgstr ""

#: app/common/constants.py:90
msgid "繁体中文"
msgstr ""

#: app/common/constants.py:91
msgid "日语"
msgstr ""

#: app/common/constants.py:92
msgid "俄语"
msgstr ""

#: app/common/constants.py:93
msgid "韩语"
msgstr ""

#: app/common/constants.py:94
msgid "印尼语"
msgstr ""

#: app/common/constants.py:95
msgid "西班牙语"
msgstr ""

#: app/common/constants.py:96
msgid "波斯语"
msgstr ""

#: app/common/constants.py:97
msgid "土耳其语"
msgstr ""

#: app/common/constants.py:98
msgid "越南语"
msgstr ""

#: app/common/constants.py:99
msgid "阿拉伯语"
msgstr ""

#: app/common/constants.py:100
msgid "法语"
msgstr ""

#: app/common/constants.py:101
msgid "葡萄牙语"
msgstr ""

#: app/common/constants.py:102
msgid "德语"
msgstr ""

#: app/common/constants.py:103
msgid "泰语"
msgstr ""

#: app/common/constants.py:104
msgid "意大利语"
msgstr ""

#: app/common/constants.py:105
msgid "波兰语"
msgstr ""

#: app/common/constants.py:1035 app/models/activity.py:1232
msgid "充值"
msgstr ""

#: app/common/constants.py:1036
msgid "提现"
msgstr ""

#: app/common/constants.py:1037
msgid "提现手续费"
msgstr ""

#: app/common/constants.py:1038
msgid "取消充值"
msgstr ""

#: app/common/constants.py:1039
msgid "取消提现"
msgstr ""

#: app/common/constants.py:1040
msgid "提现手续费退回"
msgstr ""

#: app/common/constants.py:1041
#: app/templates/email/notice/margin_function_introduction.j2:84
msgid "交易"
msgstr ""

#: app/common/constants.py:1042
msgid "交易费"
msgstr ""

#: app/common/constants.py:1043 app/common/constants.py:1044
msgid "系统"
msgstr ""

#: app/common/constants.py:1045
msgid "赠送"
msgstr ""

#: app/common/constants.py:1046
msgid "奖励撤销"
msgstr ""

#: app/common/constants.py:1047 app/common/constants.py:1133
msgid "返佣"
msgstr ""

#: app/common/constants.py:1048
msgid "OTC划转"
msgstr ""

#: app/common/constants.py:1049
msgid "杠杆划转"
msgstr ""

#: app/common/constants.py:1050
msgid "杠杆借币"
msgstr ""

#: app/common/constants.py:1051
msgid "杠杆还币"
msgstr ""

#: app/common/constants.py:1052 app/common/constants.py:1129
msgid "强制平仓"
msgstr ""

#: app/common/constants.py:1053 app/common/constants.py:1130
msgid "强平清算费"
msgstr ""

#: app/common/constants.py:1054
msgid "小额兑换"
msgstr ""

#: app/common/constants.py:1055
msgid "子账号划转"
msgstr ""

#: app/common/constants.py:1056
msgid "发C-Box"
msgstr ""

#: app/common/constants.py:1057
msgid "领C-Box"
msgstr ""

#: app/common/constants.py:1058
msgid "C-Box退还"
msgstr ""

#: app/common/constants.py:1059
msgid "体验金"
msgstr ""

#: app/common/constants.py:1060
msgid "回收体验金"
msgstr ""

#: app/common/constants.py:1061
msgid "交易赠金券"
msgstr ""

#: app/common/constants.py:1062 app/models/activity.py:50
msgid "手续费返现券"
msgstr ""

#: app/common/constants.py:1063 app/models/activity.py:51
msgid "合约补贴金"
msgstr ""

#: app/common/constants.py:1064
msgid "maker返现"
msgstr ""

#: app/common/constants.py:1065
msgid "期货划转"
msgstr ""

#: app/common/constants.py:1066
msgid "期货借币"
msgstr ""

#: app/common/constants.py:1067
msgid "期货还币"
msgstr ""

#: app/common/constants.py:1068
msgid "期货强平"
msgstr ""

#: app/common/constants.py:1069
msgid "期货交割"
msgstr ""

#: app/common/constants.py:1070
msgid "交割兑换"
msgstr ""

#: app/common/constants.py:1071
msgid "发行"
msgstr ""

#: app/common/constants.py:1072 app/common/constants.py:1148
msgid "赎回"
msgstr ""

#: app/common/constants.py:1073
msgid "交割结算"
msgstr ""

#: app/common/constants.py:1074
msgid "活期理财划转"
msgstr ""

#: app/common/constants.py:1075
msgid "利息收入"
msgstr ""

#: app/common/constants.py:1076
msgid "加息收入"
msgstr ""

#: app/common/constants.py:1077
msgid "dex_node_vote"
msgstr ""

#: app/common/constants.py:1078
msgid "loss_balance"
msgstr ""

#: app/common/constants.py:1079
msgid "划入"
msgstr ""

#: app/common/constants.py:1080
msgid "划出"
msgstr ""

#: app/common/constants.py:1081
msgid "合约划转"
msgstr ""

#: app/common/constants.py:1082
msgid "结算盈亏"
msgstr ""

#: app/common/constants.py:1083
msgid "清仓盈亏"
msgstr ""

#: app/common/constants.py:1084
msgid "授信"
msgstr ""

#: app/common/constants.py:1085
msgid "授信还币"
msgstr ""

#: app/common/constants.py:1087
msgid "small_coin_transfer"
msgstr ""

#: app/common/constants.py:1089
msgid "income_total_transfer"
msgstr ""

#: app/common/constants.py:1091
msgid "buyback_transfer"
msgstr ""

#: app/common/constants.py:1093
msgid "income_to_admin_transfer"
msgstr ""

#: app/common/constants.py:1095
msgid "buyback_exchange"
msgstr ""

#: app/common/constants.py:1097 app/common/constants.py:1098
msgid "insurance_transfer"
msgstr ""

#: app/common/constants.py:1100
msgid "insurance_to_admin_transfer"
msgstr ""

#: app/common/constants.py:1102
msgid "注销账号划转到admin"
msgstr ""

#: app/common/constants.py:1104 app/models/amm.py:103
msgid "增加流动性"
msgstr ""

#: app/common/constants.py:1105 app/models/amm.py:104
msgid "提取流动性"
msgstr ""

#: app/common/constants.py:1106
msgid "AMM手续费注入"
msgstr ""

#: app/common/constants.py:1108
msgid "mining_activity"
msgstr ""

#: app/common/constants.py:1110
msgid "申购"
msgstr ""

#: app/common/constants.py:1111
msgid "退款"
msgstr ""

#: app/common/constants.py:1112
msgid "中签"
msgstr ""

#: app/common/constants.py:1113 app/models/activity.py:1235
#: app/models/operation.py:2724
msgid "兑换"
msgstr ""

#: app/common/constants.py:1114
msgid "定投"
msgstr ""

#: app/common/constants.py:1115
msgid "经纪商返佣"
msgstr ""

#: app/common/constants.py:1116
msgid "普通返佣"
msgstr ""

#: app/common/constants.py:1117
msgid "大使返佣"
msgstr ""

#: app/common/constants.py:1118
msgid "兑换手续费"
msgstr ""

#: app/common/constants.py:1119 app/models/operation.py:2728
msgid "现货网格"
msgstr ""

#: app/common/constants.py:1121
msgid "Dibs申购"
msgstr ""

#: app/common/constants.py:1122
msgid "Dibs退款"
msgstr ""

#: app/common/constants.py:1123
msgid "Dibs中签"
msgstr ""

#: app/common/constants.py:1125
msgid "借币(借贷)"
msgstr ""

#: app/common/constants.py:1126
msgid "锁定质押资产"
msgstr ""

#: app/common/constants.py:1127
msgid "释放质押资产"
msgstr ""

#: app/common/constants.py:1128
msgid "还币(借贷)"
msgstr ""

#: app/common/constants.py:1131
msgid "预付金"
msgstr ""

#: app/common/constants.py:1132
msgid "预付金还款"
msgstr ""

#: app/common/constants.py:1135
msgid "跟单资金划转"
msgstr ""

#: app/common/constants.py:1136
msgid "跟单分润结算"
msgstr ""

#: app/common/constants.py:1137
msgid "预测发行"
msgstr ""

#: app/common/constants.py:1138
msgid "预测赎回"
msgstr ""

#: app/common/constants.py:1139
msgid "发行交割"
msgstr ""

#: app/common/constants.py:1140
msgid "持仓交割"
msgstr ""

#: app/common/constants.py:1142
msgid "挖矿收益分发"
msgstr ""

#: app/common/constants.py:1144 app/models/equity_center.py:15
msgid "空投"
msgstr ""

#: app/common/constants.py:1145 app/models/equity_center.py:14
msgid "手续费返现"
msgstr ""

#: app/common/constants.py:1147
msgid "质押"
msgstr ""

#: app/common/constants.py:1149
msgid "质押收益"
msgstr ""

#: app/common/constants.py:1151
msgid "p2p锁定订单资产"
msgstr ""

#: app/common/constants.py:1152
msgid "p2p取消订单"
msgstr ""

#: app/common/constants.py:1153
msgid "p2p卖出"
msgstr ""

#: app/common/constants.py:1154
msgid "p2p买入"
msgstr ""

#: app/common/constants.py:1155 app/common/constants.py:1157
msgid "商家保证金缴纳"
msgstr ""

#: app/common/constants.py:1156 app/common/constants.py:1158
msgid "商家保证金返还"
msgstr ""

#: app/common/constants.py:1159
msgid "P2P交易赔付"
msgstr ""

#: app/common/constants.py:1160
msgid "大使激励"
msgstr ""

#: app/common/constants.py:1162
msgid "收到礼物(打赏)"
msgstr ""

#: app/common/constants.py:1163
msgid "发出礼物(打赏)"
msgstr ""

#: app/common/constants.py:1221 app/common/constants.py:1270
msgid "逐仓"
msgstr ""

#: app/common/constants.py:1222 app/common/constants.py:1271
msgid "全仓"
msgstr ""

#: app/common/constants.py:1226 app/common/constants.py:1265
msgid "追加保证金"
msgstr ""

#: app/common/constants.py:1227 app/common/constants.py:1266
msgid "减少保证金"
msgstr ""

#: app/common/constants.py:1267
msgid "调整杠杆"
msgstr ""

#: app/common/constants.py:1269
msgid "模式调整"
msgstr ""

#: app/common/constants.py:1274
msgid "逐仓结算"
msgstr ""

#: app/common/constants.py:1275
msgid "全仓结算"
msgstr ""

#: app/common/constants.py:1285
msgid "减仓（强制减仓）"
msgstr ""

#: app/common/constants.py:1288
msgid "减仓（系统强平）"
msgstr ""

#: app/common/constants.py:1289
msgid "减仓（自动减仓）"
msgstr ""

#: app/common/constants.py:1290
msgid "减仓（止盈）"
msgstr ""

#: app/common/constants.py:1291
msgid "减仓（止损）"
msgstr ""

#: app/common/constants.py:1292
msgid "平仓（系统强平）"
msgstr ""

#: app/common/constants.py:1293
msgid "平仓（自动减仓）"
msgstr ""

#: app/common/constants.py:1294
msgid "平仓（止盈）"
msgstr ""

#: app/common/constants.py:1295
msgid "平仓（止损）"
msgstr ""

#: app/common/constants.py:1296
msgid "自动减仓开仓"
msgstr ""

#: app/common/constants.py:1300
#: app/templates/email/notice/margin_function_introduction.j2:33
msgid "做空"
msgstr ""

#: app/common/constants.py:1301
#: app/templates/email/notice/margin_function_introduction.j2:23
msgid "做多"
msgstr ""

#: app/common/constants.py:1305
msgid "上涨"
msgstr ""

#: app/common/constants.py:1306
msgid "下跌"
msgstr ""

#: app/common/constants.py:1310
msgid "购买"
msgstr ""

#: app/common/constants.py:1311
msgid "出售"
msgstr ""

#: app/common/constants.py:1317
msgid "赠送成功"
msgstr ""

#: app/common/constants.py:1318
#: app/templates/email/notice/send_coin_withdraw_notice.j2:2
msgid "提现审核通过"
msgstr ""

#: app/common/constants.py:1319
msgid "提现需邮件确认"
msgstr ""

#: app/common/constants.py:1320
msgid "修改密码"
msgstr ""

#: app/common/constants.py:1321
msgid "手机号绑定成功"
msgstr ""

#: app/common/constants.py:1322
msgid "提现密码设置成功"
msgstr ""

#: app/common/constants.py:1323
msgid "修改交易密码成功"
msgstr ""

#: app/common/constants.py:1324
msgid "绑定邮箱"
msgstr ""

#: app/common/constants.py:1325
msgid "TOTP绑定成功"
msgstr ""

#: app/common/constants.py:1326
msgid "修改邮箱"
msgstr ""

#: app/common/constants.py:1327
msgid "手机号更换成功"
msgstr ""

#: app/common/constants.py:1328
msgid "手机号解绑成功"
msgstr ""

#: app/common/constants.py:1329
msgid "提现密码修改成功"
msgstr ""

#: app/common/constants.py:1330
msgid "修改谷歌验证器"
msgstr ""

#: app/common/constants.py:1331
msgid "二次验证"
msgstr ""

#: app/common/constants.py:1332
msgid "认证状态"
msgstr ""

#: app/common/constants.py:1333
msgid "恭喜"
msgstr ""

#: app/common/constants.py:1334
msgid "申购中签通知"
msgstr ""

#: app/common/constants.py:1335 app/common/constants.py:1336
msgid "强平预警"
msgstr ""

#: app/common/constants.py:1337 app/common/constants.py:1338
#: app/common/constants.py:1340
msgid "强平通知"
msgstr ""

#: app/common/constants.py:1339
msgid "margin gift"
msgstr ""

#: app/common/constants.py:1341
#: app/templates/email/notice/perpetual_liquidation.j2:2
msgid "合约强制平仓通知"
msgstr ""

#: app/common/constants.py:1343
#: app/templates/email/notice/perpetual_liquidation_warning.j2:2
msgid "强平预警通知"
msgstr ""

#: app/common/constants.py:1344
msgid "自动减仓通知"
msgstr ""

#: app/common/constants.py:1345 app/templates/email/notice/perpetual_adl.j2:2
msgid "合约自动减仓通知"
msgstr ""

#: app/common/constants.py:1348
msgid "续借失败"
msgstr ""

#: app/common/constants.py:1349
msgid "预选节点投票抽奖"
msgstr ""

#: app/common/constants.py:1350
msgid "红包到账通知"
msgstr ""

#: app/common/constants.py:1351
msgid "C-Box到账通知"
msgstr ""

#: app/common/constants.py:1352
msgid "红包退还"
msgstr ""

#: app/common/constants.py:1353
msgid "红包退还通知"
msgstr ""

#: app/common/constants.py:1354
msgid "C-Box退还通知"
msgstr ""

#: app/common/constants.py:1355 app/common/constants.py:1357
#: app/common/constants.py:1366
msgid "奖励发放"
msgstr ""

#: app/common/constants.py:1356
msgid "活动邀请"
msgstr ""

#: app/common/constants.py:1358
#: app/templates/email/notice/airdrop_rewords_success.j2:2
msgid "空投中奖通知"
msgstr ""

#: app/common/constants.py:1359
#: app/templates/email/notice/airdrop_rewords_fail.j2:2
msgid "空投未中奖通知"
msgstr ""

#: app/common/constants.py:1360
#: app/templates/email/notice/airdrop_rewords_result.j2:2
msgid "参与空投结果通知"
msgstr ""

#: app/common/constants.py:1361
#: app/templates/email/notice/dibs_rewords_success.j2:2
msgid "Dibs中奖通知"
msgstr ""

#: app/common/constants.py:1362
#: app/templates/email/notice/dibs_rewords_fail.j2:2
msgid "Dibs未中奖通知"
msgstr ""

#: app/common/constants.py:1363
msgid "稀有聪竞标成功"
msgstr ""

#: app/common/constants.py:1367
msgid "重置安全项成功"
msgstr ""

#: app/common/constants.py:1368
msgid "重置安全项失败"
msgstr ""

#: app/common/constants.py:1369
#: app/templates/email/notice/withdraw_password_reset_pass.j2:2
msgid "提现密码重置成功"
msgstr ""

#: app/common/constants.py:1370
#: app/templates/email/notice/withdraw_password_reset_fail.j2:2
msgid "提现密码重置失败"
msgstr ""

#: app/common/constants.py:1371
msgid "止盈止损通知"
msgstr ""

#: app/common/constants.py:1372
msgid "合约止盈失败通知"
msgstr ""

#: app/common/constants.py:1373
msgid "合约止损失败通知"
msgstr ""

#: app/common/constants.py:1374
msgid "合约平仓失败通知"
msgstr ""

#: app/common/constants.py:1375
msgid "资料补充通知：充值未到账找回申请"
msgstr ""

#: app/common/constants.py:1376
msgid "手续费补充通知：充值未到账找回申请"
msgstr ""

#: app/common/constants.py:1377
msgid "充值未到账找回申请审核失败"
msgstr ""

#: app/common/constants.py:1378
msgid "充值未到账找回成功"
msgstr ""

#: app/common/constants.py:1379
msgid "提现功能关闭通知"
msgstr ""

#: app/common/constants.py:1380
msgid "交易功能关闭通知"
msgstr ""

#: app/common/constants.py:1381
msgid "开通杠杆交易"
msgstr ""

#: app/common/constants.py:1382
msgid "开通合约交易"
msgstr ""

#: app/common/constants.py:1383
msgid "卡券领取成功"
msgstr ""

#: app/common/constants.py:1384
msgid "卡券兑换成功"
msgstr ""

#: app/common/constants.py:1385
msgid "卡券资金已满足赠送标准"
msgstr ""

#: app/common/constants.py:1386
msgid "卡券资金到账成功"
msgstr ""

#: app/common/constants.py:1387
msgid "卡券已使用"
msgstr ""

#: app/common/constants.py:1389
msgid "合约跟单体验金券领取成功"
msgstr ""

#: app/common/constants.py:1390
msgid "恭喜兑换合约跟单体验金券"
msgstr ""

#: app/common/constants.py:1391
#, python-format
msgid "VIP+%(value)s升级券待领取"
msgstr ""

#: app/common/constants.py:1392
msgid "恭喜兑换VIP升级券"
msgstr ""

#: app/common/constants.py:1393
msgid "VIP升级券领取成功"
msgstr ""

#: app/common/constants.py:1394
msgid "VIP等级变更"
msgstr ""

#: app/common/constants.py:1395 app/templates/email/notice/sign_in_unusual.j2:2
msgid "异地登录提醒"
msgstr ""

#: app/common/constants.py:1396
msgid "实名认证结果"
msgstr ""

#: app/common/constants.py:1397 app/templates/email/notice/kyc_pro_pass.j2:2
msgid "KYC高级认证通过"
msgstr ""

#: app/common/constants.py:1398 app/templates/email/notice/kyc_pro_fail.j2:2
msgid "KYC高级认证不通过"
msgstr ""

#: app/common/constants.py:1399
#: app/templates/email/notice/risk_screen_fail.j2:2
msgid "【重要】请尽快提币或处置资产"
msgstr ""

#: app/common/constants.py:1400
msgid "邮箱重置成功"
msgstr ""

#: app/common/constants.py:1401
msgid "TOTP重置成功"
msgstr ""

#: app/common/constants.py:1402
msgid "重置通行密钥成功"
msgstr ""

#: app/common/constants.py:1403
msgid "密码重置提醒"
msgstr ""

#: app/common/constants.py:1404
msgid "恭喜你！获得CoinEx白银大使资格！"
msgstr ""

#: app/common/constants.py:1405
msgid "【CoinEx大使】50%永久返佣，USDT日结"
msgstr ""

#: app/common/constants.py:1406
#: app/templates/email/notice/trade_rank_activity_gift.j2:2
msgid "交易排位赛奖励发放"
msgstr ""

#: app/common/constants.py:1408
msgid "恭喜获得终极盲盒开箱机会！"
msgstr ""

#: app/common/constants.py:1409
msgid "恭喜获得周年盲盒开箱机会！"
msgstr ""

#: app/common/constants.py:1410
#: app/templates/email/notice/spot_grid_triggered.j2:3
#, python-format
msgid "%(market)s现货市场已达网格触发价"
msgstr ""

#: app/common/constants.py:1411
#: app/templates/email/notice/spot_grid_take_profit.j2:3
#, python-format
msgid "%(market)s现货市场已达网格止盈价"
msgstr ""

#: app/common/constants.py:1412
#: app/templates/email/notice/spot_grid_stop_loss.j2:3
#, python-format
msgid "%(market)s现货市场已达网格止损价"
msgstr ""

#: app/common/constants.py:1413
#: app/templates/email/notice/spot_grid_exceed_price_range.j2:3
#, python-format
msgid "%(market)s现货市价超出网格价格区间"
msgstr ""

#: app/common/constants.py:1414
#: app/templates/email/notice/spot_grid_exceed_recommend_run_days.j2:3
#, python-format
msgid "%(market)s现货网格策略已超时"
msgstr ""

#: app/common/constants.py:1419
#: app/templates/email/notice/sub_account_bind_manager.j2:3
msgid "子账号授权成功"
msgstr ""

#: app/common/constants.py:1420
#: app/templates/email/notice/sub_account_cancel_manager.j2:3
msgid "子账号授权解除"
msgstr ""

#: app/common/constants.py:1421
#: app/templates/email/notice/auto_invest_deal_all.j2:2
msgid "自动定投全部成交通知"
msgstr ""

#: app/common/constants.py:1422
#: app/templates/email/notice/auto_invest_deal_partial.j2:2
msgid "自动定投部分成交通知"
msgstr ""

#: app/common/constants.py:1423
#: app/templates/email/notice/auto_invest_deal_failed.j2:2
msgid "自动定投未成交通知"
msgstr ""

#: app/common/constants.py:1424 app/common/constants.py:1425
#: app/templates/email/notice/auto_invest_profit_amount.j2:2
#: app/templates/email/notice/auto_invest_profit_rate.j2:2
msgid "自动定投到达盈利目标通知"
msgstr ""

#: app/common/constants.py:1426
#: app/templates/email/notice/auto_invest_failed.j2:2
msgid "自动定投失败通知"
msgstr ""

#: app/common/constants.py:1427
#: app/templates/email/notice/auto_invest_paused.j2:2
msgid "自动定投暂停通知"
msgstr ""

#: app/common/constants.py:1428
#: app/templates/email/notice/auto_invest_closed.j2:2
msgid "自动定投关闭通知"
msgstr ""

#: app/common/constants.py:1429
#: app/templates/email/notice/pledge_liq_warning.j2:3
msgid "借贷补仓通知"
msgstr ""

#: app/common/constants.py:1430 app/templates/email/notice/pledge_liq.j2:3
msgid "借贷强平通知"
msgstr ""

#: app/common/constants.py:1431
msgid "活动奖励到账通知"
msgstr ""

#: app/common/constants.py:1433
#: app/templates/email/notice/perpetual_open_position_stop_loss_notice.j2:2
#: app/templates/email/notice/perpetual_open_position_take_profit_notice.j2:2
msgid "开仓止盈止损设置失效提醒"
msgstr ""

#: app/common/constants.py:1440
msgid "P2P订单已完成"
msgstr ""

#: app/common/constants.py:1441
msgid "P2P订单已取消"
msgstr ""

#: app/common/constants.py:1442
msgid "P2P订单发起申诉"
msgstr ""

#: app/common/constants.py:1443
msgid "P2P申诉订单进度更新"
msgstr ""

#: app/common/constants.py:1444
msgid "P2P订单申诉已处理"
msgstr ""

#: app/common/constants.py:1445
msgid "P2P订单申诉已取消"
msgstr ""

#: app/common/constants.py:1446
msgid "P2P订单申诉已完成"
msgstr ""

#: app/common/constants.py:1447
msgid "P2P订单申诉重新开启"
msgstr ""

#: app/common/constants.py:1448
msgid "P2P广告单被下架"
msgstr ""

#: app/common/constants.py:1449
msgid "P2P新增收款方式"
msgstr ""

#: app/common/constants.py:1450
msgid "P2P支付渠道失效"
msgstr ""

#: app/common/constants.py:1452
msgid "P2P商家活动：报名提交成功"
msgstr ""

#: app/common/constants.py:1453
msgid "P2P商家活动：报名成功"
msgstr ""

#: app/common/constants.py:1454
msgid "P2P商家活动：审核未通过"
msgstr ""

#: app/common/constants.py:1455
msgid "P2P商家活动：参与资格取消"
msgstr ""

#: app/common/constants.py:1456
msgid "P2P商家活动：得奖通知"
msgstr ""

#: app/common/constants.py:1457
msgid "P2P商家活动：奖励冻结通知"
msgstr ""

#: app/common/constants.py:1458
msgid "P2P商家活动：撤消活动奖励"
msgstr ""

#: app/common/constants.py:1459
msgid "P2P商家活动：奖励发放失败"
msgstr ""

#: app/common/constants.py:1460
msgid "P2P商家活动：排名取消通知"
msgstr ""

#: app/common/constants.py:1463
#: app/templates/email/notice/p2p/p2p_margin_payment.j2:2
msgid "P2P商家保证金补充通知"
msgstr ""

#: app/common/constants.py:1464
#: app/templates/email/notice/p2p/p2p_margin_shortfall.j2:2
msgid "P2P商家权限受限通知"
msgstr ""

#: app/common/constants.py:1465
#: app/templates/email/notice/p2p/p2p_margin_change.j2:2
msgid "P2P商家保证金调整通知"
msgstr ""

#: app/common/constants.py:1466
#: app/templates/email/notice/p2p/p2p_mer_cancel.j2:2
msgid "P2P商家身份取消通知"
msgstr ""

#: app/common/constants.py:1468
#: app/templates/email/notice/p2p/p2p_mer_compensation.j2:2
msgid "商家保证金扣除通知"
msgstr ""

#: app/common/constants.py:1469
#: app/templates/email/notice/p2p/p2p_mer_penalty.j2:2
msgid "商家违规扣款通知"
msgstr ""

#: app/common/constants.py:1470
#: app/templates/email/notice/p2p/p2p_mer_excess_refund.j2:2
msgid "商家保证金返还通知"
msgstr ""

#: app/common/constants.py:1471
#: app/templates/email/notice/p2p/p2p_user_compensation.j2:2
msgid "P2P交易赔付到账通知"
msgstr ""

#: app/common/constants.py:1474
#: app/templates/email/notice/staking_remove_success.j2:3
#, python-format
msgid "成功赎回%(asset)s"
msgstr ""

#: app/common/constants.py:1475
#: app/templates/email/notice/staking_remove_notice.j2:3
msgid "质押生效提醒"
msgstr ""

#: app/common/constants.py:1477
#: app/templates/email/notice/pre_trading_settlement.j2:3
#, python-format
msgid "预测市场 %(asset)s已完成交割"
msgstr ""

#: app/common/constants.py:1479
msgid "恭喜成为跟单交易员"
msgstr ""

#: app/common/constants.py:1480
#, python-format
msgid "%(market)s %(leverage)s 带单开仓成功"
msgstr ""

#: app/common/constants.py:1481
#, python-format
msgid "%(market)s %(leverage)s 带单平仓成功"
msgstr ""

#: app/common/constants.py:1482 app/common/constants.py:1494
msgid "交易员调整分润比例"
msgstr ""

#: app/common/constants.py:1483
#, python-format
msgid "%(market)s %(leverage)s 带单强平通知"
msgstr ""

#: app/common/constants.py:1484
#, python-format
msgid "%(market)s %(leverage)s 带单止盈触发"
msgstr ""

#: app/common/constants.py:1485
#, python-format
msgid "%(market)s %(leverage)s 带单止损触发"
msgstr ""

#: app/common/constants.py:1486
msgid "结束带单"
msgstr ""

#: app/common/constants.py:1487
msgid "交易员身份取消通知"
msgstr ""

#: app/common/constants.py:1488
msgid "交易员身份取消提醒"
msgstr ""

#: app/common/constants.py:1489
#, python-format
msgid "%(market)s %(leverage)s 跟单开仓成功"
msgstr ""

#: app/common/constants.py:1490
#, python-format
msgid "%(market)s %(leverage)s 跟单失败"
msgstr ""

#: app/common/constants.py:1491
#, python-format
msgid "%(market)s %(leverage)s 跟单交易平仓成功"
msgstr ""

#: app/common/constants.py:1492
#, python-format
msgid "%(market)s %(leverage)s 跟单强平通知"
msgstr ""

#: app/common/constants.py:1493
msgid "分润结算"
msgstr ""

#: app/common/constants.py:1495
msgid "交易员转入保证金"
msgstr ""

#: app/common/constants.py:1496
msgid "跟单止盈触发"
msgstr ""

#: app/common/constants.py:1497
msgid "跟单止损触发"
msgstr ""

#: app/common/constants.py:1498
msgid "结束跟单关系"
msgstr ""

#: app/common/constants.py:1500
#, python-format
msgid "你的%(stake_assets)s挖矿币种奖励已到账"
msgstr ""

#: app/common/constants.py:1502
#: app/templates/email/notice/equity_center/cashback_expiring.j2:3
msgid "手续费返现权益即将到期"
msgstr ""

#: app/common/constants.py:1503
#: app/templates/email/notice/equity_center/cashback_delivery.j2:3
msgid "手续费返现权益发放"
msgstr ""

#: app/common/constants.py:1504
#: app/templates/email/notice/equity_center/airdrop_delivery.j2:3
msgid "空投奖励发放"
msgstr ""

#: app/common/constants.py:1514
msgid "大使激励包奖励到账提醒"
msgstr ""

#: app/common/constants.py:1515
msgid "大使激励包考核未达标提醒"
msgstr ""

#: app/common/constants.py:1521 app/common/constants.py:1523
#: app/templates/email/notice/deposit_pass_notice.j2:5
#, python-format
msgid "你于%(create_time)s充值的%(amount)s已到账。"
msgstr ""

#: app/common/constants.py:1522
#, python-format
msgid ""
"你于%(create_time)s充值的%(amount)s已到账。\n"
"交易ID：%(tx_id)s"
msgstr ""

#: app/common/constants.py:1524
#, python-format
msgid ""
"你于%(create_time)s提交的提现申请已通过，提现金额为%(amount)s。\n"
"交易ID：%(tx_id)s\n"
"\n"
"如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
msgstr ""

#: app/common/constants.py:1528
#, python-format
msgid ""
"你于%(create_time)s提交的提现申请已通过，提现金额为%(amount)s。\n"
"\n"
"如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
msgstr ""

#: app/common/constants.py:1532
#, python-format
msgid "你于%(create_time)s提交了提现申请，金额为%(amount)s。现已审核汇出。"
msgstr ""

#: app/common/constants.py:1534
#, python-format
msgid "你于%(create_time)s在CoinEx发生提现，金额为%(amount)s，现已审核，一个工作日内到账。"
msgstr ""

#: app/common/constants.py:1536
msgid "你的登录密码已修改成功。"
msgstr ""

#: app/common/constants.py:1538
msgid "你的资金密码已修改成功。"
msgstr ""

#: app/common/constants.py:1539
msgid "你的登录密码已重置成功。"
msgstr ""

#: app/common/constants.py:1541
msgid "你的资金密码已重置成功。"
msgstr ""

#: app/common/constants.py:1544
msgid "你的手机号已绑定成功。"
msgstr ""

#: app/common/constants.py:1545
#, python-format
msgid ""
"你已成功绑定手机号（%(mobile)s），后续可使用该手机号进行手机验证。\n"
"\n"
"如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
msgstr ""

#: app/common/constants.py:1549
msgid ""
"你在CoinEx的提现密码设置成功。\n"
"\n"
"如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
msgstr ""

#: app/common/constants.py:1553
msgid ""
"你在CoinEx的交易密码修改成功。\n"
"\n"
"如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
msgstr ""

#: app/common/constants.py:1557
msgid "你的邮箱已绑定成功。"
msgstr ""

#: app/common/constants.py:1558
msgid "你的谷歌验证器已绑定成功。"
msgstr ""

#: app/common/constants.py:1559
msgid ""
"你已成功绑定TOTP，后续可使用TOTP验证器进行验证。\n"
"\n"
"如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
msgstr ""

#: app/common/constants.py:1563
#, python-format
msgid "You had reset %(reset_type)s for your CoinEx account."
msgstr ""

#: app/common/constants.py:1565
msgid "You had reset email to your CoinEx account."
msgstr ""

#: app/common/constants.py:1567
#, python-format
msgid "Your reset security application has been rejected. Reason is %(reason)s."
msgstr ""

#: app/common/constants.py:1570
msgid "你的手机号已修改成功。"
msgstr ""

#: app/common/constants.py:1571
#, python-format
msgid ""
"你已成功更换绑定的手机号，后续需使用新手机号（%(mobile)s）进行验证。为了保障你的账号安全，24小时内禁止提现。\n"
"\n"
"如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
msgstr ""

#: app/common/constants.py:1575
msgid "你绑定的手机号已解绑，请尽快绑定新手机号或其他安全验证方式，保证账号安全。"
msgstr ""

#: app/common/constants.py:1578
msgid ""
"你在CoinEx的提现密码修改成功。为了保障你的账户安全，24小时内禁止提现。\n"
"\n"
"若非你本人操作，请立即重置密码或禁用账户，并尽快向CoinEx客服提交工单。"
msgstr ""

#: app/common/constants.py:1582
msgid "你的邮箱已修改成功。"
msgstr ""

#: app/common/constants.py:1583
msgid "你的谷歌验证器已修改成功。"
msgstr ""

#: app/common/constants.py:1585
msgid "你的二次验证方式已修改成功。"
msgstr ""

#: app/common/constants.py:1588
msgid "你的账户已通过实名认证。"
msgstr ""

#: app/common/constants.py:1590
msgid "你的账户已通过高级实名认证。"
msgstr ""

#: app/common/constants.py:1592
#, python-format
msgid "恭喜你在CoinEx的「 1.5亿随机送 回馈老用户 」活动中获得%(amount)s枚CET，请在钱包中查看。"
msgstr ""

#: app/common/constants.py:1594
#, python-format
msgid "你在CoinEx的活动中获得%(amount)s个%(coin_type)s，请在钱包中查看。"
msgstr ""

#: app/common/constants.py:1597
#, python-format
msgid "你的申购编号（%(lottery_tickets)s）成功中签，%(amount)s %(coin_type)s 已到账。"
msgstr ""

#: app/common/constants.py:1599
#, python-format
msgid "你的 %(market)s 杠杆交易风险率已低于 %(risk_limit)s，请注意仓位控制以免发生强平。"
msgstr ""

#: app/common/constants.py:1601
#, python-format
msgid "你的 %(market)s 期货交易风险率已低于 %(risk_limit)s，请注意仓位控制以免发生强平。"
msgstr ""

#: app/common/constants.py:1604
#, python-format
msgid "因行情剧烈波动，你的%(market)s 杠杆交易，触发强平"
msgstr ""

#: app/common/constants.py:1605
#, python-format
msgid "因行情剧烈波动，你的 %(market)s 期货交易，触发强平"
msgstr ""

#: app/common/constants.py:1606
#, python-format
msgid "恭喜你获得CoinEx送出的 %(amount)s BCH杠杆体验新手奖励，可到币币钱包中查收。"
msgstr ""

#: app/common/constants.py:1608
#, python-format
msgid ""
"你在CoinEx的账户(%(user_name)s)中的保证金余额低于所需维持保证金， "
"你的%(market)s合约持仓已被强制平仓，触发强平价格为%(liq_price)s。"
msgstr ""

#: app/common/constants.py:1611
#, python-format
msgid ""
"你的%(market_type)s%(market)s持仓已被强制平仓。\n"
"强制平仓均价：%(liq_price)s\n"
"强制平仓原因：保证金余额低于所需维持保证金\n"
"\n"
"请注意，如果你设置了止盈止损平仓，可能会由于行情剧烈波动而触发失败。\n"
"风险提示：合约交易具有高风险性，请你密切关注市场动向。CoinEx对加密货币价格波动可能造成的任何损失不承担任何责任。"
msgstr ""

#: app/common/constants.py:1616
#, python-format
msgid "你在CoinEx的账户(%(user_name)s)中的%(market)s合约的爆仓风险率已达到%(risk)s%%，为避免强制平仓，请你立即补充保证金或降低你的持仓。"
msgstr ""

#: app/common/constants.py:1618
#, python-format
msgid ""
"你在CoinEx的账户(%(user_name)s)触发自动减仓， "
"你的%(market)s合约持仓以%(liq_price)s价格平仓，平仓数量为%(liq_amount)s。"
msgstr ""

#: app/common/constants.py:1620
#, python-format
msgid ""
"你的%(market_type)s%(market)s持仓已触发自动减仓流程。\n"
"减仓价格：%(liq_price)s\n"
"减仓数量：%(liq_amount)s\n"
"触发原因：对手盘发生穿仓，且保险基金余额不足\n"
"\n"
"风险提示：合约交易具有高风险性，请你密切关注市场动向。CoinEx对加密货币价格波动可能造成的任何损失不承担任何责任。"
msgstr ""

#: app/common/constants.py:1626
#, python-format
msgid ""
"很遗憾地通知你，由于你在CoinEx的账户(%(user_name)s)中的保证金余额低于所需维持保证金， "
"你的%(market)s合约持仓触发了降档减仓，减仓数量为%(amount)s。"
msgstr ""

#: app/common/constants.py:1630
#, python-format
msgid ""
"你的%(market_type)s%(market)s持仓已触发降档减仓。\n"
"触发时的标记价格：%(sign_price)s\n"
"降档减仓数量：%(amount)s\n"
"触发原因：保证金余额低于所需维持保证金\n"
"\n"
"风险提示：合约交易具有高风险性，请你密切关注市场动向。CoinEx对加密货币价格波动可能造成的任何损失不承担任何责任。"
msgstr ""

#: app/common/constants.py:1637
#, python-format
msgid "你的%(coin_type)s借币订单将于%(expired_time)s到期，请及时归还借币或开启自动续期以免发生强制还币。"
msgstr ""

#: app/common/constants.py:1640
#, python-format
msgid "你的%(coin_type)s借币订单已在%(expired_time)s到期，触发强制还币。"
msgstr ""

#: app/common/constants.py:1642
#, python-format
msgid ""
"你的%(market)s杠杆账户已触发强制还币。\n"
"强制还币金额：%(amount)s\n"
"强制还币原因：风险率已达%(risk_rate)s"
msgstr ""

#: app/common/constants.py:1645
#, python-format
msgid ""
"你的%(market)s杠杆账户已触发强制还币。\n"
"强制还币金额：%(amount)s\n"
"强制还币原因：借币池余额不足"
msgstr ""

#: app/common/constants.py:1648
#, python-format
msgid ""
"你的%(market)s杠杆账户已触发强制还币。\n"
"强制还币金额：%(amount)s\n"
"强制还币原因：到期未续借或续借失败"
msgstr ""

#: app/common/constants.py:1651
#, python-format
msgid "你的%(coin_type)s借币订单由于续借失败，请于%(expired_time)s前归还借币以免发生强制还币。"
msgstr ""

#: app/common/constants.py:1654
msgid ""
"CoinEx Chain启动节点预选计划，用CET投票，支持你心仪的节点，获得抽取大奖的机会。BTC数字货币大礼包，最新高配Iphone "
"11，CoinEx专属周边任你拿。机不可失，动动手指头就有机会抱走千万壕礼，快来参与吧！活动详情（www.coinex.com/activity"
"/node-vote）"
msgstr ""

#: app/common/constants.py:1660
#, python-format
msgid "恭喜你于%(time)s抢到红包，到账 %(amount)s %(coin_type)s。"
msgstr ""

#: app/common/constants.py:1661
#, python-format
msgid ""
"恭喜，手气不错，你已成功领取一个C-Box。\n"
"领取时间：%(time)s\n"
"到账金额：%(amount)s %(coin_type)s。"
msgstr ""

#: app/common/constants.py:1663
#, python-format
msgid "你发的红包48小时未领取完，已退还%(amount)s %(coin_type)s。"
msgstr ""

#: app/common/constants.py:1665
#, python-format
msgid ""
"你发出的红包已退还到账 %(amount)s %(coin_type)s。\n"
"红包发出时间：%(time)s\n"
"红包退还原因：发出的红包未在48小时内被领取完"
msgstr ""

#: app/common/constants.py:1668
#, python-format
msgid ""
"你发出的C-Box，数字货币已原路退回。\n"
"发出时间：%(time)s\n"
"退还到账：%(amount)s %(coin_type)s。\n"
"退还原因：发出的C-Box未在48小时内被领取完"
msgstr ""

#: app/common/constants.py:1672
#, python-format
msgid ""
"你发出的C-Box，数字货币已原路退回。\n"
"发出时间：%(time)s\n"
"退还到账：%(amount)s %(coin_type)s。\n"
"退还原因：发出的C-Box未在%(valid_days)s天内被领取完"
msgstr ""

#: app/common/constants.py:1676
#, python-format
msgid ""
"你发出的C-Box，数字货币已原路退回。\n"
"发出时间：%(time)s\n"
"退还到账：%(amount)s %(coin_type)s。\n"
"退还原因：领取C-Box的用户未在领取7天内注册"
msgstr ""

#: app/common/constants.py:1681
msgid "“买入AYA，瓜分5000USDT大奖”活动正在火热进行中，CoinEx邀请你参加此次活动。活动期间内，净买入（总买入量-总卖出量）数量大于100枚AYA即可参与排名，即有机会领取最高250USDT的现金大奖，高达300份奖励等你来领。了解更多：https://announcement.coinex.com/hc/articles/360051182091"
msgstr ""

#: app/common/constants.py:1687
msgid "恭喜你获得CoinEx送出的5 USDT OKEx用户专享福利奖励，可到现货账户中查看。"
msgstr ""

#: app/common/constants.py:1689
msgid "恭喜你获得CoinEx送出的200 CET OKEx用户专享福利奖励（额外返佣），可到现货账户中查看。"
msgstr ""

#: app/common/constants.py:1692
msgid "恭喜你获得CoinEx送出的5 USDT AYA活动奖励，可到现货账户中查看。"
msgstr ""

#: app/common/constants.py:1695
msgid "恭喜你获得CoinEx送出的100 CET AYA活动奖励（额外返佣），可到现货账户中查看。"
msgstr ""

#: app/common/constants.py:1697
msgid ""
"CoinEx新人礼，注册瓜分10000 "
"USDT”活动正在火热进行中，CoinEx邀请你参加此次活动。11月6日至11月26日（UTC+8）期间，CoinEx新注册用户只要完成规定的任务，将立即获得5"
" "
"USDT的奖励，先到先得，送完为止。同时平台所有用户都可以邀请新用户来参与本次活动，除了获取邀请返佣之外，每邀请一名成功完成任务并获得奖励的新用户，将额外获得CET奖励。更多活动详情，请前往公告查看。"
msgstr ""

#: app/common/constants.py:1706
msgid ""
"“CoinEx上线Conflux（CFX），充值瓜分5000 "
"USDT”活动正在火热进行中，CoinEx邀请你参加此次活动。CoinEx将于2020年11月9日（UTC+8）正式开放CFX充值，并于11月10日（UTC+8）支持CFX/BTC、CFX/USDT交易对。为了庆祝CFX上线，CoinEx将开启充值交易瓜分活动。更多活动详情，请前往公告查看。"
msgstr ""

#: app/common/constants.py:1713
#, python-format
msgid "恭喜你获得CoinEx送出的%(amount)s %(asset)s 合约体验奖励，可到现货账户中查看"
msgstr ""

#: app/common/constants.py:1716
#, python-format
msgid "你的账号%(account)s在%(market)s合约的止盈设置已被触发，且平仓成功。请知晓。"
msgstr ""

#: app/common/constants.py:1719
#, python-format
msgid "你的子账号%(account)s在%(market)s合约的止盈设置已被触发，且平仓成功。请知晓。"
msgstr ""

#: app/common/constants.py:1722
#, python-format
msgid "你的账号%(account)s在%(market)s合约的止损设置已被触发，且平仓成功。请知晓。"
msgstr ""

#: app/common/constants.py:1725
#, python-format
msgid "你的子账号%(account)s在%(market)s合约的止损设置已被触发，且平仓成功。请知晓。"
msgstr ""

#: app/common/constants.py:1728
#, python-format
msgid ""
"你的账号%(account)s在合约%(market)s中的止盈设置未能全部平仓，剩余仓位%(amount)s "
"%(asset)s，且原止盈设置已失效。请知晓。"
msgstr ""

#: app/common/constants.py:1732
#, python-format
msgid ""
"你的子账号%(account)s在%(market)s合约中的止盈设置未能全部平仓，剩余仓位%(amount)s "
"%(asset)s，且原止盈设置已失效。请知晓。"
msgstr ""

#: app/common/constants.py:1736
#, python-format
msgid ""
"你的账号%(account)s在合约%(market)s止损设置未能全部平仓，剩余仓位%(amount)s "
"%(asset)s，且原止损设置已失效。请知晓"
msgstr ""

#: app/common/constants.py:1740
#, python-format
msgid ""
"你的子账号%(account)s在%(market)s合约中的止损设置未能全部平仓，剩余仓位%(amount)s "
"%(asset)s，且原止损设置已失效。请知晓。"
msgstr ""

#: app/common/constants.py:1745
#, python-format
msgid "你的账号%(account)s在合约%(market)s中的一键全平操作未能全部平仓，剩余仓位%(amount)s %(asset)s。请知晓。"
msgstr ""

#: app/common/constants.py:1749
#, python-format
msgid "你的子账号%(account)s在%(market)s合约中的一键全平操作未能全部平仓，剩余仓位%(amount)s %(asset)s。请知晓。"
msgstr ""

#: app/common/constants.py:1753
#, python-format
msgid "你的账号%(account)s在合约%(market)s中的一键平仓操作未能全部平仓，剩余仓位%(amount)s %(asset)s。请知晓。"
msgstr ""

#: app/common/constants.py:1757
#, python-format
msgid "你的子账号%(account)s在%(market)s合约中的一键平仓操作未能全部平仓，剩余仓位%(amount)s %(asset)s。请知晓。"
msgstr ""

#: app/common/constants.py:1760
msgid "CoinEx合约交易活动火热进行中，首次体验合约交易将获得CET奖励，参与交易比赛，更有机会瓜分20000USDT的大奖！更多活动详情，请前往官网活动主页查看。"
msgstr ""

#: app/common/constants.py:1764
#, python-format
msgid "恭喜你获得CoinEx送出的%(amount)s %(asset)s 合约交易大赛奖励，可到现货账户中查看。"
msgstr ""

#: app/common/constants.py:1767
#, python-format
msgid "恭喜你获得ViaBTC & CoinEx送出的%(amount)s %(asset)s 2021矿年狂欢奖励，可到现货账户中查看。"
msgstr ""

#: app/common/constants.py:1770 app/common/constants.py:1771
#, python-format
msgid "恭喜你收到 %(amount)s %(asset)s %(activity_name)s奖励。"
msgstr ""

#: app/common/constants.py:1773
#, python-format
msgid "恭喜你，在CoinEx Mart幸运盲盒活动中瓜分到%(amount)s USDT奖励，奖励已发放，可到现货账户中查看"
msgstr ""

#: app/common/constants.py:1777
#, python-format
msgid "恭喜你，在CoinEx Mart幸运盲盒活动中收集的套娃最多，获得%(amount)s CET奖励，奖励已发放，可到现货账户中查看"
msgstr ""

#: app/common/constants.py:1782
#, python-format
msgid "你在CoinEx安全项重置审核不通过，原因是%(reason)s."
msgstr ""

#: app/common/constants.py:1786
msgid "你在CoinEx的绑定邮箱重置成功。完成重置后只能通过新邮箱登录。为了保障你的账号安全，24小时内禁止提现。"
msgstr ""

#: app/common/constants.py:1790
msgid ""
"你已成功重置提现密码。 为了你的账户安全，24小时内将禁止提现。\n"
"\n"
"若非你本人操作，请立即重置密码或禁用账户，并尽快向CoinEx客服提交工单。"
msgstr ""

#: app/common/constants.py:1795
#, python-format
msgid ""
"你在CoinEx的提现密码重置审核不通过。\n"
"\n"
"原因是%(reason)s，请按照示例重新拍照上传。"
msgstr ""

#: app/common/constants.py:1800
#, python-format
msgid ""
"你已成功解绑%(reset_type)s验证。请及时重新绑定%(reset_type)s，以免造成损失。\n"
"\n"
"为了保障你的账号安全，24小时内禁止提现。\n"
"\n"
"如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
msgstr ""

#: app/common/constants.py:1806
#, python-format
msgid "恭喜你，在CoinEx的“%(asset)s空投”活动中，获得%(amount)s%(asset)s的空投奖励。奖励已发放，将于%(lock_day)s天后解冻。"
msgstr ""

#: app/common/constants.py:1809
#, python-format
msgid "恭喜你，在CoinEx的“%(title)s空投”活动中，获得%(amount)s%(asset)s的空投奖励。奖励已发放，%(unlocked_at)s后支持交易/提现。"
msgstr ""

#: app/common/constants.py:1812
#: app/templates/email/notice/airdrop_rewords_success.j2:4
#, python-format
msgid "恭喜您，在CoinEx的“%(title)s空投”活动中，获得%(rewards)s的空投奖励。"
msgstr ""

#: app/common/constants.py:1815
#: app/templates/email/notice/airdrop_rewords_fail.j2:4
#, python-format
msgid "抱歉，很遗憾，在CoinEx的“%(title)s空投”活动中，您的抽奖签号未中奖。"
msgstr ""

#: app/common/constants.py:1818
#, python-format
msgid ""
"抱歉，您在CoinEx的“%(title)s空投”活动中，%(rewards)s领取失败。\n"
"原因：当前账号已有使用中的合约体验金或交易赠金券，无法领取更多同类型卡券。"
msgstr ""

#: app/common/constants.py:1823
#, python-format
msgid "“%(title)s”活动中，获得%(amount)s%(asset)s，申购已发放。"
msgstr ""

#: app/common/constants.py:1826
#, python-format
msgid "抱歉，很遗憾，“%(title)s”活动中，未中奖，期待下次参与。"
msgstr ""

#: app/common/constants.py:1829
msgid ""
"恭喜！您在稀有聪（Sat "
"1,968,750,000,000,000）的拍卖中，赢得竞拍，请通过当前账户的邮箱联系（<EMAIL>），并提供验证码：CoinExRareSatWinner"
" \n"
"\n"
" 我们将有专员为您处理该稀有聪的提取事宜。"
msgstr ""

#: app/common/constants.py:1834
#, python-format
msgid ""
"你的%(amount)s %(asset)s充值未到账找回申请已受理，还需补充相关资料，请尽快补充。\n"
"申请时间：%(time)s"
msgstr ""

#: app/common/constants.py:1838
#, python-format
msgid ""
"你的%(amount)s %(asset)s充值未到账找回申请已受理，请确保你的现货账户资产有%(fee_amount)s "
"%(fee_asset)s作为手续费，找回资产时平台将自动扣除手续费。\n"
"\n"
"当前你的余额不足，请尽快补充，否则申请将被驳回。"
msgstr ""

#: app/common/constants.py:1843
#, python-format
msgid ""
"你的%(amount)s %(asset)s充值未到账找回申请审核失败。\n"
"申请时间：%(time)s\n"
"失败原因：%(rejection_reason)s"
msgstr ""

#: app/common/constants.py:1848
#, python-format
msgid ""
"你的%(amount)s %(asset)s充值未到账找回成功，已入账至你的现货账户。\n"
"申请时间：%(time)s"
msgstr ""

#: app/common/constants.py:1852
#, python-format
msgid ""
"你的%(amount)s %(asset)s充值未到账找回成功，已入账至你的现货账户。\n"
"申请时间：%(time)s\n"
"由于需要额外的人力和技术投入以帮您找回资产，我们已从您的充值资产里收取一定的手续费，手续费金额为：%(fee_amount)s "
"%(fee_asset)s。手续费收取规则可点击下列按钮查看。"
msgstr ""

#: app/common/constants.py:1858
#, python-format
msgid ""
"你的%(amount)s %(asset)s充值未到账找回成功，已退回至你提供的地址。\n"
"申请时间：%(time)s\n"
"退回的交易ID：%(refund_tx_id)s"
msgstr ""

#: app/common/constants.py:1863
#, python-format
msgid ""
"你的%(amount)s %(asset)s充值未到账找回成功，已退回至你提供的地址。\n"
"申请时间：%(time)s\n"
"退回的交易ID：%(refund_tx_id)s\n"
"由于需要额外的人力和技术投入以帮您找回资产，我们已从您的充值资产里收取一定的手续费，手续费金额为：%(fee_amount)s "
"%(fee_asset)s。手续费收取规则可点击下列按钮查看。"
msgstr ""

#: app/common/constants.py:1870
#, python-format
msgid ""
"恭喜你在CoinEx "
"Dock成功登船！你在%(project_name)s（%(asset)s）项目的申购中，中签%(count)s份包含%(amount)s "
"%(asset)s，代币已发放。将于%(unlocked_at)s 解冻。"
msgstr ""

#: app/common/constants.py:1875
#, python-format
msgid ""
"恭喜你在CoinEx "
"Dock成功登船！你在%(project_name)s（%(asset)s）项目的申购中，中签%(count)s份包含%(amount)s "
"%(asset)s，代币已发放。将于%(unlocked_at)s 解冻。该项目代币有特定释放规则详情见申购详情；"
msgstr ""

#: app/common/constants.py:1880
#, python-format
msgid "很遗憾，你在%(project_name)s（%(asset)s）项目的申购中未中签，期待您的下次参与。"
msgstr ""

#: app/common/constants.py:1883
#, python-format
msgid ""
"你的授信账户当前风险率已低于可提现风险率，系统已关闭提现功能, 请尽快补充非授信资产。\n"
"\n"
"当前风险率：%(warn_rate)s\n"
"可提现风险率：%(withdraw_rate)s\n"
"\n"
"如你的当前风险率大于可提现风险率时，提现功能将自动开启。"
msgstr ""

#: app/common/constants.py:1889
#, python-format
msgid ""
"你的授信账户当前风险率已低于可交易风险率，系统已关闭现货交易功能，杠杆及合约交易仍可正常使用，请尽快补充非授信资产。\n"
"\n"
"当前风险率：%(warn_rate)s\n"
"可提现风险率：%(withdraw_rate)s\n"
"可交易风险率：%(trade_rate)s\n"
"\n"
"如你的当前风险率大于可交易风险率时，现货交易功能将自动开启。"
msgstr ""

#: app/common/constants.py:1896
msgid "你已成功开通杠杆交易，请注意风险。为你奉上杠杆交易操作手册，祝你交易愉快。"
msgstr ""

#: app/common/constants.py:1897
msgid "你已成功开通合约交易，请注意风险。为你奉上合约交易操作手册，祝你交易愉快。"
msgstr ""

#: app/common/constants.py:1898
#, python-format
msgid "恭喜你获得一张 %(amount)s %(coupon_type)s，领取后即可使用，先到先得。"
msgstr ""

#: app/common/constants.py:1900
#, python-format
msgid ""
"恭喜你成功领取%(value)s %(value_type)s合约体验金，请尽快使用。如在使用有效期内正向合约交易额达标，则体验金到期后不回收。"
" \n"
"\n"
"使用截止日期：%(expired_at)s \n"
"正向合约交易达标额：%(qualified_trade_amount)s %(value_type)s"
msgstr ""

#: app/common/constants.py:1905
#, python-format
msgid ""
"恭喜获得一张%(value)s %(value_type)s交易赠金券！\n"
"\n"
"有效期内，%(trade_type)s交易额累计达到%(qualified_trade_amount)s "
"%(value_type)s即可使用。快去交易吧～ \n"
"使用截止日期：%(expired_at)s"
msgstr ""

#: app/common/constants.py:1910
#, python-format
msgid ""
"恭喜你成功领取%(value)s%(value_type)s理财加息券一张。激活并划转资产到指定理财账户，即可享受额外加息收益。\n"
"激活截止日期：%(expired_at)s"
msgstr ""

#: app/common/constants.py:1914
#, python-format
msgid ""
"恭喜你成功领取%(value)s %(value_type)s手续费返现券-%(trade_type)s。\n"
"即刻去交易，符合条件的交易订单手续费将于次日返现。"
msgstr ""

#: app/common/constants.py:1918
#, python-format
msgid "恭喜你成功领取一张%(value)s %(value_type)s合约补贴金，使用有效期为：%(usable_days)s天。"
msgstr ""

#: app/common/constants.py:1921
#, python-format
msgid ""
"恭喜你成功兑换%(value)s %(value_type)s合约体验金，请尽快使用。如在使用有效期内正向合约交易额达标，则体验金到期后不回收。"
" \n"
"\n"
"使用截止日期：%(expired_at)s \n"
"正向合约交易达标额：%(qualified_trade_amount)s %(value_type)s"
msgstr ""

#: app/common/constants.py:1926
#, python-format
msgid ""
"恭喜你成功兑换%(value)s%(value_type)s理财加息券一张。激活并划转资产到指定理财账户，即可享受额外加息收益。\n"
"激活截止日期：%(expired_at)s"
msgstr ""

#: app/common/constants.py:1930
#, python-format
msgid ""
"恭喜你成功兑换%(value)s %(value_type)s手续费返现券-%(trade_type)s。\n"
"即刻去交易，符合条件的交易订单手续费将于次日返现。"
msgstr ""

#: app/common/constants.py:1934
#, python-format
msgid ""
"你的理财加息券已使用完毕。\n"
"本次累计加息收益：%(value)s %(value_type)s"
msgstr ""

#: app/common/constants.py:1938
#, python-format
msgid ""
"你的手续费返现券已使用完毕。\n"
"本次共为你返现 %(value)s %(value_type)s"
msgstr ""

#: app/common/constants.py:1942
#, python-format
msgid ""
"恭喜成功兑换一张%(value)s%(value_type)s交易赠金券！\n"
"\n"
"有效期内，%(trade_type)s交易额累计达到%(qualified_trade_amount)s "
"%(value_type)s即可使用。快去交易吧～\n"
"使用截止日期：%(expired_at)s"
msgstr ""

#: app/common/constants.py:1947
#, python-format
msgid "恭喜你成功兑换一张%(value)s%(value_type)s合约补贴金，使用有效期为：%(usable_days)s天。"
msgstr ""

#: app/common/constants.py:1950
#, python-format
msgid ""
"恭喜你，合约交易额已达标。\n"
"\n"
"%(value)s %(value_type)s 合约体验金已满足赠送标准，过期后不收回，你可前往CoinEx合约交易继续使用。"
msgstr ""

#: app/common/constants.py:1954
#, python-format
msgid "恭喜你成功激活一张%(value)s %(value_type)s 交易赠金券。现金已发放至你的现货账户，快去查看吧～"
msgstr ""

#: app/common/constants.py:1961
msgid ""
"合约跟单体验金券领取成功！\n"
"卡券有效期内，你可以在卡券指定的适用范围内，在跟单或带单时使用卡券。"
msgstr ""

#: app/common/constants.py:1965
#, python-format
msgid ""
"恭喜兑换合约跟单体验金券！\n"
"卡券有效期内，你可以在卡券指定的适用范围内，在跟单或带单时使用卡券。\n"
"权益到期时间：%(expired_at)s。"
msgstr ""

#: app/common/constants.py:1975
#, python-format
msgid ""
"恭喜兑换VIP升级券，立刻体验更多特权。\n"
"\n"
"卡券有效期内，你的VIP等级将被提升%(value)s级，最高可提升到VIP5。\n"
"\n"
"权益到期时间：%(expired_at)s"
msgstr ""

#: app/common/constants.py:1981
#, python-format
msgid ""
"VIP升级券(+%(value)s级)领取成功！\n"
"\n"
"卡券有效期内，你的VIP等级将被提升%(value)s级，最高可提升到VIP5。\n"
"\n"
"了解更多VIP特权。"
msgstr ""

#: app/common/constants.py:1987
#, python-format
msgid ""
"恭喜，你的VIP等级已升级为%(new_level)s，相关权益调整如下：\n"
"\n"
"现货费率：%(spot_taker_fee_rate)s\n"
"现货费率 (开启CET抵扣)：%(spot_taker_discount_fee_rate)s\n"
"合约费率：Maker %(perpetual_maker_fee_rate)s，Taker "
"%(perpetual_taker_fee_rate)s\n"
"杠杆日息：以%(new_level)s等级日息为准（详情请点击下方“前往查看”）\n"
"返佣比例：%(referral_rate)s"
msgstr ""

#: app/common/constants.py:1995
#, python-format
msgid ""
"抱歉，你的VIP等级已降级为%(new_level)s，相关权益调整如下：\n"
"\n"
"现货费率：%(spot_taker_fee_rate)s\n"
"现货费率 (开启CET抵扣)：%(spot_taker_discount_fee_rate)s\n"
"合约费率：Maker %(perpetual_maker_fee_rate)s，Taker "
"%(perpetual_taker_fee_rate)s\n"
"杠杆日息：以%(new_level)s等级日息为准（详情请点击下方“前往查看”）\n"
"返佣比例：%(referral_rate)s"
msgstr ""

#: app/common/constants.py:2003
#, python-format
msgid ""
"系统检测到你的登录地点与上次不一致。\n"
"\n"
"时间：%(time)s\n"
"IP: %(ip)s\n"
"地点: %(location)s\n"
"\n"
"如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
msgstr ""

#: app/common/constants.py:2010
msgid "恭喜，你的实名认证已通过审核，系统已为你开启相关权益。"
msgstr ""

#: app/common/constants.py:2011
#, python-format
msgid ""
"抱歉，你的实名认证未通过审核。\n"
"原因：%(reject_reason)s\n"
"\n"
"你可以点击重新提交认证资料。"
msgstr ""

#: app/common/constants.py:2012 app/templates/email/notice/kyc_pro_pass.j2:4
msgid "恭喜，您的KYC高级认证已通过审核，系统已为您开启对应权益。"
msgstr ""

#: app/common/constants.py:2013
#, python-format
msgid ""
"抱歉，您的KYC高级认证未通过审核。\n"
"原因：%(reject_reason)s。\n"
"\n"
msgstr ""

#: app/common/constants.py:2014
msgid ""
"基于监管规定及要求，非常抱歉我们无法继续为你提供服务。\n"
"\n"
"我们将于7个工作日后对你的账户进行清退。届时你的账户将进入“仅提现模式”，即关闭充值、交易等功能，仅支持提现服务。请尽快处理正在进行中的交易和资产提现。"
"\n"
"\n"
"感谢你的理解与配合。"
msgstr ""

#: app/common/constants.py:2019
#, python-format
msgid ""
"你已成功重置邮箱，后续需使用新邮箱（%(email)s）登录。为了保障你的账号安全，24小时内禁止提现。\n"
"\n"
"如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
msgstr ""

#: app/common/constants.py:2023
msgid ""
"你已成功重置TOTP验证器。为了保障你的账号安全，24小时内禁止提现。\n"
"\n"
"如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
msgstr ""

#: app/common/constants.py:2027
msgid ""
"您的通行密钥解绑成功，为了保障您的账号安全，24小时内禁止提现。您的账号已失去通行密钥的保护，请及时重新绑定，以免造成损失。\n"
"\n"
"如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
msgstr ""

#: app/common/constants.py:2031
msgid ""
"你已成功重置登录密码。为了保障你的账号安全，24小时内禁止提现。\n"
"\n"
"如果不是你本人操作，请立即重设密码或禁用账户，并尽快提交工单联系CoinEx客服。"
msgstr ""

#: app/common/constants.py:2035
msgid "每月仅需邀请3个用户，即可成为CoinEx大使，享受最高50%永久返佣。点击“查看详情”立即申请！"
msgstr ""

#: app/common/constants.py:2036
msgid "成为CoinEx大使，每月仅需邀请3个用户。点击“查看详情”，立即享受最高50%永久返佣。"
msgstr ""

#: app/common/constants.py:2037
#: app/templates/email/notice/trade_rank_activity_gift.j2:4
#, python-format
msgid "恭喜你，在CoinEx的%(title)s活动中，获得%(amount)s %(asset)s的奖励，奖励已发放至现货账户。"
msgstr ""

#: app/common/constants.py:2039
#: app/templates/email/notice/tax_export_success.j2:5
msgid "你的账户数据已生成，请前往CoinEx资金流水页面进行下载，下载链接 7 天内有效，请注意文件隐私安全。"
msgstr ""

#: app/common/constants.py:2041
msgid ""
"恭喜你在CoinEx五周年感恩庆典的「大挑战」成功超越自我，获得终极盲盒的开箱机会！\n"
"\n"
"所有盲盒开箱时间：2023年1月1日 00:00（UTC+0），先到先得哦～\n"
"\n"
"注：终极盲盒和周年盲盒仅可选择一个盲盒开箱。"
msgstr ""

#: app/common/constants.py:2046
msgid ""
"恭喜你在CoinEx五周年感恩庆典的「大挑战」获得周年盲盒的开箱机会。\n"
"\n"
"继续挑战，完成所有任务，即可冲击终极盲盒，$2000只有一步之遥！\n"
"\n"
"所有盲盒开箱时间：2023年1月1日 00:00（UTC+0），先到先得哦～\n"
"\n"
"注：终极盲盒和周年盲盒仅可选择一个盲盒开箱。"
msgstr ""

#: app/common/constants.py:2052
#, python-format
msgid ""
"%(market)s市场已达设定的网格触发价，网格策略开始运行。\n"
"\n"
"网格触发价：%(trigger_price)s %(quote_asset)s\n"
"\n"
"策略ID：%(strategy_id)s"
msgstr ""

#: app/common/constants.py:2057
#, python-format
msgid ""
"%(market)s市场已达设定的网格止盈价，系统将自动平仓，及时止盈，为你锁定利润。\n"
"\n"
"网格止盈价：%(take_profit_price)s %(quote_asset)s\n"
"\n"
"策略ID：%(strategy_id)s"
msgstr ""

#: app/common/constants.py:2062
#, python-format
msgid ""
"%(market)s市场已达设定的网格止损价，系统将自动平仓，及时止损，降低单边行情损失。\n"
"\n"
"网格止损价：%(stop_loss_price)s %(quote_asset)s\n"
"\n"
"策略ID：%(strategy_id)s"
msgstr ""

#: app/common/constants.py:2067
#, python-format
msgid ""
"%(market)s市场的现货价格已超过网格策略的价格区间，你可手动终止策略或修改止盈止损价格。\n"
"\n"
"%(base_asset)s市价：%(last_price)s %(quote_asset)s\n"
"\n"
"网格价格区间：%(lowest_price)s - %(highest_price)s %(quote_asset)s\n"
"\n"
"策略ID：%(strategy_id)s"
msgstr ""

#: app/common/constants.py:2073
#, python-format
msgid ""
"%(market)s市场的现货网格设置已达到推荐的运行时间，你可手动终止或继续运行当前策略。\n"
"\n"
"策略ID：%(strategy_id)s\n"
"\n"
"时间区间：%(recommend_days)sD"
msgstr ""

#: app/common/constants.py:2078
#, python-format
msgid ""
"你在%(market_type)s %(market)s中的止盈设置已被触发，并且全部平仓成功。\n"
"\n"
"风险提示：合约交易具有高风险性，请你密切关注市场动向。CoinEx对加密货币价格波动可能造成的任何损失不承担任何责任。"
msgstr ""

#: app/common/constants.py:2081
#, python-format
msgid ""
"你在%(market_type)s "
"%(market)s中的止盈设置已被触发并且部分平仓，但由于行情波动剧烈、触发自动减仓等原因，导致未能完全平仓。\n"
"\n"
"剩余未平仓位：%(amount)s%(asset)s。\n"
"\n"
"请注意，止盈止损设置对剩余未平仓位已失效。\n"
"\n"
"风险警告：合约交易具有高风险性，请你密切关注市场动向。CoinEx对加密货币价格波动可能造成的任何损失不承担任何责任。"
msgstr ""

#: app/common/constants.py:2087
#, python-format
msgid ""
"你在%(market_type)s %(market)s中的止损设置已被触发，并且全部平仓成功。\n"
"\n"
"风险提示：合约交易具有高风险性，请你密切关注市场动向。CoinEx对加密货币价格波动可能造成的任何损失不承担任何责任。"
msgstr ""

#: app/common/constants.py:2090
#, python-format
msgid ""
"你在%(market_type)s "
"%(market)s中的止损设置已被触发并且部分平仓，但由于行情波动剧烈、触发自动减仓等原因，导致未能完全平仓。\n"
"\n"
"剩余未平仓位：%(amount)s%(asset)s。\n"
"\n"
"请注意，止盈止损设置对剩余未平仓位已失效。\n"
"\n"
"风险警告：合约交易具有高风险性，请你密切关注市场动向。CoinEx对加密货币价格波动可能造成的任何损失不承担任何责任。"
msgstr ""

#: app/common/constants.py:2096
#, python-format
msgid ""
"你已与子账号 %(sub_user_name)s 建立授权关系，可访问并管理该子账号，如需解除授权，请联系授权人操作解除。\n"
"\n"
"授权人：%(main_user_email)s\n"
"被授权人：%(manager_email)s （你）\n"
"授权子账号： %(sub_user_name)s\n"
"账号权限：%(permissions)s\n"
msgstr ""

#: app/common/constants.py:2103
#, python-format
msgid ""
"你已与子账号 %(sub_user_name)s 解除授权关系，将无法访问和管理该子账号。\n"
"\n"
"授权人：%(main_user_email)s\n"
"被授权人：%(manager_email)s （你）\n"
"授权子账号： %(sub_user_name)s\n"
"账号权限：%(permissions)s\n"
msgstr ""

#: app/common/constants.py:2110
#, python-format
msgid ""
"你的%(target_asset)s自动定投计划，本次定投已全部成交。\n"
"定投时间：%(time)s\n"
"定投金额：%(source_asset_traded_amount)s %(source_asset)s\n"
"买入数量：%(target_asset_traded_amount)s %(target_asset)s\n"
msgstr ""

#: app/common/constants.py:2116
#, python-format
msgid ""
"因市场深度原因，你的%(target_asset)s自动定投计划，本次定投部分成交。\n"
"定投时间：%(time)s\n"
"计划定投金额：%(source_asset_amount)s %(source_asset)s\n"
"实际成交金额：%(source_asset_traded_amount)s %(source_asset)s\n"
"买入数量：%(target_asset_traded_amount)s %(target_asset)s\n"
msgstr ""

#: app/common/constants.py:2123
#, python-format
msgid ""
"因市场深度原因，你的%(target_asset)s自动定投计划，本次定投未成交。\n"
"定投时间：%(time)s\n"
"计划定投金额：%(source_asset_amount)s %(source_asset)s\n"
"实际成交金额：%(source_asset_traded_amount)s %(source_asset)s\n"
"买入数量：%(target_asset_traded_amount)s %(target_asset)s\n"
msgstr ""

#: app/common/constants.py:2130
#, python-format
msgid ""
"你的%(target_asset)s自动定投计划已到达设置的盈利目标。\n"
"收益额：%(profit_amount)s %(source_asset)s"
msgstr ""

#: app/common/constants.py:2134
#, python-format
msgid ""
"你的%(target_asset)s自动定投计划已到达设置的盈利目标。\n"
"收益率：%(profit_rate)s%%"
msgstr ""

#: app/common/constants.py:2138
#: app/templates/email/notice/auto_invest_failed.j2:4
#, python-format
msgid "由于现货账户余额不足，你的%(target_asset)s自动定投计划，本次定投失败，请充值资产后再继续定投。"
msgstr ""

#: app/common/constants.py:2139
#: app/templates/email/notice/auto_invest_paused.j2:4
#, python-format
msgid "由于连续5次现货账户余额不足导致定投失败，你的%(target_asset)s自动定投计划已暂停，可充值资产后再手动开启定投。"
msgstr ""

#: app/common/constants.py:2141
#, python-format
msgid ""
"你的%(target_asset)s自动定投计划已到达设置的定投总额，定投计划已自动关闭。\n"
"定投总额：%(total_source_amount)s %(source_asset)s"
msgstr ""

#: app/common/constants.py:2145
#, python-format
msgid "你在CoinEx的%(activity)s活动中，获得了%(reward)s的奖励，奖励已到账"
msgstr ""

#: app/common/constants.py:2146
#, python-format
msgid "前往%(email)s邮箱确认你的提现信息。提现申请24小时内未确认将自动取消。"
msgstr ""

#: app/common/constants.py:2147
#: app/templates/email/notice/pledge_liq_warning.j2:6
#, python-format
msgid "您的%(loan_asset)s借贷持仓当前质押率已达到 %(ltv)s%%，为避免强制平仓，请及时补充质押资产或主动还币。"
msgstr ""

#: app/common/constants.py:2150 app/templates/email/notice/pledge_liq.j2:6
#, python-format
msgid "您的%(loan_asset)s借贷持仓当前质押率已达到 %(ltv)s%%，超过强平质押率 %(liq_ltv)s%%，您的仓位已被强制平仓。"
msgstr ""

#: app/common/constants.py:2154
#, python-format
msgid "由于市场波动和您设置的参数间隔过小，合约市场%(market)s的开仓止损未生效，请及时关注。"
msgstr ""

#: app/common/constants.py:2156
#, python-format
msgid "由于市场波动和您设置的参数间隔过小，合约市场%(market)s的开仓止盈未生效，请及时关注。"
msgstr ""

#: app/common/constants.py:2160
#, python-format
msgid ""
"您发布的广告已有用户下单，请前往确认订单。\n"
"订单编号：%(order_id)s。\n"
"下单方向：%(side)s。\n"
"数字货币：%(base_amount)s %(base)s；法币：%(quote_amount)s%(quote)s。\n"
msgstr ""

#: app/common/constants.py:2166
#, python-format
msgid ""
"您发起的P2P订单商家拒绝接单。\n"
"订单编号：%(order_id)s。\n"
msgstr ""

#: app/common/constants.py:2170
#, python-format
msgid ""
"您的P2P买币订单商家已确认接单，请查看订单并尽快完成付款。\n"
"订单编号：%(order_id)s\n"
"您需支付：%(quote_amount)s %(quote)s\n"
"请使用本人实名认证的支付方式，确认完成付款后再点击“我已付款”按钮。\n"
msgstr ""

#: app/common/constants.py:2176
#, python-format
msgid ""
"您发起的卖币订单商家已确认。\n"
"订单编号：%(order_id)s。\n"
"您将收到：%(quote_amount)s %(quote)s。\n"
"收到后需放币：%(from_amount)s %(base)s。\n"
msgstr ""

#: app/common/constants.py:2182
#, python-format
msgid ""
"您的P2P买币订单将在5分钟后超时，超时后将自动取消订单，请查看订单并尽快完成付款。\n"
"订单编号：%(order_id)s\n"
"您需支付：%(quote_amount)s %(quote)s\n"
"请使用本人实名认证的支付方式，确认完成付款后再点击“我已付款”按钮。\n"
msgstr ""

#: app/common/constants.py:2188
#, python-format
msgid ""
"您的P2P卖币订单商家已点击完成付款，请尽快前往收款账号核实款项。\n"
"收款时，请确保资金来自商家实名认证的支付渠道，金额正确，再点击“确认放币”按钮。\n"
"如需帮助，请联系客服。\n"
"订单编号：%(order_id)s\n"
"您将收到：%(quote_amount)s %(quote)s。\n"
msgstr ""

#: app/common/constants.py:2195
#, python-format
msgid ""
"您的P2P订单卖家已放币，订单已完成。\n"
"订单编号：%(order_id)s。\n"
"您已收到：%(to_amount)s%(base)s。\n"
"数字货币已经划转到您的现货账户。\n"
msgstr ""

#: app/common/constants.py:2201
#, python-format
msgid ""
"您的P2P订单已取消，原因是：%(cancel_reason)s \n"
"订单编号：%(order_id)s。"
msgstr ""

#: app/common/constants.py:2205
#, python-format
msgid ""
"您的P2P订单已被买家取消，原因是：%(cancel_reason)s \n"
"订单编号：%(order_id)s。"
msgstr ""

#: app/common/constants.py:2209
#, python-format
msgid ""
"您的P2P订单%(user_type)s已发起申诉，原因是：%(reason)s \n"
"订单编号：%(order_id)s。"
msgstr ""

#: app/common/constants.py:2213
#, python-format
msgid ""
"您的P2P订单申诉有新的通知。\n"
"订单编号：%(order_id)s。"
msgstr ""

#: app/common/constants.py:2217
#, python-format
msgid ""
"您的P2P订单申诉客服已处理，数字货币已经释放给卖家。\n"
"订单编号：%(order_id)s。\n"
"释放数字货币：%(to_amount)s%(base)s。"
msgstr ""

#: app/common/constants.py:2222
#, python-format
msgid ""
"您的P2P订单客服已处理，冻结的数字货币已经释放，请前往现货账户查看记录。\n"
"订单编号：%(order_id)s。\n"
"释放数字货币：%(from_amount)s%(base)s。详情请点击查看。"
msgstr ""

#: app/common/constants.py:2228
#, python-format
msgid ""
"您的P2P订单申诉客服已处理，数字货币已经划转到您的现货账户。\n"
"订单编号：%(order_id)s。\n"
"您已收到：%(to_amount)s%(base)s。"
msgstr ""

#: app/common/constants.py:2233
#, python-format
msgid ""
"您的P2P订单申诉客服已处理，您的数字货币已经划转给买家。\n"
"订单编号：%(order_id)s。\n"
"划转数字货币：%(from_amount)s%(base)s。"
msgstr ""

#: app/common/constants.py:2238
#, python-format
msgid ""
"您的P2P订单申诉已被%(user_type)s取消申诉。\n"
"订单编号：%(order_id)s。"
msgstr ""

#: app/common/constants.py:2242
#, python-format
msgid ""
"您的P2P订单申诉已完成。\n"
"订单编号：%(order_id)s。"
msgstr ""

#: app/common/constants.py:2246
#, python-format
msgid ""
"您的P2P订单申诉被重新开启。\n"
"订单编号：%(order_id)s。"
msgstr ""

#: app/common/constants.py:2250
#, python-format
msgid "你已成功增加P2P收款方式：%(pay_channel)s"
msgstr ""

#: app/common/constants.py:2253
#, python-format
msgid ""
"您的P2P广告单被自动下架。\n"
"原因是：%(reason)s。\n"
"广告单编号：%(adv_number)s。"
msgstr ""

#: app/common/constants.py:2258
#: app/templates/email/notice/p2p/payment_channel_invalid.j2:4
#, python-format
msgid "你设置的支付渠道【%(pay_channel)s】已失效，请重新设置。"
msgstr ""

#: app/common/constants.py:2262
#, python-format
msgid "您已成功提交 %(act_name)s 报名申请，CoinEx将通过邮件及站内信通知审核结果，请耐心等待。"
msgstr ""

#: app/common/constants.py:2265
#, python-format
msgid "恭喜您已通过 %(act_name)s 报名审核，请前往商家后台发布广告单，赢取活动奖励。"
msgstr ""

#: app/common/constants.py:2268
#, python-format
msgid ""
"很遗憾地通知，你未能通过 %(act_name)s 的报名审核。\n"
"感谢报名，如对审核结果有疑问，请联系CoinEx客服。"
msgstr ""

#: app/common/constants.py:2272
#, python-format
msgid ""
"很遗憾地通知，你在 %(act_name)s 的参与资格已被取消。\n"
"感谢参与，如对审核结果有疑问，请联系CoinEx客服。"
msgstr ""

#: app/common/constants.py:2276
#, python-format
msgid ""
"恭喜你获得P2P商家活动的奖励瓜分资格。\n"
"获奖日期：%(reward_date)s\n"
"活动名称：%(act_name)s\n"
"活动奖金已发放至现货账户，请前往活动主页查看详情。"
msgstr ""

#: app/common/constants.py:2282
#, python-format
msgid ""
"很遗憾地通知，你在 %(act_name)s 的活动奖励已被冻结。\n"
"请立即联系CoinEx客服处理异常。"
msgstr ""

#: app/common/constants.py:2286
#, python-format
msgid ""
"很遗憾地通知，你在 %(act_name)s 的活动奖励已被撤销。\n"
"感谢参与，如对处理结果有疑问，请联系CoinEx客服。"
msgstr ""

#: app/common/constants.py:2290
#, python-format
msgid "由于你的账户异常， %(act_name)s 奖励发放失败，请立即联系客服处理。"
msgstr ""

#: app/common/constants.py:2293
msgid ""
"很遗憾地通知，由于你已连续3次接单超时，今日的活动排名已被取消。\n"
"如对处理结果有疑问，请联系CoinEx客服。"
msgstr ""

#: app/common/constants.py:2298
#, python-format
msgid ""
"为保障P2P交易安全、维护商家服务信誉，CoinEx已于2025-03-07（UTC）起实施商家保证金制度。\n"
"我们发现您当前的保证金余额低于P2P商家保证金最低要求，请在 %(grace_deadline)s（UTC）前补充保证金，以免P2P交易受到限制。"
"\n"
"\n"
"补充方式：前往商家后台 > 总览 > 去补充，按页面提示完成操作"
msgstr ""

#: app/common/constants.py:2303
#, python-format
msgid ""
"为保障P2P交易安全、维护商家服务信誉，CoinEx已于2025-03-07（UTC）起实施商家保证金制度。\n"
"由于您未在 %(grace_deadline)s（UTC）前补充保证金，目前已被限制发布P2P广告单。\n"
"补充保证金后即可恢复广告发布权益，请尽快补充。\n"
"\n"
"补充方式：前往商家后台 > 总览 > 去补充，按页面提示完成操作"
msgstr ""

#: app/common/constants.py:2309
#, python-format
msgid ""
"基于商家所在地区和账户风险评估，系统已将您的保证金要求调整为 %(amount)s USDT。请及时补充保证金，以免广告发布受到限制。\n"
"\n"
"补充方式：前往商家后台 > 总览 > 去补充，按页面提示完成操作"
msgstr ""

#: app/common/constants.py:2313
#, python-format
msgid ""
"基于商家所在地区和账户风险评估，系统已将您的保证金要求调整为 %(amount)s USDT。\n"
"\n"
msgstr ""

#: app/common/constants.py:2316
msgid ""
"P2P商家身份已取消，商家保证金将于48小时内自动退回现货账户，如需查看历史交易记录，请登录商家后台。\n"
"\n"
"感谢您对CoinEx P2P的支持和信任，期待未来再次合作。"
msgstr ""

#: app/common/constants.py:2321
#, python-format
msgid ""
"尊敬的客户，您的P2P交易赔付已完成，赔付金额 %(amount)s USDT，已划转至您的现货账户，资金可随时用于交易或提现。\n"
"\n"
"如有疑问请提交工单联系客服，感谢您的理解与支持！"
msgstr ""

#: app/common/constants.py:2326
#, python-format
msgid ""
"尊敬的商家，平台已根据《平台商家行为规范》处理P2P交易赔付，赔付金额 %(amount)s USDT "
"已从您的保证金内扣除并转至用户现货账户。请及时登录商家后台查看保证金余额，确保后续交易正常进行。\n"
"\n"
"如有疑问请提交工单联系客服，感谢您的理解与支持！"
msgstr ""

#: app/common/constants.py:2332
#, python-format
msgid ""
"尊敬的商家，您超额缴纳的P2P保证金已完成返还，金额 %(amount)s USDT "
"已划转至您的现货账户。请及时登录商家后台查看保证金余额，确保后续交易正常进行。\n"
"\n"
"如有疑问请提交工单联系客服，感谢您的理解与支持！"
msgstr ""

#: app/common/constants.py:2338
#, python-format
msgid ""
"尊敬的商家，经核实您近期存在违反《平台商家行为规范》的行为，现依据规则扣除保证金 %(amount)s "
"USDT。款项已从您的商家保证金余额内划扣。请及时登录商家后台查看保证金余额，确保后续交易正常进行。\n"
"\n"
"如有疑问请提交工单联系客服，感谢您的理解与支持！"
msgstr ""

#: app/common/constants.py:2346
#: app/templates/email/notice/staking_remove_notice.j2:6
#, python-format
msgid "你质押的%(amount)s%(asset)s已经生效，次日08:30 （UTC+8）左右，产生的收益将发放至你的现货账户。"
msgstr ""

#: app/common/constants.py:2348
#, python-format
msgid "你质押的%(amount)s%(asset)s成功赎回 ，已存入到你的现货账户中。"
msgstr ""

#: app/common/constants.py:2350
#: app/templates/email/notice/pre_trading_settlement.j2:6
#, python-format
msgid "%(asset)s已于%(settle_at)s 完成交割，交割价格%(settle_price)s %(pledge_asset)s"
msgstr ""

#: app/common/constants.py:2352
msgid "你已成为CoinEx合约跟单交易员，请在交易员主页完善你的个人简介。"
msgstr ""

#: app/common/constants.py:2353
#, python-format
msgid "开仓均价 %(price)s %(asset)s，请在官网带单数据-当前带单中查看详情。"
msgstr ""

#: app/common/constants.py:2354
#, python-format
msgid "平仓均价 %(price)s %(asset)s，请在官网带单数据-历史带单中查看详情。"
msgstr ""

#: app/common/constants.py:2355
#, python-format
msgid "你的分润比例已从%(old_rate)s调整为%(new_rate)s，新的分润比例从下个周期开始生效。"
msgstr ""

#: app/common/constants.py:2358
#: app/templates/email/notice/copy_trading/trader_take_profit.j2:4
#, python-format
msgid "你的%(market)s跟单合约市场止盈设置已触发平仓，同时，跟单员仓位已全部平仓成功。"
msgstr ""

#: app/common/constants.py:2361
#, python-format
msgid "你的%(market)s跟单合约市场止损设置已触发平仓，同时，跟单员仓位已全部平仓成功。"
msgstr ""

#: app/common/constants.py:2364
#, python-format
msgid "很遗憾地通知你，你的跟单交易正向合约%(market)s持仓已被强制平仓。请在带单数据-历史带单中查看详情。"
msgstr ""

#: app/common/constants.py:2366
#: app/templates/email/notice/copy_trading/trader_trade_finished.j2:4
msgid "你已结束带单和仓位会以市价平仓，你获得的分润金额将会转入到现货账户。"
msgstr ""

#: app/common/constants.py:2367
#, python-format
msgid ""
"由于你已超过%(days)s天未进行合约带单交易，系统已取消你的交易员身份。\n"
"如需重新成为合约交易员，请重新申请带单，通过申请后才可发起带单交易。"
msgstr ""

#: app/common/constants.py:2371
#: app/templates/email/notice/copy_trading/trader_trade_pre_cancel.j2:4
#, python-format
msgid "请注意，你已超过%(days)s天未进行合约带单交易。如连续%(stop_days)s天未带单，系统将自动取消你的交易员身份。"
msgstr ""

#: app/common/constants.py:2374
#, python-format
msgid "你已跟随交易员@%(trader_nickname)s开仓成功 ，均价 %(price)s %(asset)s。"
msgstr ""

#: app/common/constants.py:2375
#, python-format
msgid "你已跟随交易员@%(trader_nickname)s平仓成功 ，均价 %(price)s %(asset)s。"
msgstr ""

#: app/common/constants.py:2376
#, python-format
msgid "你对交易员@%(trader_nickname)s 止盈设置已成功触发平仓。同时，你的跟单仓位已全部平仓成功和结束跟单"
msgstr ""

#: app/common/constants.py:2379
#, python-format
msgid "你对交易员@%(trader_nickname)s 止损设置已成功触发平仓。同时，你的跟单仓位已全部平仓成功和结束跟单"
msgstr ""

#: app/common/constants.py:2382
#: app/templates/email/notice/copy_trading/follower_position_liquidation.j2:4
#: app/templates/email/notice/copy_trading/trader_liquidation.j2:4
#, python-format
msgid "很遗憾地通知你，你的跟单交易正向合约%(market)s持仓已被强制平仓。"
msgstr ""

#: app/common/constants.py:2385
#, python-format
msgid ""
"你的交易员@%(trader_nickname)s 获得本次分润结算金额 %(amount)s %(asset)s，本结算周期为 "
"%(start_time)s - %(end_time)s"
msgstr ""

#: app/common/constants.py:2388
#, python-format
msgid "你的交易员@%(trader_nickname)s分润比例已从%(old_rate)s调整为%(new_rate)s，新的分润比例从下个周期开始生效。"
msgstr ""

#: app/common/constants.py:2391
#, python-format
msgid ""
"你的交易员@%(trader_nickname)s 已转入%(amount)s %(asset)s保证金，建议同步增加同等 "
"数量的保证金，并注意控制风险。"
msgstr ""

#: app/common/constants.py:2394
#, python-format
msgid "由于跟单保证金余额不足，你未能跟随交易员@%(trader_nickname)s成功开仓/加仓，请追加跟单金额。"
msgstr ""

#: app/common/constants.py:2397
#: app/templates/email/notice/copy_trading/follower_follow_finished.j2:4
#, python-format
msgid "你已与交易员@%(trader_nickname)s 结束跟单关系，仓位以市价全平成功。扣除分润金额后，剩余资金将会转入到现货账户。"
msgstr ""

#: app/common/constants.py:2400
#, python-format
msgid "感谢参与%(stake_assets)s挖矿活动，%(reward_amount)s %(reward_asset)s奖励已发放至你的现货账户。"
msgstr ""

#: app/common/constants.py:2403
#, python-format
msgid ""
"你的%(amount)s %(asset)s 手续费返现权益即将到期，请尽快使用。\n"
"返现到期时间：%(expired_at)s\n"
"\n"
"请前往「我的奖励」查看详情。"
msgstr ""

#: app/common/constants.py:2408
#, python-format
msgid ""
"恭喜你获得 %(amount)s %(asset)s 手续费返现权益，请尽快使用。返现到期时间：%(expired_at)s "
"。请前往「我的奖励」查看详情。"
msgstr ""

#: app/common/constants.py:2411
#, python-format
msgid "恭喜你获得 %(amount)s %(asset)s 空投奖励，请前往「我的奖励」查看详情。"
msgstr ""

#: app/common/constants.py:2414
#, python-format
msgid ""
"“%(title)s”已上线，快来奖励中心接受挑战吧！\n"
"请前往「奖励中心」查看详情。"
msgstr ""

#: app/common/constants.py:2418
#, python-format
msgid ""
"“%(title)s”等多个任务已上线，快来奖励中心接受挑战吧！\n"
"请前往「奖励中心」查看详情。"
msgstr ""

#: app/common/constants.py:2422
#, python-format
msgid ""
"恭喜，你已完成新用户专属任务！\n"
"任务名称：%(title)s\n"
"请前往「我的奖励」查看详情。"
msgstr ""

#: app/common/constants.py:2427
#, python-format
msgid ""
"你的新用户专属任务即将到期，完成即可获得%(value)s %(value_type)s%(reward_type)s奖励！\n"
"\n"
"请前往「奖励中心」完成任务。"
msgstr ""

#: app/common/constants.py:2434
#, python-format
msgid "由于您的账号币种评论内容涉及“%(reason)s”，您的账号%(count)s天无法发表币种评论和回复。"
msgstr ""

#: app/common/constants.py:2435
#, python-format
msgid "由于您的账号币种评论内容涉及“%(reason)s”，您的账号永久无法发表币种评论和回复。"
msgstr ""

#: app/common/constants.py:2438
#, python-format
msgid "恭喜通过本期大使激励包考核，%(settled_amount)s %(asset)s奖励已成功发放。"
msgstr ""

#: app/common/constants.py:2439
#, python-format
msgid ""
"恭喜通过本期大使激励包考核，%(settled_amount)s "
"%(asset)s奖励已成功发放。感谢你一直以来对CoinEx大使项目的支持，你的所有%(asset)s激励包奖励已经全部发放完毕！"
msgstr ""

#: app/common/constants.py:2441
#, python-format
msgid "很遗憾，你在本期大使激励包考核中未能达标，%(period_amount)s %(asset)s奖励未能正常发放。"
msgstr ""

#: app/common/constants.py:2442
#, python-format
msgid ""
"很遗憾，你的大使激励包考核未达标，%(period_amount)s "
"%(asset)s奖励未能正常发放。感谢你一直以来对CoinEx大使项目的支持，本次%(asset)s激励包考核已全部结束。"
msgstr ""

#: app/common/constants.py:2444
#, python-format
msgid "很遗憾，你的大使身份已失效，本期大使激励包%(period_amount)s %(asset)s未能正常发放。"
msgstr ""

#: app/common/constants.py:2445
#, python-format
msgid "您发表的内容收到%(count)s份礼物(打赏)共%(amount)s"
msgstr ""

#: app/common/constants.py:2618 app/models/activity.py:1234
#: app/models/mission_center.py:102 app/models/operation.py:2723
msgid "合约交易"
msgstr ""

#: app/common/constants.py:2619 app/models/operation.py:2725
msgid "杠杆交易"
msgstr ""

#: app/common/constants.py:2620
msgid "参与做市"
msgstr ""

#: app/common/constants.py:2621
msgid "API管理"
msgstr ""

#: app/common/constants.py:2635
msgid "钱包维护中，暂停充值"
msgstr ""

#: app/common/constants.py:2636
msgid "主网升级中，暂停充值"
msgstr ""

#: app/common/constants.py:2637
msgid "合约更新中，暂停充值"
msgstr ""

#: app/common/constants.py:2638
msgid "暂停充值"
msgstr ""

#: app/common/constants.py:2639
#, python-format
msgid "%(asset)s-%(chain)s已下架，停止充值"
msgstr ""

#: app/common/constants.py:2641
msgid "钱包维护中，暂停提现"
msgstr ""

#: app/common/constants.py:2642
msgid "主网升级中，暂停提现"
msgstr ""

#: app/common/constants.py:2643
msgid "合约更新中，暂停提现"
msgstr ""

#: app/common/constants.py:2644 app/exceptions/legacy.py:334
msgid "暂停提现"
msgstr ""

#: app/common/constants.py:2645
#, python-format
msgid "%(asset)s-%(chain)s已下架，停止提现"
msgstr ""

#: app/common/countries.py:10
msgid "阿富汗"
msgstr ""

#: app/common/countries.py:11
msgid "奥兰"
msgstr ""

#: app/common/countries.py:12
msgid "阿尔巴尼亚"
msgstr ""

#: app/common/countries.py:13
msgid "阿尔及利亚"
msgstr ""

#: app/common/countries.py:14
msgid "美属萨摩亚"
msgstr ""

#: app/common/countries.py:15
msgid "安道尔"
msgstr ""

#: app/common/countries.py:16
msgid "安哥拉"
msgstr ""

#: app/common/countries.py:17
msgid "安圭拉"
msgstr ""

#: app/common/countries.py:18
msgid "南极洲"
msgstr ""

#: app/common/countries.py:19 app/common/countries.py:262
msgid "安提瓜和巴布达"
msgstr ""

#: app/common/countries.py:20
msgid "阿根廷"
msgstr ""

#: app/common/countries.py:21
msgid "亚美尼亚"
msgstr ""

#: app/common/countries.py:22
msgid "阿鲁巴"
msgstr ""

#: app/common/countries.py:23
msgid "澳大利亚"
msgstr ""

#: app/common/countries.py:24
msgid "奥地利"
msgstr ""

#: app/common/countries.py:25
msgid "阿塞拜疆"
msgstr ""

#: app/common/countries.py:26
msgid "巴哈马"
msgstr ""

#: app/common/countries.py:27
msgid "巴林"
msgstr ""

#: app/common/countries.py:28
msgid "孟加拉国"
msgstr ""

#: app/common/countries.py:29
msgid "巴巴多斯"
msgstr ""

#: app/common/countries.py:30
msgid "白俄罗斯"
msgstr ""

#: app/common/countries.py:31
msgid "比利时"
msgstr ""

#: app/common/countries.py:32
msgid "伯利兹"
msgstr ""

#: app/common/countries.py:33
msgid "贝宁"
msgstr ""

#: app/common/countries.py:34
msgid "百慕大"
msgstr ""

#: app/common/countries.py:35
msgid "不丹"
msgstr ""

#: app/common/countries.py:36
msgid "玻利维亚"
msgstr ""

#: app/common/countries.py:37
msgid "荷兰加勒比区"
msgstr ""

#: app/common/countries.py:38
msgid "波黑"
msgstr ""

#: app/common/countries.py:39
msgid "博茨瓦纳"
msgstr ""

#: app/common/countries.py:40
msgid "布韦岛"
msgstr ""

#: app/common/countries.py:41
msgid "巴西"
msgstr ""

#: app/common/countries.py:42
msgid "英属印度洋领地"
msgstr ""

#: app/common/countries.py:43
msgid "文莱"
msgstr ""

#: app/common/countries.py:44
msgid "保加利亚"
msgstr ""

#: app/common/countries.py:45
msgid "布基纳法索"
msgstr ""

#: app/common/countries.py:46
msgid "布隆迪"
msgstr ""

#: app/common/countries.py:47
msgid "佛得角"
msgstr ""

#: app/common/countries.py:48
msgid "柬埔寨"
msgstr ""

#: app/common/countries.py:49
msgid "喀麦隆"
msgstr ""

#: app/common/countries.py:50
msgid "加拿大"
msgstr ""

#: app/common/countries.py:51
msgid "开曼群岛"
msgstr ""

#: app/common/countries.py:52
msgid "中非"
msgstr ""

#: app/common/countries.py:53
msgid "乍得"
msgstr ""

#: app/common/countries.py:54
msgid "智利"
msgstr ""

#: app/common/countries.py:55
msgid "中国"
msgstr ""

#: app/common/countries.py:56
msgid "圣诞岛"
msgstr ""

#: app/common/countries.py:57
msgid "科科斯（基林）群岛"
msgstr ""

#: app/common/countries.py:58
msgid "哥伦比亚"
msgstr ""

#: app/common/countries.py:59
msgid "科摩罗"
msgstr ""

#: app/common/countries.py:60
msgid "刚果共和国"
msgstr ""

#: app/common/countries.py:62
msgid "刚果民主共和国"
msgstr ""

#: app/common/countries.py:63
msgid "库克群岛"
msgstr ""

#: app/common/countries.py:64
msgid "哥斯达黎加"
msgstr ""

#: app/common/countries.py:65
msgid "科特迪瓦"
msgstr ""

#: app/common/countries.py:66
msgid "克罗地亚"
msgstr ""

#: app/common/countries.py:67
msgid "古巴"
msgstr ""

#: app/common/countries.py:68
msgid "库拉索"
msgstr ""

#: app/common/countries.py:69
msgid "塞浦路斯"
msgstr ""

#: app/common/countries.py:70
msgid "捷克"
msgstr ""

#: app/common/countries.py:71
msgid "丹麦"
msgstr ""

#: app/common/countries.py:72
msgid "吉布提"
msgstr ""

#: app/common/countries.py:73
msgid "多米尼克"
msgstr ""

#: app/common/countries.py:74
msgid "多米尼加"
msgstr ""

#: app/common/countries.py:75
msgid "厄瓜多尔"
msgstr ""

#: app/common/countries.py:76
msgid "埃及"
msgstr ""

#: app/common/countries.py:77
msgid "萨尔瓦多"
msgstr ""

#: app/common/countries.py:78
msgid "赤道几内亚"
msgstr ""

#: app/common/countries.py:79
msgid "厄立特里亚"
msgstr ""

#: app/common/countries.py:80
msgid "爱沙尼亚"
msgstr ""

#: app/common/countries.py:81
msgid "斯威士兰"
msgstr ""

#: app/common/countries.py:82
msgid "埃塞俄比亚"
msgstr ""

#: app/common/countries.py:83
msgid "福克兰群岛"
msgstr ""

#: app/common/countries.py:84
msgid "法罗群岛"
msgstr ""

#: app/common/countries.py:85
msgid "斐济"
msgstr ""

#: app/common/countries.py:86
msgid "芬兰"
msgstr ""

#: app/common/countries.py:87
msgid "法国"
msgstr ""

#: app/common/countries.py:88
msgid "法属圭亚那"
msgstr ""

#: app/common/countries.py:89
msgid "法属波利尼西亚"
msgstr ""

#: app/common/countries.py:90
msgid "法属南部和南极领地"
msgstr ""

#: app/common/countries.py:91
msgid "加蓬"
msgstr ""

#: app/common/countries.py:92
msgid "冈比亚"
msgstr ""

#: app/common/countries.py:93
msgid "格鲁吉亚"
msgstr ""

#: app/common/countries.py:94
msgid "德国"
msgstr ""

#: app/common/countries.py:95
msgid "加纳"
msgstr ""

#: app/common/countries.py:96
msgid "直布罗陀"
msgstr ""

#: app/common/countries.py:97
msgid "希腊"
msgstr ""

#: app/common/countries.py:98
msgid "格陵兰"
msgstr ""

#: app/common/countries.py:99
msgid "格林纳达"
msgstr ""

#: app/common/countries.py:100
msgid "瓜德罗普"
msgstr ""

#: app/common/countries.py:101 app/common/countries.py:263
msgid "关岛"
msgstr ""

#: app/common/countries.py:102
msgid "危地马拉"
msgstr ""

#: app/common/countries.py:103
msgid "根西"
msgstr ""

#: app/common/countries.py:104
msgid "几内亚"
msgstr ""

#: app/common/countries.py:105
msgid "几内亚比绍"
msgstr ""

#: app/common/countries.py:106
msgid "圭亚那"
msgstr ""

#: app/common/countries.py:107
msgid "海地"
msgstr ""

#: app/common/countries.py:108
msgid "赫德岛和麦克唐纳群岛"
msgstr ""

#: app/common/countries.py:109
msgid "梵蒂冈"
msgstr ""

#: app/common/countries.py:110
msgid "洪都拉斯"
msgstr ""

#: app/common/countries.py:111
msgid "中国香港"
msgstr ""

#: app/common/countries.py:112
msgid "匈牙利"
msgstr ""

#: app/common/countries.py:113
msgid "冰岛"
msgstr ""

#: app/common/countries.py:114
msgid "印度"
msgstr ""

#: app/common/countries.py:115
msgid "印尼"
msgstr ""

#: app/common/countries.py:116
msgid "伊朗"
msgstr ""

#: app/common/countries.py:117
msgid "伊拉克"
msgstr ""

#: app/common/countries.py:118
msgid "爱尔兰"
msgstr ""

#: app/common/countries.py:119
msgid "马恩岛"
msgstr ""

#: app/common/countries.py:120
msgid "以色列"
msgstr ""

#: app/common/countries.py:121
msgid "意大利"
msgstr ""

#: app/common/countries.py:122
msgid "牙买加"
msgstr ""

#: app/common/countries.py:123
msgid "日本"
msgstr ""

#: app/common/countries.py:124
msgid "泽西"
msgstr ""

#: app/common/countries.py:125
msgid "约旦"
msgstr ""

#: app/common/countries.py:126 app/common/countries.py:261
msgid "哈萨克斯坦"
msgstr ""

#: app/common/countries.py:127
msgid "肯尼亚"
msgstr ""

#: app/common/countries.py:128
msgid "基里巴斯"
msgstr ""

#: app/common/countries.py:129
msgid "朝鲜"
msgstr ""

#: app/common/countries.py:130
msgid "韩国"
msgstr ""

#: app/common/countries.py:131
msgid "科索沃"
msgstr ""

#: app/common/countries.py:132
msgid "科威特"
msgstr ""

#: app/common/countries.py:133
msgid "吉尔吉斯斯坦"
msgstr ""

#: app/common/countries.py:134
msgid "老挝"
msgstr ""

#: app/common/countries.py:135
msgid "拉脱维亚"
msgstr ""

#: app/common/countries.py:136
msgid "黎巴嫩"
msgstr ""

#: app/common/countries.py:137
msgid "莱索托"
msgstr ""

#: app/common/countries.py:138
msgid "利比里亚"
msgstr ""

#: app/common/countries.py:139
msgid "利比亚"
msgstr ""

#: app/common/countries.py:140
msgid "列支敦士登"
msgstr ""

#: app/common/countries.py:141
msgid "立陶宛"
msgstr ""

#: app/common/countries.py:142
msgid "卢森堡"
msgstr ""

#: app/common/countries.py:143
msgid "中国澳门"
msgstr ""

#: app/common/countries.py:144
msgid "马达加斯加"
msgstr ""

#: app/common/countries.py:145
msgid "马拉维"
msgstr ""

#: app/common/countries.py:146
msgid "马来西亚"
msgstr ""

#: app/common/countries.py:147
msgid "马尔代夫"
msgstr ""

#: app/common/countries.py:148
msgid "马里"
msgstr ""

#: app/common/countries.py:149
msgid "马耳他"
msgstr ""

#: app/common/countries.py:150
msgid "马绍尔群岛"
msgstr ""

#: app/common/countries.py:151
msgid "马提尼克"
msgstr ""

#: app/common/countries.py:152
msgid "毛里塔尼亚"
msgstr ""

#: app/common/countries.py:153
msgid "毛里求斯"
msgstr ""

#: app/common/countries.py:154
msgid "马约特"
msgstr ""

#: app/common/countries.py:155
msgid "墨西哥"
msgstr ""

#: app/common/countries.py:156
msgid "密克罗尼西亚联邦"
msgstr ""

#: app/common/countries.py:157
msgid "摩尔多瓦"
msgstr ""

#: app/common/countries.py:158
msgid "摩纳哥"
msgstr ""

#: app/common/countries.py:159
msgid "蒙古国"
msgstr ""

#: app/common/countries.py:160
msgid "黑山"
msgstr ""

#: app/common/countries.py:161
msgid "蒙特塞拉特"
msgstr ""

#: app/common/countries.py:162
msgid "摩洛哥"
msgstr ""

#: app/common/countries.py:163
msgid "莫桑比克"
msgstr ""

#: app/common/countries.py:164
msgid "缅甸"
msgstr ""

#: app/common/countries.py:165
msgid "纳米比亚"
msgstr ""

#: app/common/countries.py:166
msgid "瑙鲁"
msgstr ""

#: app/common/countries.py:167
msgid "尼泊尔"
msgstr ""

#: app/common/countries.py:168
msgid "荷兰"
msgstr ""

#: app/common/countries.py:169
msgid "新喀里多尼亚"
msgstr ""

#: app/common/countries.py:170
msgid "新西兰"
msgstr ""

#: app/common/countries.py:171
msgid "尼加拉瓜"
msgstr ""

#: app/common/countries.py:172
msgid "尼日尔"
msgstr ""

#: app/common/countries.py:173
msgid "尼日利亚"
msgstr ""

#: app/common/countries.py:174
msgid "纽埃"
msgstr ""

#: app/common/countries.py:175
msgid "诺福克岛"
msgstr ""

#: app/common/countries.py:176
msgid "北马其顿"
msgstr ""

#: app/common/countries.py:177
msgid "北马里亚纳群岛"
msgstr ""

#: app/common/countries.py:178
msgid "挪威"
msgstr ""

#: app/common/countries.py:179
msgid "阿曼"
msgstr ""

#: app/common/countries.py:180
msgid "巴基斯坦"
msgstr ""

#: app/common/countries.py:181
msgid "帕劳"
msgstr ""

#: app/common/countries.py:182
msgid "巴勒斯坦"
msgstr ""

#: app/common/countries.py:183
msgid "巴拿马"
msgstr ""

#: app/common/countries.py:184
msgid "巴布亚新几内亚"
msgstr ""

#: app/common/countries.py:185
msgid "巴拉圭"
msgstr ""

#: app/common/countries.py:186
msgid "秘鲁"
msgstr ""

#: app/common/countries.py:187
msgid "菲律宾"
msgstr ""

#: app/common/countries.py:188
msgid "皮特凯恩群岛"
msgstr ""

#: app/common/countries.py:189
msgid "波兰"
msgstr ""

#: app/common/countries.py:190
msgid "葡萄牙"
msgstr ""

#: app/common/countries.py:191
msgid "波多黎各"
msgstr ""

#: app/common/countries.py:192
msgid "卡塔尔"
msgstr ""

#: app/common/countries.py:193
msgid "留尼汪"
msgstr ""

#: app/common/countries.py:194
msgid "罗马尼亚"
msgstr ""

#: app/common/countries.py:195
msgid "俄罗斯"
msgstr ""

#: app/common/countries.py:196
msgid "卢旺达"
msgstr ""

#: app/common/countries.py:197
msgid "圣巴泰勒米"
msgstr ""

#: app/common/countries.py:198
msgid "圣赫勒拿、阿森松和特里斯坦-达库尼亚"
msgstr ""

#: app/common/countries.py:199
msgid "圣基茨和尼维斯"
msgstr ""

#: app/common/countries.py:200
msgid "圣卢西亚"
msgstr ""

#: app/common/countries.py:201
msgid "法属圣马丁"
msgstr ""

#: app/common/countries.py:202
msgid "圣皮埃尔和密克隆"
msgstr ""

#: app/common/countries.py:203
msgid "圣文森特和格林纳丁斯"
msgstr ""

#: app/common/countries.py:204
msgid "萨摩亚"
msgstr ""

#: app/common/countries.py:205
msgid "圣马力诺"
msgstr ""

#: app/common/countries.py:206
msgid "圣多美和普林西比"
msgstr ""

#: app/common/countries.py:207
msgid "沙特阿拉伯"
msgstr ""

#: app/common/countries.py:208
msgid "塞内加尔"
msgstr ""

#: app/common/countries.py:209
msgid "塞尔维亚"
msgstr ""

#: app/common/countries.py:210
msgid "塞舌尔"
msgstr ""

#: app/common/countries.py:211
msgid "塞拉利昂"
msgstr ""

#: app/common/countries.py:212
msgid "新加坡"
msgstr ""

#: app/common/countries.py:213
msgid "荷属圣马丁"
msgstr ""

#: app/common/countries.py:214
msgid "斯洛伐克"
msgstr ""

#: app/common/countries.py:215
msgid "斯洛文尼亚"
msgstr ""

#: app/common/countries.py:216
msgid "所罗门群岛"
msgstr ""

#: app/common/countries.py:217
msgid "索马里"
msgstr ""

#: app/common/countries.py:218
msgid "南非"
msgstr ""

#: app/common/countries.py:219
msgid "南乔治亚和南桑威奇群岛"
msgstr ""

#: app/common/countries.py:220
msgid "南苏丹"
msgstr ""

#: app/common/countries.py:221
msgid "西班牙"
msgstr ""

#: app/common/countries.py:222
msgid "斯里兰卡"
msgstr ""

#: app/common/countries.py:223
msgid "苏丹"
msgstr ""

#: app/common/countries.py:224
msgid "苏里南"
msgstr ""

#: app/common/countries.py:225
msgid "斯瓦尔巴和扬马延"
msgstr ""

#: app/common/countries.py:226
msgid "瑞典"
msgstr ""

#: app/common/countries.py:227
msgid "瑞士"
msgstr ""

#: app/common/countries.py:228
msgid "叙利亚"
msgstr ""

#: app/common/countries.py:229
msgid "台湾 中国台湾省"
msgstr ""

#: app/common/countries.py:230
msgid "塔吉克斯坦"
msgstr ""

#: app/common/countries.py:231
msgid "坦桑尼亚"
msgstr ""

#: app/common/countries.py:232
msgid "泰国"
msgstr ""

#: app/common/countries.py:233
msgid "东帝汶"
msgstr ""

#: app/common/countries.py:234
msgid "多哥"
msgstr ""

#: app/common/countries.py:235
msgid "托克劳"
msgstr ""

#: app/common/countries.py:236
msgid "汤加"
msgstr ""

#: app/common/countries.py:237
msgid "特立尼达和多巴哥"
msgstr ""

#: app/common/countries.py:238
msgid "突尼斯"
msgstr ""

#: app/common/countries.py:239
msgid "土耳其"
msgstr ""

#: app/common/countries.py:240
msgid "土库曼斯坦"
msgstr ""

#: app/common/countries.py:241
msgid "特克斯和凯科斯群岛"
msgstr ""

#: app/common/countries.py:242
msgid "图瓦卢"
msgstr ""

#: app/common/countries.py:243
msgid "乌干达"
msgstr ""

#: app/common/countries.py:244
msgid "乌克兰"
msgstr ""

#: app/common/countries.py:245
msgid "阿联酋"
msgstr ""

#: app/common/countries.py:246
msgid "英国"
msgstr ""

#: app/common/countries.py:247
msgid "美国"
msgstr ""

#: app/common/countries.py:248
msgid "美国本土外小岛屿"
msgstr ""

#: app/common/countries.py:249
msgid "乌拉圭"
msgstr ""

#: app/common/countries.py:250
msgid "乌兹别克斯坦"
msgstr ""

#: app/common/countries.py:251
msgid "瓦努阿图"
msgstr ""

#: app/common/countries.py:252
msgid "委内瑞拉"
msgstr ""

#: app/common/countries.py:253
msgid "越南"
msgstr ""

#: app/common/countries.py:254
msgid "英属维尔京群岛"
msgstr ""

#: app/common/countries.py:255
msgid "美属维尔京群岛"
msgstr ""

#: app/common/countries.py:256
msgid "瓦利斯和富图纳"
msgstr ""

#: app/common/countries.py:257
msgid "阿拉伯撒哈拉民主共和国"
msgstr ""

#: app/common/countries.py:258
msgid "也门"
msgstr ""

#: app/common/countries.py:259
msgid "赞比亚"
msgstr ""

#: app/common/countries.py:260
msgid "津巴布韦"
msgstr ""

#: app/common/countries.py:264
msgid "多米尼加共和国"
msgstr ""

#: app/exceptions/admin.py:10
msgid "Require Super Admin Role Permission."
msgstr ""

#: app/exceptions/admin.py:16
msgid "Require Admin Role Permission."
msgstr ""

#: app/exceptions/basic.py:10
msgid "错误"
msgstr ""

#: app/exceptions/basic.py:16
msgid "参数错误"
msgstr ""

#: app/exceptions/basic.py:22
msgid "系统繁忙，请稍后再试"
msgstr ""

#: app/exceptions/basic.py:28
msgid "表头缺失支持的语言"
msgstr ""

#: app/exceptions/basic.py:34
msgid "请输入验证码"
msgstr ""

#: app/exceptions/basic.py:40
msgid "2FA验证失败，请重试"
msgstr ""

#: app/exceptions/basic.py:46
msgid "TOTP验证码错误"
msgstr ""

#: app/exceptions/basic.py:51
msgid "短信验证码错误"
msgstr ""

#: app/exceptions/basic.py:56
msgid "通行密钥验证错误"
msgstr ""

#: app/exceptions/basic.py:62
msgid "操作已超时，请刷新重试"
msgstr ""

#: app/exceptions/basic.py:68
msgid "请先绑定手机或谷歌验证器或通行密钥"
msgstr ""

#: app/exceptions/basic.py:74
msgid "错误账号"
msgstr ""

#: app/exceptions/basic.py:80 app/exceptions/perpetual.py:85
#: app/exceptions/perpetual.py:89
msgid "服务超时"
msgstr ""

#: app/exceptions/basic.py:86 app/exceptions/perpetual.py:84
#: app/exceptions/perpetual.py:88
msgid "服务不可用"
msgstr ""

#: app/exceptions/basic.py:92
msgid "验证码已经被使用。请等待下一个验证码。"
msgstr ""

#: app/exceptions/basic.py:98
msgid "提现金额过小"
msgstr ""

#: app/exceptions/basic.py:104
msgid "暂无法交易，如需更多帮助请提交工单咨询。"
msgstr ""

#: app/exceptions/basic.py:110
msgid "暂无法开仓或加仓，可补充保证金，如需减仓/平仓，可使用“一键平仓“功能，更多帮助请提交工单咨询。"
msgstr ""

#: app/exceptions/basic.py:116
msgid "暂无法划出资金，仅可划入资金，如需更多帮助请提交工单咨询。"
msgstr ""

#: app/exceptions/basic.py:122
msgid "禁止现货交易：您的授信账户当前风险率已低于可交易风险率，系统已关闭现货交易功能，请尽快补充非授信资产"
msgstr ""

#: app/exceptions/basic.py:128
msgid "当前市场不可下单，请稍后再试。"
msgstr ""

#: app/exceptions/basic.py:134
msgid "当前市场不可撤单，请稍后再试。"
msgstr ""

#: app/exceptions/basic.py:140
msgid "可用资产不足"
msgstr ""

#: app/exceptions/basic.py:146
msgid "停止充值。"
msgstr ""

#: app/exceptions/basic.py:152
msgid "钱包维护中"
msgstr ""

#: app/exceptions/basic.py:158
msgid "超过提现精度限制"
msgstr ""

#: app/exceptions/basic.py:164
msgid "暂停站内转账"
msgstr ""

#: app/exceptions/basic.py:170
msgid "请绑定TOTP认证"
msgstr ""

#: app/exceptions/basic.py:176
msgid "请先完成实名认证"
msgstr ""

#: app/exceptions/basic.py:182
msgid "Already submitted reset application"
msgstr ""

#: app/exceptions/basic.py:188
msgid "已超时，请重试。"
msgstr ""

#: app/exceptions/basic.py:193
msgid "该业务不支持子账号"
msgstr ""

#: app/exceptions/basic.py:198
msgid "暂无法划转，如需更多帮助请提交工单咨询。"
msgstr ""

#: app/exceptions/basic.py:203
msgid "授信风险风险率过低或者授信余额不足"
msgstr ""

#: app/exceptions/basic.py:215
msgid "该地址不支持更换。"
msgstr ""

#: app/exceptions/basic.py:220
msgid "in maintain mode"
msgstr ""

#: app/exceptions/basic.py:225
msgid "该功能已下架"
msgstr ""

#: app/exceptions/basic.py:230
msgid "Not Supported"
msgstr ""

#: app/exceptions/basic.py:235
msgid "Out Of Range"
msgstr ""

#: app/exceptions/basic.py:240
msgid "Confirmation Required"
msgstr ""

#: app/exceptions/basic.py:245
msgid "Data Not Ready"
msgstr ""

#: app/exceptions/basic.py:251
msgid "很抱歉，根据您所在地区的监管要求，我们无法为您提供该服务。"
msgstr ""

#: app/exceptions/basic.py:259
msgid "我们无法为您IP所在地区未完成实名认证的用户提供服务，请先前往实名认证。"
msgstr ""

#: app/exceptions/basic.py:267
msgid "当前版本不支持该功能，请升级App后使用"
msgstr ""

#: app/exceptions/basic.py:271
msgid "暂无数据"
msgstr ""

#: app/exceptions/basic.py:277
msgid "很抱歉，根据反洗钱政策要求，我们无法为您提供该功能服务。"
msgstr ""

#: app/exceptions/basic.py:284
msgid "验证失败"
msgstr ""

#: app/exceptions/basic.py:290
msgid "签名失败"
msgstr ""

#: app/exceptions/basic.py:296
msgid "近30天可提现额度不足"
msgstr ""

#: app/exceptions/basic.py:302
msgid "仅支持上传png/jpg/jpeg/webp图片格式。"
msgstr ""

#: app/exceptions/comment.py:9
#, python-format
msgid "无法发表评论，你被禁言，%(banned_date)s后可发表评论"
msgstr ""

#: app/exceptions/comment.py:15
msgid "无法发表评论，你被禁言"
msgstr ""

#: app/exceptions/comment.py:21
#, python-format
msgid "无法发表回复，你被禁言，%(banned_date)s后可发表回复"
msgstr ""

#: app/exceptions/comment.py:27
msgid "无法发表回复，你被禁言"
msgstr ""

#: app/exceptions/comment.py:33
msgid "当前内容已违规，无法进行互动"
msgstr ""

#: app/exceptions/comment.py:38
msgid "翻译失败"
msgstr ""

#: app/exceptions/comment.py:44
msgid "当前内容不存在"
msgstr ""

#: app/exceptions/comment.py:50
msgid "当前内容已删除"
msgstr ""

#: app/exceptions/comment.py:60
msgid "英语频道仅可发表英文内容，无法发表其他语言内容。"
msgstr ""

#: app/exceptions/comment.py:71
msgid "内容相似啦，试试换个表达方式吧～"
msgstr ""

#: app/exceptions/denied.py:10 app/exceptions/denied.py:15
msgid "禁止登录。"
msgstr ""

#: app/exceptions/invalid_argument.py:9
msgid "全部答对才可领取奖励，请认真学习项目资料后重新答题。"
msgstr ""

#: app/exceptions/legacy.py:10
msgid "只能输入数字。"
msgstr ""

#: app/exceptions/legacy.py:16
msgid "仅支持上传png/jpg/jpeg/bmp/gif图片格式。"
msgstr ""

#: app/exceptions/legacy.py:22
msgid "仅支持上传.xls格式文件。"
msgstr ""

#: app/exceptions/legacy.py:28
msgid "仅支持6M以下大小。"
msgstr ""

#: app/exceptions/legacy.py:34
msgid "取消失败"
msgstr ""

#: app/exceptions/legacy.py:40
msgid "修改失败"
msgstr ""

#: app/exceptions/legacy.py:46
msgid "绑定失败"
msgstr ""

#: app/exceptions/legacy.py:52
msgid "无法取消"
msgstr ""

#: app/exceptions/legacy.py:58
msgid "状态已通过"
msgstr ""

#: app/exceptions/legacy.py:64
msgid "状态审核中"
msgstr ""

#: app/exceptions/legacy.py:70 app/exceptions/legacy.py:310
msgid "状态已完成"
msgstr ""

#: app/exceptions/legacy.py:76
msgid "状态已创建"
msgstr ""

#: app/exceptions/legacy.py:82
msgid "状态未通过"
msgstr ""

#: app/exceptions/legacy.py:88
msgid "IP禁用"
msgstr ""

#: app/exceptions/legacy.py:94
msgid "Access id不存在"
msgstr ""

#: app/exceptions/legacy.py:100
msgid "签名错误"
msgstr ""

#: app/exceptions/legacy.py:106
msgid "待确认"
msgstr ""

#: app/exceptions/legacy.py:112
msgid "x_csrf_token error"
msgstr ""

#: app/exceptions/legacy.py:118
msgid "请绑定二次验证。"
msgstr ""

#: app/exceptions/legacy.py:124
msgid "操作不允许"
msgstr ""

#: app/exceptions/legacy.py:130
msgid "邮件验证码错误"
msgstr ""

#: app/exceptions/legacy.py:136
msgid "请升级您的App版本"
msgstr ""

#: app/exceptions/legacy.py:142
msgid "Access id is expires"
msgstr ""

#: app/exceptions/legacy.py:148
msgid "用户不存在"
msgstr ""

#: app/exceptions/legacy.py:154
msgid "can not transfer between sub accounts"
msgstr ""

#: app/exceptions/legacy.py:160
msgid "can not transfer between master accounts"
msgstr ""

#: app/exceptions/legacy.py:166
msgid "Main and sub accounts unpaired"
msgstr ""

#: app/exceptions/legacy.py:172
msgid "您的子账号内还有资金余额，需将余额全部划转至主账号后才能删除"
msgstr ""

#: app/exceptions/legacy.py:178
msgid "子账号不能登录"
msgstr ""

#: app/exceptions/legacy.py:184
msgid "该子账号已被删除"
msgstr ""

#: app/exceptions/legacy.py:190
msgid "该域名已被使用，请重新设置"
msgstr ""

#: app/exceptions/legacy.py:196
msgid "该子账号已被冻结"
msgstr ""

#: app/exceptions/legacy.py:202
msgid "subordinate relation error"
msgstr ""

#: app/exceptions/legacy.py:208
msgid "子账号用户名已存在"
msgstr ""

#: app/exceptions/legacy.py:214
msgid "子账号用户名不合法"
msgstr ""

#: app/exceptions/legacy.py:220
msgid "Transfer to sub account rejected"
msgstr ""

#: app/exceptions/legacy.py:226
msgid "请先进行实名认证"
msgstr ""

#: app/exceptions/legacy.py:232
msgid "no_position_qualification"
msgstr ""

#: app/exceptions/legacy.py:238
msgid "not sign agreement"
msgstr ""

#: app/exceptions/legacy.py:244
msgid "invalid answers"
msgstr ""

#: app/exceptions/legacy.py:250
msgid "trade amount not enough"
msgstr ""

#: app/exceptions/legacy.py:256
msgid "1点(UTC)自动更新做市等级, 您已经是做市商, 请稍后再试"
msgstr ""

#: app/exceptions/legacy.py:262
msgid "客户端版本过低，需要升级"
msgstr ""

#: app/exceptions/legacy.py:268 app/exceptions/order.py:79
msgid "当前市场深度不足，暂无法市价下单"
msgstr ""

#: app/exceptions/legacy.py:274
msgid "语言验证码服务已暂停。"
msgstr ""

#: app/exceptions/legacy.py:280
msgid "银行卡不存在。"
msgstr ""

#: app/exceptions/legacy.py:286
msgid "24小时之前修改过资金密码。"
msgstr ""

#: app/exceptions/legacy.py:292
msgid "提现超出余额"
msgstr ""

#: app/exceptions/legacy.py:298
msgid "提现金额过大"
msgstr ""

#: app/exceptions/legacy.py:304
msgid "当日提现额度不足"
msgstr ""

#: app/exceptions/legacy.py:316
msgid "余额低于手续费"
msgstr ""

#: app/exceptions/legacy.py:322
msgid "银行卡未验证"
msgstr ""

#: app/exceptions/legacy.py:328
msgid "兑换已于北京时间2017年8月1日8:00停止。"
msgstr ""

#: app/exceptions/legacy.py:340
msgid "兑换已于北京时间2017年8月1日20:20停止。"
msgstr ""

#: app/exceptions/legacy.py:346
msgid "停止注册。"
msgstr ""

#: app/exceptions/legacy.py:352
msgid "停止领取。"
msgstr ""

#: app/exceptions/legacy.py:358
msgid "提现失败！您的提现金额超过本日可提额度。"
msgstr ""

#: app/exceptions/legacy.py:364
msgid "投票需持仓至少 1000 CET"
msgstr ""

#: app/exceptions/legacy.py:370
msgid "暂无法提现，如需更多帮助请提交工单咨询。"
msgstr ""

#: app/exceptions/legacy.py:376
msgid "账号异常，禁止提现，请联系客服。"
msgstr ""

#: app/exceptions/legacy.py:382
msgid "修改安全工具后24小时无法提现。"
msgstr ""

#: app/exceptions/legacy.py:388
msgid "修改提现密码后24小时无法提现。"
msgstr ""

#: app/exceptions/legacy.py:394 app/exceptions/legacy.py:520
msgid "请先绑定邮箱"
msgstr ""

#: app/exceptions/legacy.py:400
msgid "不能关闭邮箱通知。"
msgstr ""

#: app/exceptions/legacy.py:406
msgid "请先绑定手机号码"
msgstr ""

#: app/exceptions/legacy.py:412
msgid "请先绑定谷歌认证器"
msgstr ""

#: app/exceptions/legacy.py:418
msgid "原资金密码错误"
msgstr ""

#: app/exceptions/legacy.py:424
msgid "原登录密码错误"
msgstr ""

#: app/exceptions/legacy.py:430
msgid "请先设置资金密码。"
msgstr ""

#: app/exceptions/legacy.py:436
msgid "The withdrawal amount can only be an integer."
msgstr ""

#: app/exceptions/legacy.py:442
msgid "API permission is not allowed"
msgstr ""

#: app/exceptions/legacy.py:448
msgid "仅支持上传png/jpeg图片格式。"
msgstr ""

#: app/exceptions/legacy.py:454
msgid "仅支持5M以下大小。"
msgstr ""

#: app/exceptions/legacy.py:460
msgid "重复认证"
msgstr ""

#: app/exceptions/legacy.py:466
msgid "暂不支持印度尼西亚用户进行实名认证"
msgstr ""

#: app/exceptions/legacy.py:472
msgid "暂不支持越南用户进行实名认证"
msgstr ""

#: app/exceptions/legacy.py:478
msgid "KYC chances have been used up"
msgstr ""

#: app/exceptions/legacy.py:484
msgid "请重新登录"
msgstr ""

#: app/exceptions/legacy.py:490
msgid "邮箱已通过验证"
msgstr ""

#: app/exceptions/legacy.py:496
msgid "手机号不存在，无法重置资金密码"
msgstr ""

#: app/exceptions/legacy.py:502
msgid "无法重置资金密码"
msgstr ""

#: app/exceptions/legacy.py:508
msgid "已经绑定谷歌认证器"
msgstr ""

#: app/exceptions/legacy.py:514
msgid "谷歌认证器码已经过期"
msgstr ""

#: app/exceptions/legacy.py:526
msgid "手机号不存在"
msgstr ""

#: app/exceptions/legacy.py:532
msgid "请输入资金密码"
msgstr ""

#: app/exceptions/legacy.py:538
msgid "您已成功绑定手机号。"
msgstr ""

#: app/exceptions/legacy.py:544
msgid "已经创建了50个API账户，不能再创建。"
msgstr ""

#: app/exceptions/legacy.py:550
msgid "Token不存在。"
msgstr ""

#: app/exceptions/legacy.py:556
msgid "邮箱链接错误。"
msgstr ""

#: app/exceptions/legacy.py:562
msgid "验证不通过。"
msgstr ""

#: app/exceptions/legacy.py:568
msgid "绑定手机或实名认证。"
msgstr ""

#: app/exceptions/legacy.py:574
msgid "密码错误。"
msgstr ""

#: app/exceptions/legacy.py:580
msgid "tonce校验错误，正确的tonce应该在当前时间戳的一分钟以内"
msgstr ""

#: app/exceptions/legacy.py:586
msgid "用户名不得超出40个字符"
msgstr ""

#: app/exceptions/legacy.py:592
msgid "用户名已经存在。"
msgstr ""

#: app/exceptions/legacy.py:598
msgid "邮件链接已失效"
msgstr ""

#: app/exceptions/legacy.py:604
msgid "你不是大使"
msgstr ""

#: app/exceptions/legacy.py:610
msgid "你还没有同意大使协议"
msgstr ""

#: app/exceptions/legacy.py:616
msgid "无法修改"
msgstr ""

#: app/exceptions/legacy.py:622 app/models/user.py:1440
msgid "证件号码已存在"
msgstr ""

#: app/exceptions/legacy.py:628
msgid "卡号已存在"
msgstr ""

#: app/exceptions/legacy.py:634
msgid "初级实名认证不匹配"
msgstr ""

#: app/exceptions/legacy.py:640
msgid "卡号不匹配"
msgstr ""

#: app/exceptions/legacy.py:646
msgid "身份证号错误"
msgstr ""

#: app/exceptions/legacy.py:652
msgid "只支付借记卡。"
msgstr ""

#: app/exceptions/legacy.py:658
msgid "提现地址已经被绑定。"
msgstr ""

#: app/exceptions/legacy.py:664
msgid "添加充值地址错误"
msgstr ""

#: app/exceptions/legacy.py:670
msgid "卡号已经被其他账号绑定"
msgstr ""

#: app/exceptions/legacy.py:676
msgid "支行信息错误。"
msgstr ""

#: app/exceptions/legacy.py:682
msgid "银行不支持"
msgstr ""

#: app/exceptions/legacy.py:688
msgid "该充值地址未被使用过，无法更换。"
msgstr ""

#: app/exceptions/legacy.py:694
msgid "30天内只能更换一次充值地址"
msgstr ""

#: app/exceptions/legacy.py:700
msgid "Country not support."
msgstr ""

#: app/exceptions/legacy.py:706
msgid "系统繁忙提现失败，请稍后再尝试。"
msgstr ""

#: app/exceptions/legacy.py:736
msgid "限额过高"
msgstr ""

#: app/exceptions/legacy.py:742
msgid "合并深度值错误"
msgstr ""

#: app/exceptions/legacy.py:748
msgid "公告不存在"
msgstr ""

#: app/exceptions/legacy.py:754
msgid "该邀请码不存在"
msgstr ""

#: app/exceptions/legacy.py:760
msgid "Activity does not exist"
msgstr ""

#: app/exceptions/legacy.py:766
msgid "CoinEx大使邀请码错误，请咨询您的大使推荐官再重新填写。"
msgstr ""

#: app/exceptions/legacy.py:772
msgid "类型不存在"
msgstr ""

#: app/exceptions/legacy.py:778
msgid "文章不存在"
msgstr ""

#: app/exceptions/legacy.py:784
msgid "内容太长"
msgstr ""

#: app/exceptions/legacy.py:790
msgid "文件太大"
msgstr ""

#: app/exceptions/legacy.py:796
msgid "文件太小"
msgstr ""

#: app/exceptions/legacy.py:802
msgid "超出投票上限"
msgstr ""

#: app/exceptions/legacy.py:808
msgid "投票失败"
msgstr ""

#: app/exceptions/legacy.py:814
msgid "项目已存在"
msgstr ""

#: app/exceptions/legacy.py:820 app/exceptions/legacy.py:826
msgid "Project does not exist"
msgstr ""

#: app/exceptions/legacy.py:832
msgid "Project does not start"
msgstr ""

#: app/exceptions/legacy.py:838
msgid "Project has finished"
msgstr ""

#: app/exceptions/legacy.py:844
msgid "Coin type is error"
msgstr ""

#: app/exceptions/legacy.py:850 app/exceptions/legacy.py:862
msgid "超出项目可兑换最大额"
msgstr ""

#: app/exceptions/legacy.py:856
msgid "未达到单笔兑换最小额"
msgstr ""

#: app/exceptions/legacy.py:868
msgid "Price is not match"
msgstr ""

#: app/exceptions/legacy.py:874
msgid "Sorry, not yet available for project purchase"
msgstr ""

#: app/exceptions/legacy.py:880
msgid "Over KYC can join"
msgstr ""

#: app/exceptions/legacy.py:886
msgid "Complete test can join"
msgstr ""

#: app/exceptions/legacy.py:892
msgid "杠杆交易对未找到"
msgstr ""

#: app/exceptions/legacy.py:898
msgid "杠杆市场未开启"
msgstr ""

#: app/exceptions/legacy.py:904 app/exceptions/legacy.py:1090
msgid "借币订单未找到"
msgstr ""

#: app/exceptions/legacy.py:910 app/exceptions/legacy.py:1114
msgid "用户未同意协议"
msgstr ""

#: app/exceptions/legacy.py:916 app/exceptions/legacy.py:1120
msgid "抱歉，强平过程中禁止还币"
msgstr ""

#: app/exceptions/legacy.py:922 app/exceptions/legacy.py:1126
msgid "抱歉，可还数量超过限制"
msgstr ""

#: app/exceptions/legacy.py:928 app/exceptions/legacy.py:1132
msgid "您的可还数量需要大于零"
msgstr ""

#: app/exceptions/legacy.py:934
msgid "指数价格有误"
msgstr ""

#: app/exceptions/legacy.py:940
msgid "获取指数价格失败"
msgstr ""

#: app/exceptions/legacy.py:946 app/exceptions/legacy.py:1066
msgid "获取账户余额失败"
msgstr ""

#: app/exceptions/legacy.py:952
msgid "错误，还币失败"
msgstr ""

#: app/exceptions/legacy.py:958 app/exceptions/legacy.py:1060
msgid "抱歉，您的账户禁止借币"
msgstr ""

#: app/exceptions/legacy.py:964 app/exceptions/legacy.py:1072
msgid "抱歉，可转数量限制"
msgstr ""

#: app/exceptions/legacy.py:970 app/exceptions/legacy.py:1048
msgid "错误，资产划转失败"
msgstr ""

#: app/exceptions/legacy.py:976
msgid "错误，借币失败"
msgstr ""

#: app/exceptions/legacy.py:982 app/exceptions/user.py:480
#, python-format
msgid "低于最小可借数量%(amount)s %(asset)s"
msgstr ""

#: app/exceptions/legacy.py:988 app/exceptions/legacy.py:1108
msgid "抱歉，借币数量超过最大值"
msgstr ""

#: app/exceptions/legacy.py:994
msgid "首笔借币市值不能大于10000USD"
msgstr ""

#: app/exceptions/legacy.py:1042
msgid "期货市场未开启"
msgstr ""

#: app/exceptions/legacy.py:1054
msgid "抱歉，您被禁止资金划转"
msgstr ""

#: app/exceptions/legacy.py:1078
msgid "抱歉，最新价格获取出现错误"
msgstr ""

#: app/exceptions/legacy.py:1084
msgid "抱歉，无法获取最新价格"
msgstr ""

#: app/exceptions/legacy.py:1096
msgid "抱歉，期货还币出现错误"
msgstr ""

#: app/exceptions/legacy.py:1102
msgid "低于最低限额"
msgstr ""

#: app/exceptions/legacy.py:1138
msgid "交割结算中，无法操作"
msgstr ""

#: app/exceptions/legacy.py:1144
msgid "Future market delivery error"
msgstr ""

#: app/exceptions/legacy.py:1150
msgid "option issue amount limit"
msgstr ""

#: app/exceptions/legacy.py:1156
msgid "option issue error"
msgstr ""

#: app/exceptions/legacy.py:1162
msgid "option redeem amount limit"
msgstr ""

#: app/exceptions/legacy.py:1168
msgid "option redeem error"
msgstr ""

#: app/exceptions/legacy.py:1174
msgid "option not agreement"
msgstr ""

#: app/exceptions/legacy.py:1180
msgid "option delivery error"
msgstr ""

#: app/exceptions/legacy.py:1186
msgid "option type error"
msgstr ""

#: app/exceptions/legacy.py:1192
msgid "option market margin percent error"
msgstr ""

#: app/exceptions/legacy.py:1198
msgid "option contract forbidden"
msgstr ""

#: app/exceptions/legacy.py:1204
msgid "option market error"
msgstr ""

#: app/exceptions/legacy.py:1210
msgid "option get lock error"
msgstr ""

#: app/exceptions/legacy.py:1216
msgid "option backup asset error"
msgstr ""

#: app/exceptions/legacy.py:1222
msgid "您的签名验证不通过，请检查输入内容。"
msgstr ""

#: app/exceptions/legacy.py:1228
msgid "绑定地址数量已达到上限。"
msgstr ""

#: app/exceptions/legacy.py:1234
msgid "该地址已被绑定。"
msgstr ""

#: app/exceptions/legacy.py:1240
msgid "only one lottery draw request only at the same time"
msgstr ""

#: app/exceptions/legacy.py:1246
msgid "your lottery remain time not enough"
msgstr ""

#: app/exceptions/legacy.py:1252
msgid "同一个地址一天只能邀请一次"
msgstr ""

#: app/exceptions/legacy.py:1258
msgid "每天最多邀请100次"
msgstr ""

#: app/exceptions/legacy.py:1264
msgid "红包金额输入有误"
msgstr ""

#: app/exceptions/legacy.py:1270
#, python-format
msgid "红包数量不能大于%(count)s"
msgstr ""

#: app/exceptions/legacy.py:1276
msgid "红包额度不足"
msgstr ""

#: app/exceptions/legacy.py:1282
msgid "未绑定二次验证或手机"
msgstr ""

#: app/exceptions/legacy.py:1288
msgid "红包过期或不存在"
msgstr ""

#: app/exceptions/legacy.py:1294
msgid "红包已过期"
msgstr ""

#: app/exceptions/legacy.py:1300
msgid "红包已领完"
msgstr ""

#: app/exceptions/legacy.py:1306
msgid "红包汇率有误"
msgstr ""

#: app/exceptions/legacy.py:1312
msgid "用户已领过此红包"
msgstr ""

#: app/exceptions/legacy.py:1318
msgid "红包数据有误"
msgstr ""

#: app/exceptions/legacy.py:1324
msgid "红包状态错误"
msgstr ""

#: app/exceptions/legacy.py:1330
#, python-format
msgid "今天可发红包额度不足，最多可发%(amount)s %(coin_type)s"
msgstr ""

#: app/exceptions/legacy.py:1336
msgid "余额不足，红包已撤销"
msgstr ""

#: app/exceptions/legacy.py:1342
#, python-format
msgid "操作过于频繁，%(seconds)s秒内只能发送%(count)s次"
msgstr ""

#: app/exceptions/legacy.py:1348
msgid "forbid red packet"
msgstr ""

#: app/exceptions/legacy.py:1354
msgid "Exceeding quota"
msgstr ""

#: app/exceptions/legacy.py:1360
msgid "价格已失效，请重新购买"
msgstr ""

#: app/exceptions/legacy.py:1366
msgid "用户非授信用户"
msgstr ""

#: app/exceptions/legacy.py:1372
msgid "账户余额不足"
msgstr ""

#: app/exceptions/legacy.py:1378
msgid "超过未还数量"
msgstr ""

#: app/exceptions/legacy.py:1384
msgid "输入金额不能小于等于0"
msgstr ""

#: app/exceptions/legacy.py:1390
msgid "授信还币失败"
msgstr ""

#: app/exceptions/not_found.py:41
msgid "暂无法获取币种价格"
msgstr ""

#: app/exceptions/order.py:49
msgid "无法找到订单"
msgstr ""

#: app/exceptions/order.py:50
msgid "错误订单"
msgstr ""

#: app/exceptions/order.py:51 app/exceptions/perpetual.py:122
msgid "低于最小下单数量"
msgstr ""

#: app/exceptions/order.py:52
msgid "金额过低"
msgstr ""

#: app/exceptions/order.py:53
msgid "市场未开放"
msgstr ""

#: app/exceptions/order.py:54
msgid "已暂时关闭市价单功能。"
msgstr ""

#: app/exceptions/order.py:55
#, python-format
msgid "预计成交价与最新成交价偏差过大，系统无法下单，可适当降低%(placeholder)s后重试"
msgstr ""

#: app/exceptions/order.py:56
msgid "触发价格需小于当前市场价格"
msgstr ""

#: app/exceptions/order.py:58
msgid "触发价格需大于当前市场价格"
msgstr ""

#: app/exceptions/order.py:60
msgid "系统订单不能取消"
msgstr ""

#: app/exceptions/order.py:61
msgid "竞价模式禁止撤单时间"
msgstr ""

#: app/exceptions/order.py:62
msgid "contract price not match"
msgstr ""

#: app/exceptions/order.py:63
#, python-format
msgid "预计卖出价低于当前周期最低卖出价%(rate)s，可适当降低数量后重试"
msgstr ""

#: app/exceptions/order.py:65
#, python-format
msgid "预计买入价高于当前周期最高买入价%(rate)s，可适当降低数量后重试"
msgstr ""

#: app/exceptions/order.py:67
#, python-format
msgid "预计成交价与最新成交价偏差大于%(rate)s，可适当降低委托数后重试"
msgstr ""

#: app/exceptions/order.py:69 app/exceptions/order.py:75
#, python-format
msgid "委托价与指数价偏差大于%(rate)s，可适当调整委托价后重试"
msgstr ""

#: app/exceptions/order.py:71 app/exceptions/order.py:86
#, python-format
msgid "当前周期最高买入价为%(rate)s，可适当调整委托价后重试"
msgstr ""

#: app/exceptions/order.py:73 app/exceptions/order.py:88
#, python-format
msgid "当前周期最低卖出价为%(rate)s，可适当调整委托价后重试"
msgstr ""

#: app/exceptions/order.py:77
#, python-format
msgid "委托价与触发价偏差大于%(rate)s，可适当调整委托价后重试"
msgstr ""

#: app/exceptions/order.py:80 app/exceptions/order.py:82
msgid "当前盘口深度较低，可适当降低数量后重试"
msgstr ""

#: app/exceptions/order.py:84
msgid "当前盘口深度较低，可适当降低委托数后重试"
msgstr ""

#: app/exceptions/order.py:90 app/exceptions/order.py:93
#, python-format
msgid "预计成交价与指数价偏差大于%(rate)s，可适当降低数量后重试"
msgstr ""

#: app/exceptions/order.py:96
msgid "订单委托失败，但是自动借币订单已生成，请知晓。"
msgstr ""

#: app/exceptions/order.py:99 app/exceptions/order.py:101
#: app/exceptions/perpetual.py:158
msgid "当前为保护期，只允许下Maker Only限价单和撤单"
msgstr ""

#: app/exceptions/order.py:100
msgid "该市场暂停服务"
msgstr ""

#: app/exceptions/order.py:116
msgid "该订单无法完全成交，已取消。"
msgstr ""

#: app/exceptions/order.py:121
msgid "该订单无法只做Maker，已取消。"
msgstr ""

#: app/exceptions/order.py:126
#, python-format
msgid "至少提供价值%(amount)s USD的资产"
msgstr ""

#: app/exceptions/order.py:131
msgid "流动性被锁定，不能提取。"
msgstr ""

#: app/exceptions/order.py:136
msgid "流动性不足"
msgstr ""

#: app/exceptions/order.py:141
msgid "价格波动太大，增加流动性失败，请刷新后重试！"
msgstr ""

#: app/exceptions/order.py:146
msgid "Adding or removing liquidity is currently not allowed"
msgstr ""

#: app/exceptions/order.py:151
#, python-format
msgid "计划委托订单数量超过限制，每个市场最多%(max_count)s个"
msgstr ""

#: app/exceptions/order.py:157
msgid "禁止提取"
msgstr ""

#: app/exceptions/p2p.py:98
msgid "未开通P2P业务"
msgstr ""

#: app/exceptions/p2p.py:99
msgid "未开通P2P商家业务"
msgstr ""

#: app/exceptions/p2p.py:100
msgid "你已经开通P2P业务"
msgstr ""

#: app/exceptions/p2p.py:101
msgid "你已经开通P2P商家权限"
msgstr ""

#: app/exceptions/p2p.py:102
msgid "昵称重复，提交失败"
msgstr ""

#: app/exceptions/p2p.py:103
msgid "未完成高级kyc审核"
msgstr ""

#: app/exceptions/p2p.py:104
msgid "当前币种不支持P2P交易"
msgstr ""

#: app/exceptions/p2p.py:105
msgid "当前法币不支持P2P交易"
msgstr ""

#: app/exceptions/p2p.py:106
msgid "禁止P2P交易"
msgstr ""

#: app/exceptions/p2p.py:107
msgid "商家已被冻结"
msgstr ""

#: app/exceptions/p2p.py:108
msgid "商家已休息"
msgstr ""

#: app/exceptions/p2p.py:110
msgid "支付渠道已失效，请重新配置"
msgstr ""

#: app/exceptions/p2p.py:111 app/models/mongo/p2p/advertising.py:26
msgid "取消订单数量达到单日上限"
msgstr ""

#: app/exceptions/p2p.py:112 app/models/mongo/p2p/advertising.py:27
msgid "处理中的申诉订单数量达到上限"
msgstr ""

#: app/exceptions/p2p.py:113
msgid "昵称不合法，提交失败"
msgstr ""

#: app/exceptions/p2p.py:114
msgid "您还不是商家，请重新申请成为商家后重试"
msgstr ""

#: app/exceptions/p2p.py:115
msgid "商家保证金不足，缴纳后恢复发布广告权限"
msgstr ""

#: app/exceptions/p2p.py:116
msgid "已划转保证金"
msgstr ""

#: app/exceptions/p2p.py:117
msgid "商家保证金扣款失败，请重试"
msgstr ""

#: app/exceptions/p2p.py:118
msgid "暂不支持申请成为商家，请稍后重试或联系客服"
msgstr ""

#: app/exceptions/p2p.py:119
msgid "保证金存在待审核记录，请稍后重试"
msgstr ""

#: app/exceptions/p2p.py:120
msgid "现货账户余额不满足保证金要求"
msgstr ""

#: app/exceptions/p2p.py:121
msgid "初级KYC身份异常，请工单联系客服"
msgstr ""

#: app/exceptions/p2p.py:122
msgid "高级KYC身份异常，请工单联系客服"
msgstr ""

#: app/exceptions/p2p.py:123
msgid "机构认证状态异常，请工单联系客服"
msgstr ""

#: app/exceptions/p2p.py:124
msgid "当前账号未绑定2FA，请前往安全设置进行绑定后重试"
msgstr ""

#: app/exceptions/p2p.py:125
msgid "当前账号权限异常，请工单联系客服"
msgstr ""

#: app/exceptions/p2p.py:126
msgid "余额不足，保证金退还失败"
msgstr ""

#: app/exceptions/p2p.py:128
msgid "单笔限额数量不符合法币的最大最小限额"
msgstr ""

#: app/exceptions/p2p.py:129
msgid "单笔限额数量不符合交易区最大最小限额"
msgstr ""

#: app/exceptions/p2p.py:130
msgid "库存数量不符合的要求"
msgstr ""

#: app/exceptions/p2p.py:131
msgid "库存数量应大于单笔限额最大值"
msgstr ""

#: app/exceptions/p2p.py:132
msgid "广告暂未生效"
msgstr ""

#: app/exceptions/p2p.py:133
msgid "不能和自己的广告交易"
msgstr ""

#: app/exceptions/p2p.py:134
msgid "不满足商家广告要求"
msgstr ""

#: app/exceptions/p2p.py:135
msgid "广告不支持此支付渠道，请刷新页面"
msgstr ""

#: app/exceptions/p2p.py:136
msgid "价格数据已更新，请刷新页面"
msgstr ""

#: app/exceptions/p2p.py:137
msgid "未满足广告最小下单金额"
msgstr ""

#: app/exceptions/p2p.py:138
msgid "超过广告最大下单金额"
msgstr ""

#: app/exceptions/p2p.py:139
msgid "数字货币已下架，请刷新页面"
msgstr ""

#: app/exceptions/p2p.py:140
msgid "法币已下架，请刷新页面"
msgstr ""

#: app/exceptions/p2p.py:141
msgid "数字货币数量错误"
msgstr ""

#: app/exceptions/p2p.py:142
msgid "法币数量错误"
msgstr ""

#: app/exceptions/p2p.py:143
msgid "价格比例错误"
msgstr ""

#: app/exceptions/p2p.py:144
msgid "暂无法买币，如需更多帮助请提交工单咨询"
msgstr ""

#: app/exceptions/p2p.py:145
msgid "暂无法卖币，如需更多帮助请提交工单咨询"
msgstr ""

#: app/exceptions/p2p.py:146
msgid "暂无法发布买单广告，如需更多帮助请提交工单咨询"
msgstr ""

#: app/exceptions/p2p.py:147
msgid "暂无法发布卖单广告，如需更多帮助请提交工单咨询"
msgstr ""

#: app/exceptions/p2p.py:148
msgid "您输入的库存数量超过限制"
msgstr ""

#: app/exceptions/p2p.py:149
msgid "当前的广告已下架"
msgstr ""

#: app/exceptions/p2p.py:150
msgid "当前的广告已上架"
msgstr ""

#: app/exceptions/p2p.py:151
msgid "法币发生变更，请刷新页面"
msgstr ""

#: app/exceptions/p2p.py:152
msgid "广告信息已发生变更，请刷新页面后重试"
msgstr ""

#: app/exceptions/p2p.py:153
msgid "广告单价格低于最低发布价格，请调高"
msgstr ""

#: app/exceptions/p2p.py:154
msgid "广告单价格超出最高发布价格，请调低"
msgstr ""

#: app/exceptions/p2p.py:157
msgid "该广告库存不足，暂时无法下单"
msgstr ""

#: app/exceptions/p2p.py:158
msgid "商家进行中的订单达到上限，请重新选择"
msgstr ""

#: app/exceptions/p2p.py:159
msgid "已达到当日取消订单上限，暂时无法下单"
msgstr ""

#: app/exceptions/p2p.py:160
msgid "广告状态异常，暂时无法下单"
msgstr ""

#: app/exceptions/p2p.py:161
msgid "放币失败，请稍后重试"
msgstr ""

#: app/exceptions/p2p.py:162
msgid "订单申诉中，无法放币"
msgstr ""

#: app/exceptions/p2p.py:163
msgid "已达到当日取消订单上限，暂时无法取消"
msgstr ""

#: app/exceptions/p2p.py:164
msgid "接单超时"
msgstr ""

#: app/exceptions/p2p.py:165
msgid "付款超时"
msgstr ""

#: app/exceptions/p2p.py:166 app/exceptions/p2p.py:167
msgid "该商家的广告无法下单"
msgstr ""

#: app/exceptions/p2p.py:168
msgid "暂无法接单，如需更多帮助请提交工单咨询"
msgstr ""

#: app/exceptions/p2p.py:169
msgid "暂无法放币，如需更多帮助请提交工单咨询"
msgstr ""

#: app/exceptions/p2p.py:170
msgid "该订单无法放币，如需更多帮助请提交工单咨询"
msgstr ""

#: app/exceptions/p2p.py:171
msgid "不满足交易条件，提交订单失败"
msgstr ""

#: app/exceptions/p2p.py:173
msgid "未接单不允许创建申诉"
msgstr ""

#: app/exceptions/p2p.py:174
msgid "订单申诉中"
msgstr ""

#: app/exceptions/p2p.py:175
msgid "请联系客服重新打开申诉"
msgstr ""

#: app/exceptions/p2p.py:176
msgid "对方发起了申诉，请在申诉详情中联系客服"
msgstr ""

#: app/exceptions/p2p.py:177
msgid "申诉详情已更新，请刷新页面重试"
msgstr ""

#: app/exceptions/p2p.py:178
msgid "暂无法交易，如需更多帮助请提交工单咨询"
msgstr ""

#: app/exceptions/p2p.py:179
msgid "暂无法放币，请稍等"
msgstr ""

#: app/exceptions/p2p.py:180
msgid "订单状态已更新，请刷新重试"
msgstr ""

#: app/exceptions/p2p.py:194
msgid "发布广告数量达到限制"
msgstr ""

#: app/exceptions/p2p.py:199
#, python-format
msgid "一个交易市场单方向只能发布%(count)s个广告"
msgstr ""

#: app/exceptions/p2p.py:205
msgid "生效中的广告达到限制"
msgstr ""

#: app/exceptions/p2p.py:210
#, python-format
msgid "最多只能生效%(count)s个广告"
msgstr ""

#: app/exceptions/p2p.py:216
msgid "P2P权限异常"
msgstr ""

#: app/exceptions/p2p.py:225
#, python-format
msgid "当前进行中的订单数量%(count)s单，已超过进行中订单的数量限制，请完成进行中的订单"
msgstr ""

#: app/exceptions/p2p.py:231
msgid "交易区失效"
msgstr ""

#: app/exceptions/p2p.py:236
#, python-format
msgid "交易区 %(asset)s 暂不支持"
msgstr ""

#: app/exceptions/p2p.py:242
msgid "法币失效"
msgstr ""

#: app/exceptions/p2p.py:247
#, python-format
msgid "法币 %(fiat)s 暂不支持"
msgstr ""

#: app/exceptions/p2p.py:253 app/models/mongo/p2p/advertising.py:25
msgid "支付渠道失效"
msgstr ""

#: app/exceptions/p2p.py:258
#, python-format
msgid "支付渠道 %(channel_names)s 失效"
msgstr ""

#: app/exceptions/p2p.py:264
msgid "法币支付渠道不支持"
msgstr ""

#: app/exceptions/p2p.py:270
#, python-format
msgid "法币 %(fiat)s 不支持 %(channel_names)s 支付渠道"
msgstr ""

#: app/exceptions/p2p.py:277
msgid "修改安全工具后24小时无法进行p2p卖币"
msgstr ""

#: app/exceptions/p2p.py:283
msgid "修改提现密码后24小时无法进行p2p卖币"
msgstr ""

#: app/exceptions/p2p.py:288
msgid "库存不足"
msgstr ""

#: app/exceptions/p2p.py:294
#, python-format
msgid "数量应大于单笔限额：%(stock_amount)s %(base)s"
msgstr ""

#: app/exceptions/perpetual.py:82
msgid "划转资金失败"
msgstr ""

#: app/exceptions/perpetual.py:87
msgid "无效参数"
msgstr ""

#: app/exceptions/perpetual.py:90
msgid "该方法不存在"
msgstr ""

#: app/exceptions/perpetual.py:91
msgid "需要登录"
msgstr ""

#: app/exceptions/perpetual.py:92
msgid "内部错误"
msgstr ""

#: app/exceptions/perpetual.py:93
msgid "该市场不存在"
msgstr ""

#: app/exceptions/perpetual.py:94
msgid "该用户不存在"
msgstr ""

#: app/exceptions/perpetual.py:95
msgid "该笔委托不存在"
msgstr ""

#: app/exceptions/perpetual.py:99
msgid "该币种不存在"
msgstr ""

#: app/exceptions/perpetual.py:100
msgid "重复更新资产"
msgstr ""

#: app/exceptions/perpetual.py:101
msgid "委托单数量超过当前杠杆最高可开数量"
msgstr ""

#: app/exceptions/perpetual.py:102
msgid "余额不足"
msgstr ""

#: app/exceptions/perpetual.py:103
msgid "没有对手委托单导致委托失败"
msgstr ""

#: app/exceptions/perpetual.py:104
msgid "超过最大额度限制"
msgstr ""

#: app/exceptions/perpetual.py:105
msgid "与该用户不匹配"
msgstr ""

#: app/exceptions/perpetual.py:106
msgid "非法的杠杆"
msgstr ""

#: app/exceptions/perpetual.py:107
msgid "非法的下单价格"
msgstr ""

#: app/exceptions/perpetual.py:108
msgid "仓位正在强平"
msgstr ""

#: app/exceptions/perpetual.py:109
msgid "“完全成交或取消”订单委托失败"
msgstr ""

#: app/exceptions/perpetual.py:110
msgid "委托失败,该笔委托将导致仓位被强平"
msgstr ""

#: app/exceptions/perpetual.py:111
msgid "多仓不能以低于强平价发起委托"
msgstr ""

#: app/exceptions/perpetual.py:112
msgid "空仓不能以高于强平价发起委托"
msgstr ""

#: app/exceptions/perpetual.py:114
msgid "仓位不存在"
msgstr ""

#: app/exceptions/perpetual.py:115
msgid "全仓不能调整保证金"
msgstr ""

#: app/exceptions/perpetual.py:116
msgid "当前市场存在委托挂单，不支持调整仓位设置"
msgstr ""

#: app/exceptions/perpetual.py:118
msgid "未实现亏损超过追加保证金,不能减少保证金"
msgstr ""

#: app/exceptions/perpetual.py:119
msgid "不能减少该数额保证金"
msgstr ""

#: app/exceptions/perpetual.py:120
msgid "触发价不可等于最新指数价格"
msgstr ""

#: app/exceptions/perpetual.py:121
msgid "触发价不可等于最新标记价格"
msgstr ""

#: app/exceptions/perpetual.py:123
msgid "下单失败,最小价格粒度为0.5"
msgstr ""

#: app/exceptions/perpetual.py:124
msgid "“只做Maker”订单委托失败"
msgstr ""

#: app/exceptions/perpetual.py:125
msgid "禁止交易"
msgstr ""

#: app/exceptions/perpetual.py:126
msgid "平仓策略执行中，暂时无法下单，请稍后重试"
msgstr ""

#: app/exceptions/perpetual.py:127
msgid "平仓数量不得超过当前仓位最大可平数量"
msgstr ""

#: app/exceptions/perpetual.py:128
msgid "止损价格不得低于强平价格"
msgstr ""

#: app/exceptions/perpetual.py:129
msgid "止损价格不得高于当前价格"
msgstr ""

#: app/exceptions/perpetual.py:130
msgid "止盈价格不得低于当前价格"
msgstr ""

#: app/exceptions/perpetual.py:131
msgid "止盈价格不得高于当前价格"
msgstr ""

#: app/exceptions/perpetual.py:132
msgid "止损价格不得高于强平价格"
msgstr ""

#: app/exceptions/perpetual.py:133
msgid "止损价格不得低于当前价格"
msgstr ""

#: app/exceptions/perpetual.py:134
msgid "降档减仓中，暂不可操作"
msgstr ""

#: app/exceptions/perpetual.py:135
msgid "系统委托价低于强平价，暂无法平仓"
msgstr ""

#: app/exceptions/perpetual.py:136
msgid "系统委托价高于强平价，暂无法平仓"
msgstr ""

#: app/exceptions/perpetual.py:137
msgid "委托失败，平仓委托订单过多，请撤销部分已有订单后再委托"
msgstr ""

#: app/exceptions/perpetual.py:140
msgid "止盈价格应高于限价委托价格"
msgstr ""

#: app/exceptions/perpetual.py:141
msgid "止盈价格应高于市场最新成交价"
msgstr ""

#: app/exceptions/perpetual.py:142
msgid "止盈价格应高于该计划委托的触发价"
msgstr ""

#: app/exceptions/perpetual.py:143
msgid "止盈价格应低于限价委托价格"
msgstr ""

#: app/exceptions/perpetual.py:144
msgid "止盈价格应低于市场最新成交价"
msgstr ""

#: app/exceptions/perpetual.py:145
msgid "止盈价格应低于该计划委托的触发价"
msgstr ""

#: app/exceptions/perpetual.py:146
msgid "止损价格应高于限价委托价格"
msgstr ""

#: app/exceptions/perpetual.py:148
msgid "止损价格应高于市场最新成交价"
msgstr ""

#: app/exceptions/perpetual.py:150
msgid "止损价格应高于该计划委托的触发价"
msgstr ""

#: app/exceptions/perpetual.py:152
msgid "止损价格应低于限价委托价格"
msgstr ""

#: app/exceptions/perpetual.py:154
msgid "止损价格应低于市场最新成交价"
msgstr ""

#: app/exceptions/perpetual.py:156
msgid "止损价格应低于该计划委托的触发价"
msgstr ""

#: app/exceptions/user.py:10
msgid "用户名或密码错误"
msgstr ""

#: app/exceptions/user.py:20
#, python-format
msgid "用户名或密码错误，还有%(count)s次机会。"
msgstr ""

#: app/exceptions/user.py:27
msgid "提现密码输入有误"
msgstr ""

#: app/exceptions/user.py:34
#, python-format
msgid "提现密码输入有误，你还有%(count)s次机会。"
msgstr ""

#: app/exceptions/user.py:41
msgid "交易密码输入有误"
msgstr ""

#: app/exceptions/user.py:48
#, python-format
msgid "交易密码输入有误，你还有%(count)s次机会。"
msgstr ""

#: app/exceptions/user.py:55
msgid "提现密码已存在"
msgstr ""

#: app/exceptions/user.py:61
msgid "交易密码已存在"
msgstr ""

#: app/exceptions/user.py:65 app/exceptions/user.py:70
msgid "请设置6位数字密码"
msgstr ""

#: app/exceptions/user.py:75
msgid "你已开启提现密码，请升级到最新版本后继续操作"
msgstr ""

#: app/exceptions/user.py:80
msgid "请前往WEB下单或在WEB端关闭交易密码"
msgstr ""

#: app/exceptions/user.py:85
msgid "账户名不得超出20个字符"
msgstr ""

#: app/exceptions/user.py:90
msgid "账户名已存在"
msgstr ""

#: app/exceptions/user.py:96
msgid "操作太频繁"
msgstr ""

#: app/exceptions/user.py:102
msgid "邮箱已存在"
msgstr ""

#: app/exceptions/user.py:108
msgid "邮箱不存在"
msgstr ""

#: app/exceptions/user.py:114
msgid "手机号已经使用"
msgstr ""

#: app/exceptions/user.py:120 app/models/security.py:96
msgid "不能使用该邮箱"
msgstr ""

#: app/exceptions/user.py:126
msgid "该账号已注销"
msgstr ""

#: app/exceptions/user.py:131
msgid "账号注销失败"
msgstr ""

#: app/exceptions/user.py:137
msgid "二维码已失效"
msgstr ""

#: app/exceptions/user.py:143
msgid "推荐码重复，请重新设置。"
msgstr ""

#: app/exceptions/user.py:148
msgid "The account has been bound by another user."
msgstr ""

#: app/exceptions/user.py:153
msgid "密码至少10位字符，必须包含1位大写字母及1位数字"
msgstr ""

#: app/exceptions/user.py:158
#, python-format
msgid "单个市场的价格提醒上限为%(count)s个，目前已达上限。"
msgstr ""

#: app/exceptions/user.py:163
#, python-format
msgid "全部市场的价格提醒上限为%(count)s个，目前已达上限。"
msgstr ""

#: app/exceptions/user.py:168
msgid "登录超时"
msgstr ""

#: app/exceptions/user.py:174
#, python-format
msgid "超出数量限制，最多只能添加%(num)s个子账号"
msgstr ""

#: app/exceptions/user.py:179
msgid "当前你已有使用中的卡券，交易达标或到期后才可以使用新的卡券。"
msgstr ""

#: app/exceptions/user.py:184
msgid "兑换失败，兑换码不存在"
msgstr ""

#: app/exceptions/user.py:189
msgid "兑换失败，兑换码已过期"
msgstr ""

#: app/exceptions/user.py:194
msgid "兑换失败，当前账号已领取此兑换券"
msgstr ""

#: app/exceptions/user.py:199
msgid "兑换失败，不在兑换时间范围内"
msgstr ""

#: app/exceptions/user.py:204
msgid "兑换失败，兑换券已抢光"
msgstr ""

#: app/exceptions/user.py:209
msgid "当前卡券已经被领取"
msgstr ""

#: app/exceptions/user.py:214
msgid "抱歉，已超出领取有效期啦～"
msgstr ""

#: app/exceptions/user.py:219
msgid "抱歉，卡券被抢光了。"
msgstr ""

#: app/exceptions/user.py:224
msgid "你已经有一张加息券正在使用中，到期后才可以继续使用新的理财加息券"
msgstr ""

#: app/exceptions/user.py:229
msgid "卡券已过期"
msgstr ""

#: app/exceptions/user.py:234
msgid "卡券激活失败，请刷新后重试"
msgstr ""

#: app/exceptions/user.py:239
msgid "卡券已失效"
msgstr ""

#: app/exceptions/user.py:244
msgid "活动期间注册用户才可领取卡券。"
msgstr ""

#: app/exceptions/user.py:249
msgid "活动期间首次充值用户才可领取卡券。(站内转账、空投等不符合领取条件)"
msgstr ""

#: app/exceptions/user.py:254
msgid "活动期间首次交易(兑换、币币、合约)用户才可领取卡券。"
msgstr ""

#: app/exceptions/user.py:259
msgid "领取失败"
msgstr ""

#: app/exceptions/user.py:264
msgid "您已有使用中的合约补贴金，卡券使用完毕或到期后才可以继续激活新的合约补贴金。"
msgstr ""

#: app/exceptions/user.py:269
msgid "抱歉，由于风险控制，你不具备领取资格"
msgstr ""

#: app/exceptions/user.py:275
msgid "API续期请求已失效"
msgstr ""

#: app/exceptions/user.py:281
#, python-format
msgid "最多只能添加%(num)s个提现地址"
msgstr ""

#: app/exceptions/user.py:287
#, python-format
msgid "最多只能添加%(num)s个提现审核人"
msgstr ""

#: app/exceptions/user.py:293
msgid "超出导出次数限制"
msgstr ""

#: app/exceptions/user.py:298
msgid "盲盒已抢光"
msgstr ""

#: app/exceptions/user.py:303
msgid "此盲盒已打开，无法重复打开"
msgstr ""

#: app/exceptions/user.py:308
msgid "盲盒2选1，仅可开启一个盲盒"
msgstr ""

#: app/exceptions/user.py:313
msgid "已触发风控规则，无法领取盲盒"
msgstr ""

#: app/exceptions/user.py:319
msgid "当前子账号存在授权关系，禁用前请先解除授权。"
msgstr ""

#: app/exceptions/user.py:327
msgid "当前子账号无杠杆交易权限"
msgstr ""

#: app/exceptions/user.py:328
msgid "当前子账号无合约交易权限"
msgstr ""

#: app/exceptions/user.py:329
msgid "当前子账号无参与做市权限"
msgstr ""

#: app/exceptions/user.py:330
msgid "当前子账号无API管理权限"
msgstr ""

#: app/exceptions/user.py:341
#, python-format
msgid "超过授权上限，1个子账号最多可授权%(num)s个账号共同管理，请先解除部分生效中的授权，再新增授权"
msgstr ""

#: app/exceptions/user.py:347
msgid "当前子账号存在授权关系，修改账号名前请先解除授权。"
msgstr ""

#: app/exceptions/user.py:353
msgid "该口令不可用"
msgstr ""

#: app/exceptions/user.py:359
msgid "禁止发送C-Box"
msgstr ""

#: app/exceptions/user.py:365
msgid "该币种不支持发送C-Box"
msgstr ""

#: app/exceptions/user.py:371
#, python-format
msgid "C-Box数量不能大于%(count)s"
msgstr ""

#: app/exceptions/user.py:377
#, python-format
msgid "单个C-Box金额不能小于%(per_amount)s%(coin_type)s"
msgstr ""

#: app/exceptions/user.py:383
#, python-format
msgid "C-Box每日发送金额上限为%(limit_usd)sUSD，今日已发送%(send_usd)sUSD，超出金额请明日再发。"
msgstr ""

#: app/exceptions/user.py:389
#, python-format
msgid "口令错误，已输错%(fail_times)s次，再输错%(remain_times)s次将禁止输入口令%(failure_ttl_hour)s小时"
msgstr ""

#: app/exceptions/user.py:396
msgid "C-Box已过期"
msgstr ""

#: app/exceptions/user.py:402
msgid "用户已领过此C-Box"
msgstr ""

#: app/exceptions/user.py:408
msgid "您已经是CoinEx用户，该C-Box仅限新用户领取"
msgstr ""

#: app/exceptions/user.py:414
msgid "C-Box已领完"
msgstr ""

#: app/exceptions/user.py:420
msgid "C-Box状态错误"
msgstr ""

#: app/exceptions/user.py:426
msgid "C-Box过期或不存在"
msgstr ""

#: app/exceptions/user.py:432
msgid "C-Box汇率有误"
msgstr ""

#: app/exceptions/user.py:438
msgid "该账号未注册"
msgstr ""

#: app/exceptions/user.py:444
#, python-format
msgid "该邮箱尚未绑定%(source)s账户，暂无法支持快捷登录"
msgstr ""

#: app/exceptions/user.py:450
#, python-format
msgid "该%(source)s账户已被其他CoinEx账号绑定"
msgstr ""

#: app/exceptions/user.py:456
msgid "需要设置账户密码后解绑"
msgstr ""

#: app/exceptions/user.py:461
#, python-format
msgid "CoinEx账号已绑定其他%(source)s账号，无法重复绑定"
msgstr ""

#: app/exceptions/user.py:465
msgid "不符合要求，领取失败，详情请联系客服。"
msgstr ""

#: app/exceptions/user.py:470
msgid "该账号无法领取"
msgstr ""

#: app/exceptions/user.py:475
msgid "操作失败"
msgstr ""

#: app/exceptions/user.py:485
#, python-format
msgid "最多可借%(amount)s %(asset)s"
msgstr ""

#: app/exceptions/user.py:490
#, python-format
msgid "最多可减少%(amount)s %(asset)s"
msgstr ""

#: app/exceptions/user.py:495
msgid "你已经是KOL"
msgstr ""

#: app/exceptions/user.py:500
msgid "你已经提交过KOL申请"
msgstr ""

#: app/exceptions/user.py:505
msgid "基于CoinEx的AML政策，提现隐私币必须通过实名认证，请先完成认证。"
msgstr ""

#: app/exceptions/user.py:510
msgid "基于CoinEx的AML政策，充值隐私币必须通过实名认证，请先完成认证。"
msgstr ""

#: app/exceptions/user.py:515
msgid "当前币种没有价格，只允许设置该币种作为提现手续费。"
msgstr ""

#: app/exceptions/user.py:520
msgid "已绑定邀请人"
msgstr ""

#: app/exceptions/user.py:525
msgid "已超时，不允许绑定"
msgstr ""

#: app/exceptions/user.py:530 app/models/kyc.py:198 app/models/user.py:1466
msgid "由于监管要求，无法为你提供更多服务"
msgstr ""

#: app/exceptions/user.py:535
msgid "此交易员跟单人数已达上限，请稍后再进行跟单"
msgstr ""

#: app/exceptions/user.py:540
#, python-format
msgid "由于你近期已修改昵称，请于%(days)s天后再试"
msgstr ""

#: app/exceptions/user.py:545
msgid "超过每天开仓次数上限，请明天再试"
msgstr ""

#: app/exceptions/user.py:550
msgid "带单生效时，请先关闭生效中的仓位才能切换保证金模式"
msgstr ""

#: app/exceptions/user.py:555
msgid "昵称已被使用，请修改后再提交"
msgstr ""

#: app/exceptions/user.py:560
#, python-format
msgid "由于你最近主动取消带单，请于%(days)s天后重新申请"
msgstr ""

#: app/exceptions/user.py:565
msgid "暂无法使用跟单交易，如需更多帮助请提交工单咨询"
msgstr ""

#: app/exceptions/user.py:570
msgid "此交易员已结束带单，请于交易员重新带单后再进行跟单"
msgstr ""

#: app/exceptions/user.py:575
msgid "交易员不存在"
msgstr ""

#: app/exceptions/user.py:580
msgid "交易员已调整跟单金额范围，请刷新页面"
msgstr ""

#: app/exceptions/user.py:595
#, python-format
msgid "%(id_type)s验证错误次数达到上限，请选择其他证件类型"
msgstr ""

#: app/exceptions/user.py:605
msgid "填写邮箱不可与旧邮箱相同，请使用新邮箱填写"
msgstr ""

#: app/models/academy.py:13 app/models/blog.py:12
msgid "最新"
msgstr ""

#: app/models/activity.py:47
msgid "合约体验金"
msgstr ""

#: app/models/activity.py:48
msgid "交易赠金劵"
msgstr ""

#: app/models/activity.py:49
msgid "理财加息劵"
msgstr ""

#: app/models/activity.py:52
msgid "VIP升级券"
msgstr ""

#: app/models/activity.py:53
msgid "合约跟单体验金"
msgstr ""

#: app/models/activity.py:1236
msgid "策略"
msgstr ""

#: app/models/equity_center.py:67
msgid "任务发放"
msgstr ""

#: app/models/equity_center.py:70
msgid "平台发放"
msgstr ""

#: app/models/kyc.py:95 app/models/user.py:1365
msgid "身份证"
msgstr ""

#: app/models/kyc.py:96 app/models/user.py:1366
msgid "护照"
msgstr ""

#: app/models/message.py:30
msgid "系统消息"
msgstr ""

#: app/models/message.py:31
msgid "充值提现"
msgstr ""

#: app/models/message.py:32
msgid "交易通知"
msgstr ""

#: app/models/message.py:33
msgid "福利动态"
msgstr ""

#: app/models/message.py:34
msgid "账号安全"
msgstr ""

#: app/models/mission_center.py:100
msgid "入金"
msgstr ""

#: app/models/mission_center.py:101
#: app/templates/email/notice/sign_up_success.j2:30
msgid "币币交易"
msgstr ""

#: app/models/mission_center.py:103
msgid "跟单交易"
msgstr ""

#: app/models/mission_center.py:104
msgid "模拟交易"
msgstr ""

#: app/models/operation.py:669
msgid "24小时"
msgstr ""

#: app/models/operation.py:670
msgid "7天"
msgstr ""

#: app/models/operation.py:671
msgid "15天"
msgstr ""

#: app/models/operation.py:672
msgid "30天"
msgstr ""

#: app/models/operation.py:2645
#, python-format
msgid "等级为VIP%(number)s及以上"
msgstr ""

#: app/models/operation.py:2651
msgid "完成KYC认证"
msgstr ""

#: app/models/operation.py:2660
#, python-format
msgid "持有过%(asset)s"
msgstr ""

#: app/models/operation.py:2662
#, python-format
msgid "从未持有过%(asset)s"
msgstr ""

#: app/models/operation.py:2676
#, python-format
msgid "总资产 %(op_type)s %(balance)s USD"
msgstr ""

#: app/models/operation.py:2693
#, python-format
msgid "近%(trade_day_range)s天累计交易额 %(op_type)s %(trade_value)s USD"
msgstr ""

#: app/models/operation.py:2698
#, python-format
msgid "活动开始前近%(trade_day_range)s天累计交易额 %(op_type)s %(trade_value)s USD"
msgstr ""

#: app/models/operation.py:2711
#, python-format
msgid "注册时间在%(registered_time)s（UTC）之后"
msgstr ""

#: app/models/operation.py:2714
#, python-format
msgid "注册时间在%(registered_time)s（UTC）之前"
msgstr ""

#: app/models/operation.py:2726
msgid "法币"
msgstr ""

#: app/models/operation.py:2727
msgid "定投计划"
msgstr ""

#: app/models/operation.py:2730
msgid "AMM"
msgstr ""

#: app/models/operation.py:2731
msgid "质押借币"
msgstr ""

#: app/models/operation.py:2735
#, python-format
msgid "有过：%(used_functions)s"
msgstr ""

#: app/models/p2p.py:30
msgid "商家超时未确认"
msgstr ""

#: app/models/p2p.py:31
msgid "商家拒绝接单"
msgstr ""

#: app/models/p2p.py:32
msgid "用户接单前取消"
msgstr ""

#: app/models/p2p.py:34
msgid "买家超时未付款"
msgstr ""

#: app/models/p2p.py:35
msgid "买方取消"
msgstr ""

#: app/models/p2p.py:36
msgid "客服手动取消"
msgstr ""

#: app/models/p2p.py:38
msgid "系统取消"
msgstr ""

#: app/models/p2p.py:42
msgid "不想交易了"
msgstr ""

#: app/models/p2p.py:43
msgid "信息填写错误"
msgstr ""

#: app/models/p2p.py:44
msgid "误点\"我已付款\""
msgstr ""

#: app/models/p2p.py:45
msgid "其它原因"
msgstr ""

#: app/models/p2p.py:47
msgid "卖家收款方式错误"
msgstr ""

#: app/models/p2p.py:48
msgid "协商一致取消"
msgstr ""

#: app/models/p2p.py:313
msgid "已付款，卖家未放币"
msgstr ""

#: app/models/p2p.py:314
msgid "已付款，但订单已取消"
msgstr ""

#: app/models/p2p.py:315
msgid "转账金额大于订单金额"
msgstr ""

#: app/models/p2p.py:316
msgid "卖家收款方式问题"
msgstr ""

#: app/models/p2p.py:318
msgid "未收到买家付款"
msgstr ""

#: app/models/p2p.py:319
msgid "转账金额与订单金额不一致"
msgstr ""

#: app/models/p2p.py:320
msgid "买方付款方式出现问题"
msgstr ""

#: app/models/p2p.py:322
msgid "其他"
msgstr ""

#: app/models/referral.py:330
msgid "白银"
msgstr ""

#: app/models/referral.py:331
msgid "黄金"
msgstr ""

#: app/models/referral.py:332
msgid "钻石"
msgstr ""

#: app/models/security.py:27
msgid "手机"
msgstr ""

#: app/models/security.py:28
msgid "email"
msgstr ""

#: app/models/security.py:29
msgid "TOTP"
msgstr ""

#: app/models/security.py:30
msgid "提现密码"
msgstr ""

#: app/models/security.py:31
msgid "解冻账户"
msgstr ""

#: app/models/security.py:32
msgid "通行密钥"
msgstr ""

#: app/models/security.py:44
msgid "CoinEx历史邮件截图不符"
msgstr ""

#: app/models/security.py:45 app/models/security.py:105
msgid "第三方平台的历史充提记录截图不符"
msgstr ""

#: app/models/security.py:46
msgid "CoinEx历史邮件截图及第三方充提记录截图均不符"
msgstr ""

#: app/models/security.py:47 app/models/security.py:115 app/models/user.py:1399
msgid "请按要求露脸，并同时手持声明书和证件"
msgstr ""

#: app/models/security.py:48
msgid "邮箱地址或邮件内容未展示完全"
msgstr ""

#: app/models/security.py:49
msgid "邮件显示邮箱与提交资料邮箱不匹配"
msgstr ""

#: app/models/security.py:50
msgid "请提供CoinEx发送的历史邮件，仅限于绑定/重置安全密钥、成功注册/登录记录。"
msgstr ""

#: app/models/security.py:51
msgid "CoinEx历史邮件截图模糊"
msgstr ""

#: app/models/security.py:52 app/models/security.py:108
#: app/models/wallet.py:1364
msgid "请勿上传区块浏览器截图"
msgstr ""

#: app/models/security.py:53 app/models/security.py:107
msgid "第三方平台的历史充提记录不可截取自邮箱"
msgstr ""

#: app/models/security.py:54 app/models/security.py:106
msgid "第三方平台的历史充提记录未包含币种名、数量、时间和TXID"
msgstr ""

#: app/models/security.py:55 app/models/security.py:124
msgid "请前往ViaBTC获取您的充提记录"
msgstr ""

#: app/models/security.py:56 app/models/security.py:121
msgid "第三方充提记录不可来自本人CoinEx账号"
msgstr ""

#: app/models/security.py:57 app/models/security.py:122
msgid "第三方充提记录与CoinEx记录不匹配，请提供与CoinEx关联的充提记录"
msgstr ""

#: app/models/security.py:58
msgid "第三方充提记录截图模糊"
msgstr ""

#: app/models/security.py:59
msgid "请提供完整的充提页面截图"
msgstr ""

#: app/models/security.py:60
msgid "KYC信息不足以验证账户，请重新提交申请并按页面提示补充资料"
msgstr ""

#: app/models/security.py:61 app/models/security.py:102 app/models/user.py:1385
msgid "证件信息模糊"
msgstr ""

#: app/models/security.py:62 app/models/security.py:110
msgid "证件类型不支持"
msgstr ""

#: app/models/security.py:63 app/models/security.py:114
msgid "证件已过期，请换证后再重新提交"
msgstr ""

#: app/models/security.py:64 app/models/security.py:118
msgid "证件已损坏，无法认证，请换证后再重新提交"
msgstr ""

#: app/models/security.py:65 app/models/security.py:119
msgid "上传证件照片非原件，请重新上传"
msgstr ""

#: app/models/security.py:66 app/models/security.py:116
msgid "手持声明书时间有误，请按Year/Month/Day格式重新书写"
msgstr ""

#: app/models/security.py:67 app/models/security.py:117 app/models/user.py:1402
msgid "手持声明书模糊"
msgstr ""

#: app/models/security.py:68 app/models/security.py:103 app/models/user.py:1395
msgid "手持声明书信息不符"
msgstr ""

#: app/models/security.py:69 app/models/security.py:111
msgid "持证人脸部与证件人像照片不符"
msgstr ""

#: app/models/security.py:70 app/models/security.py:120
msgid "照片不可来自截图或打印扫描件"
msgstr ""

#: app/models/security.py:71
msgid "录屏模糊，我们已通过工单联系您，请查看邮件"
msgstr ""

#: app/models/security.py:72
msgid "录屏展示的第三方充提记录不符"
msgstr ""

#: app/models/security.py:73
msgid "录屏展示的第三方充提记录不可取于区块浏览器或邮箱"
msgstr ""

#: app/models/security.py:74
msgid "未使用另一设备录屏"
msgstr ""

#: app/models/security.py:75
msgid "录屏未露出人脸"
msgstr ""

#: app/models/security.py:76
msgid "录屏未展示从设备桌面进入第三方平台的完整操作"
msgstr ""

#: app/models/security.py:77 app/models/security.py:113
msgid "图片信息被篡改或处理过"
msgstr ""

#: app/models/security.py:78 app/models/user.py:1718
msgid "我们已通过工单联系您，请查看邮件"
msgstr ""

#: app/models/security.py:81 app/models/user.py:1924
msgid "未能清楚识别脸部，请重新自拍确保您的脸部清晰可见"
msgstr ""

#: app/models/security.py:82 app/models/user.py:1925
msgid "活体检测失败，请再次尝试或使用其他设备完成"
msgstr ""

#: app/models/security.py:83 app/models/user.py:1926
msgid "请重新自拍确保相机无水印"
msgstr ""

#: app/models/security.py:84 app/models/user.py:1927
msgid "活体检测时请勿让手机出现在画面中"
msgstr ""

#: app/models/security.py:85 app/models/user.py:1928
msgid "请确保活体检测时仅一人出镜"
msgstr ""

#: app/models/security.py:86 app/models/user.py:1929
msgid "检测到可疑行为"
msgstr ""

#: app/models/security.py:87 app/models/user.py:1930
msgid "检测到强制验证行为"
msgstr ""

#: app/models/security.py:88 app/models/user.py:1931
msgid "自拍照与证件照片不符"
msgstr ""

#: app/models/security.py:89 app/models/user.py:1932
msgid "请手持之前上传的身份证件完成自拍，确保身份证件信息清晰可见"
msgstr ""

#: app/models/security.py:90 app/models/user.py:1933
msgid "请确保您的脸部在视频中清晰可见"
msgstr ""

#: app/models/security.py:91 app/models/user.py:1934
msgid "活体检测失败"
msgstr ""

#: app/models/security.py:94
msgid "第三方平台的历史提现记录截图不符合"
msgstr ""

#: app/models/security.py:95
msgid "第三方平台的历史充值记录截图不符"
msgstr ""

#: app/models/security.py:97 app/models/security.py:112
msgid "未按要求同时手持申请书和证件"
msgstr ""

#: app/models/security.py:98 app/models/security.py:104
msgid "持证人照片未露出面部"
msgstr ""

#: app/models/security.py:99
msgid "证件信息不符"
msgstr ""

#: app/models/security.py:109
msgid "KYC信息不足以验证您的账户，请重新提交申请并提供有效验证信息"
msgstr ""

#: app/models/user.py:421
msgid "创建通行密钥超过最大上限"
msgstr ""

#: app/models/user.py:424
msgid "通行密钥已绑定"
msgstr ""

#: app/models/user.py:1326
#, python-format
msgid "请在%(seconds)s秒后再试。"
msgstr ""

#: app/models/user.py:1367
msgid "驾驶证"
msgstr ""

#: app/models/user.py:1382 app/models/user.py:1476
msgid "持证人照片与证件信息不符"
msgstr ""

#: app/models/user.py:1383
msgid "无证件信息"
msgstr ""

#: app/models/user.py:1384
msgid "抱歉，暂不支持该证件类型"
msgstr ""

#: app/models/user.py:1386
msgid "证件已过期，请换证后再认证"
msgstr ""

#: app/models/user.py:1387
msgid "年龄不符"
msgstr ""

#: app/models/user.py:1388 app/models/user.py:1483
msgid "国籍有误，请核对国籍选项"
msgstr ""

#: app/models/user.py:1389
msgid "请勿上传样本证件照，情节严重或触发系统封号"
msgstr ""

#: app/models/user.py:1390
msgid "自拍使用完整证件照"
msgstr ""

#: app/models/user.py:1392
msgid "拍照画面来自打印件、截屏、视频等"
msgstr ""

#: app/models/user.py:1394
msgid "照片未露出面部"
msgstr ""

#: app/models/user.py:1396
msgid "提供的信息有误"
msgstr ""

#: app/models/user.py:1398
msgid "请上传居住国家发行的有效证件"
msgstr ""

#: app/models/user.py:1400
msgid "我们已通过工单联系您，请查看邮箱"
msgstr ""

#: app/models/user.py:1401
msgid "手持声明书时间有误，请按年/月/日格式重新书写"
msgstr ""

#: app/models/user.py:1404
msgid "证件信息被篡改或被处理过，无法认证"
msgstr ""

#: app/models/user.py:1405
msgid "证件照片被篡改或被后期处理过"
msgstr ""

#: app/models/user.py:1406
msgid "证件人可能存在个人信誉问题，情节严重将触发系统封号"
msgstr ""

#: app/models/user.py:1407
msgid "上传证件照片为网络假证"
msgstr ""

#: app/models/user.py:1408
msgid "持证人人脸与证件信息不符"
msgstr ""

#: app/models/user.py:1409
msgid "证件已经失效或损坏，无法认证"
msgstr ""

#: app/models/user.py:1410
msgid "证件信息无法匹配，请重新上传"
msgstr ""

#: app/models/user.py:1411
msgid "请补充提供其他文件"
msgstr ""

#: app/models/user.py:1412
msgid "上传证件照非原件，请重新上传"
msgstr ""

#: app/models/user.py:1413
msgid "证件照模糊，请拍摄清晰的图片"
msgstr ""

#: app/models/user.py:1414 app/models/user.py:1477
msgid "证件照模糊，无法认证"
msgstr ""

#: app/models/user.py:1415 app/models/user.py:1478 app/models/user.py:1479
msgid "证件照片信息被遮挡，请按示例上传"
msgstr ""

#: app/models/user.py:1416
msgid "证件被损坏或证件照拍摄不规范，请按要求重新认证"
msgstr ""

#: app/models/user.py:1417
msgid "填入的信息与证件信息不符，请重新填写"
msgstr ""

#: app/models/user.py:1418 app/models/user.py:1480
msgid "证件照片不符合要求，请按示例上传"
msgstr ""

#: app/models/user.py:1419
msgid "请勿上传样本证件照，情节严重将触发系统封号"
msgstr ""

#: app/models/user.py:1420
msgid "请按要求上传证件正面照"
msgstr ""

#: app/models/user.py:1421
msgid "请按要求上传证件反面照"
msgstr ""

#: app/models/user.py:1422
msgid "请上传完整证件页面"
msgstr ""

#: app/models/user.py:1423
msgid "证件未签名，无法认证"
msgstr ""

#: app/models/user.py:1424
msgid "请勿上传黑白证件照"
msgstr ""

#: app/models/user.py:1425
msgid "证件照片出现水印无效，暂不支持该证件类型"
msgstr ""

#: app/models/user.py:1426
msgid "证件照片被裁剪，无法认证"
msgstr ""

#: app/models/user.py:1427
msgid "和上次完成认证的人员信息不一致"
msgstr ""

#: app/models/user.py:1429
msgid "人脸识别操作不规范，请重试"
msgstr ""

#: app/models/user.py:1430
msgid "人脸识别认证与本人证件照年龄差距过大，无法认证"
msgstr ""

#: app/models/user.py:1431 app/models/user.py:1482
msgid "人脸识别未露出面部，无法认证"
msgstr ""

#: app/models/user.py:1432
msgid "人脸识别像素太低，无法认证"
msgstr ""

#: app/models/user.py:1433
msgid "人脸识别不能用黑白像认证"
msgstr ""

#: app/models/user.py:1434
msgid "人脸识别图像像素太低，无法认证"
msgstr ""

#: app/models/user.py:1435
msgid "人脸识别操作不规范，请重新认证"
msgstr ""

#: app/models/user.py:1436
msgid "视频认证未完成，请重新认证"
msgstr ""

#: app/models/user.py:1437
msgid "不支持的视频认证语言"
msgstr ""

#: app/models/user.py:1439
msgid "平台仅支持18至70周岁的用户进行实名认证"
msgstr ""

#: app/models/user.py:1441
msgid "证件信息上传失败，请重新上传"
msgstr ""

#: app/models/user.py:1443 app/models/user.py:1481
msgid "证件信息无法匹配，请重新上传证件认证"
msgstr ""

#: app/models/user.py:1445
msgid "持证人存在信誉问题，无法认证通过"
msgstr ""

#: app/models/user.py:1447
msgid "人脸检测离摄像头太近，请保持距离重新拍摄认证"
msgstr ""

#: app/models/user.py:1448
msgid "人脸识别头像太小，请重新拍摄认证"
msgstr ""

#: app/models/user.py:1449
msgid "无法获取到人脸信息，请按操作重新认证"
msgstr ""

#: app/models/user.py:1452
msgid "填写的公司地址不存在"
msgstr ""

#: app/models/user.py:1453
msgid "公司信息无法查证"
msgstr ""

#: app/models/user.py:1454
msgid "暂不支持该国籍认证"
msgstr ""

#: app/models/user.py:1455
msgid "注册公司存在违法违规行为，无法认证"
msgstr ""

#: app/models/user.py:1456
msgid "提供的资料有误"
msgstr ""

#: app/models/user.py:1457
msgid "上传的文件信息模糊"
msgstr ""

#: app/models/user.py:1458
msgid "上传的文件非原文件"
msgstr ""

#: app/models/user.py:1459
msgid "上传文件有误"
msgstr ""

#: app/models/user.py:1460
msgid "公司未明确受益人"
msgstr ""

#: app/models/user.py:1461
msgid "公司未明确代表人"
msgstr ""

#: app/models/user.py:1462
msgid "公司未明确股权结构"
msgstr ""

#: app/models/user.py:1463
msgid "经验要求不符"
msgstr ""

#: app/models/user.py:1464
msgid "申请人在接受第三方验证"
msgstr ""

#: app/models/user.py:1468
msgid "地址证明有误"
msgstr ""

#: app/models/user.py:1469
msgid "付款证明有误"
msgstr ""

#: app/models/user.py:1470
msgid "文件地址与用户输入的地址不匹配"
msgstr ""

#: app/models/user.py:1471
msgid "不支持的文件语言"
msgstr ""

#: app/models/user.py:1473 app/models/user.py:1732
msgid "第三方超时未审核"
msgstr ""

#: app/models/user.py:1702
msgid "居住地址信息有误"
msgstr ""

#: app/models/user.py:1703 app/models/user.py:1706
msgid "地址证明文件不符合要求"
msgstr ""

#: app/models/user.py:1704
msgid "由于监管要求，无法为您提供更多服务。"
msgstr ""

#: app/models/user.py:1705
msgid "个人初级认证失效"
msgstr ""

#: app/models/user.py:1707
msgid "居住地址信息填写不完整"
msgstr ""

#: app/models/user.py:1708
msgid "请提供签发日期在三个月内的居住地址证明文件"
msgstr ""

#: app/models/user.py:1709
msgid "填写的居住地址与地址证明文件不匹配"
msgstr ""

#: app/models/user.py:1710
msgid "居住地址证明文件不完整"
msgstr ""

#: app/models/user.py:1711
msgid "居住地址证明文件未包含地址信息"
msgstr ""

#: app/models/user.py:1712
msgid "居住地址证明文件无法打开"
msgstr ""

#: app/models/user.py:1713
msgid "居住地址证明文件上无姓名"
msgstr ""

#: app/models/user.py:1714
msgid "居住地址证明文件模糊"
msgstr ""

#: app/models/user.py:1715
msgid "初级KYC证件为扫描件，请重新认证后提交高级KYC"
msgstr ""

#: app/models/user.py:1716
msgid "初级KYC证件已损坏，请重新认证后提交高级KYC"
msgstr ""

#: app/models/user.py:1717
msgid "初级KYC证件已过期，请重新认证后提交高级KYC"
msgstr ""

#: app/models/user.py:1720
msgid "地址证明文件为截图"
msgstr ""

#: app/models/user.py:1721
msgid "地址证明文件上未展示全名"
msgstr ""

#: app/models/user.py:1722
msgid "地址证明文件上未展示全部地址信息"
msgstr ""

#: app/models/user.py:1723
msgid "地址证明文件上没有签发机构的印章或签名"
msgstr ""

#: app/models/user.py:1724
msgid "之前提供了相同的地址证明文件"
msgstr ""

#: app/models/user.py:1725
msgid "地址证明文件被篡改或被后期处理过"
msgstr ""

#: app/models/user.py:1726
msgid "地址证明文件语言无法识别"
msgstr ""

#: app/models/user.py:1727
msgid "认证次数超过了第三方的限制"
msgstr ""

#: app/models/user.py:1728
msgid "无法通过高级认证"
msgstr ""

#: app/models/user.py:1729
msgid "识别的姓名与用户初级KYC姓名不一致"
msgstr ""

#: app/models/user.py:1730
msgid "身份证件签发国家/地区与地址证明文件不匹配"
msgstr ""

#: app/models/user.py:3603
msgid "合约账户"
msgstr ""

#: app/models/user.py:3604
msgid "杠杆账户"
msgstr ""

#: app/models/wallet.py:1365
msgid "请上传提现已完成的第三方截图，如无法上传，请通过工单/电子邮件与我们联系"
msgstr ""

#: app/models/wallet.py:1366 app/models/wallet.py:1417
msgid "第三方平台的提现记录截图不符，截图需包含名称、金额、日期、TXID、地址、标签（如有）等信息"
msgstr ""

#: app/models/wallet.py:1368
msgid "第三方平台的历史提现记录不可截取自邮箱"
msgstr ""

#: app/models/wallet.py:1369
msgid "第三方提现记录不可来自本人CoinEx账号"
msgstr ""

#: app/models/wallet.py:1370
msgid "第三方提现记录与该笔申请不相关，请提供与CoinEx关联的提现记录"
msgstr ""

#: app/models/wallet.py:1371
msgid "第三方提现记录截图模糊"
msgstr ""

#: app/models/wallet.py:1372
msgid "请提供完整的提现页面截图"
msgstr ""

#: app/models/wallet.py:1374
msgid "充值验证的提现记录截图不符，截图需包含名称、金额、日期、TXID、地址、标签（如有）等信息"
msgstr ""

#: app/models/wallet.py:1376
msgid "请通过工单/电子邮件提供无法填写标签Memo/Tag的凭证"
msgstr ""

#: app/models/wallet.py:1377
msgid "请提供同时包含未到账充值和充值验证在同一页面的截图"
msgstr ""

#: app/models/wallet.py:1379
msgid "未完成指定数量的充值验证"
msgstr ""

#: app/models/wallet.py:1380
msgid "充值验证在申请之前，为无效充值验证"
msgstr ""

#: app/models/wallet.py:1381
msgid "充值验证需从同一平台打出；如已完成此步骤，请额外补充未到账充值和充值验证在同一页面的录屏"
msgstr ""

#: app/models/wallet.py:1383
msgid "充值验证截图不可来自本人CoinEx账号"
msgstr ""

#: app/models/wallet.py:1384
msgid "充值验证的提现记录与该笔申请不相关，请提供与CoinEx关联的提现记录"
msgstr ""

#: app/models/wallet.py:1386
msgid "退回地址格式不符"
msgstr ""

#: app/models/wallet.py:1387
msgid "请提供非CoinEx的收币地址/截图"
msgstr ""

#: app/models/wallet.py:1388
msgid "退回地址的验证截图不符"
msgstr ""

#: app/models/wallet.py:1389
msgid "请提供相同公链的退款地址/截图"
msgstr ""

#: app/models/wallet.py:1390
msgid "请上传您收款平台存款页面的截图，以证明您拥有退款地址。"
msgstr ""

#: app/models/wallet.py:1392
msgid "第三方平台退回凭证不符，录屏需包含名称、金额、日期、充值和退回TXID、地址、标签（如有）等信息"
msgstr ""

#: app/models/wallet.py:1394
msgid "第三方平台客服沟通凭证不符，录屏需包含“充值”和“退回”的TXID"
msgstr ""

#: app/models/wallet.py:1395
msgid "录屏未使用另一部设备露脸拍摄"
msgstr ""

#: app/models/wallet.py:1396
msgid "您提交的录屏画质模糊，无法核验交易信息，请重新提供清晰完整的操作录屏"
msgstr ""

#: app/models/wallet.py:1397
msgid "录屏未展现设备时间或浏览器显示的时间"
msgstr ""

#: app/models/wallet.py:1398
msgid "录屏未刷新页面或未点击TXID展示链上信息"
msgstr ""

#: app/models/wallet.py:1399
msgid "录屏未展示进入充值地址 & memo 页面，并查看退回凭证"
msgstr ""

#: app/models/wallet.py:1400
msgid "录屏未完整展示与对方客服的聊天记录；录屏需展示“充值”和“退回”的TXID"
msgstr ""

#: app/models/wallet.py:1402
msgid "该笔充值已成功入账"
msgstr ""

#: app/models/wallet.py:1403
msgid "该笔充值将在钱包维护完成后自动入账，请耐心等待"
msgstr ""

#: app/models/wallet.py:1404
msgid "未到账充值在充值地址生成之前；请用正确账号提交申请"
msgstr ""

#: app/models/wallet.py:1405
msgid "未生成需找回充值的地址，请用正确账号提交申请"
msgstr ""

#: app/models/wallet.py:1406
msgid "您的充值价值未知，我们不建议您进行资产找回。如仍需找回请通过工单/邮件联系我们"
msgstr ""

#: app/models/wallet.py:1407
msgid "您的未到账充值价值少于找回手续费50U，我们不建议您进行资产找回"
msgstr ""

#: app/models/wallet.py:1408
msgid "该笔充值由于充值至错误的地址，无法找回"
msgstr ""

#: app/models/wallet.py:1410
msgid "图片或录屏信息被篡改或处理过"
msgstr ""

#: app/models/wallet.py:1411
msgid "长时间未提交验证资料"
msgstr ""

#: app/models/wallet.py:1412
msgid "多次额外补充资料不合格；请确认资料合格后重新提交申请"
msgstr ""

#: app/models/wallet.py:1413
msgid "我们已通过工单联系您，请查看工单并提供所需资料"
msgstr ""

#: app/models/wallet.py:1416
#, python-format
msgid "手续费不足，请保证你的现货账户有%(amount)s %(asset)s的资产"
msgstr ""

#: app/models/wallet.py:1419
msgid "退回地址为某平台的热钱包地址，请重新提交"
msgstr ""

#: app/models/mongo/p2p/advertising.py:18
msgid "库存短缺"
msgstr ""

#: app/models/mongo/p2p/advertising.py:19
msgid "触发风控"
msgstr ""

#: app/models/mongo/p2p/advertising.py:20
msgid "高级kyc失效"
msgstr ""

#: app/models/mongo/p2p/advertising.py:21
msgid "机构kyc失效"
msgstr ""

#: app/models/mongo/p2p/advertising.py:22
msgid "商家权限被冻结"
msgstr ""

#: app/models/mongo/p2p/advertising.py:23
msgid "系统不支持该币种"
msgstr ""

#: app/models/mongo/p2p/advertising.py:24
msgid "系统不支持该法币"
msgstr ""

#: app/models/mongo/p2p/advertising.py:28
msgid "安全工具失效"
msgstr ""

#: app/models/mongo/p2p/advertising.py:29
msgid "广告单违反P2P交易规则，已被自动下架"
msgstr ""

#: app/models/mongo/p2p/advertising.py:30
msgid "价格偏离限价区间"
msgstr ""

#: app/models/mongo/p2p/advertising.py:31
msgid "商家取消身份"
msgstr ""

#: app/models/mongo/p2p/advertising.py:32
msgid "保证金不足"
msgstr ""

#: app/schedules/operation.py:1581
msgid "现货排位赛：恭喜进入排名！冲刺赢得{{ max_gift_amount }} {{ gift_asset }}"
msgstr ""

#: app/schedules/operation.py:1584
msgid "合约排位赛：恭喜进入排名！冲刺赢得{{ max_gift_amount }} {{ gift_asset }}"
msgstr ""

#: app/schedules/operation.py:1597
msgid "现货排位赛：恭喜达标！继续交易，瓜分{{ total_gift_amount }} {{ gift_asset }}奖池"
msgstr ""

#: app/schedules/operation.py:1600
msgid "合约排位赛：恭喜达标！继续交易，瓜分{{ total_gift_amount }} {{ gift_asset }}奖池"
msgstr ""

#: app/schedules/operation.py:1613
msgid "现货排位赛：你离瓜分{{ total_gift_amount }} {{ gift_asset }}仅差一步"
msgstr ""

#: app/schedules/operation.py:1616
msgid "合约排位赛：你离瓜分{{ total_gift_amount }} {{ gift_asset }}仅差一步"
msgstr ""

#: app/schedules/reports/ambassador_report.py:2556
msgid "用户邮箱"
msgstr ""

#: app/schedules/reports/ambassador_report.py:2559
msgid "首次充值时间"
msgstr ""

#: app/schedules/reports/ambassador_report.py:2561
msgid "首次充值金额"
msgstr ""

#: app/schedules/reports/ambassador_report.py:2562
msgid "币币交易金额"
msgstr ""

#: app/schedules/reports/ambassador_report.py:2563
msgid "合约交易金额"
msgstr ""

#: app/schedules/reports/ambassador_report.py:2564
msgid "币币手续费"
msgstr ""

#: app/schedules/reports/ambassador_report.py:2566
msgid "币币返佣"
msgstr ""

#: app/schedules/reports/ambassador_report.py:2589
msgid "【CoinEx】{{ report_date }} 商务大使邀请数据"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:32
#: app/templates/email/notice/user_trade_summary.j2:70
#: app/templates/email/notice/user_trade_summary.j2:100
msgid "排位类型"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:33
#: app/templates/email/notice/user_trade_summary.j2:71
msgid "当月累计交易量"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:34
#: app/templates/email/notice/user_trade_summary.j2:72
#: app/templates/email/notice/user_trade_summary.j2:102
msgid "最新排名"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:35
#: app/templates/email/notice/user_trade_summary.j2:73
#: app/templates/email/notice/user_trade_summary.j2:103
msgid "预估下月等级"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:36
#: app/templates/email/notice/user_trade_summary.j2:74
#: app/templates/email/notice/user_trade_summary.j2:104
msgid "距离上一等级交易量相差"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:37
#: app/templates/email/notice/user_trade_summary.j2:75
#: app/templates/email/notice/user_trade_summary.j2:105
msgid "距离下一等级交易量相差"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:42
#: app/schedules/reports/daily_market_maker_trade_report.py:66
#: app/templates/email/notice/user_trade_summary.j2:128
#: app/templates/email/notice/user_trade_summary.j2:194
msgid "市场"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:43
#: app/schedules/reports/daily_market_maker_trade_report.py:67
#: app/templates/email/notice/user_trade_summary.j2:129
#: app/templates/email/notice/user_trade_summary.j2:195
msgid "Taker交易量"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:44
#: app/schedules/reports/daily_market_maker_trade_report.py:68
#: app/templates/email/notice/user_trade_summary.j2:130
#: app/templates/email/notice/user_trade_summary.j2:196
msgid "Maker交易量"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:45
#: app/schedules/reports/daily_market_maker_trade_report.py:69
#: app/templates/email/notice/user_trade_summary.j2:131
#: app/templates/email/notice/user_trade_summary.j2:197
msgid "总交易量"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:46
#: app/schedules/reports/daily_market_maker_trade_report.py:58
#: app/schedules/reports/daily_market_maker_trade_report.py:70
#: app/schedules/reports/daily_market_maker_trade_report.py:82
#: app/templates/email/notice/user_trade_summary.j2:132
#: app/templates/email/notice/user_trade_summary.j2:167
#: app/templates/email/notice/user_trade_summary.j2:198
#: app/templates/email/notice/user_trade_summary.j2:233
msgid "账户比例"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:47
#: app/schedules/reports/daily_market_maker_trade_report.py:59
#: app/schedules/reports/daily_market_maker_trade_report.py:71
#: app/schedules/reports/daily_market_maker_trade_report.py:83
#: app/templates/email/notice/user_trade_summary.j2:133
#: app/templates/email/notice/user_trade_summary.j2:168
#: app/templates/email/notice/user_trade_summary.j2:199
#: app/templates/email/notice/user_trade_summary.j2:234
msgid "全站比例"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:48
#: app/schedules/reports/daily_market_maker_trade_report.py:60
#: app/schedules/reports/daily_market_maker_trade_report.py:72
#: app/schedules/reports/daily_market_maker_trade_report.py:84
#: app/templates/email/notice/user_trade_summary.j2:134
#: app/templates/email/notice/user_trade_summary.j2:169
#: app/templates/email/notice/user_trade_summary.j2:200
#: app/templates/email/notice/user_trade_summary.j2:235
msgid "手续费"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:49
#: app/schedules/reports/daily_market_maker_trade_report.py:73
#: app/templates/email/notice/user_trade_summary.j2:135
#: app/templates/email/notice/user_trade_summary.j2:201
msgid "当月Taker交易量"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:50
#: app/schedules/reports/daily_market_maker_trade_report.py:74
#: app/templates/email/notice/user_trade_summary.j2:136
#: app/templates/email/notice/user_trade_summary.j2:202
msgid "当月Maker交易量"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:51
#: app/schedules/reports/daily_market_maker_trade_report.py:61
#: app/schedules/reports/daily_market_maker_trade_report.py:75
#: app/schedules/reports/daily_market_maker_trade_report.py:85
#: app/templates/email/notice/user_trade_summary.j2:137
#: app/templates/email/notice/user_trade_summary.j2:170
#: app/templates/email/notice/user_trade_summary.j2:203
#: app/templates/email/notice/user_trade_summary.j2:236
msgid "当月总交易量"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:56
#: app/schedules/reports/daily_market_maker_trade_report.py:80
#: app/templates/email/notice/user_trade_summary.j2:165
#: app/templates/email/notice/user_trade_summary.j2:231
msgid "交易类型"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:102
#: app/templates/email/notice/user_trade_summary.j2:65
msgid "当月【做市商等级】排位情况"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:104
#: app/templates/email/notice/user_trade_summary.j2:123
msgid "【现货-市场】统计日报"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:106
#: app/templates/email/notice/user_trade_summary.j2:160
msgid "【现货-交易类型】统计日报"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:108
#: app/templates/email/notice/user_trade_summary.j2:189
msgid "【合约-市场】统计日报"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:110
#: app/templates/email/notice/user_trade_summary.j2:226
msgid "【合约-交易类型】统计日报"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:737
#: app/schedules/reports/daily_market_maker_trade_report.py:755
#: app/schedules/reports/daily_market_maker_trade_report.py:772
msgid "现货做市"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:737
#: app/schedules/reports/daily_market_maker_trade_report.py:755
#: app/schedules/reports/daily_market_maker_trade_report.py:772
msgid "合约做市"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:738
msgid "ranking"
msgstr ""

#: app/schedules/reports/daily_market_maker_trade_report.py:872
msgid "【CoinEx】做市商日报（{{ report_date }}）"
msgstr ""

#: app/schedules/statistics/market_monitor/base.py:273
msgid "风险提示：当前杠杆市场的最新成交价与指数价格存在偏离，请做好风险控制。"
msgstr ""

#: app/schedules/statistics/market_monitor/base.py:275
msgid "风险提示：当前合约市场的最新成交价与标记价格存在偏离，强平价格将根据标记价格计算，请做好风险控制。"
msgstr ""

#: app/templates/email/index.j2:49
#: app/templates/email/notice/user_trade_summary.j2:55
msgid "尊敬的用户"
msgstr ""

#: app/templates/email/index.j2:53
msgid "子账号名："
msgstr ""

#: app/templates/email/index.j2:63
#: app/templates/email/notice/user_trade_summary.j2:260
msgid "CoinEx团队"
msgstr ""

#: app/templates/email/index.j2:65
#: app/templates/email/notice/user_trade_summary.j2:264
#, python-format
msgid ""
"如果您有任何问题，请 <a style=\"color: #0ead98;font-weight: 600;\" "
"href=\"%(support_url)s/hc/requests/new\">提交工单</a> 或与我们的客服团队联系"
msgstr ""

#: app/templates/email/index.j2:73
#: app/templates/email/notice/user_trade_summary.j2:272
msgid "防钓鱼码"
msgstr ""

#: app/templates/email/index.j2:79
#: app/templates/email/notice/user_trade_summary.j2:278
msgid "系统邮件，请勿回复。"
msgstr ""

#: app/templates/email/index.j2:201
#: app/templates/email/notice/announcement_notice.j2:29
#: app/templates/email/notice/user_trade_summary.j2:345
msgid "官方网站"
msgstr ""

#: app/templates/email/index.j2:208
#: app/templates/email/notice/announcement_notice.j2:36
#: app/templates/email/notice/user_trade_summary.j2:352
msgid "帮助中心"
msgstr ""

#: app/templates/email/index.j2:215
#: app/templates/email/notice/announcement_notice.j2:43
#: app/templates/email/notice/user_trade_summary.j2:359
msgid "服务协议"
msgstr ""

#: app/templates/email/confirmation/add_api_address.j2:2
msgid "添加API提现白名单地址确认"
msgstr ""

#: app/templates/email/confirmation/add_api_address.j2:5
#, python-format
msgid "你于 %(time)s 正在添加API提现白名单地址："
msgstr ""

#: app/templates/email/confirmation/add_api_address.j2:7
#, python-format
msgid "%(from_name)s(%(from_account)s) 于 %(time)s 正在添加API提现白名单地址："
msgstr ""

#: app/templates/email/confirmation/add_api_address.j2:14
msgid "公链"
msgstr ""

#: app/templates/email/confirmation/add_api_address.j2:15
msgid "地址"
msgstr ""

#: app/templates/email/confirmation/add_api_address.j2:16
msgid "标签"
msgstr ""

#: app/templates/email/confirmation/add_api_address.j2:30
#: app/templates/email/confirmation/withdraw_coin.j2:23
msgid "请点击如下按钮，确认或取消以上申请"
msgstr ""

#: app/templates/email/confirmation/add_api_address.j2:32
#: app/templates/email/confirmation/multi_approve_confirm.j2:23
#: app/templates/email/confirmation/multi_approve_join.j2:12
#: app/templates/email/confirmation/multi_approve_leave.j2:10
#: app/templates/email/confirmation/red_packet_confirm.j2:10
#: app/templates/email/confirmation/withdraw_coin.j2:25
msgid "去确认"
msgstr ""

#: app/templates/email/confirmation/add_api_address.j2:34
#: app/templates/email/confirmation/api_expiration_extend.j2:11
#: app/templates/email/confirmation/multi_approve_confirm.j2:25
#: app/templates/email/confirmation/multi_approve_join.j2:14
#: app/templates/email/confirmation/multi_approve_leave.j2:12
#: app/templates/email/confirmation/red_packet_confirm.j2:12
#: app/templates/email/confirmation/withdraw_coin.j2:27
#: app/templates/email/notice/red_packet_register_notice.j2:11
msgid "如点击上方按钮无法完成确认，请复制下方地址至浏览器地址栏进行确认："
msgstr ""

#: app/templates/email/confirmation/add_api_address.j2:38
#: app/templates/email/confirmation/api_expiration_extend.j2:14
#: app/templates/email/confirmation/multi_approve_confirm.j2:27
#: app/templates/email/confirmation/red_packet_confirm.j2:15
#: app/templates/email/notice/edit_login_password.j2:4
#: app/templates/email/notice/edit_mobile.j2:4
#: app/templates/email/notice/edit_totp.j2:4
#: app/templates/email/notice/edit_withdraw_password.j2:4
#: app/templates/email/notice/email_reset_pass.j2:4
#: app/templates/email/notice/set_login_password_success.j2:5
#: app/templates/email/notice/unfreeze_account_pass.j2:5
#: app/templates/email/notice/update_trade_password.j2:4
#, python-format
msgid ""
"如果此次活动不是您本人操作，请立即<a href=\"%(site_url)s/signin\">重设密码</a>或<a "
"href=\"%(site_url)s/my/info/basic/forbid\">禁用账户</a>，并尽快<a "
"href=\"%(support_url)s/hc/requests/new\">提交工单</a>联系CoinEx客服。"
msgstr ""

#: app/templates/email/confirmation/api_expiration_extend.j2:2
msgid "API到期提醒"
msgstr ""

#: app/templates/email/confirmation/api_expiration_extend.j2:4
#, python-format
msgid ""
"您在CoinEx的账户(%(graph_name)s)创建的备注为 %(remark)s "
"的API,将于%(expired_at)s过期,为避免您的交易受影响,请点击如下按钮,去续期API时效"
msgstr ""

#: app/templates/email/confirmation/api_expiration_extend.j2:8
msgid "去续期"
msgstr ""

#: app/templates/email/confirmation/api_expiration_extend.j2:13
#, python-format
msgid ""
"为保障账号安全，该链接30分钟内有效，失效后您仍可以登录CoinEx前往API管理页面进行续期或重新创建API，<a "
"href=\"%(api_url)s\">立即前往</a>"
msgstr ""

#: app/templates/email/confirmation/multi_approve_confirm.j2:2
msgid "多人审核提现确认"
msgstr ""

#: app/templates/email/confirmation/multi_approve_confirm.j2:4
msgid "你正在邀请以下成员参与提现审核"
msgstr ""

#: app/templates/email/confirmation/multi_approve_confirm.j2:12
msgid "风险提示：删除已认证成员邮箱时，需对方邮箱确认，请谨慎添加！"
msgstr ""

#: app/templates/email/confirmation/multi_approve_confirm.j2:14
msgid "请确认是否邀请？确认后"
msgstr ""

#: app/templates/email/confirmation/multi_approve_confirm.j2:16
msgid "平台将发送邀请邮件"
msgstr ""

#: app/templates/email/confirmation/multi_approve_confirm.j2:17
msgid "对方接受邀请"
msgstr ""

#: app/templates/email/confirmation/multi_approve_confirm.j2:18
msgid "邀请成功"
msgstr ""

#: app/templates/email/confirmation/multi_approve_join.j2:2
msgid "邀请加入提现审核"
msgstr ""

#: app/templates/email/confirmation/multi_approve_join.j2:4
#, python-format
msgid ""
"CoinEx用户 %(from_name)s(%(from_account)s) "
"邀请你加入提现相关审核，加入后该用户进行以下操作时，需要你的邮件审核："
msgstr ""

#: app/templates/email/confirmation/multi_approve_join.j2:6
msgid "提现时"
msgstr ""

#: app/templates/email/confirmation/multi_approve_join.j2:7
msgid "添加API提现白名单地址"
msgstr ""

#: app/templates/email/confirmation/multi_approve_leave.j2:2
msgid "退出提现审核确认"
msgstr ""

#: app/templates/email/confirmation/multi_approve_leave.j2:4
#, python-format
msgid "你被Coinex用户 %(from_name)s(%(from_account)s) 从提现审核名单中移除。"
msgstr ""

#: app/templates/email/confirmation/multi_approve_leave.j2:5
msgid "请确认是否退出？"
msgstr ""

#: app/templates/email/confirmation/multi_approve_leave.j2:6
msgid "退出后将不在参与该用户的提现相关审核。"
msgstr ""

#: app/templates/email/confirmation/red_packet_confirm.j2:2
msgid "红包确认"
msgstr ""

#: app/templates/email/confirmation/red_packet_confirm.j2:4
#, python-format
msgid "您的账户于 %(time)s 发起了一个红包申请："
msgstr ""

#: app/templates/email/confirmation/red_packet_confirm.j2:5
msgid "金额："
msgstr ""

#: app/templates/email/confirmation/red_packet_confirm.j2:8
msgid "请点击如下按钮，确认或取消您的红包申请"
msgstr ""

#: app/templates/email/confirmation/red_packet_confirm.j2:14
msgid "为保障账号安全，该链接30分钟内有效。"
msgstr ""

#: app/templates/email/confirmation/withdraw_coin.j2:2
msgid "提现确认"
msgstr ""

#: app/templates/email/confirmation/withdraw_coin.j2:5
msgid "你的账户发起了一笔提现申请："
msgstr ""

#: app/templates/email/confirmation/withdraw_coin.j2:7
#, python-format
msgid "%(from_name)s(%(from_account)s) 发起了一笔提现申请："
msgstr ""

#: app/templates/email/confirmation/withdraw_coin.j2:9
#: app/templates/email/notice/send_coin_withdraw_notice.j2:5
msgid "提现时间："
msgstr ""

#: app/templates/email/confirmation/withdraw_coin.j2:12
msgid "实际汇出："
msgstr ""

#: app/templates/email/confirmation/withdraw_coin.j2:14
msgid "实际到账："
msgstr ""

#: app/templates/email/confirmation/withdraw_coin.j2:18
#: app/templates/email/notice/send_coin_withdraw_notice.j2:8
#: app/templates/email/notice/send_coin_withdraw_notice.j2:11
msgid "提现地址："
msgstr ""

#: app/templates/email/confirmation/withdraw_coin.j2:20
msgid "所属公链："
msgstr ""

#: app/templates/email/confirmation/withdraw_coin.j2:31
#: app/templates/email/notice/email_registered.j2:5
#: app/templates/email/notice/sign_in.j2:8
#: app/templates/email/verification_code/add_anti_phishing_code.j2:5
#: app/templates/email/verification_code/add_api_withdraw_address.j2:5
#: app/templates/email/verification_code/add_email.j2:5
#: app/templates/email/verification_code/add_mobile.j2:5
#: app/templates/email/verification_code/add_totp.j2:5
#: app/templates/email/verification_code/add_trade_password.j2:5
#: app/templates/email/verification_code/add_withdraw_password.j2:5
#: app/templates/email/verification_code/bind_admin_webauthn.j2:5
#: app/templates/email/verification_code/bind_third_party_account.j2:5
#: app/templates/email/verification_code/bind_webauthn.j2:5
#: app/templates/email/verification_code/edit_anti_phishing_code.j2:5
#: app/templates/email/verification_code/edit_mobile.j2:5
#: app/templates/email/verification_code/edit_new_email.j2:5
#: app/templates/email/verification_code/edit_old_email.j2:5
#: app/templates/email/verification_code/edit_totp.j2:5
#: app/templates/email/verification_code/edit_trade_password.j2:5
#: app/templates/email/verification_code/edit_withdraw_password.j2:5
#: app/templates/email/verification_code/non_login_bind_third_party_account.j2:5
#: app/templates/email/verification_code/non_login_edit_new_email.j2:5
#: app/templates/email/verification_code/non_login_reset_login_password.j2:5
#: app/templates/email/verification_code/remove_trade_password.j2:5
#: app/templates/email/verification_code/reset_2fa.j2:5
#: app/templates/email/verification_code/reset_login_password.j2:5
#: app/templates/email/verification_code/reset_webauthn.j2:5
#: app/templates/email/verification_code/reset_withdraw_password.j2:5
#: app/templates/email/verification_code/send_c_box.j2:5
#: app/templates/email/verification_code/set_login_password.j2:5
#: app/templates/email/verification_code/sign_in.j2:5
#: app/templates/email/verification_code/sign_off.j2:5
#: app/templates/email/verification_code/unbind_mobile.j2:5
#: app/templates/email/verification_code/unbind_totp.j2:5
#: app/templates/email/verification_code/unbind_webauthn.j2:5
#: app/templates/email/verification_code/unfreeze_account.j2:5
#, python-format
msgid ""
"如果不是你本人操作，请立即<a href='%(reset_url)s'>重设密码</a>或<a "
"href='%(forbid_url)s'>禁用账户</a>，并尽快<a "
"href='%(submit_url)s'>提交工单</a>联系CoinEx客服。"
msgstr ""

#: app/templates/email/notice/2fa_reset_fail.j2:3
#, python-format
msgid "您在CoinEx安全项重置审核不通过，原因是：%(reason)s，请登录账户查看。"
msgstr ""

#: app/templates/email/notice/2fa_reset_fail.j2:4
#: app/templates/email/notice/mobile_reset_pass.j2:4
#: app/templates/email/notice/totp_reset_pass.j2:4
#: app/templates/email/notice/webauthn_reset_pass.j2:4
#: app/templates/email/notice/withdraw_password_reset_pass.j2:5
#, python-format
msgid ""
"如果此次行为不是您本人所为：请立即<a "
"href=\"https://www.coinex.com/my/info/basic\">重设密码</a>或<a "
"href=\"https://www.coinex.com/my/info/basic\">禁用账户</a>，并尽快<a "
"href=\"%(support_url)s/hc/requests/new\">提交工单</a>联系CoinEx客服。"
msgstr ""

#: app/templates/email/notice/abnormal_deposit_application_additional_info.j2:6
#, python-format
msgid ""
"您于%(time)s提交的金额为%(amount)s %(asset)s的充值未到账找回申请已初步审核，还需要补充相关资料，请尽快补充。<a "
"href=\"%(url)s\">立即补充资料</a>"
msgstr ""

#: app/templates/email/notice/abnormal_deposit_application_fee.j2:6
#, python-format
msgid ""
"您于%(time)s提交的金额为%(amount)s %(asset)s的充值未到账找回申请已初步通过审核，但还需确保您的CoinEx现货账户有 "
"%(fee_amount)s %(fee_asset)s 的资产作为手续费，找回资产时平台将自动扣除手续费。"
msgstr ""

#: app/templates/email/notice/abnormal_deposit_application_fee.j2:8
msgid "当前您的余额不足，请尽快补充，否则申请将被驳回。"
msgstr ""

#: app/templates/email/notice/abnormal_deposit_application_finished.j2:7
#, python-format
msgid "您于%(time)s提交的金额为%(amount)s %(asset)s的充值未到账找回成功，已入账至您的现货账户。"
msgstr ""

#: app/templates/email/notice/abnormal_deposit_application_finished.j2:9
#, python-format
msgid ""
"您于%(time)s提交的金额为%(amount)s %(asset)s的充值未到账找回成功，已退回至您提供的地址。退回的交易ID为：<a "
"href=\"%(refund_tx_url)s\">%(refund_tx_id)s</a>"
msgstr ""

#: app/templates/email/notice/abnormal_deposit_application_finished.j2:12
#, python-format
msgid ""
"由于需要额外的人力和技术投入以帮您找回资产，我们已从您的充值资产里收取一定的手续费，手续费金额为:%(fee_amount)s "
"%(fee_asset)s。手续费收取规则可<a href=\"%(url)s\">点击查看</a>。"
msgstr ""

#: app/templates/email/notice/abnormal_deposit_application_passed.j2:6
#, python-format
msgid ""
"您于%(time)s提交的金额为%(amount)s "
"%(asset)s的充值未到账找回申请已初步通过审核，资金正在处理中，预计还需%(weeks_needed)s周即可入账/退回。"
msgstr ""

#: app/templates/email/notice/abnormal_deposit_application_passed.j2:7
#, python-format
msgid ""
"由于需要额外的人力和技术投入以帮您找回资产，我们将从您的充值资产里收取一定的手续费，预计手续费金额为:%(fee_amount)s "
"%(fee_asset)s。手续费收取规则可<a href=\"%(url)s\">点击查看</a>。"
msgstr ""

#: app/templates/email/notice/abnormal_deposit_application_rejected.j2:6
#, python-format
msgid ""
"您于%(time)s提交的金额为%(amount)s "
"%(asset)s的充值未到账找回申请审核失败，失败原因是：%(rejection_reason)s。<a "
"href=\"%(url)s\">详情可点击查看</a>"
msgstr ""

#: app/templates/email/notice/airdrop_rewords_fail.j2:5
#: app/templates/email/notice/dibs_rewords_fail.j2:5
#, python-format
msgid "抽奖规则可<a href='%(site_url)s'>点击查看</a>。"
msgstr ""

#: app/templates/email/notice/airdrop_rewords_result.j2:4
#, python-format
msgid "抱歉，您在CoinEx的“%(title)s空投”活动中，%(rewards)s领取失败。"
msgstr ""

#: app/templates/email/notice/airdrop_rewords_result.j2:5
msgid "原因：当前账号已有使用中的合约体验金或交易赠金券，无法领取更多同类型卡券。"
msgstr ""

#: app/templates/email/notice/airdrop_rewords_success.j2:5
#, python-format
msgid "奖励已发放，<a href='%(activity_url)s'>查看详情</a>。"
msgstr ""

#: app/templates/email/notice/ambassador_agent_appraisal.j2:9
msgid "恭喜您，上个月CoinEx大使推荐官考核通过。"
msgstr ""

#: app/templates/email/notice/ambassador_agent_appraisal.j2:12
#, python-format
msgid "很遗憾，您的CoinEx大使推荐官考核已连续%(fail_count)s个月不通过，已经取消大使推荐官身份。"
msgstr ""

#: app/templates/email/notice/ambassador_agent_appraisal.j2:14
#, python-format
msgid "很遗憾，您上个月CoinEx大使推荐官考核不通过，CoinEx已为您触发缓冲保护机制。（您已%(fail_count)s个月考核不通过，如连续%(max_fail_count)s个月考核不通过，则取消大使推荐官身份）"
msgstr ""

#: app/templates/email/notice/ambassador_agent_appraisal.j2:18
#, python-format
msgid "大使推荐官主要职责为帮助CoinEx招募大使，可获得名下大使邀请用户产生手续费总额%(referral_rate)s的返佣奖励。"
msgstr ""

#: app/templates/email/notice/ambassador_agent_appraisal.j2:19
#, python-format
msgid "详情查看：%(detail_url)s"
msgstr ""

#: app/templates/email/notice/ambassador_agent_appraisal.j2:20
#: app/templates/email/notice/ambassador_level_appraisal.j2:34
#: app/templates/email/notice/ambassador_level_change.j2:15
#: app/templates/email/notice/ambassador_level_change_pre_notify.j2:20
#: app/templates/email/notice/perpetual_market_maker_level_appraisal.j2:39
#: app/templates/email/notice/spot_market_maker_level_appraisal.j2:45
#, python-format
msgid "如有任何疑问，可<a href=\"%(support_url)s/hc/requests/new\">提交工单</a>与我们联系。"
msgstr ""

#: app/templates/email/notice/ambassador_application_approve.j2:8
msgid "你推荐的大使已成功通过大使申请审核。"
msgstr ""

#: app/templates/email/notice/ambassador_application_approve.j2:10
#: app/templates/email/notice/ambassador_application_reject.j2:10
#: app/templates/email/notice/ambassador_application_submit.j2:10
msgid "详情如下："
msgstr ""

#: app/templates/email/notice/ambassador_application_approve.j2:13
#: app/templates/email/notice/ambassador_application_reject.j2:12
#: app/templates/email/notice/ambassador_application_submit.j2:12
#, python-format
msgid "邮箱：%(ambassador_email)s"
msgstr ""

#: app/templates/email/notice/ambassador_application_approve.j2:14
#: app/templates/email/notice/ambassador_application_reject.j2:13
#: app/templates/email/notice/ambassador_application_submit.j2:13
#, python-format
msgid "地区：%(location)s"
msgstr ""

#: app/templates/email/notice/ambassador_application_approve.j2:15
#: app/templates/email/notice/ambassador_application_reject.j2:14
#: app/templates/email/notice/ambassador_application_submit.j2:14
#, python-format
msgid "电报：%(telegram)s"
msgstr ""

#: app/templates/email/notice/ambassador_application_approve.j2:16
#, python-format
msgid "申请时间：%(created_at)s"
msgstr ""

#: app/templates/email/notice/ambassador_application_approve.j2:17
#, python-format
msgid "通过时间：%(approved_at)s"
msgstr ""

#: app/templates/email/notice/ambassador_application_approve.j2:18
#, python-format
msgid "大使等级：%(level)s"
msgstr ""

#: app/templates/email/notice/ambassador_application_reject.j2:8
msgid "你推荐的大使未通过大使申请审核。"
msgstr ""

#: app/templates/email/notice/ambassador_application_submit.j2:3
msgid "【CoinEx】大使提交申请提醒"
msgstr ""

#: app/templates/email/notice/ambassador_application_submit.j2:8
msgid "你推荐的大使已提交大使申请。"
msgstr ""

#: app/templates/email/notice/ambassador_level_appraisal.j2:7
#: app/templates/email/notice/ambassador_level_change.j2:7
#, python-format
msgid "恭喜，您的CoinEx大使等级已升级为%(level_name)s，可享受被邀请用户交易手续费%(referral_rate)s的返佣（%(referral_asset)s）及其他%(level_name)s大使特权。"
msgstr ""

#: app/templates/email/notice/ambassador_level_appraisal.j2:9
#: app/templates/email/notice/ambassador_level_change.j2:9
#, python-format
msgid "抱歉，您的CoinEx大使等级已降级为%(level_name)s，享受被邀请用户交易手续费%(referral_rate)s的返佣（%(referral_asset)s）及其他%(level_name)s大使特权。"
msgstr ""

#: app/templates/email/notice/ambassador_level_appraisal.j2:11
#: app/templates/email/notice/ambassador_level_appraisal.j2:15
#: app/templates/email/notice/ambassador_level_change.j2:11
#, python-format
msgid "您的CoinEx大使等级不变，等级为%(level_name)s，可享受被邀请用户交易手续费%(referral_rate)s的返佣（%(referral_asset)s）以及其他%(level_name)s大使特权。"
msgstr ""

#: app/templates/email/notice/ambassador_level_appraisal.j2:17
#: app/templates/email/notice/ambassador_level_appraisal.j2:21
#: app/templates/email/notice/ambassador_level_appraisal.j2:24
#, python-format
msgid "很遗憾，您上个月未达CoinEx大使最低考核要求。CoinEx已为您触发缓冲保护机制，本月大使等级保留%(level_name)s，享受邀请用户交易手续费%(referral_rate)s返佣及其他%(level_name)s大使特权。"
msgstr ""

#: app/templates/email/notice/ambassador_level_appraisal.j2:18
#: app/templates/email/notice/ambassador_level_appraisal.j2:22
#, python-format
msgid "*您已%(month_count)d个月不符合最低考核要求，如连续3个月不符合最低考核要求，则取消大使身份。"
msgstr ""

#: app/templates/email/notice/ambassador_level_appraisal.j2:25
#, python-format
msgid "*您已%(month_count)d个月不符合最低考核要求，如下个月考核不符合最低考核要求，则取消大使身份。"
msgstr ""

#: app/templates/email/notice/ambassador_level_appraisal.j2:27
#: app/templates/email/notice/potential_invalid_ambassador.j2:6
msgid "很遗憾，您连续3个月未达CoinEx大使最低考核要求，已取消大使身份，享受的交易手续费返佣比例将为普通推荐返佣比例。"
msgstr ""

#: app/templates/email/notice/ambassador_level_appraisal.j2:28
#: app/templates/email/notice/potential_invalid_ambassador.j2:7
#, python-format
msgid "我们为您保留了大使推荐官的身份，推荐好友成为CoinEx大使，即可获得名下大使所邀请用户的%(rate)s手续费返佣，考核详情可查看：%(agent_detail_url)s。"
msgstr ""

#: app/templates/email/notice/ambassador_level_appraisal.j2:29
#, python-format
msgid "您在大使项目中遇到什么问题？提交问卷帮助我们做得更好吧：%(url)s"
msgstr ""

#: app/templates/email/notice/ambassador_level_appraisal.j2:33
#: app/templates/email/notice/ambassador_level_change.j2:14
#: app/templates/email/notice/ambassador_level_change_pre_notify.j2:19
#, python-format
msgid "考核详情: %(detail_url)s"
msgstr ""

#: app/templates/email/notice/ambassador_level_change_pre_notify.j2:6
msgid "你在CoinEx的大使等级存在降级风险，距离下次考核还剩1周，请及时采取保级措施。截止今天，你的邀请数据如下："
msgstr ""

#: app/templates/email/notice/ambassador_level_change_pre_notify.j2:7
#, python-format
msgid "当前大使等级：%(level_name)s"
msgstr ""

#: app/templates/email/notice/ambassador_level_change_pre_notify.j2:9
#, python-format
msgid "已邀请用户累计交易量（本月）：%(cur_amount)s，已达标"
msgstr ""

#: app/templates/email/notice/ambassador_level_change_pre_notify.j2:11
#, python-format
msgid "已邀请用户累计交易量（本月）：%(cur_amount)s，距离保级还差%(cur_delta_amount)s"
msgstr ""

#: app/templates/email/notice/ambassador_level_change_pre_notify.j2:14
#, python-format
msgid "已邀请新交易用户数（本月）：%(cur_count)s，已达标"
msgstr ""

#: app/templates/email/notice/ambassador_level_change_pre_notify.j2:16
#, python-format
msgid "已邀请新交易用户数（本月）：%(cur_count)s，距离保级还差%(cur_delta_count)s"
msgstr ""

#: app/templates/email/notice/ambassador_package_fail_amb_invalid.j2:3
#, python-format
msgid "【CoinEx】立即成为大使，持续解锁%(package_amount)s %(asset)s激励包"
msgstr ""

#: app/templates/email/notice/ambassador_package_fail_amb_invalid.j2:6
#, python-format
msgid ""
"很遗憾，你的大使身份已失效，本期大使激励包 %(period_amount)s %(asset)s "
"未能正常发放。请注意，如果大使身份连续三个月未恢复，%(asset)s激励包将停止发放。"
msgstr ""

#: app/templates/email/notice/ambassador_package_fail_amb_invalid.j2:8
msgid "别灰心！你仍然有机会恢复大使身份，并继续享受激励包带来的丰厚奖励。"
msgstr ""

#: app/templates/email/notice/ambassador_package_fail_amb_invalid.j2:9
#, python-format
msgid "👉<a href=\"%(apply_link)s\">立即申请成为大使</a>，重新解锁%(asset)s激励包！"
msgstr ""

#: app/templates/email/notice/ambassador_package_fail_amb_invalid.j2:11
#: app/templates/email/notice/ambassador_package_fail_assessment.j2:13
#: app/templates/email/notice/ambassador_package_fail_final.j2:13
#: app/templates/email/notice/ambassador_package_success.j2:13
#: app/templates/email/notice/ambassador_package_success_final.j2:13
#: app/templates/email/notice/ambassador_package_suspend.j2:8
msgid "激励包发放数据："
msgstr ""

#: app/templates/email/notice/ambassador_package_fail_amb_invalid.j2:12
#: app/templates/email/notice/ambassador_package_fail_assessment.j2:14
#: app/templates/email/notice/ambassador_package_success.j2:14
#, python-format
msgid ""
"· 发放期数：累计已发放 %(total_release_periods)s期，未达标失效 %(lose_periods)s期，剩余 "
"%(remaining_periods)s期"
msgstr ""

#: app/templates/email/notice/ambassador_package_fail_amb_invalid.j2:13
#: app/templates/email/notice/ambassador_package_fail_assessment.j2:15
#: app/templates/email/notice/ambassador_package_success.j2:15
#, python-format
msgid ""
"· 发放数量：累计已发放 %(total_release_amount)s %(asset)s，未达标失效 %(lose_amount)s "
"%(asset)s，剩余 %(remaining_amount)s %(asset)s"
msgstr ""

#: app/templates/email/notice/ambassador_package_fail_amb_invalid.j2:14
#: app/templates/email/notice/ambassador_package_fail_assessment.j2:16
#: app/templates/email/notice/ambassador_package_fail_final.j2:16
#, python-format
msgid "你可以 <a href=\"%(package_url)s\">点击查看 %(asset)s激励包详细信息</a>。"
msgstr ""

#: app/templates/email/notice/ambassador_package_fail_amb_invalid.j2:16
#: app/templates/email/notice/ambassador_package_fail_assessment.j2:18
#: app/templates/email/notice/ambassador_package_fail_final.j2:18
#: app/templates/email/notice/ambassador_package_invite.j2:16
#: app/templates/email/notice/ambassador_package_success.j2:18
#: app/templates/email/notice/ambassador_package_success_final.j2:18
#: app/templates/email/notice/ambassador_package_suspend.j2:16
#, python-format
msgid "如有疑问，<a href=\"%(support_url)s/hc/requests/new\">提交工单</a>或加入电报群联系我们："
msgstr ""

#: app/templates/email/notice/ambassador_package_fail_amb_invalid.j2:20
#: app/templates/email/notice/ambassador_package_fail_assessment.j2:22
#: app/templates/email/notice/ambassador_package_fail_final.j2:22
#: app/templates/email/notice/ambassador_package_invite.j2:20
#: app/templates/email/notice/ambassador_package_success.j2:22
#: app/templates/email/notice/ambassador_package_success_final.j2:22
#: app/templates/email/notice/ambassador_package_suspend.j2:20
msgid "本活动最终解释权归 CoinEx 所有。"
msgstr ""

#: app/templates/email/notice/ambassador_package_fail_assessment.j2:6
#, python-format
msgid "很遗憾，你在本期大使激励包考核中未能达标，%(period_amount)s %(asset)s 奖励未能正常发放。"
msgstr ""

#: app/templates/email/notice/ambassador_package_fail_assessment.j2:8
#: app/templates/email/notice/ambassador_package_fail_final.j2:8
#: app/templates/email/notice/ambassador_package_success.j2:8
#: app/templates/email/notice/ambassador_package_success_final.j2:8
msgid "本期考核数据："
msgstr ""

#: app/templates/email/notice/ambassador_package_fail_assessment.j2:9
#: app/templates/email/notice/ambassador_package_fail_final.j2:9
#: app/templates/email/notice/ambassador_package_success.j2:9
#: app/templates/email/notice/ambassador_package_success_final.j2:9
#, python-format
msgid "· 新增交易用户：%(current_refer_users)s"
msgstr ""

#: app/templates/email/notice/ambassador_package_fail_assessment.j2:10
#: app/templates/email/notice/ambassador_package_fail_final.j2:10
#: app/templates/email/notice/ambassador_package_success.j2:10
#: app/templates/email/notice/ambassador_package_success_final.j2:10
#, python-format
msgid "· 累计邀请交易量：%(current_refer_amount)s USD"
msgstr ""

#: app/templates/email/notice/ambassador_package_fail_assessment.j2:11
msgid "· 考核结果：本期未能达到考核要求，激励包奖励暂时无法发放，期待你在下期的表现！"
msgstr ""

#: app/templates/email/notice/ambassador_package_fail_final.j2:6
#, python-format
msgid ""
"很遗憾，你的大使激励包考核未达标，%(period_amount)s %(asset)s "
"奖励未能正常发放。感谢你一直以来对CoinEx大使项目的支持，本次%(asset)s激励包考核已全部结束。"
msgstr ""

#: app/templates/email/notice/ambassador_package_fail_final.j2:11
msgid "· 考核结果：本期未能达到考核要求，激励包奖励暂时无法发放。"
msgstr ""

#: app/templates/email/notice/ambassador_package_fail_final.j2:14
#: app/templates/email/notice/ambassador_package_success_final.j2:14
#: app/templates/email/notice/ambassador_package_suspend.j2:9
#, python-format
msgid "· 发放期数：累计已发放 %(total_release_periods)s期，未达标失效 %(lose_periods)s期"
msgstr ""

#: app/templates/email/notice/ambassador_package_fail_final.j2:15
#: app/templates/email/notice/ambassador_package_success_final.j2:15
#: app/templates/email/notice/ambassador_package_suspend.j2:10
#, python-format
msgid ""
"· 发放数量：累计已发放 %(total_release_amount)s %(asset)s，未达标失效 %(lose_amount)s "
"%(asset)s"
msgstr ""

#: app/templates/email/notice/ambassador_package_invite.j2:3
#, python-format
msgid "【CoinEx】你的专属%(package_amount)s %(asset)s激励包已就位，立即领取！"
msgstr ""

#: app/templates/email/notice/ambassador_package_invite.j2:6
#, python-format
msgid ""
"感谢你在社区拓展与用户邀请中做出的杰出贡献！你的出色表现，赢得了 CoinEx 的高度认可——我们为你特别定制了 "
"%(package_amount)s %(asset)s "
"大使激励包，现已开放领取！激励包不仅是对你成绩的肯定，更是你作为核心大使的专属荣誉与福利。领取后，你将享有为期 %(periods)s "
"期的持续激励，助你在未来的每一步收获更多。"
msgstr ""

#: app/templates/email/notice/ambassador_package_invite.j2:8
msgid "🎁 你的专属激励权益"
msgstr ""

#: app/templates/email/notice/ambassador_package_invite.j2:9
#, python-format
msgid ""
"1、%(asset)s激励包： 总计 %(package_amount)s %(asset)s，按月分 %(periods)s 期 "
"发放，奖励灵活到账。"
msgstr ""

#: app/templates/email/notice/ambassador_package_invite.j2:10
msgid "2、尊贵身份象征： 该激励包仅面向表现突出的核心大使开放。"
msgstr ""

#: app/templates/email/notice/ambassador_package_invite.j2:11
msgid "3、激励灵活升级：表现优异将有机会解锁叠加激励包，持续提升收益。"
msgstr ""

#: app/templates/email/notice/ambassador_package_invite.j2:13
#, python-format
msgid "👉 <a href=\"%(package_url)s\">点击领取激励包</a> ，开启收益升级之旅！"
msgstr ""

#: app/templates/email/notice/ambassador_package_invite.j2:14
#, python-format
msgid "📅 领取截止时间：%(deadline)s（UTC）"
msgstr ""

#: app/templates/email/notice/ambassador_package_stop.j2:6
#, python-format
msgid ""
"很遗憾，由于系统原因，CoinEx将暂停此次<a "
"href=\"%(package_url)s\">%(asset)s激励包</a>的发放。对你造成不便，还望谅解！"
msgstr ""

#: app/templates/email/notice/ambassador_package_stop.j2:8
msgid "再次感谢你的支持和理解！CoinEx竭诚为你提供优质服务。"
msgstr ""

#: app/templates/email/notice/ambassador_package_stop.j2:10
#, python-format
msgid "如有任何疑问，<a href=\"%(support_url)s/hc/requests/new\">提交工单</a>或加入电报群联系我们："
msgstr ""

#: app/templates/email/notice/ambassador_package_stop.j2:14
msgid "本活动所有解释权归CoinEx所有。"
msgstr ""

#: app/templates/email/notice/ambassador_package_success.j2:6
#, python-format
msgid "恭喜你通过本期大使激励包考核，%(settled_amount)s %(asset)s 奖励已成功发放。"
msgstr ""

#: app/templates/email/notice/ambassador_package_success.j2:11
msgid "· 考核结果：达标。继续努力，表现越好，还有机会解锁叠加奖励包！"
msgstr ""

#: app/templates/email/notice/ambassador_package_success.j2:16
#, python-format
msgid "你可以 <a href=\"%(package_url)s\">点击查看 %(asset)s激励包的详细信息</a>。"
msgstr ""

#: app/templates/email/notice/ambassador_package_success_final.j2:6
#, python-format
msgid ""
"恭喜通过本期大使激励包考核，%(settled_amount)s %(asset)s "
"奖励已成功发放。感谢你一直以来对CoinEx大使项目的支持，你的所有%(asset)s激励包奖励已经全部发放完毕！"
msgstr ""

#: app/templates/email/notice/ambassador_package_success_final.j2:11
msgid "· 考核结果：达标"
msgstr ""

#: app/templates/email/notice/ambassador_package_success_final.j2:16
#, python-format
msgid "你可以 <a href=\"%(package_url)s\">点击查看 %(asset)s激励包</a>。的详细信息"
msgstr ""

#: app/templates/email/notice/ambassador_package_suspend.j2:6
#, python-format
msgid "很遗憾，由于你的大使身份已连续三个月失效，你的<a href=\"%(package_url)s\">%(asset)s 激励包</a>已停止发放。"
msgstr ""

#: app/templates/email/notice/ambassador_package_suspend.j2:12
msgid "感谢你一直以来与CoinEx共同成长的每一段旅程，你的努力和付出我们都铭记在心。虽然本期激励包停止发放，但我们期待你能够重新回归大使群体，继续在社区建设与用户邀请中发挥重要作用。"
msgstr ""

#: app/templates/email/notice/ambassador_package_suspend.j2:14
#, python-format
msgid "<a href=\"%(apply_link)s\">立即申请成为大使</a>，重享高达50%%的USDT返佣！"
msgstr ""

#: app/templates/email/notice/ambassador_train_book.j2:3
#: app/templates/email/notice/bus_ambassador_train_book.j2:3
msgid "【CoinEx】成为大使"
msgstr ""

#: app/templates/email/notice/ambassador_train_book.j2:6
#: app/templates/email/notice/bus_ambassador_train_book.j2:6
msgid "欢迎加入CoinEx大使团队!"
msgstr ""

#: app/templates/email/notice/ambassador_train_book.j2:8
#: app/templates/email/notice/bus_ambassador_train_book.j2:8
msgid "CoinEx大使团队因你的加入而变得更加壮大，我们非常高兴能够在这个数字货币迅速发展的领域中与你携手同行。"
msgstr ""

#: app/templates/email/notice/ambassador_train_book.j2:10
#: app/templates/email/notice/bus_ambassador_train_book.j2:10
msgid "你的加入让我们向着实现“通过区块链，让世界更美好”的使命迈出了一大步。我们期望你的新视角和新想法能为CoinEx带来新的维度，也希望通过整个团队的共同努力为用户提供更好、更优质的服务。"
msgstr ""

#: app/templates/email/notice/ambassador_train_book.j2:12
#: app/templates/email/notice/bus_ambassador_train_book.j2:12
msgid "CoinEx致力于“让交易更简单“——这并不仅仅是品牌愿景或关于交易的口号，更是对用户体验和服务的关注。\"\"正直、创业、极致、共赢\"\"的价值观是我们发展道路上的重要支撑。始终把用户权益放在第一位，不断探索最先进的技术和最优质的服务，为广大用户提供安全、便捷、快速的加密服务，是CoinEx一如既往的初心，也是我们的每位团队成员的要求。"
msgstr ""

#: app/templates/email/notice/ambassador_train_book.j2:14
#: app/templates/email/notice/bus_ambassador_train_book.j2:14
msgid "我们相信，你的加入能增强我们的实力和广度。作为团队，所有成员齐心协力共同创造的成绩也将远远超过单枪匹马的战斗。"
msgstr ""

#: app/templates/email/notice/ambassador_train_book.j2:16
msgid "再次祝贺你成为CoinEx大使团队中的最新一员！"
msgstr ""

#: app/templates/email/notice/ambassador_train_book.j2:18
#: app/templates/email/notice/bus_ambassador_train_book.j2:16
msgid "希望CoinEx大使这个新角色能给你带去成就和挑战，祝你在CoinEx旅途愉快"
msgstr ""

#: app/templates/email/notice/ambassador_train_book.j2:19
#: app/templates/email/notice/bus_ambassador_train_book.j2:17
#, python-format
msgid "<a href=\"%(url)s\" target=\"_blank\">下载CoinEx大使手册</a>"
msgstr ""

#: app/templates/email/notice/anniversary_of_registration.j2:2
msgid "您已经注册CoinEx满1年啦！感谢过去1年来您的支持与陪伴。"
msgstr ""

#: app/templates/email/notice/anniversary_of_registration.j2:3
#, python-format
msgid "一直以来，CoinEx坚持以用户体验为本，不断提升产品与服务质量。目前CoinEx已经成为拥有%(asset_count)s个币种、%(market_count)s个交易对、服务全球%(country_count)s个国家和地区的交易所。未来，我们将继续聆听用户的声音，打磨产品与服务，为您提供更全面、细致的加密货币交易体验。"
msgstr ""

#: app/templates/email/notice/anniversary_of_registration.j2:4
msgid "再次感谢您选择、相信并陪伴CoinEx，在往后的时光里，我们期待与您携手前行、共登顶峰。最后祝您生活愉快，一切顺利！"
msgstr ""

#: app/templates/email/notice/anniversary_of_registration.j2:5
#: app/templates/email/notice/fifth_anniversary_of_registration.j2:5
#: app/templates/email/notice/fourth_anniversary_of_registration.j2:5
#: app/templates/email/notice/second_anniversary_of_registration.j2:5
#: app/templates/email/notice/third_anniversary_of_registration.j2:5
#, python-format
msgid "<a href=\"%(site_url)s\">点击进入CoinEx主页>></a>"
msgstr ""

#: app/templates/email/notice/announcement_notice.j2:50
msgid "退订"
msgstr ""

#: app/templates/email/notice/api_address_cancelled.j2:2
msgid "添加API提现白名单地址取消"
msgstr ""

#: app/templates/email/notice/api_address_cancelled.j2:4
#, python-format
msgid "你的账户于 %(time)s 添加API提现白名单地址，由于超过24小时未进行邮箱确认，系统已自动取消申请。"
msgstr ""

#: app/templates/email/notice/auto_invest_closed.j2:4
#, python-format
msgid "你的%(target_asset)s自动定投计划已到达设置的定投总额，定投计划已自动关闭。"
msgstr ""

#: app/templates/email/notice/auto_invest_closed.j2:5
#, python-format
msgid "定投总额：%(total_source_amount)s %(source_asset)s"
msgstr ""

#: app/templates/email/notice/auto_invest_closed.j2:6
#: app/templates/email/notice/auto_invest_deal_all.j2:8
#: app/templates/email/notice/auto_invest_deal_failed.j2:9
#: app/templates/email/notice/auto_invest_deal_partial.j2:9
#: app/templates/email/notice/auto_invest_failed.j2:5
#: app/templates/email/notice/auto_invest_paused.j2:5
#: app/templates/email/notice/auto_invest_profit_amount.j2:6
#: app/templates/email/notice/auto_invest_profit_rate.j2:6
#: app/templates/email/notice/exchange_order_failed.j2:12
#: app/templates/email/notice/exchange_order_finished.j2:8
#: app/templates/email/notice/exchange_order_partial.j2:9
#: app/templates/email/notice/margin_liquidation.j2:12
#: app/templates/email/notice/margin_liquidation_warning.j2:5
#: app/templates/email/notice/margin_loan_order_expired.j2:8
#: app/templates/email/notice/perpetual_adl.j2:12
#: app/templates/email/notice/perpetual_liquidation.j2:12
#: app/templates/email/notice/perpetual_liquidation_warning.j2:9
#: app/templates/email/notice/perpetual_position_reduce_notice.j2:12
#, python-format
msgid "详情可<a href='%(site_url)s'>登录</a>后查看"
msgstr ""

#: app/templates/email/notice/auto_invest_deal_all.j2:4
#, python-format
msgid "你的%(target_asset)s自动定投计划，本次定投已全部成交。"
msgstr ""

#: app/templates/email/notice/auto_invest_deal_all.j2:5
#: app/templates/email/notice/auto_invest_deal_failed.j2:5
#: app/templates/email/notice/auto_invest_deal_partial.j2:5
#, python-format
msgid "定投时间：%(time)s"
msgstr ""

#: app/templates/email/notice/auto_invest_deal_all.j2:6
#, python-format
msgid "定投金额：%(source_asset_traded_amount)s %(source_asset)s"
msgstr ""

#: app/templates/email/notice/auto_invest_deal_all.j2:7
#: app/templates/email/notice/auto_invest_deal_failed.j2:8
#: app/templates/email/notice/auto_invest_deal_partial.j2:8
#, python-format
msgid "买入数量：%(target_asset_traded_amount)s %(target_asset)s"
msgstr ""

#: app/templates/email/notice/auto_invest_deal_failed.j2:4
#, python-format
msgid "因市场深度原因，你的%(target_asset)s自动定投计划，本次定投未成交。"
msgstr ""

#: app/templates/email/notice/auto_invest_deal_failed.j2:6
#: app/templates/email/notice/auto_invest_deal_partial.j2:6
#, python-format
msgid "计划定投金额：%(source_asset_amount)s %(source_asset)s"
msgstr ""

#: app/templates/email/notice/auto_invest_deal_failed.j2:7
#: app/templates/email/notice/auto_invest_deal_partial.j2:7
#, python-format
msgid "实际成交金额：%(source_asset_traded_amount)s %(source_asset)s"
msgstr ""

#: app/templates/email/notice/auto_invest_deal_partial.j2:4
#, python-format
msgid "因市场深度原因，你的%(target_asset)s自动定投计划，本次定投部分成交。"
msgstr ""

#: app/templates/email/notice/auto_invest_profit_amount.j2:4
#: app/templates/email/notice/auto_invest_profit_rate.j2:4
#, python-format
msgid "你的%(target_asset)s自动定投计划已到达设置的盈利目标。"
msgstr ""

#: app/templates/email/notice/auto_invest_profit_amount.j2:5
#, python-format
msgid "收益额：%(profit_amount)s %(source_asset)s"
msgstr ""

#: app/templates/email/notice/auto_invest_profit_rate.j2:5
#, python-format
msgid "收益率：%(profit_rate)s%%"
msgstr ""

#: app/templates/email/notice/broker_approve.j2:3
msgid "欢迎成为CoinEx经纪商"
msgstr ""

#: app/templates/email/notice/broker_approve.j2:8
msgid "恭喜成为CoinEx的经纪商，初始返佣比例为40%，想要获得更高的返佣比例请联系你的专属机构经理。"
msgstr ""

#: app/templates/email/notice/broker_approve.j2:10
#, python-format
msgid ""
"<a style=\"color: #0ead98;\" href=\"%(broker_info_url)s\">查看你的Broker "
"ID>></a>"
msgstr ""

#: app/templates/email/notice/broker_approve.j2:11
#, python-format
msgid ""
"<a style=\"color: #0ead98;\" href=\"%(broker_api_url)s\">查看你的Broker "
"API>></a>"
msgstr ""

#: app/templates/email/notice/broker_auth_approve.j2:3
msgid "成功开启API授权"
msgstr ""

#: app/templates/email/notice/broker_auth_approve.j2:8
msgid "恭喜你已开通API授权功能，以下client_id和client_secret是识别你授权的唯一标识，请妥善保存，切勿泄露。"
msgstr ""

#: app/templates/email/notice/broker_auth_approve.j2:9
#, python-format
msgid "Client_ID：%(client_id)s"
msgstr ""

#: app/templates/email/notice/broker_auth_approve.j2:10
#, python-format
msgid "Client_Secret：%(client_secret)s"
msgstr ""

#: app/templates/email/notice/bus_ambassador_delete_notice.j2:3
msgid "大使淘汰提醒"
msgstr ""

#: app/templates/email/notice/bus_ambassador_delete_notice.j2:6
msgid "你在CoinEx成为大使至今，邀请数据为0，你的大使身份已失效。"
msgstr ""

#: app/templates/email/notice/bus_ambassador_delete_pre_notice.j2:3
msgid "大使预淘汰提醒"
msgstr ""

#: app/templates/email/notice/bus_ambassador_delete_pre_notice.j2:6
msgid "你在CoinEx成为大使至今，邀请数据为0，存在失效风险。"
msgstr ""

#: app/templates/email/notice/bus_ambassador_delete_pre_notice.j2:7
#, python-format
msgid "如果到%(end_at)s，邀请数据仍旧为0，你的大使身份将会失效，请您及时<a href=\"%(detail_url)s\">邀请好友</a>！"
msgstr ""

#: app/templates/email/notice/business_ambassador_referral_detail.j2:3
msgid "商务大使邀请数据"
msgstr ""

#: app/templates/email/notice/business_ambassador_referral_detail.j2:7
msgid "截至当天，您的大使邀请数据可见附件。"
msgstr ""

#: app/templates/email/notice/business_ambassador_referral_detail.j2:9
#, python-format
msgid "<a style=\"color: #0ead98;\" href=\"%(url)s\">点击下载</a>"
msgstr ""

#: app/templates/email/notice/coin_application_rejected.j2:4
#, python-format
msgid "感谢您申请来CoinEx上币。很抱歉地通知您，您申请的项目%(project_name)s暂时不符合我们的上币审核标准，但我们依然后会持续保持关注，期待项目有更大的进展。"
msgstr ""

#: app/templates/email/notice/coin_application_rejected.j2:6
#: app/templates/email/notice/ieo_application_rejected.j2:6
msgid "祝好！"
msgstr ""

#: app/templates/email/notice/comment_interaction.j2:4
#, python-format
msgid "<a href=\"%(site_url)s\">点击查看详情>></a>"
msgstr ""

#: app/templates/email/notice/coupon_balance_warn.j2:2
msgid "卡券账户资金不足，请补充资金"
msgstr ""

#: app/templates/email/notice/coupon_balance_warn.j2:4
#, python-format
msgid "%(warn_text)s"
msgstr ""

#: app/templates/email/notice/credit_risk_withdraw_close.j2:6
msgid "您的授信账户当前风险率已低于可提现风险率，系统已关闭提现功能, 请尽快补充非授信资产。"
msgstr ""

#: app/templates/email/notice/credit_risk_withdraw_close.j2:9
#: app/templates/email/notice/credit_risk_withdraw_close_pre_warning.j2:9
msgid "当前风险率"
msgstr ""

#: app/templates/email/notice/credit_risk_withdraw_close.j2:14
#: app/templates/email/notice/credit_risk_withdraw_close_pre_warning.j2:14
msgid "可提现风险率"
msgstr ""

#: app/templates/email/notice/credit_risk_withdraw_close.j2:18
#: app/templates/email/notice/credit_risk_withdraw_close_pre_warning.j2:18
msgid "*当前风险率需大于可提现风险率时,提现功能自动开启"
msgstr ""

#: app/templates/email/notice/credit_risk_withdraw_close_pre_warning.j2:6
msgid "您的授信账户当前风险率较低，已进入预警范围，存在提现功能被关闭的风险，请尽快补充非授信资产。"
msgstr ""

#: app/templates/email/notice/deposit_amount_too_small.j2:3
msgid "充值低于最小金额"
msgstr ""

#: app/templates/email/notice/deposit_amount_too_small.j2:6
#, python-format
msgid "你于 %(deposit_time_str)s 进行的充值，金额为：%(amount)s %(asset_chain_str)s；"
msgstr ""

#: app/templates/email/notice/deposit_amount_too_small.j2:7
#, python-format
msgid ""
"由于低于%(min_amount)s %(asset_chain_str)s的最小充值要求，无法入账，请往此地址继续充值 "
"%(diff_amount)s %(asset_chain_str)s， 即可全部入账。"
msgstr ""

#: app/templates/email/notice/deposit_amount_too_small.j2:8
#, python-format
msgid "前往%(asset_chain_str)s充值：<a href='%(url)s'>%(url)s</a>。"
msgstr ""

#: app/templates/email/notice/deposit_bonus_rewords_result.j2:4
msgid "充值福利：币种奖励发放通知"
msgstr ""

#: app/templates/email/notice/deposit_bonus_rewords_result.j2:6
msgid "充值福利：卡券奖励发放通知"
msgstr ""

#: app/templates/email/notice/deposit_bonus_rewords_result.j2:10
msgid "恭喜你获得充值活动奖励。"
msgstr ""

#: app/templates/email/notice/deposit_bonus_rewords_result.j2:11
#, python-format
msgid "活动名称：%(title)s"
msgstr ""

#: app/templates/email/notice/deposit_bonus_rewords_result.j2:12
#, python-format
msgid "活动奖励：%(rewards)s"
msgstr ""

#: app/templates/email/notice/deposit_bonus_rewords_result.j2:14
#, python-format
msgid "活动奖励已发放至现货账户，<a href='%(url)s'>立即查看>></a>"
msgstr ""

#: app/templates/email/notice/deposit_bonus_rewords_result.j2:16
#, python-format
msgid "活动奖励已发放至我的卡券，<a href='%(url)s'>立即查看>></a>"
msgstr ""

#: app/templates/email/notice/deposit_bonus_rewords_result.j2:19
msgid "感谢你对CoinEx的信任和支持！我们将继续为你提供最新活动资讯，并致力于为你带来更好的交易体验。"
msgstr ""

#: app/templates/email/notice/deposit_pass_notice.j2:6
#, python-format
msgid "交易ID：%(transaction_id)s"
msgstr ""

#: app/templates/email/notice/deposit_pass_notice.j2:8
#, python-format
msgid "你于%(create_time)s通过内部转账充值的%(amount)s已到账。"
msgstr ""

#: app/templates/email/notice/deposit_pass_notice.j2:10
#: app/templates/email/notice/margin_loan_order_force_flat.j2:12
#: app/templates/email/notice/margin_renew_failed.j2:8
#: app/templates/email/notice/send_coin_withdraw_notice.j2:13
#: app/templates/email/notice/withdrawal_cancelled_notice.j2:12
#: app/templates/email/notice/withdrawal_expired_notice.j2:12
#, python-format
msgid "详情可<a href='%(url)s'>登录</a>后查看"
msgstr ""

#: app/templates/email/notice/deposit_privacy_asset_require_kyc.j2:2
msgid "充值隐私币请完成实名认证"
msgstr ""

#: app/templates/email/notice/deposit_privacy_asset_require_kyc.j2:4
#, python-format
msgid "你有一笔充值即将入账，充值金额：%(amount)s"
msgstr ""

#: app/templates/email/notice/deposit_privacy_asset_require_kyc.j2:5
#, python-format
msgid ""
"基于CoinEx的AML政策，充值隐私币，必须通过实名认证后，才可入账，请先<a "
"href=\"%(site_url)s/my/info/kyc\">前往认证</a>。"
msgstr ""

#: app/templates/email/notice/deposit_privacy_asset_require_kyc.j2:7
msgid "实名认证通过后10分钟左右即可入账，请耐心等待。"
msgstr ""

#: app/templates/email/notice/deposit_resumed_notice.j2:3
msgid "充值恢复通知"
msgstr ""

#: app/templates/email/notice/deposit_resumed_notice.j2:6
#, python-format
msgid "CoinEx已恢复 %(asset_chain_str)s 的充币业务。暂停期间给您带来的不便，敬请谅解！"
msgstr ""

#: app/templates/email/notice/deposit_resumed_notice.j2:7
#, python-format
msgid "前往%(asset)s充值：<a href='%(url)s'>%(url)s</a>"
msgstr ""

#: app/templates/email/notice/deposit_to_receive.j2:2
msgid "充值待入账"
msgstr ""

#: app/templates/email/notice/deposit_to_receive.j2:4
msgid "你有一笔充值已在区块链上可查。"
msgstr ""

#: app/templates/email/notice/deposit_to_receive.j2:5
#, python-format
msgid "充值金额：%(amount)s"
msgstr ""

#: app/templates/email/notice/deposit_to_receive.j2:6
#, python-format
msgid "当前区块确认数：%(confirmations)s"
msgstr ""

#: app/templates/email/notice/deposit_to_receive.j2:7
#, python-format
msgid "入账所需区块确认数：%(freeze_confirmations)s"
msgstr ""

#: app/templates/email/notice/deposit_to_receive.j2:8
msgid "此过程大约需要5-30分钟，若该笔交易手续费过低或区块拥堵，则所需时间更长。请不要着急，耐心等待。"
msgstr ""

#: app/templates/email/notice/deposit_to_receive.j2:9
msgid "您可在以下区块浏览器中查看详情："
msgstr ""

#: app/templates/email/notice/deposit_to_receive.j2:11
msgid "您无需任何操作，若充值已入账，我们会再次通过邮件告知您。"
msgstr ""

#: app/templates/email/notice/dibs_rewords_fail.j2:4
#, python-format
msgid "抱歉，很遗憾，在CoinEx的“%(title)s”活动中，未中奖，期待下次参与。"
msgstr ""

#: app/templates/email/notice/dibs_rewords_success.j2:4
#, python-format
msgid "恭喜您，在CoinEx的“%(title)s”活动中，获得%(amount)s%(asset)s，申购已发放。"
msgstr ""

#: app/templates/email/notice/edd_extra_info_required.j2:4
msgid "请提供充值证明补充资料"
msgstr ""

#: app/templates/email/notice/edd_extra_info_required.j2:8
#, python-format
msgid "你于 %(deposit_at)s 充值的 %(amount)s %(asset)s ，你所提供的充值证明文件未通过审核，原因为："
msgstr ""

#: app/templates/email/notice/edd_extra_info_required.j2:10
#: app/templates/email/notice/kyt_deposit_extra_info_required.j2:10
#, python-format
msgid "请 <a href=\"%(kyt_page)s\">点击这里</a> 补充充值证明文件。"
msgstr ""

#: app/templates/email/notice/edd_extra_info_required.j2:11
#: app/templates/email/notice/edd_freezing.j2:10
#: app/templates/email/notice/edd_info_required.j2:13
#: app/templates/email/notice/kyt_deposit_extra_info_required.j2:11
#: app/templates/email/notice/kyt_deposit_freezing.j2:10
#: app/templates/email/notice/kyt_deposit_info_required.j2:17
msgid "充值TXID:"
msgstr ""

#: app/templates/email/notice/edd_freezing.j2:4
#: app/templates/email/notice/kyt_deposit_freezing.j2:4
msgid "充值证明审核不通过"
msgstr ""

#: app/templates/email/notice/edd_freezing.j2:8
#: app/templates/email/notice/kyt_deposit_freezing.j2:8
#, python-format
msgid "你于 %(deposit_at)s 充值的 %(amount)s %(asset)s ，未能通过审核且此充值资产已被冻结，原因为："
msgstr ""

#: app/templates/email/notice/edd_info_required.j2:4
#: app/templates/email/notice/kyt_deposit_info_required.j2:5
msgid "请提供充值证明"
msgstr ""

#: app/templates/email/notice/edd_info_required.j2:8
#, python-format
msgid "你于 %(deposit_at)s 充值的 %(amount)s %(asset)s ，因应合规要求需先完成以下事项，才可完成充值流程。"
msgstr ""

#: app/templates/email/notice/edd_info_required.j2:10
#: app/templates/email/notice/edd_manual_info_required.j2:10
#, python-format
msgid "完成个人实名认证：可于 <a href='%(kyc_page)s'>实名认证</a> 进行认证"
msgstr ""

#: app/templates/email/notice/edd_info_required.j2:12
#, python-format
msgid "提供资金来源证明及增强尽职调查证明：请 <a href=\"%(edd_page)s\">点击这里</a> 上传文件。"
msgstr ""

#: app/templates/email/notice/edd_manual_extra_info_required.j2:4
msgid "请提供增强尽职调查证明补充资料"
msgstr ""

#: app/templates/email/notice/edd_manual_extra_info_required.j2:8
msgid "你所提供的增强尽职调查证明文件未通过审核，原因为："
msgstr ""

#: app/templates/email/notice/edd_manual_extra_info_required.j2:10
#, python-format
msgid "请 <a href=\"%(edd_page)s\">点击这里</a> 补充充值证明文件。"
msgstr ""

#: app/templates/email/notice/edd_manual_info_required.j2:4
msgid "请提供增强尽职调查证明"
msgstr ""

#: app/templates/email/notice/edd_manual_info_required.j2:8
msgid "检测到你的账户存在风险，因应合规要求需先完成以下事项，才可继续使用。"
msgstr ""

#: app/templates/email/notice/edd_manual_info_required.j2:12
#: app/templates/email/notice/kyt_deposit_info_required.j2:16
#, python-format
msgid "提供资金来源及地址证明及增强尽职调查表格：请 <a href=\"%(kyt_page)s\">点击这里</a> 上传文件。"
msgstr ""

#: app/templates/email/notice/edd_manual_pass.j2:4
msgid "增强尽职调查证明审核通过"
msgstr ""

#: app/templates/email/notice/edd_manual_pass.j2:8
msgid "你所提供的增强尽职调查证明已通过审核，账户已恢复正常。"
msgstr ""

#: app/templates/email/notice/edit_login_password.j2:3
msgid "您在CoinEx更改了登录密码，为了保障您的账号安全，24小时内禁止提现。"
msgstr ""

#: app/templates/email/notice/edit_mobile.j2:3
msgid "您在CoinEx绑定的手机修改成功，为了保障您的账号安全，24小时内禁止提现。"
msgstr ""

#: app/templates/email/notice/edit_totp.j2:3
msgid "您在CoinEx的绑定的TOTP修改成功，为了保障您的账号安全，24小时内禁止提现。"
msgstr ""

#: app/templates/email/notice/edit_trade_password.j2:2
msgid "资金密码修改提醒"
msgstr ""

#: app/templates/email/notice/edit_trade_password.j2:4
#, python-format
msgid "您在%(create_time)s更改了资金密码，如非您本人操作，请尽快联系我们锁定账户，以避免资金被盗。"
msgstr ""

#: app/templates/email/notice/edit_withdraw_password.j2:3
msgid "你已成功修改提现密码。为了保障你的账户安全，24小时内禁止提现。"
msgstr ""

#: app/templates/email/notice/email_registered.j2:2
msgid "邮箱已注册"
msgstr ""

#: app/templates/email/notice/email_registered.j2:4
#, python-format
msgid "你的邮箱 %(email)s 已经注册，可以直接<a href='%(url)s'>登录官网</a>"
msgstr ""

#: app/templates/email/notice/email_reset_pass.j2:3
msgid "您在CoinEx的绑定邮箱重置成功，后续只能用新邮箱登录账户，为了保障您的账号安全，24小时内禁止提现。"
msgstr ""

#: app/templates/email/notice/exchange_order_failed.j2:2
msgid "兑换失败通知"
msgstr ""

#: app/templates/email/notice/exchange_order_failed.j2:4
#, python-format
msgid "你创建的 %(market)s 兑换交易失败，未兑换部分已返还至你的现货账户。"
msgstr ""

#: app/templates/email/notice/exchange_order_failed.j2:5
#: app/templates/email/notice/exchange_order_finished.j2:5
#: app/templates/email/notice/exchange_order_partial.j2:5
#, python-format
msgid "创建时间：%(time)s"
msgstr ""

#: app/templates/email/notice/exchange_order_failed.j2:6
#: app/templates/email/notice/exchange_order_partial.j2:8
#, python-format
msgid "返还数量：%(source_asset_remain_amount)s %(source_asset)s"
msgstr ""

#: app/templates/email/notice/exchange_order_failed.j2:9
msgid "温馨提示：由于市场行情波动等原因，请以实际返还的数量为准。"
msgstr ""

#: app/templates/email/notice/exchange_order_finished.j2:2
msgid "完全兑换成功通知"
msgstr ""

#: app/templates/email/notice/exchange_order_finished.j2:4
#, python-format
msgid "你创建的%(market)s兑换交易已全部成交。"
msgstr ""

#: app/templates/email/notice/exchange_order_finished.j2:6
#: app/templates/email/notice/exchange_order_partial.j2:6
#, python-format
msgid "成交数量：%(source_asset_exchanged_amount)s %(source_asset)s"
msgstr ""

#: app/templates/email/notice/exchange_order_finished.j2:7
#: app/templates/email/notice/exchange_order_partial.j2:7
#, python-format
msgid "兑换获得：%(target_asset_exchanged_amount)s %(target_asset)s"
msgstr ""

#: app/templates/email/notice/exchange_order_partial.j2:2
msgid "部分兑换成功通知"
msgstr ""

#: app/templates/email/notice/exchange_order_partial.j2:4
#, python-format
msgid "你创建的%(market)s兑换交易已部分成交，剩余未兑换部分已返还至你的现货账户。"
msgstr ""

#: app/templates/email/notice/fifth_anniversary_of_registration.j2:2
msgid "5年来，您一路见证CoinEx的成长和进步，而我们的今天也离不开您的陪伴和支持，我们想向您表达最诚挚的谢意。"
msgstr ""

#: app/templates/email/notice/fifth_anniversary_of_registration.j2:3
#, python-format
msgid "目前CoinEx已经成为拥有%(asset_count)s个币种、%(market_count)s个交易对、服务全球%(country_count)s个国家和地区的交易所。未来我们将继续致力于推出优质资产、丰富产品形态、提升您的交易体验。"
msgstr ""

#: app/templates/email/notice/fifth_anniversary_of_registration.j2:4
#: app/templates/email/notice/fourth_anniversary_of_registration.j2:4
#: app/templates/email/notice/second_anniversary_of_registration.j2:4
#: app/templates/email/notice/third_anniversary_of_registration.j2:4
msgid "再次感谢您选择并陪伴CoinEx。祝您生活愉快，一切顺利！"
msgstr ""

#: app/templates/email/notice/fourth_anniversary_of_registration.j2:2
msgid "时光匆匆，我们已携手走过了4年时光。感谢你始终如一的陪伴和支持，让这段旅程变得无比珍贵。"
msgstr ""

#: app/templates/email/notice/fourth_anniversary_of_registration.j2:3
#, python-format
msgid ""
"为满足用户的多元需求，CoinEx "
"不断丰富产品形态，提供币币交易、合约交易、理财借贷等一系列产品及服务。目前CoinEx已经成为拥有%(asset_count)s个币种、%(market_count)s个交易对、服务全球%(country_count)s个国家和地区的交易所。未来，我们将继续以用户需求为核心，打磨产品与服务，为您的投资之旅提供更优质的体验。"
msgstr ""

#: app/templates/email/notice/get_promotion_gift.j2:2
msgid "活动奖励通知"
msgstr ""

#: app/templates/email/notice/get_promotion_gift.j2:4
#, python-format
msgid ""
"您在CoinEx活动中获得的%(amount)s个%(coin_type)s已发放，请登录<a "
"href=\"%(wallet_url)s\">钱包</a>查看。"
msgstr ""

#: app/templates/email/notice/ieo_application_rejected.j2:4
#, python-format
msgid ""
"感谢您申请与CoinEx "
"Dock合作。很抱歉地通知您，您申请的项目%(project_name)s暂时不符合我们的合作审核标准，但我们依然后会持续保持关注，期待项目有更大的进展。"
msgstr ""

#: app/templates/email/notice/ieo_rewords_fail.j2:4
#, python-format
msgid "%(project_name)s 申购未中签"
msgstr ""

#: app/templates/email/notice/ieo_rewords_fail.j2:9
#, python-format
msgid "很遗憾，在%(project_name)s 项目的Dock申购中，你未中签。"
msgstr ""

#: app/templates/email/notice/ieo_rewords_fail.j2:13
#: app/templates/email/notice/ieo_rewords_success.j2:29
msgid "*提示"
msgstr ""

#: app/templates/email/notice/ieo_rewords_fail.j2:14
#: app/templates/email/notice/ieo_rewords_success.j2:30
#, python-format
msgid "质押的%(pledge_asset)s已解冻并退回您的现货账户"
msgstr ""

#: app/templates/email/notice/ieo_rewords_fail.j2:15
#: app/templates/email/notice/ieo_rewords_success.j2:31
msgid "未中签的申购资金已退回您的现货账户"
msgstr ""

#: app/templates/email/notice/ieo_rewords_fail.j2:19
#: app/templates/email/notice/ieo_rewords_success.j2:35
msgid "查看Dock订单"
msgstr ""

#: app/templates/email/notice/ieo_rewords_success.j2:10
#, python-format
msgid "%(project_name)s 申购已中签"
msgstr ""

#: app/templates/email/notice/ieo_rewords_success.j2:15
msgid "恭喜您在CoinEx Dock成功登船！"
msgstr ""

#: app/templates/email/notice/ieo_rewords_success.j2:16
#, python-format
msgid "项目名称：%(project_name)s"
msgstr ""

#: app/templates/email/notice/ieo_rewords_success.j2:17
#, python-format
msgid "中签份数：%(count)s"
msgstr ""

#: app/templates/email/notice/ieo_rewords_success.j2:18
#, python-format
msgid "中签数量：%(amount)s %(asset)s"
msgstr ""

#: app/templates/email/notice/ieo_rewords_success.j2:20
#, python-format
msgid "中签资产已发放至你的现货账户，并于%(unlocked_at)s 解冻。"
msgstr ""

#: app/templates/email/notice/ieo_rewords_success.j2:22
msgid "中签资产已发放至你的现货账户。"
msgstr ""

#: app/templates/email/notice/ieo_rewords_success.j2:26
#, python-format
msgid "注：该项目代币有特殊释放机制 <a class='btn-link' href='%(rule_url)s'>查看详情</a>"
msgstr ""

#: app/templates/email/notice/kyc_fail.j2:2
msgid "您的实名认证未通过审核"
msgstr ""

#: app/templates/email/notice/kyc_fail.j2:4
msgid "很抱歉，您的实名认证未通过审核。"
msgstr ""

#: app/templates/email/notice/kyc_fail.j2:6
#, python-format
msgid "原因：<strong>%(verify_id_result)s</strong>"
msgstr ""

#: app/templates/email/notice/kyc_fail.j2:8
#, python-format
msgid ""
"您可以<a href=\"%(account_link)s\">点击这里</a>重新提交认证资料。<a "
"href=\"%(support_url)s/hc/articles/************\">如何完成实名认证？</a>"
msgstr ""

#: app/templates/email/notice/kyc_fail.j2:9
#: app/templates/email/notice/kyc_institution_fail.j2:9
#: app/templates/email/notice/kyc_pass.j2:5
#: app/templates/email/notice/kyc_pro_fail.j2:9
#: app/templates/email/notice/kyc_pro_pass.j2:6
#: app/templates/email/notice/p2p/add_payment_channel.j2:6
#: app/templates/email/notice/p2p/become_merchant.j2:5
#: app/templates/email/notice/p2p/become_merchant_not_2fa.j2:5
#: app/templates/email/notice/withdraw_password_reset_fail.j2:9
#, python-format
msgid "如非本人操作，请尽快<a href=\"%(support_url)s/hc/requests/new\">联系我们</a>。"
msgstr ""

#: app/templates/email/notice/kyc_institution_fail.j2:4
msgid "很抱歉，您的机构认证未通过审核。"
msgstr ""

#: app/templates/email/notice/kyc_institution_fail.j2:6
#, python-format
msgid "原因：%(reject_reason)s"
msgstr ""

#: app/templates/email/notice/kyc_institution_fail.j2:8
#, python-format
msgid ""
"您可以<a href=\"%(account_link)s\">点击这里</a>重新提交认证资料。<a "
"href=\"%(support_url)s/hc/articles/*************\">如何完成实名认证？</a>"
msgstr ""

#: app/templates/email/notice/kyc_institution_pass.j2:5
#, python-format
msgid ""
"恭喜，您的机构认证已通过审核，系统已为您开启对应权益，详情请登录<a "
"href='%(home_link)s'>CoinEx官网</a>查看。如非本人操作，请尽快<a "
"href='%(support_url)s/hc/requests/new'>联系我们</a>"
msgstr ""

#: app/templates/email/notice/kyc_pass.j2:2
msgid "您的实名认证已通过审核"
msgstr ""

#: app/templates/email/notice/kyc_pass.j2:4
#, python-format
msgid "恭喜，您的实名认证(初级)已通过审核，系统已为您开启更多权益，详情请<a href=\"%(home_link)s\">登录官网</a>查看。"
msgstr ""

#: app/templates/email/notice/kyc_pro_fail.j2:4
msgid "抱歉，您的KYC高级认证未通过审核。"
msgstr ""

#: app/templates/email/notice/kyc_pro_fail.j2:6
#, python-format
msgid "原因是：<strong>%(verify_id_result)s</strong>"
msgstr ""

#: app/templates/email/notice/kyc_pro_fail.j2:8
#, python-format
msgid "您可以<a href=\"%(account_link)s\">点击这里</a>重新提交认证资料。"
msgstr ""

#: app/templates/email/notice/kyc_pro_pass.j2:5
#, python-format
msgid "详情请前往<a href=\"%(home_link)s\">CoinEx官网</a>查看。"
msgstr ""

#: app/templates/email/notice/kyt_deposit_extra_info_required.j2:4
msgid "请重新提供充值证明文件"
msgstr ""

#: app/templates/email/notice/kyt_deposit_extra_info_required.j2:8
#, python-format
msgid "关于 %(deposit_at)s 充值的 %(amount)s %(asset)s ，你所提供的充值证明文件未通过，原因为："
msgstr ""

#: app/templates/email/notice/kyt_deposit_extra_info_required.j2:13
#: app/templates/email/notice/kyt_deposit_freezing.j2:12
#: app/templates/email/notice/kyt_deposit_info_required.j2:19
#: app/templates/email/notice/kyt_deposit_withdrawal_only.j2:10
#, python-format
msgid "如果任何疑问，请与我们<a href=\"%(support_url)s/hc/requests/new\">客服联系</a>。"
msgstr ""

#: app/templates/email/notice/kyt_deposit_info_required.j2:7
msgid "请完成KYC认证并提供充值证明"
msgstr ""

#: app/templates/email/notice/kyt_deposit_info_required.j2:12
#, python-format
msgid ""
"你于 %(deposit_at)s 充值的 %(amount)s %(asset)s "
"，因应合规要求需先完成以下事项，才可完成充值流程。同时，你的充值地址已被冻结。"
msgstr ""

#: app/templates/email/notice/kyt_deposit_info_required.j2:14
#, python-format
msgid "完成个人KYC认证：可于 <a href='%(kyc_page)s'>实名认证</a> 进行认证"
msgstr ""

#: app/templates/email/notice/kyt_deposit_withdrawal_only.j2:4
msgid "帐户只支持提现资产"
msgstr ""

#: app/templates/email/notice/kyt_deposit_withdrawal_only.j2:8
msgid "因应合规要求，你的帐户只能进行提现。"
msgstr ""

#: app/templates/email/notice/kyt_deposit_withdrawal_only.j2:9
msgid "请尽快于平台【资产】－【提现】把资产转出。"
msgstr ""

#: app/templates/email/notice/liveness_check.j2:3
msgid "为了验证你的身份，你需要点击下方按钮完成生物识别验证。"
msgstr ""

#: app/templates/email/notice/liveness_check.j2:5
msgid "去验证"
msgstr ""

#: app/templates/email/notice/liveness_check.j2:7
msgid "如果点击上方按钮无法进行验证，请复制下方地址至浏览器地址栏进行访问："
msgstr ""

#: app/templates/email/notice/liveness_check.j2:8
#, python-format
msgid "%(site_url)s"
msgstr ""

#: app/templates/email/notice/liveness_check.j2:9
#, python-format
msgid "为了保证账号安全，该链接%(hours)s小时内有效，请及时完成验证。"
msgstr ""

#: app/templates/email/notice/maker_cashback_level_appraisal.j2:7
#: app/templates/email/notice/maker_cashback_level_change.j2:7
#, python-format
msgid "恭喜，您的现货Maker返现等级已升级，升级为%(new_level)s级，可享受现货Maker返现比例为：%(rate)s。"
msgstr ""

#: app/templates/email/notice/maker_cashback_level_appraisal.j2:9
#: app/templates/email/notice/maker_cashback_level_change.j2:9
#, python-format
msgid "抱歉，您的现货Maker返现等级已降级，降级为%(new_level)s级，可享受现货Maker返现比例为：%(rate)s。"
msgstr ""

#: app/templates/email/notice/maker_cashback_level_appraisal.j2:11
#, python-format
msgid "您的现货Maker返现机制月度考核等级不变，等级为%(new_level)s级，可享受现货Maker返现比例为：%(rate)s。"
msgstr ""

#: app/templates/email/notice/maker_cashback_level_appraisal.j2:13
#, python-format
msgid "抱歉，您上个月现货交易量排名＞%(ranking_count)s，已经失去了现货Maker返现资格。如果本月现货交易量排名≤%(ranking_count)s，下个月将重新获得现货Maker返现资格。"
msgstr ""

#: app/templates/email/notice/maker_cashback_user_daily_report.j2:4
#, python-format
msgid "您昨日（%(report_date)s）的现货Maker返现已发放，请登录CoinEx账户查看。"
msgstr ""

#: app/templates/email/notice/maker_cashback_user_daily_report.j2:5
#, python-format
msgid "您的做市商等级为LV%(level)s，享受现货Maker费率为：%(rate)s，交易时按0%%手续费计算，负费率通过次日返现的方式结算。昨日具体返现金额如下："
msgstr ""

#: app/templates/email/notice/maker_cashback_user_daily_report.j2:6
msgid "总返现（主账号+所有子账号）"
msgstr ""

#: app/templates/email/notice/maker_cashback_user_daily_report.j2:12
msgid "主账号"
msgstr ""

#: app/templates/email/notice/maker_cashback_user_daily_report.j2:19
msgid "子账号"
msgstr ""

#: app/templates/email/notice/margin_function_introduction.j2:3
msgid "恭喜你成功开通CoinEx杠杆功能"
msgstr ""

#: app/templates/email/notice/margin_function_introduction.j2:7
#, python-format
msgid ""
"恭喜你已成功开通CoinEx杠杆功能，现在你可以向<a href=\"%(url)s\" style=\"color: "
"#0EAD98;\">杠杆账户</a>中转入资产开始交易。"
msgstr ""

#: app/templates/email/notice/margin_function_introduction.j2:10
msgid "玩转杠杆 实现多空双盈"
msgstr ""

#: app/templates/email/notice/margin_function_introduction.j2:13
msgid "以BTC/USDT为例"
msgstr ""

#: app/templates/email/notice/margin_function_introduction.j2:24
msgid "看涨BTC"
msgstr ""

#: app/templates/email/notice/margin_function_introduction.j2:34
msgid "看跌BTC"
msgstr ""

#: app/templates/email/notice/margin_function_introduction.j2:42
#: app/templates/email/notice/margin_function_introduction.j2:46
msgid "转入BTC或USDT至对应杠杆账户"
msgstr ""

#: app/templates/email/notice/margin_function_introduction.j2:44
msgid "划转"
msgstr ""

#: app/templates/email/notice/margin_function_introduction.j2:62
msgid "借入USDT购买BTC"
msgstr ""

#: app/templates/email/notice/margin_function_introduction.j2:64
msgid "借入"
msgstr ""

#: app/templates/email/notice/margin_function_introduction.j2:66
msgid "借入BTC后卖出获得USDT"
msgstr ""

#: app/templates/email/notice/margin_function_introduction.j2:82
msgid "待BTC上涨卖出BTC"
msgstr ""

#: app/templates/email/notice/margin_function_introduction.j2:86
msgid "待BTC下跌买回BTC"
msgstr ""

#: app/templates/email/notice/margin_function_introduction.j2:102
msgid "归还USDT本金和利息获得剩余的USDT收益"
msgstr ""

#: app/templates/email/notice/margin_function_introduction.j2:104
msgid "还币"
msgstr ""

#: app/templates/email/notice/margin_function_introduction.j2:106
msgid "归还BTC本金和利息获得剩余的USDT收益"
msgstr ""

#: app/templates/email/notice/margin_function_introduction.j2:113
msgid "请注意：杠杆交易风险与收益并存，请避免使用高杠杆的重仓交易。"
msgstr ""

#: app/templates/email/notice/cashback_fee/used_deadline_notice.j2:11
#: app/templates/email/notice/margin_function_introduction.j2:118
#: app/templates/email/notice/perpetual_trading_introduction.j2:11
msgid "前往交易"
msgstr ""

#: app/templates/email/notice/margin_function_introduction.j2:122
#, python-format
msgid "了解更多：<a href=\"%(url)s\" style=\"color: #0EAD98;\">CoinEx杠杆交易介绍</a>"
msgstr ""

#: app/templates/email/notice/margin_liquidation.j2:2
msgid "【CoinEx】强制还币通知"
msgstr ""

#: app/templates/email/notice/margin_liquidation.j2:4
#: app/templates/email/notice/margin_loan_order_force_flat.j2:4
msgid "你的杠杆借币订单已触发强制还币流程。"
msgstr ""

#: app/templates/email/notice/margin_liquidation.j2:5
#: app/templates/email/notice/margin_loan_order_expired.j2:5
#: app/templates/email/notice/margin_loan_order_force_flat.j2:5
#: app/templates/email/notice/margin_renew_failed.j2:5
#, python-format
msgid "杠杆账户：%(market_name)s"
msgstr ""

#: app/templates/email/notice/margin_liquidation.j2:6
msgid "强制还币金额："
msgstr ""

#: app/templates/email/notice/margin_liquidation.j2:11
#, python-format
msgid "强制还币原因：账户风险率已低于%(risk_rate)s"
msgstr ""

#: app/templates/email/notice/margin_liquidation_warning.j2:2
msgid "【CoinEx】强制还币预警"
msgstr ""

#: app/templates/email/notice/margin_liquidation_warning.j2:4
#, python-format
msgid ""
"你的 %(market_name)s 杠杆市场的风险率已低于 "
"%(risk_limit)s%%，请及时还币或增加账户资产，调整杠杆，以免发生强制还币。"
msgstr ""

#: app/templates/email/notice/margin_loan_order_expired.j2:4
msgid "你的杠杆借币订单即将到期，请及时还币或开启自动续期。"
msgstr ""

#: app/templates/email/notice/margin_loan_order_expired.j2:6
#: app/templates/email/notice/margin_renew_failed.j2:6
#, python-format
msgid "待还金额：%(amount)s %(asset)s"
msgstr ""

#: app/templates/email/notice/margin_loan_order_expired.j2:7
#: app/templates/email/notice/margin_renew_failed.j2:7
#, python-format
msgid "到期时间：%(expired_time)s"
msgstr ""

#: app/templates/email/notice/margin_loan_order_force_flat.j2:6
#, python-format
msgid "强制还币金额：%(amount)s %(asset)s"
msgstr ""

#: app/templates/email/notice/margin_loan_order_force_flat.j2:8
msgid "强制还币原因：借币池余额不足"
msgstr ""

#: app/templates/email/notice/margin_loan_order_force_flat.j2:10
msgid "强制还币原因：到期未续借或续借失败"
msgstr ""

#: app/templates/email/notice/margin_loan_order_renew_succeeded.j2:2
msgid "杠杆续借成功通知"
msgstr ""

#: app/templates/email/notice/margin_loan_order_renew_succeeded.j2:5
#, python-format
msgid ""
"您的CoinEx的账户 %(user_name)s 在杠杆市场 %(market)s 的借币订单 %(amount)s %(asset)s "
"续借成功。"
msgstr ""

#: app/templates/email/notice/margin_loan_order_renew_succeeded.j2:9
#, python-format
msgid "最新借币日利率为：%(day_rate)s。"
msgstr ""

#: app/templates/email/notice/margin_loan_order_renew_succeeded.j2:11
#, python-format
msgid "续借周期为：%(renew_days)s 天，将于%(expired_time)s (UTC) 到期。"
msgstr ""

#: app/templates/email/notice/margin_loan_order_renew_succeeded.j2:14
#: app/templates/email/notice/perpetual_position_close_result_notice.j2:16
#: app/templates/email/notice/perpetual_position_close_result_notice.j2:29
#: app/templates/email/notice/perpetual_profit_loss_notice.j2:39
#: app/templates/email/notice/perpetual_profit_loss_notice.j2:63
#, python-format
msgid "详情可<a href=\"%(home_link)s\">登录</a>查看。"
msgstr ""

#: app/templates/email/notice/margin_renew_failed.j2:4
msgid "你的杠杆借币订单续借失败，请及时还币，以免触发强制还币流程。"
msgstr ""

#: app/templates/email/notice/mining_rewords.j2:4
#, python-format
msgid ""
"恭喜您，昨日在CoinEx的“%(activity_name)s“活动中，瓜分到%(amount)s "
"%(asset)s的奖励，奖励已发放，请到现货账户查看。"
msgstr ""

#: app/templates/email/notice/mining_rewords.j2:6
#, python-format
msgid "恭喜您，昨日在CoinEx的“%(activity_name)s”活动中，瓜分到%(amount)s %(asset)s的奖励，奖励已发放。"
msgstr ""

#: app/templates/email/notice/mobile_reset_pass.j2:3
msgid "您在CoinEx的绑定手机解绑成功，为了保障您的账号安全，24小时内禁止提现。您的账号已失去手机验证的保护，请及时重新绑定手机验证，以免造成损失。"
msgstr ""

#: app/templates/email/notice/multi_approve_join.j2:2
msgid "成员加入提现审核通知"
msgstr ""

#: app/templates/email/notice/multi_approve_join.j2:4
#, python-format
msgid "%(target_name)s(%(account)s) 加入了你的提现审核邀请。"
msgstr ""

#: app/templates/email/notice/multi_approve_leave.j2:2
msgid "提现审核移除成功"
msgstr ""

#: app/templates/email/notice/multi_approve_leave.j2:4
#, python-format
msgid "你已将 %(target_name)s(%(account)s) 从提现审核名单中移除，即刻起，提现相关审核无需该用户的邮件确认。"
msgstr ""

#: app/templates/email/notice/multi_approve_reject.j2:2
msgid "提现审核邀请失败"
msgstr ""

#: app/templates/email/notice/multi_approve_reject.j2:4
#, python-format
msgid "%(target_name)s(%(account)s) 拒绝了你的提现审核邀请。"
msgstr ""

#: app/templates/email/notice/novice_package.j2:2
#, python-format
msgid "%(activate_title)s，新用户好礼活动即将结束！"
msgstr ""

#: app/templates/email/notice/novice_package.j2:4
#, python-format
msgid "%(activate_title)s，新用户好礼活动还有%(activate_day)s天就结束了！"
msgstr ""

#: app/templates/email/notice/novice_package.j2:5
msgid "您具备活动参与资格，为避免错过福利，请注意活动时间。"
msgstr ""

#: app/templates/email/notice/novice_package.j2:6
msgid "前往交易，领取福利礼包！"
msgstr ""

#: app/templates/email/notice/perpetual_adl.j2:5
#, python-format
msgid "你的正向合约%(market)s持仓已触发自动减仓流程。"
msgstr ""

#: app/templates/email/notice/perpetual_adl.j2:7
#, python-format
msgid "你的反向合约%(market)s持仓已触发自动减仓流程。"
msgstr ""

#: app/templates/email/notice/perpetual_adl.j2:9
#, python-format
msgid "减仓价格：%(liq_price)s"
msgstr ""

#: app/templates/email/notice/perpetual_adl.j2:10
#, python-format
msgid "减仓数量：%(liq_amount)s %(asset)s"
msgstr ""

#: app/templates/email/notice/perpetual_adl.j2:11
msgid "触发原因：对手盘发生穿仓，且保险基金余额不足"
msgstr ""

#: app/templates/email/notice/perpetual_adl.j2:13
#: app/templates/email/notice/perpetual_liquidation.j2:14
#: app/templates/email/notice/perpetual_liquidation_warning.j2:10
#: app/templates/email/notice/perpetual_position_close_result_notice.j2:31
#: app/templates/email/notice/perpetual_position_reduce_notice.j2:13
#: app/templates/email/notice/perpetual_profit_loss_notice.j2:40
msgid "风险提示：合约交易具有高风险性，请你密切关注市场动向。CoinEx对加密货币价格波动可能造成的任何损失不承担任何责任。"
msgstr ""

#: app/templates/email/notice/perpetual_funding_interval_update.j2:3
#, python-format
msgid "%(market)s市场资金费用收取周期调整"
msgstr ""

#: app/templates/email/notice/perpetual_funding_interval_update.j2:6
#, python-format
msgid ""
"%(market)s市场已触发资金费用自动调整机制，调整至%(funding_interval)sh收取，请及时<a "
"href='%(url)s'>查看</a>。"
msgstr ""

#: app/templates/email/notice/perpetual_liquidation.j2:5
#, python-format
msgid "很遗憾地通知你，你的正向合约%(market)s持仓已被强制平仓。"
msgstr ""

#: app/templates/email/notice/perpetual_liquidation.j2:7
#, python-format
msgid "很遗憾地通知你，你的反向合约%(market)s持仓已被强制平仓。"
msgstr ""

#: app/templates/email/notice/perpetual_liquidation.j2:9
#, python-format
msgid "强制平仓均价：%(liq_price)s"
msgstr ""

#: app/templates/email/notice/perpetual_liquidation.j2:10
#, python-format
msgid "强制平仓数量：%(amount)s %(asset)s"
msgstr ""

#: app/templates/email/notice/copy_trading/follower_position_liquidation.j2:5
#: app/templates/email/notice/perpetual_liquidation.j2:11
msgid "强制平仓原因：保证金余额低于所需维持保证金"
msgstr ""

#: app/templates/email/notice/perpetual_liquidation.j2:13
msgid "请注意，如果你设置了止盈止损平仓，可能会由于行情剧烈波动而触发失败。"
msgstr ""

#: app/templates/email/notice/perpetual_liquidation_warning.j2:5
#, python-format
msgid "你的正向合约%(market)s持仓风险率已低于%(risk)s%%，请注意仓位控制或增加保证金，以免发生强制还币。"
msgstr ""

#: app/templates/email/notice/perpetual_liquidation_warning.j2:7
#, python-format
msgid "你的反向合约%(market)s持仓风险率已低于%(risk)s%%，请注意仓位控制或增加保证金，以免发生强制还币。"
msgstr ""

#: app/templates/email/notice/perpetual_market_maker_level_appraisal.j2:7
#, python-format
msgid "恭喜，您的合约做市商等级已升级，升级为LV%(new_level)s，LV%(new_level)s相关费率如下："
msgstr ""

#: app/templates/email/notice/perpetual_market_maker_level_appraisal.j2:9
#: app/templates/email/notice/perpetual_market_maker_level_appraisal.j2:13
#: app/templates/email/notice/perpetual_market_maker_level_appraisal.j2:17
#: app/templates/email/notice/perpetual_market_maker_level_appraisal.j2:30
#, python-format
msgid "合约费率：Maker：%(perpetual_maker_fee_rate)s，Taker：%(perpetual_taker_fee_rate)s；"
msgstr ""

#: app/templates/email/notice/perpetual_market_maker_level_appraisal.j2:11
#, python-format
msgid "抱歉，您的合约做市商等级已降级，降级为LV%(new_level)s，LV%(new_level)s相关费率如下："
msgstr ""

#: app/templates/email/notice/perpetual_market_maker_level_appraisal.j2:15
#, python-format
msgid "您的合约做市商月度考核等级不变，等级为LV%(new_level)s，LV%(new_level)s相关费率如下："
msgstr ""

#: app/templates/email/notice/perpetual_market_maker_level_appraisal.j2:21
#, python-format
msgid ""
"<p>您的合约做市商月度考核合约交易量为%(user_trade_amount)s USD，低于%(limit_trade_amount)s "
"USD，已触发缓冲保护，等级降为LV1。</p>"
msgstr ""

#: app/templates/email/notice/perpetual_market_maker_level_appraisal.j2:24
#, python-format
msgid ""
"<p>备注：如1个月或连续2个月不符合最低要求，则保留LV1等级，如连续3个月不符合最低要求，则取消合约做市商资格。\n"
"                 （您已经连续%(month_count)s个月不符合最低要求）</p>"
msgstr ""

#: app/templates/email/notice/perpetual_market_maker_level_appraisal.j2:28
#: app/templates/email/notice/spot_market_maker_level_appraisal.j2:31
#, python-format
msgid "LV%(new_level)s相关费率如下："
msgstr ""

#: app/templates/email/notice/perpetual_market_maker_level_appraisal.j2:32
#, python-format
msgid "抱歉，您已经连续3个月月度考核合约交易量低于%(limit_trade_amount)s USD，已经失去了合约做市商资格。"
msgstr ""

#: app/templates/email/notice/perpetual_market_maker_level_appraisal.j2:34
#: app/templates/email/notice/spot_market_maker_level_appraisal.j2:38
msgid "降为普通用户费率，普通用户相关费率如下："
msgstr ""

#: app/templates/email/notice/perpetual_market_maker_level_appraisal.j2:35
#, python-format
msgid "合约费率：Maker：%(maker_fee_rate)s，Taker：%(taker_fee_rate)s；"
msgstr ""

#: app/templates/email/notice/perpetual_open_position_stop_loss_notice.j2:4
#, python-format
msgid "由于市场波动和您设置的参数间隔过小，合约市场%(market)s的开仓止损未生效，请及时<a href=\"%(url)s\">查看</a>"
msgstr ""

#: app/templates/email/notice/perpetual_open_position_take_profit_notice.j2:4
#, python-format
msgid "由于市场波动和您设置的参数间隔过小，合约市场%(market)s的开仓止盈未生效，请及时<a href=\"%(url)s\">查看</a>"
msgstr ""

#: app/templates/email/notice/perpetual_position_close_result_notice.j2:6
msgid "你的一键全平操作未能全部平仓。"
msgstr ""

#: app/templates/email/notice/perpetual_position_close_result_notice.j2:9
#: app/templates/email/notice/perpetual_position_close_result_notice.j2:22
#, python-format
msgid "操作市场：正向合约%(market)s"
msgstr ""

#: app/templates/email/notice/perpetual_position_close_result_notice.j2:11
#: app/templates/email/notice/perpetual_position_close_result_notice.j2:24
#, python-format
msgid "操作市场：反向合约%(market)s"
msgstr ""

#: app/templates/email/notice/perpetual_position_close_result_notice.j2:14
#: app/templates/email/notice/perpetual_position_close_result_notice.j2:27
#, python-format
msgid "剩余仓位：%(amount)s %(asset)s"
msgstr ""

#: app/templates/email/notice/perpetual_position_close_result_notice.j2:19
msgid "你的一键平仓操作未能全部平仓。"
msgstr ""

#: app/templates/email/notice/perpetual_position_reduce_notice.j2:5
#, python-format
msgid "很遗憾地通知你，你的正向合约%(market)s持仓已触发降档减仓。"
msgstr ""

#: app/templates/email/notice/perpetual_position_reduce_notice.j2:7
#, python-format
msgid "很遗憾地通知你，你的反向合约%(market)s持仓已触发降档减仓。"
msgstr ""

#: app/templates/email/notice/perpetual_position_reduce_notice.j2:9
#, python-format
msgid "触发时的标记价格：%(sign_price)s"
msgstr ""

#: app/templates/email/notice/perpetual_position_reduce_notice.j2:10
#, python-format
msgid "降档减仓数量：%(amount)s %(asset)s"
msgstr ""

#: app/templates/email/notice/perpetual_position_reduce_notice.j2:11
msgid "触发原因：保证金余额低于所需维持保证金"
msgstr ""

#: app/templates/email/notice/perpetual_profit_loss_notice.j2:25
#, python-format
msgid "你的子账号在%(market_type)s %(market)s中的止损设置已被触发，并且全部平仓成功。"
msgstr ""

#: app/templates/email/notice/perpetual_profit_loss_notice.j2:35
#, python-format
msgid "你的子账号在%(market_type)s %(market)s中的止盈设置已被触发，并且全部平仓成功。"
msgstr ""

#: app/templates/email/notice/perpetual_profit_loss_notice.j2:46
#, python-format
msgid "你在%(market_type)s %(market)s中的止损设置已被触发并且部分平仓，但由于行情波动剧烈、触发自动减仓等原因，导致未能完全平仓。"
msgstr ""

#: app/templates/email/notice/perpetual_profit_loss_notice.j2:48
#, python-format
msgid ""
"你的子账号在%(market_type)s "
"%(market)s中的止损设置已被触发并且部分平仓，但由于行情波动剧烈、触发自动减仓等原因，导致未能完全平仓。"
msgstr ""

#: app/templates/email/notice/perpetual_profit_loss_notice.j2:54
#, python-format
msgid "你在%(market_type)s %(market)s中的止盈设置已被触发并且部分平仓，但由于行情波动剧烈、触发自动减仓等原因，导致未能完全平仓。"
msgstr ""

#: app/templates/email/notice/perpetual_profit_loss_notice.j2:56
#, python-format
msgid ""
"你的子账号在%(market_type)s "
"%(market)s中的止盈设置已被触发并且部分平仓，但由于行情波动剧烈、触发自动减仓等原因，导致未能完全平仓。"
msgstr ""

#: app/templates/email/notice/perpetual_profit_loss_notice.j2:61
#, python-format
msgid "剩余未平仓位：%(amount)s%(asset)s"
msgstr ""

#: app/templates/email/notice/perpetual_profit_loss_notice.j2:64
msgid "请注意，止盈止损设置对剩余未平仓位已失效。"
msgstr ""

#: app/templates/email/notice/perpetual_profit_loss_notice.j2:65
msgid "风险警告：合约交易具有高风险性，请你密切关注市场动向。CoinEx对加密货币价格波动可能造成的任何损失不承担任何责任。"
msgstr ""

#: app/templates/email/notice/perpetual_trading_introduction.j2:3
msgid "恭喜你成功开通CoinEx合约功能"
msgstr ""

#: app/templates/email/notice/perpetual_trading_introduction.j2:7
msgid "恭喜你已成功开通CoinEx合约交易功能，现在你可以向合约账户中转入资产开启交易。"
msgstr ""

#: app/templates/email/notice/perpetual_trading_introduction.j2:15
msgid "简单几步，带你玩转CoinEx合约"
msgstr ""

#: app/templates/email/notice/perpetual_trading_introduction.j2:19
msgid "将资产划转到合约账户"
msgstr ""

#: app/templates/email/notice/perpetual_trading_introduction.j2:20
msgid "您可以充值、兑换然后将保证金划转到合约账户"
msgstr ""

#: app/templates/email/notice/perpetual_trading_introduction.j2:26
msgid "选择合约市场"
msgstr ""

#: app/templates/email/notice/perpetual_trading_introduction.j2:27
msgid "CoinEx提供100+热门市场，选择你想要交易的合约市场"
msgstr ""

#: app/templates/email/notice/perpetual_trading_introduction.j2:33
msgid "判断涨跌，下单交易"
msgstr ""

#: app/templates/email/notice/perpetual_trading_introduction.j2:34
msgid "如果认为市场即将上涨或者下跌，您可以对应做多或者做空，选择你认为合适的杠杆倍数和价格，然后下单"
msgstr ""

#: app/templates/email/notice/perpetual_trading_introduction.j2:40
msgid "管理订单和仓位，查看收益"
msgstr ""

#: app/templates/email/notice/perpetual_trading_introduction.j2:41
msgid "通过查看你的订单成交和持仓情况，管理你的合约收益，方便进行调整。"
msgstr ""

#: app/templates/email/notice/perpetual_trading_introduction.j2:47
msgid "更多合约知识"
msgstr ""

#: app/templates/email/notice/perpetual_trading_introduction.j2:53
msgid "风险提示：CoinEx合約交易屬於數字貨幣衍生品交易，具有高槓桿性和高風險性，过往收益不代表未来回报，在可能带来高收益的同时，也可能使您产生巨大亏损，行情剧烈波动可能会导致强制平仓，您可能会损失全部保证金余额。本邮件内容不作为任何投资建议，所有交易均由您自行决定并承担相应风险；如果您因使用CoinEx合约而可能产生的任何亏损，CoinEx不承担任何责任。"
msgstr ""

#: app/templates/email/notice/experience_fee/early_maturity_notice.j2:11
#: app/templates/email/notice/investment_increase_rate/using_deadline_notice.j2:11
#: app/templates/email/notice/pledge_liq.j2:9
#: app/templates/email/notice/pledge_liq_warning.j2:9
#: app/templates/email/notice/pre_trading_settlement.j2:9
msgid "查看详情"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_auto_repay.j2:3
msgid "借贷订单自动还币通知"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_auto_repay.j2:6
#, python-format
msgid "您的借贷订单（ID: %(loan_order_id)s）已于%(expire_at)s到期，由于您未开启自动续借，系统已为您操作自动还币。"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_auto_repay.j2:7
#: app/templates/email/notice/pledge_loan_order_force_repay.j2:7
#: app/templates/email/notice/pledge_loan_order_renew_success.j2:16
#: app/templates/email/notice/pledge_loan_order_will_renew.j2:7
#, python-format
msgid "您可点此<a href=\"%(detail_url)s\">查看订单详情</a>。"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_force_repay.j2:3
msgid "借贷订单强平通知"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_force_repay.j2:6
#, python-format
msgid "您的借贷订单（ID: %(loan_order_id)s）于%(renew_time)s续借失败，借贷订单已被强制平仓。"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_renew_fail.j2:3
msgid "借贷订单续借状态通知"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_renew_fail.j2:6
#, python-format
msgid "您的借贷订单（ID: %(loan_order_id)s）于%(renew_at)s再次尝试续借失败，宽限时间剩余%(remain_days)s天。"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_renew_fail.j2:7
#: app/templates/email/notice/pledge_loan_order_renew_first_fail.j2:7
msgid "宽限期间，借贷日利率维持不变，系统将在24小时后重新尝试续借，您无需任何操作，如宽限期结束仍未续借成功，订单将被强平以偿还借款，请及时关注订单状态和平台通知。"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_renew_fail.j2:8
#: app/templates/email/notice/pledge_loan_order_renew_first_fail.j2:8
#, python-format
msgid ""
"您可点此提前<a href=\"%(repay_url)s\">还币</a>或<a "
"href=\"%(detail_url)s\">查看订单详情</a>。"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_renew_first_fail.j2:3
msgid "借贷订单进入宽限期通知"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_renew_first_fail.j2:6
#, python-format
msgid ""
"您的借贷订单（ID: "
"%(loan_order_id)s）于%(renew_at)s续借失败，订单已进入宽限期，宽限时间剩余%(remain_days)s天。"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_renew_success.j2:3
msgid "借贷订单续借成功通知"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_renew_success.j2:6
#, python-format
msgid "您的借贷订单（ID: %(loan_order_id)s）已自动续借成功。"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_renew_success.j2:7
#, python-format
msgid "续借本金：%(loan_amount)s %(loan_asset)s"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_renew_success.j2:8
#, python-format
msgid "续借日利率：%(day_rate)s%%"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_renew_success.j2:9
#, python-format
msgid "当前质押率：%(cur_ltv)s%%"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_renew_success.j2:10
#, python-format
msgid "下次到期时间：%(expire_at)s"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_renew_success.j2:12
msgid "自动续借：已开启"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_renew_success.j2:14
msgid "自动续借：未开启"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_will_expired.j2:3
msgid "借贷订单即将到期还币提醒"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_will_expired.j2:6
#, python-format
msgid ""
"您的借贷订单（ID: "
"%(loan_order_id)s）将于%(expire_at)s到期，由于您未开启自动续借，请在订单到期前还币。如到期未还，系统将强平您的借贷订单。"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_will_expired.j2:7
#, python-format
msgid "如您仍需继续使用资金，请于订单到期时间前，<a href=\"%(detail_url)s\">点此处</a>操作打开订单自动续借。"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_will_renew.j2:3
msgid "借贷订单即将到期提醒"
msgstr ""

#: app/templates/email/notice/pledge_loan_order_will_renew.j2:6
#, python-format
msgid ""
"您的借贷订单（ID: "
"%(loan_order_id)s）将于%(expire_at)s到期，由于您已设置开启自动续借，届时系统将为您操作续借，请及时关注订单状态和平台通知。"
msgstr ""

#: app/templates/email/notice/potential_ambassador.j2:6
msgid "我们欢迎你加入CoinEx大使计划，并将CoinEx推荐给他人。弹性自由，在家也能躺赚佣金。"
msgstr ""

#: app/templates/email/notice/potential_ambassador.j2:8
msgid "CoinEx大使有什么权益？"
msgstr ""

#: app/templates/email/notice/potential_ambassador.j2:9
#, python-format
msgid "1、高返佣比例：享40%%-50%%被邀请用户的手续费返佣；"
msgstr ""

#: app/templates/email/notice/potential_ambassador.j2:10
msgid "2、返佣周期长：在职期间永久返佣，每日结算；"
msgstr ""

#: app/templates/email/notice/potential_ambassador.j2:11
msgid "3、考核门槛低：每月最低邀请3个交易用户；"
msgstr ""

#: app/templates/email/notice/potential_ambassador.j2:12
msgid "4、返佣无限制：所有被邀请用户的现货、杠杆和合约手续费均可返佣，无等级限制；"
msgstr ""

#: app/templates/email/notice/potential_ambassador.j2:13
msgid "5、大使推荐官：邀请大使，享5%额外返佣。"
msgstr ""

#: app/templates/email/notice/potential_ambassador.j2:14
msgid "据统计，平均每个CoinEx大使每月返佣金额达1,200 USD。马上行动，加入我们吧～"
msgstr ""

#: app/templates/email/notice/potential_ambassador.j2:17
msgid "点击申请CoinEx大使"
msgstr ""

#: app/templates/email/notice/potential_ambassador.j2:21
msgid "关于CoinEx"
msgstr ""

#: app/templates/email/notice/potential_ambassador.j2:22
msgid "CoinEx成立于2017年12月，是一家面向全球的专业数字货币交易平台。CoinEx已支持16种语言，聚集500多种创新资产，并提供900多个现货市场和100多个合约市场的交易。凭借着丰富的研发技术，CoinEx成为为数不多的“零事故”交易所，为全球用户的交易保驾护航。"
msgstr ""

#: app/templates/email/notice/potential_invalid_ambassador.j2:8
msgid "我们也欢迎你再次成为CoinEx大使，期待在不久的将来可以再度和你携手加密之旅。"
msgstr ""

#: app/templates/email/notice/potential_invalid_ambassador.j2:10
msgid "二次申请通道"
msgstr ""

#: app/templates/email/notice/red_packet_register_expiring_notice.j2:2
msgid "C-Box即将失效"
msgstr ""

#: app/templates/email/notice/red_packet_register_expiring_notice.j2:4
#, python-format
msgid "你收到%(sender_name)s发送的C-Box在%(expiring_hours)s小时后过期，请及时注册领取。"
msgstr ""

#: app/templates/email/notice/red_packet_register_expiring_notice.j2:7
msgid "点击此处快捷跳转官网注册"
msgstr ""

#: app/templates/email/notice/red_packet_register_notice.j2:2
msgid "C-Box通知"
msgstr ""

#: app/templates/email/notice/red_packet_register_notice.j2:4
#, python-format
msgid "您的账户于 %(receive_time)s 收到了一个C-Box."
msgstr ""

#: app/templates/email/notice/red_packet_register_notice.j2:5
#, python-format
msgid ""
"<span style=\"font-weight:500;\">金额： </span><span "
"style=\"color:#52cbca\">%(amount)s %(coin_type)s</span>"
msgstr ""

#: app/templates/email/notice/red_packet_register_notice.j2:6
#, python-format
msgid ""
"<span>有效时间: </span><span "
"style=\"color:#52cbca\">%(expired_time)s（须在该时间内完成注册，否则C-Box无法到账）</span>"
msgstr ""

#: app/templates/email/notice/red_packet_register_notice.j2:7
msgid "<span>请点击如下按钮，进行注册.</span>"
msgstr ""

#: app/templates/email/notice/red_packet_register_notice.j2:9
msgid "立即注册"
msgstr ""

#: app/templates/email/notice/risk_screen_fail.j2:5
msgid "基于监管规定及要求，非常抱歉我们无法继续为您提供服务。"
msgstr ""

#: app/templates/email/notice/risk_screen_fail.j2:8
msgid "请尽快处理正在进行中的交易，我们将于7个工作日后对你的账户进行清退。届时你的账户将进入“仅提现模式”，即关闭充值、交易等功能，仅支持提现服务。请您尽快完成资产的提现。"
msgstr ""

#: app/templates/email/notice/risk_screen_fail.j2:11
msgid "给您造成不便敬请谅解，感谢您的理解与配合！"
msgstr ""

#: app/templates/email/notice/risk_screen_kyc_edd.j2:13
#, python-format
msgid ""
"Please state your occupation status from %(from_year)s - %(to_year)s, "
"including the company you worked for and the workplace."
msgstr ""

#: app/templates/email/notice/risk_screen_kyc_edd.j2:25
#, python-format
msgid ""
"Please kindly elaborate on the above-required information by <a "
"href=\"%(support_url)s/hc/requests/new\">submitting a ticket</a>. We may "
"further restrict your account if there is no ticket created for the reply"
" from your side before %(date)s."
msgstr ""

#: app/templates/email/notice/second_anniversary_of_registration.j2:2
msgid "感谢您在过去2年里对CoinEx的信任和陪伴，在这个特殊的时刻，我们想向您表达最诚挚的谢意。"
msgstr ""

#: app/templates/email/notice/second_anniversary_of_registration.j2:3
#, python-format
msgid "CoinEx始终将用户体验放在第一位，不断升级基础设施，以提供简洁直观、专业可靠的加密资产交易服务。目前CoinEx已经成为拥有%(asset_count)s个币种、%(market_count)s个交易对、服务全球%(country_count)s个国家和地区的交易所。未来我们将继续严格筛选市场项目、持续上新优质币种，丰富您的交易选择。"
msgstr ""

#: app/templates/email/notice/send_coin_withdraw_notice.j2:4
msgid "你的提现申请已汇出。"
msgstr ""

#: app/templates/email/notice/send_coin_withdraw_notice.j2:6
msgid "提现金额："
msgstr ""

#: app/templates/email/notice/send_coin_withdraw_notice.j2:9
msgid "交易ID："
msgstr ""

#: app/templates/email/notice/set_login_password_success.j2:2
msgid "设置密码成功"
msgstr ""

#: app/templates/email/notice/set_login_password_success.j2:4
msgid "您在CoinEx平台首次设置了登录密码，进一步保障了您的账户安全。"
msgstr ""

#: app/templates/email/notice/sign_in.j2:2
msgid "登录提醒"
msgstr ""

#: app/templates/email/notice/sign_in.j2:4
#, python-format
msgid "您在%(create_time)s登录了<a href=\"https://www.coinex.com\">CoinEx</a>。"
msgstr ""

#: app/templates/email/notice/sign_in.j2:5
#, python-format
msgid "IP地址：%(ip)s"
msgstr ""

#: app/templates/email/notice/sign_in.j2:6
#, python-format
msgid "地点：%(location)s"
msgstr ""

#: app/templates/email/notice/sign_in_fail.j2:2
msgid "多次尝试登录提醒"
msgstr ""

#: app/templates/email/notice/sign_in_fail.j2:4
#, python-format
msgid "您的账号于%(create_time)s 多次尝试登录CoinEx并失败。请确认这是您本人的操作。"
msgstr ""

#: app/templates/email/notice/sign_in_fail.j2:5
#: app/templates/email/notice/sign_in_unusual.j2:8
#, python-format
msgid ""
"如果此次行为不是您本人所为，请立即<a href=\"%(site_url)s/signin\">重设密码</a>或<a "
"href=\"https://www.coinex.com/my/info/basic/forbid\">禁用账户</a>，并尽快<a "
"href=\"%(support_url)s/hc/requests/new\">提交工单</a>联系CoinEx客服。"
msgstr ""

#: app/templates/email/notice/sign_in_unusual.j2:4
#, python-format
msgid "您的账号于 %(create_time)s 登录了CoinEx，我们发现您本次登录地点与上次不一致，请确认这是您本人的操作。"
msgstr ""

#: app/templates/email/notice/sign_in_unusual.j2:5
#, python-format
msgid "本次登录 IP: %(ip)s"
msgstr ""

#: app/templates/email/notice/sign_in_unusual.j2:6
#, python-format
msgid "本次登录地点: %(location)s"
msgstr ""

#: app/templates/email/notice/sign_off.j2:2
msgid "账号注销提醒"
msgstr ""

#: app/templates/email/notice/sign_off.j2:4
#, python-format
msgid "你的CoinEx账号（%(name)s）已于%(time)s (UTC)成功注销。"
msgstr ""

#: app/templates/email/notice/sign_off.j2:5
msgid "账号注销后，CoinEx不再向此账号提供任何服务，包括但不限于："
msgstr ""

#: app/templates/email/notice/sign_off.j2:6
msgid "1、永久删除此账号（含子账号）下所有登录方式；"
msgstr ""

#: app/templates/email/notice/sign_off.j2:7
msgid "2、永久删除此账号的实名认证信息；"
msgstr ""

#: app/templates/email/notice/sign_off.j2:8
msgid "3、永久删除此账号的所有交易、充提等记录；"
msgstr ""

#: app/templates/email/notice/sign_off.j2:9
msgid "4、清除此账号的所有资产及权益；"
msgstr ""

#: app/templates/email/notice/sign_off.j2:10
msgid "5、不再向此账号提供任何相关客户服务。"
msgstr ""

#: app/templates/email/notice/sign_up_success.j2:7
msgid "充值数字资产"
msgstr ""

#: app/templates/email/notice/sign_up_success.j2:9
msgid "快捷买币"
msgstr ""

#: app/templates/email/notice/sign_up_success.j2:16
msgid "全球数字资产交易平台"
msgstr ""

#: app/templates/email/notice/sign_up_success.j2:17
msgid "您的加密资产交易专家"
msgstr ""

#: app/templates/email/notice/sign_up_success.j2:21
msgid "在CoinEx，你可以尽情使用如下热门功能："
msgstr ""

#: app/templates/email/notice/sign_up_success.j2:31
msgid "1000+交易对，支持限价、市价等4种委托模式，交易更灵活。"
msgstr ""

#: app/templates/email/notice/sign_up_success.j2:33
#: app/templates/email/notice/sign_up_success.j2:47
#: app/templates/email/notice/sign_up_success.j2:61
msgid "点击前往>>"
msgstr ""

#: app/templates/email/notice/sign_up_success.j2:43
msgid "兑换交易"
msgstr ""

#: app/templates/email/notice/sign_up_success.j2:44
msgid "自研智能算法，支持任意幣種自由組合，新手友好，极速交易。"
msgstr ""

#: app/templates/email/notice/sign_up_success.j2:58
msgid "120+交易市场，3-100x 杠杆，风险对冲，无惧牛熊。"
msgstr ""

#: app/templates/email/notice/sign_up_success.j2:68
msgid "查看CoinEx功能索引"
msgstr ""

#: app/templates/email/notice/sotoshi_bid_success.j2:2
msgid "稀有聪竞标成功！"
msgstr ""

#: app/templates/email/notice/sotoshi_bid_success.j2:4
msgid ""
"恭喜！您在稀有聪（Sat "
"1,968,750,000,000,000）的拍卖中，赢得竞拍，请通过当前账户的邮箱联系（<EMAIL>），并提供验证码：CoinExRareSatWinner"
msgstr ""

#: app/templates/email/notice/sotoshi_bid_success.j2:6
msgid "我们将有专员为您处理该稀有聪的提取事宜。"
msgstr ""

#: app/templates/email/notice/spot_grid_exceed_price_range.j2:6
#, python-format
msgid "%(market)s市场的现货价格已超过网格策略的价格区间，你可手动终止策略或修改止盈止损价格。"
msgstr ""

#: app/templates/email/notice/spot_grid_exceed_price_range.j2:7
#, python-format
msgid "%(base_asset)s市价：%(last_price)s %(quote_asset)s"
msgstr ""

#: app/templates/email/notice/spot_grid_exceed_price_range.j2:8
#, python-format
msgid "网格价格区间：%(lowest_price)s - %(highest_price)s %(quote_asset)s"
msgstr ""

#: app/templates/email/notice/spot_grid_exceed_price_range.j2:9
#: app/templates/email/notice/spot_grid_exceed_recommend_run_days.j2:7
#: app/templates/email/notice/spot_grid_stop_loss.j2:8
#: app/templates/email/notice/spot_grid_take_profit.j2:8
#: app/templates/email/notice/spot_grid_triggered.j2:8
#, python-format
msgid "策略ID：%(strategy_id)s"
msgstr ""

#: app/templates/email/notice/spot_grid_exceed_price_range.j2:12
#: app/templates/email/notice/spot_grid_exceed_recommend_run_days.j2:11
#: app/templates/email/notice/spot_grid_stop_loss.j2:11
#: app/templates/email/notice/spot_grid_take_profit.j2:11
#: app/templates/email/notice/spot_grid_triggered.j2:11
msgid "查看策略详情"
msgstr ""

#: app/templates/email/notice/spot_grid_exceed_recommend_run_days.j2:6
#, python-format
msgid "%(market)s市场的现货网格设置已达到推荐的运行时间，你可手动终止或继续运行当前策略。"
msgstr ""

#: app/templates/email/notice/spot_grid_exceed_recommend_run_days.j2:8
#, python-format
msgid "时间区间：%(recommend_days)sD"
msgstr ""

#: app/templates/email/notice/spot_grid_stop_loss.j2:6
#, python-format
msgid "%(market)s市场已达设定的网格止损价，系统将自动平仓，及时止损，降低单边行情损失。"
msgstr ""

#: app/templates/email/notice/spot_grid_stop_loss.j2:7
#, python-format
msgid "网格止损价：%(stop_loss_price)s %(quote_asset)s"
msgstr ""

#: app/templates/email/notice/spot_grid_take_profit.j2:6
#, python-format
msgid "%(market)s市场已达设定的网格止盈价，系统将自动平仓，及时止盈，为你锁定利润。"
msgstr ""

#: app/templates/email/notice/spot_grid_take_profit.j2:7
#, python-format
msgid "网格止盈价：%(take_profit_price)s %(quote_asset)s"
msgstr ""

#: app/templates/email/notice/spot_grid_triggered.j2:6
#, python-format
msgid "%(market)s市场已达设定的网格触发价，网格策略开始运行。"
msgstr ""

#: app/templates/email/notice/spot_grid_triggered.j2:7
#, python-format
msgid "网格触发价：%(trigger_price)s %(quote_asset)s"
msgstr ""

#: app/templates/email/notice/spot_market_maker_level_appraisal.j2:7
#, python-format
msgid "恭喜，您的现货做市商等级已升级，升级为LV%(new_level)s，LV%(new_level)s相关费率如下："
msgstr ""

#: app/templates/email/notice/spot_market_maker_level_appraisal.j2:9
#: app/templates/email/notice/spot_market_maker_level_appraisal.j2:14
#: app/templates/email/notice/spot_market_maker_level_appraisal.j2:19
#: app/templates/email/notice/spot_market_maker_level_appraisal.j2:33
#: app/templates/email/notice/spot_market_maker_level_appraisal.j2:40
#, python-format
msgid "现货费率：Maker：%(maker_fee_rate)s，Taker：%(taker_fee_rate)s；"
msgstr ""

#: app/templates/email/notice/spot_market_maker_level_appraisal.j2:10
#: app/templates/email/notice/spot_market_maker_level_appraisal.j2:15
#: app/templates/email/notice/spot_market_maker_level_appraisal.j2:20
#: app/templates/email/notice/spot_market_maker_level_appraisal.j2:34
#: app/templates/email/notice/spot_market_maker_level_appraisal.j2:41
#, python-format
msgid "现货费率（开启CET抵扣）：Maker：%(cet_maker_fee_rate)s，Taker：%(cet_taker_fee_rate)s"
msgstr ""

#: app/templates/email/notice/spot_market_maker_level_appraisal.j2:12
#, python-format
msgid "抱歉，您的现货做市商等级已降级，降级为LV%(new_level)s，LV%(new_level)s相关费率如下："
msgstr ""

#: app/templates/email/notice/spot_market_maker_level_appraisal.j2:17
#, python-format
msgid "您的现货做市商月度考核等级不变，等级为LV%(new_level)s，LV%(new_level)s相关费率如下："
msgstr ""

#: app/templates/email/notice/spot_market_maker_level_appraisal.j2:24
#, python-format
msgid ""
"您的现货做市商月度考核现货交易量为%(user_trade_amount)s USD，低于%(limit_trade_amount)s "
"USD，已触发缓冲保护，等级降为LV1。"
msgstr ""

#: app/templates/email/notice/spot_market_maker_level_appraisal.j2:27
#, python-format
msgid ""
"备注：如1个月或连续2个月不符合最低要求，则保留LV1等级，如连续3个月不符合最低要求，则取消现货做市商资格。\n"
"                 （您已经连续%(month_count)s个月不符合最低要求）"
msgstr ""

#: app/templates/email/notice/spot_market_maker_level_appraisal.j2:36
#, python-format
msgid "抱歉，您已经连续3个月月度考核现货交易量低于%(limit_trade_amount)s USD，已经失去了现货做市商资格。"
msgstr ""

#: app/templates/email/notice/staking_remove_success.j2:6
#, python-format
msgid ""
"你质押的%(amount)s %(asset)s 成功赎回 ，已存入到你的现货账户中，请<a "
"href=\"%(site_url)s\">登录官网</a>查看。"
msgstr ""

#: app/templates/email/notice/sub_account_bind_manager.j2:6
#, python-format
msgid "你已与子账号 %(sub_user_name)s 建立授权关系，可访问并管理该子账号，如需解除授权，请联系授权人操作解除。"
msgstr ""

#: app/templates/email/notice/sub_account_bind_manager.j2:8
#: app/templates/email/notice/sub_account_cancel_manager.j2:8
#, python-format
msgid "授权人：%(main_user_email)s"
msgstr ""

#: app/templates/email/notice/sub_account_bind_manager.j2:9
#: app/templates/email/notice/sub_account_cancel_manager.j2:9
#, python-format
msgid "被授权人：%(manager_email)s （你）"
msgstr ""

#: app/templates/email/notice/sub_account_bind_manager.j2:10
#: app/templates/email/notice/sub_account_cancel_manager.j2:10
#, python-format
msgid "授权子账号： %(sub_user_name)s"
msgstr ""

#: app/templates/email/notice/sub_account_bind_manager.j2:11
#: app/templates/email/notice/sub_account_cancel_manager.j2:11
#, python-format
msgid "账号权限：%(permissions)s"
msgstr ""

#: app/templates/email/notice/sub_account_cancel_manager.j2:6
#, python-format
msgid "你已与子账号 %(sub_user_name)s 解除授权关系，将无法访问和管理该子账号。"
msgstr ""

#: app/templates/email/notice/sub_account_register_notice.j2:2
msgid "新建子账号"
msgstr ""

#: app/templates/email/notice/sub_account_register_notice.j2:4
#, python-format
msgid "您于%(start_time)s新建了子账号，以下为您的账号密码以及登录域名，请妥善保存。"
msgstr ""

#: app/templates/email/notice/sub_account_register_notice.j2:5
#, python-format
msgid "登录名: %(user_name)s"
msgstr ""

#: app/templates/email/notice/sub_account_register_notice.j2:6
#, python-format
msgid "登录密码: %(password)s"
msgstr ""

#: app/templates/email/notice/sub_account_register_notice.j2:7
#, python-format
msgid "登录域名: https://www.coinex.com/signin/%(domain)s"
msgstr ""

#: app/templates/email/notice/tax_export_success.j2:10
msgid "前往下载"
msgstr ""

#: app/templates/email/notice/third_anniversary_of_registration.j2:2
msgid "转眼间，我们已经一起度过了3年的美好时光。感谢您一直以来的支持和信任。"
msgstr ""

#: app/templates/email/notice/third_anniversary_of_registration.j2:3
#, python-format
msgid "凭借着用户至上的产品理念、丰富多元的产品功能和精益求精的产品服务，目前CoinEx已经成为拥有%(asset_count)s个币种、%(market_count)s个交易对、服务全球%(country_count)s个国家和地区的交易所。未来我们也会坚持以用户为中心，不断推出优质资产，为您的加密货币之旅保驾护航。"
msgstr ""

#: app/templates/email/notice/totp_reset_pass.j2:3
msgid "您在CoinEx的绑定TOTP解绑成功，为了保障您的账号安全，24小时内禁止提现。您的账号已失去TOTP验证的保护，请及时重新绑定TOTP验证，以免造成损失。"
msgstr ""

#: app/templates/email/notice/trade_rank_activity_notice.j2:4
#, python-format
msgid "恭喜你进入“%(title)s”活动排名前列。"
msgstr ""

#: app/templates/email/notice/trade_rank_activity_notice.j2:5
#: app/templates/email/notice/trade_rank_activity_notice.j2:9
#, python-format
msgid ""
"活动将于%(ended_at)s（UTC）结束，冠军可获得%(max_gift_amount)s "
"%(gift_asset)s奖励，抓紧时间，提升排名吧！"
msgstr ""

#: app/templates/email/notice/trade_rank_activity_notice.j2:8
#, python-format
msgid "你已达到“%(title)s”活动的最低交易量要求，有资格瓜分%(total_gift_amount)s %(gift_asset)s！"
msgstr ""

#: app/templates/email/notice/trade_rank_activity_notice.j2:13
#, python-format
msgid "你已成功报名“%(title)s”活动，当前净买入量尚未达标。"
msgstr ""

#: app/templates/email/notice/trade_rank_activity_notice.j2:14
#, python-format
msgid ""
"%(started_at)s - %(ended_at)s（UTC）期间，在指定市场完成交易并且满足净买入量 ≥ "
"%(least_trade_amount)s %(trade_asset)s，即可参与排名，最高可获取%(max_gift_amount)s "
"%(gift_asset)s奖励！"
msgstr ""

#: app/templates/email/notice/trade_rank_activity_notice.j2:17
#, python-format
msgid "你已成功报名“%(title)s”活动，当前交易额尚未达标。"
msgstr ""

#: app/templates/email/notice/trade_rank_activity_notice.j2:18
#, python-format
msgid ""
"%(started_at)s - %(ended_at)s（UTC）期间，在指定市场完成交易并且满足交易额 ≥ "
"%(least_trade_amount)s %(trade_asset)s，即可参与排名，最高可获取%(max_gift_amount)s "
"%(gift_asset)s奖励！"
msgstr ""

#: app/templates/email/notice/unfreeze_account_fail.j2:2
msgid "解冻账号失败"
msgstr ""

#: app/templates/email/notice/unfreeze_account_fail.j2:4
#, python-format
msgid "你的CoinEx账号[%(name)s]申请自助解冻审核不通过，原因是：%(reason)s，您可以："
msgstr ""

#: app/templates/email/notice/unfreeze_account_fail.j2:5
#, python-format
msgid "1、重新<a href=\"%(unfreeze_account_url)s\">提交申请</a>。"
msgstr ""

#: app/templates/email/notice/unfreeze_account_fail.j2:6
#, python-format
msgid "2、通过<a href=\"%(support_url)s/hc/requests/new\">提交工单</a>反馈客服。"
msgstr ""

#: app/templates/email/notice/unfreeze_account_pass.j2:2
msgid "解冻账号成功"
msgstr ""

#: app/templates/email/notice/unfreeze_account_pass.j2:4
#, python-format
msgid ""
"你的CoinEx账号[%(name)s]已成功解冻，相关功能权限都已恢复，请<a "
"href='%(site_url)s'>登录CoinEx</a>查看。"
msgstr ""

#: app/templates/email/notice/update_trade_password.j2:3
msgid "您在CoinEx的交易密码修改成功。"
msgstr ""

#: app/templates/email/notice/user_trade_summary.j2:51
#: app/templates/email/notice/user_trade_summary.j2:254
#, python-format
msgid "做市商日报（%(report_date)s）"
msgstr ""

#: app/templates/email/notice/user_trade_summary.j2:62
#, python-format
msgid "以下是您的账户在 %(report_date)s（UTC）的数据报告："
msgstr ""

#: app/templates/email/notice/user_trade_summary.j2:95
msgid "当月【现货Maker返现机制】排位情况"
msgstr ""

#: app/templates/email/notice/user_trade_summary.j2:101
msgid "当月现货累计交易量"
msgstr ""

#: app/templates/email/notice/user_trade_summary.j2:132
msgid "账户比例=该账户当天在该市场总交易量/该账户当天所有现货市场的总交易量，总和为100%。"
msgstr ""

#: app/templates/email/notice/user_trade_summary.j2:133
#: app/templates/email/notice/user_trade_summary.j2:199
msgid "全站比例=该账户当天在该市场总交易量/该市场当天全站总交易量，总和为200%。"
msgstr ""

#: app/templates/email/notice/user_trade_summary.j2:167
msgid "账户比例=该账户当天该交易类型在所有现货市场总交易量/该账户当天现货总交易量，总和为100%。"
msgstr ""

#: app/templates/email/notice/user_trade_summary.j2:168
msgid "全站比例=该账户当天该交易类型在所有现货市场总交易量/当天该交易类型在所有现货市场总交易量。"
msgstr ""

#: app/templates/email/notice/user_trade_summary.j2:198
msgid "账户比例=该账户当天在该市场总交易量/该账户当天所有合约市场的总交易量，总和为100%。"
msgstr ""

#: app/templates/email/notice/user_trade_summary.j2:233
msgid "账户比例=该账户当天该交易类型在所有合约市场总交易量/该账户当天合约总交易量，总和为100%。"
msgstr ""

#: app/templates/email/notice/user_trade_summary.j2:234
msgid "全站比例=该账户当天该交易类型在所有合约市场总交易量/当天该交易类型在所有合约市场总交易量。"
msgstr ""

#: app/templates/email/notice/vip_level_down.j2:3
#, python-format
msgid "VIP等级变动提醒：VIP%(old_level)s >> VIP%(new_level)s，点击查看调整后权益"
msgstr ""

#: app/templates/email/notice/vip_level_down.j2:6
#, python-format
msgid "感谢你一直以来对CoinEx的支持！由于你的账号未满足要求，VIP等级已调整至VIP%(new_level)s。当前权益如下："
msgstr ""

#: app/templates/email/notice/vip_level_down.j2:8
#: app/templates/email/notice/vip_level_lose.j2:8
msgid "1、交易费率"
msgstr ""

#: app/templates/email/notice/vip_level_down.j2:13
#: app/templates/email/notice/vip_level_up.j2:13
#, python-format
msgid "VIP%(old_level)s"
msgstr ""

#: app/templates/email/notice/vip_level_down.j2:14
#: app/templates/email/notice/vip_level_gain.j2:15
#: app/templates/email/notice/vip_level_up.j2:14
#, python-format
msgid "VIP%(new_level)s"
msgstr ""

#: app/templates/email/notice/vip_level_down.j2:19
#: app/templates/email/notice/vip_level_gain.j2:20
#: app/templates/email/notice/vip_level_lose.j2:19
#: app/templates/email/notice/vip_level_up.j2:19
msgid "现货费率"
msgstr ""

#: app/templates/email/notice/vip_level_down.j2:24
#: app/templates/email/notice/vip_level_gain.j2:25
#: app/templates/email/notice/vip_level_lose.j2:24
#: app/templates/email/notice/vip_level_up.j2:24
msgid "现货费率（CET抵扣）"
msgstr ""

#: app/templates/email/notice/vip_level_down.j2:29
#: app/templates/email/notice/vip_level_gain.j2:30
#: app/templates/email/notice/vip_level_lose.j2:29
#: app/templates/email/notice/vip_level_up.j2:29
msgid "合约费率（Taker）"
msgstr ""

#: app/templates/email/notice/vip_level_down.j2:34
#: app/templates/email/notice/vip_level_gain.j2:35
#: app/templates/email/notice/vip_level_lose.j2:34
#: app/templates/email/notice/vip_level_up.j2:34
msgid "合约费率（Maker）"
msgstr ""

#: app/templates/email/notice/vip_level_down.j2:40
#, python-format
msgid "提升VIP等级，即可重享更优交易费率，<a href=\"%(vip_url)s\">点击查看VIP费率优惠>></a>"
msgstr ""

#: app/templates/email/notice/vip_level_down.j2:42
#: app/templates/email/notice/vip_level_lose.j2:41
msgid "2、返佣比例"
msgstr ""

#: app/templates/email/notice/vip_level_down.j2:43
#: app/templates/email/notice/vip_level_lose.j2:42
#, python-format
msgid "你的返佣比例已调整至 %(referral_rate)s。"
msgstr ""

#: app/templates/email/notice/vip_level_down.j2:44
#, python-format
msgid ""
"提升VIP等级，最高可享受 %(max_referral_rate)s 返佣！<a href=\"%(refer_url)s\">了解更多返佣详情"
" >></a> "
msgstr ""

#: app/templates/email/notice/vip_level_down.j2:46
msgid "如何恢复至更高VIP等级？"
msgstr ""

#: app/templates/email/notice/vip_level_down.j2:47
msgid "保持账户活跃度，如持有更多CET、提升资产或增加交易量，即可不断提升VIP等级；成为高等级VIP，享受更多尊享服务："
msgstr ""

#: app/templates/email/notice/vip_level_down.j2:48
#: app/templates/email/notice/vip_level_gain.j2:54
#: app/templates/email/notice/vip_level_up.j2:53
msgid "• 享受快速响应通道"
msgstr ""

#: app/templates/email/notice/vip_level_down.j2:49
#: app/templates/email/notice/vip_level_gain.j2:55
#: app/templates/email/notice/vip_level_up.j2:54
msgid "• 不定期专属周边礼物"
msgstr ""

#: app/templates/email/notice/vip_level_down.j2:50
#: app/templates/email/notice/vip_level_gain.j2:56
#: app/templates/email/notice/vip_level_up.j2:55
msgid "• 全球线下沙龙优先参与权"
msgstr ""

#: app/templates/email/notice/vip_level_down.j2:51
msgid "• 享有更低借币日利率"
msgstr ""

#: app/templates/email/notice/vip_level_down.j2:53
#: app/templates/email/notice/vip_level_gain.j2:60
#: app/templates/email/notice/vip_level_up.j2:59
#, python-format
msgid "<a href=\"%(vip_url)s\">点击查看各等级晋升标准 >></a>"
msgstr ""

#: app/templates/email/notice/vip_level_down.j2:55
#: app/templates/email/notice/vip_level_gain.j2:62
#: app/templates/email/notice/vip_level_up.j2:61
msgid "感谢你一直以来的支持与信任，祝你交易愉快！"
msgstr ""

#: app/templates/email/notice/vip_level_gain.j2:3
#, python-format
msgid "恭喜成为VIP%(new_level)s，多项专属权益已解锁！"
msgstr ""

#: app/templates/email/notice/vip_level_gain.j2:6
msgid "欢迎加入CoinEx VIP大家庭，让我们一起共同探索数字金融的未来价值。"
msgstr ""

#: app/templates/email/notice/vip_level_gain.j2:7
#, python-format
msgid "作为新晋的VIP%(new_level)s用户，你已解锁以下专属权益："
msgstr ""

#: app/templates/email/notice/vip_level_gain.j2:9
#: app/templates/email/notice/vip_level_up.j2:8
msgid "1、更优交易费率"
msgstr ""

#: app/templates/email/notice/vip_level_gain.j2:41
#: app/templates/email/notice/vip_level_up.j2:40
#, python-format
msgid "VIP等级越高，费率优惠越大！<a href=\"%(vip_url)s\">点击查看VIP费率优惠>></a>"
msgstr ""

#: app/templates/email/notice/vip_level_gain.j2:43
#: app/templates/email/notice/vip_level_up.j2:42
msgid "2、更低借币日利率"
msgstr ""

#: app/templates/email/notice/vip_level_gain.j2:44
#: app/templates/email/notice/vip_level_up.j2:43
#, python-format
msgid ""
"VIP用户可享受每日借币利率优惠，等级越高，借币利率越低！<a "
"href=\"%(borrowing_fee_url)s\">点击查看VIP借币利率>></a>"
msgstr ""

#: app/templates/email/notice/vip_level_gain.j2:46
#: app/templates/email/notice/vip_level_up.j2:45
msgid "3、更高返佣比例"
msgstr ""

#: app/templates/email/notice/vip_level_gain.j2:47
#: app/templates/email/notice/vip_level_up.j2:46
#, python-format
msgid "你的返佣比例已从 %(old_referral_rate)s 提升至 %(referral_rate)s。"
msgstr ""

#: app/templates/email/notice/vip_level_gain.j2:48
#: app/templates/email/notice/vip_level_up.j2:47
#, python-format
msgid ""
"持续提升VIP等级，最高可享受 %(max_referral_rate)s 返佣！<a "
"href=\"%(refer_url)s\">了解更多返佣详情 >></a> "
msgstr ""

#: app/templates/email/notice/vip_level_gain.j2:50
#: app/templates/email/notice/vip_level_up.j2:49
msgid "4、更多专属福利"
msgstr ""

#: app/templates/email/notice/vip_level_gain.j2:51
#: app/templates/email/notice/vip_level_up.j2:50
msgid "你将有机会参与VIP专属活动，例如专属空投等。保持活跃，精彩活动等着你！"
msgstr ""

#: app/templates/email/notice/vip_level_gain.j2:53
#: app/templates/email/notice/vip_level_up.j2:52
msgid "5、更高等级VIP尊享服务"
msgstr ""

#: app/templates/email/notice/vip_level_gain.j2:58
#: app/templates/email/notice/vip_level_up.j2:57
msgid "升级VIP，获得更高权益"
msgstr ""

#: app/templates/email/notice/vip_level_gain.j2:59
#: app/templates/email/notice/vip_level_up.j2:58
msgid "保持账户活跃度，如持有更多CET、提升资产或增加交易量，即可不断提升VIP等级，享受更多服务"
msgstr ""

#: app/templates/email/notice/vip_level_lose.j2:3
msgid "你的VIP权益已失效，重新升级，享更多专属福利！"
msgstr ""

#: app/templates/email/notice/vip_level_lose.j2:6
#, python-format
msgid "感谢你一直以来对CoinEx的支持！由于你的账号未满足要求，VIP等级已从VIP%(old_level)s调整为普通用户。当前权益如下："
msgstr ""

#: app/templates/email/notice/vip_level_lose.j2:14
msgid "VIP用户"
msgstr ""

#: app/templates/email/notice/vip_level_lose.j2:21
#, python-format
msgid "最低%(min_spot_fee_rate)s"
msgstr ""

#: app/templates/email/notice/vip_level_lose.j2:26
#, python-format
msgid "最低%(min_spot_cet_fee_rate)s"
msgstr ""

#: app/templates/email/notice/vip_level_lose.j2:31
#, python-format
msgid "最低%(min_perpetual_taker_fee_rate)s"
msgstr ""

#: app/templates/email/notice/vip_level_lose.j2:36
#, python-format
msgid "最低%(min_perpetual_maker_fee_rate)s"
msgstr ""

#: app/templates/email/notice/vip_level_lose.j2:44
msgid "虽然暂时无法享受VIP专属权益，但你仍然可以使用CoinEx的所有基础服务。同时，可通过以下方式快速恢复VIP身份，重新解锁专属福利："
msgstr ""

#: app/templates/email/notice/vip_level_lose.j2:45
#, python-format
msgid ""
"- 仅需持有 %(vip_min_cet_amount)sCET，即可恢复VIP特权。<a "
"href=\"%(cet_market_url)s\">立即购买 >></a>"
msgstr ""

#: app/templates/email/notice/vip_level_lose.j2:46
msgid "- 提升资产持仓量，达成资产目标。"
msgstr ""

#: app/templates/email/notice/vip_level_lose.j2:47
msgid "- 提升交易金额或频率，达成交易量目标。"
msgstr ""

#: app/templates/email/notice/vip_level_lose.j2:48
#, python-format
msgid "<a href=\"%(vip_url)s\">查看VIP等级标准 >></a>"
msgstr ""

#: app/templates/email/notice/vip_level_lose.j2:50
msgid "恢复VIP后，你将享受更多优惠，包括更优交易费率、更高返佣比例、专属活动和福利等。此外，高等级VIP用户还可享有优先工单相应、专属礼品及线下沙龙参与等特别待遇。"
msgstr ""

#: app/templates/email/notice/vip_level_lose.j2:52
msgid "感谢你一直以来对CoinEx的信任与支持，期待重回CoinEx VIP大家庭，再次共同探索数字金融的未来价值。"
msgstr ""

#: app/templates/email/notice/vip_level_up.j2:3
#, python-format
msgid "恭喜晋升VIP%(new_level)s，专属权益已升级！"
msgstr ""

#: app/templates/email/notice/vip_level_up.j2:6
#, python-format
msgid "恭喜你成功晋升为VIP%(new_level)s，多项专属权益已升级："
msgstr ""

#: app/templates/email/notice/webauthn_reset_pass.j2:3
msgid "您在CoinEx的通行密钥解绑成功，为了保障您的账号安全，24小时内禁止提现。您的账号已失去通行密钥的保护，请及时重新绑定，以免造成损失。"
msgstr ""

#: app/templates/email/notice/withdraw_password_reset_fail.j2:4
msgid "你在CoinEx的提现密码重置审核不通过。"
msgstr ""

#: app/templates/email/notice/withdraw_password_reset_fail.j2:6
#, python-format
msgid "原因：%(reason)s，请按照示例重新拍照上传。"
msgstr ""

#: app/templates/email/notice/withdraw_password_reset_fail.j2:8
#, python-format
msgid "您可以<a href=\"%(reset_withdraw_password_url)s\">重置提现密码</a>。"
msgstr ""

#: app/templates/email/notice/withdraw_password_reset_pass.j2:4
msgid "你已成功重置提现密码，请尽快重新设置提现密码，以免造成资产损失。为了你的账户安全，24小时内禁止提现。"
msgstr ""

#: app/templates/email/notice/withdrawal_cancelled_notice.j2:6
#: app/templates/email/notice/withdrawal_expired_notice.j2:6
msgid "你的提现申请已被取消。"
msgstr ""

#: app/templates/email/notice/withdrawal_cancelled_notice.j2:7
#: app/templates/email/notice/withdrawal_expired_notice.j2:7
#, python-format
msgid "提现时间：%(withdrawal_time_str)s"
msgstr ""

#: app/templates/email/notice/withdrawal_cancelled_notice.j2:8
#: app/templates/email/notice/withdrawal_expired_notice.j2:8
#, python-format
msgid "提现金额：%(amount)s %(asset_chain_str)s"
msgstr ""

#: app/templates/email/notice/withdrawal_cancelled_notice.j2:9
#: app/templates/email/notice/withdrawal_expired_notice.j2:9
#, python-format
msgid "提现地址：%(address)s"
msgstr ""

#: app/templates/email/notice/withdrawal_cancelled_notice.j2:10
#, python-format
msgid "取消原因：由于%(asset_chain_str)s钱包维护中，暂无法进行提现，请确认提现信息后再重新发起。"
msgstr ""

#: app/templates/email/notice/withdrawal_expired_notice.j2:10
msgid "由于超过24小时未进行邮箱确认，系统已自动取消申请。"
msgstr ""

#: app/templates/email/notice/withdrawal_resumed_notice.j2:6
#, python-format
msgid "CoinEx已恢复 %(asset_chain_str)s 的提现业务。暂停期间给您带来的不便，敬请谅解！"
msgstr ""

#: app/templates/email/notice/withdrawal_resumed_notice.j2:7
#, python-format
msgid "%(asset_chain_str)s 提现页面：<a href=\"%(url)s\">%(url)s</a>"
msgstr ""

#: app/templates/email/notice/activity/airdrop_activity_start.j2:2
#: app/templates/email/notice/activity/dibs_activity_start.j2:2
#: app/templates/email/notice/activity/spot_trade_rank_start.j2:2
#, python-format
msgid "“%(title)s”活动正火热进行中！"
msgstr ""

#: app/templates/email/notice/activity/airdrop_activity_start.j2:4
#: app/templates/email/notice/activity/asset_airdrop_activity_start.j2:4
#: app/templates/email/notice/activity/dibs_activity_start.j2:4
#: app/templates/email/notice/activity/spot_trade_rank_start.j2:4
#, python-format
msgid "活动时间：%(start_time)s - %(end_time)s（UTC）"
msgstr ""

#: app/templates/email/notice/activity/airdrop_activity_start.j2:5
#: app/templates/email/notice/activity/asset_airdrop_activity_start.j2:5
#, python-format
msgid "参与条件：%(condition)s"
msgstr ""

#: app/templates/email/notice/activity/airdrop_activity_start.j2:6
msgid "活动期间，学习相关资料，完成答题，即可参与空投，瓜分奖励！"
msgstr ""

#: app/templates/email/notice/activity/airdrop_activity_start.j2:7
#: app/templates/email/notice/activity/asset_airdrop_activity_start.j2:7
#: app/templates/email/notice/activity/dibs_activity_start.j2:7
msgid "查看更多>>"
msgstr ""

#: app/templates/email/notice/activity/airdrop_activity_start.j2:9
#: app/templates/email/notice/activity/asset_airdrop_activity_start.j2:9
#: app/templates/email/notice/activity/dibs_activity_start.j2:9
#: app/templates/email/notice/activity/launch_pool_mining_start.j2:24
#: app/templates/email/notice/activity/spot_trade_rank_start.j2:9
msgid "感谢您对CoinEx的信任和支持！我们将继续为您提供最新活动资讯，并致力于为您带来更好的交易体验。"
msgstr ""

#: app/templates/email/notice/activity/asset_airdrop_activity_start.j2:2
#, python-format
msgid "“参与%(asset)s空投活动，赢取%(amount)s %(asset)s”活动正火热进行中！"
msgstr ""

#: app/templates/email/notice/activity/asset_airdrop_activity_start.j2:6
#, python-format
msgid "活动期间，学习相关资料，完成答题，即可参与空投，瓜分%(amount)s %(asset)s奖励！"
msgstr ""

#: app/templates/email/notice/activity/dibs_activity_start.j2:5
#, python-format
msgid "申购条件：%(condition)s"
msgstr ""

#: app/templates/email/notice/activity/dibs_activity_start.j2:6
#, python-format
msgid "活动期间，满足申购条件，即可参与%(asset)s打折购，立减%(discount)s%%！"
msgstr ""

#: app/templates/email/notice/activity/launch_pool_mining_start.j2:3
#, python-format
msgid ""
"“存入%(stake_asset)s，瓜分%(reward_total_amount)s "
"%(reward_asset)s”活动现已开启！最低存入%(min_stake_amount)s "
"%(stake_asset)s，即可参与活动！活动详情如下："
msgstr ""

#: app/templates/email/notice/activity/launch_pool_mining_start.j2:4
#: app/templates/email/notice/activity/launch_pool_mining_start.j2:13
#, python-format
msgid "1、活动时间：%(start_time)s - %(end_time)s（UTC）"
msgstr ""

#: app/templates/email/notice/activity/launch_pool_mining_start.j2:5
#: app/templates/email/notice/activity/launch_pool_mining_start.j2:14
#, python-format
msgid "2、参与要求：%(condition)s"
msgstr ""

#: app/templates/email/notice/activity/launch_pool_mining_start.j2:6
#: app/templates/email/notice/activity/launch_pool_mining_start.j2:15
msgid "3、参与步骤："
msgstr ""

#: app/templates/email/notice/activity/launch_pool_mining_start.j2:7
#, python-format
msgid "第一步：购买%(stake_asset)s，做好挖矿准备；"
msgstr ""

#: app/templates/email/notice/activity/launch_pool_mining_start.j2:8
#: app/templates/email/notice/activity/launch_pool_mining_start.j2:17
msgid "第二步：进入活动页面；"
msgstr ""

#: app/templates/email/notice/activity/launch_pool_mining_start.j2:9
#, python-format
msgid "第三步：选择%(stake_asset)s资金池，存入至少%(min_stake_amount)s %(stake_asset)s。"
msgstr ""

#: app/templates/email/notice/activity/launch_pool_mining_start.j2:10
#: app/templates/email/notice/activity/launch_pool_mining_start.j2:19
#, python-format
msgid "完成以上三步，即可0风险参与此次挖矿活动，享受每小时%(reward_asset)s收益！"
msgstr ""

#: app/templates/email/notice/activity/launch_pool_mining_start.j2:12
#, python-format
msgid ""
"“存入%(stake_asset1)s 或 %(stake_asset2)s，瓜分%(reward_total_amount)s "
"%(reward_asset)s”活动现已开启！最低存入%(min_stake_amount1)s %(stake_asset1)s 或 "
"%(min_stake_amount2)s %(stake_asset2)s，即可参与活动！活动详情如下："
msgstr ""

#: app/templates/email/notice/activity/launch_pool_mining_start.j2:16
#, python-format
msgid "第一步：购买%(stake_asset1)s 或 %(stake_asset2)s，做好挖矿准备；"
msgstr ""

#: app/templates/email/notice/activity/launch_pool_mining_start.j2:18
msgid "第三步：存入相应资金。"
msgstr ""

#: app/templates/email/notice/activity/launch_pool_mining_start.j2:22
msgid "开始挖矿>>"
msgstr ""

#: app/templates/email/notice/activity/perpetual_trade_rank_start.j2:2
#, python-format
msgid "合约交易赛已正式开始，总奖池%(amount)s %(asset)s。"
msgstr ""

#: app/templates/email/notice/activity/perpetual_trade_rank_start.j2:3
#, python-format
msgid "活动截止%(end_time)s（UTC），错过可惜！"
msgstr ""

#: app/templates/email/notice/activity/perpetual_trade_rank_start.j2:4
msgid "立即报名参赛 >>"
msgstr ""

#: app/templates/email/notice/activity/spot_trade_rank_start.j2:5
#, python-format
msgid "活动市场：%(market)s"
msgstr ""

#: app/templates/email/notice/activity/spot_trade_rank_start.j2:6
#, python-format
msgid "活动期间，在指定市场中交易量（买入量+卖出量）大于%(threshold)s USD即可参与排名，瓜分%(amount)s %(asset)s奖励！"
msgstr ""

#: app/templates/email/notice/activity/spot_trade_rank_start.j2:7
msgid "点击报名 >>"
msgstr ""

#: app/templates/email/notice/cashback_fee/active_deadline_notice.j2:3
#, python-format
msgid "%(value)s %(value_type)s 返现券即将到期，请尽快领取！"
msgstr ""

#: app/templates/email/notice/cashback_fee/active_deadline_notice.j2:7
#, python-format
msgid "你有 %(value)s %(value_type)s 手续费返现券将于%(expired_at)s截止领取。"
msgstr ""

#: app/templates/email/notice/cashback_fee/active_deadline_notice.j2:8
#: app/templates/email/notice/cashback_fee/send_coupon_notice.j2:8
msgid "领取成功后，符合条件的交易订单手续费将于次日返现。"
msgstr ""

#: app/templates/email/notice/cashback_fee/active_deadline_notice.j2:9
msgid "先到先得！"
msgstr ""

#: app/templates/email/notice/cashback_fee/active_deadline_notice.j2:12
#: app/templates/email/notice/cashback_fee/send_coupon_notice.j2:11
#: app/templates/email/notice/experience_fee/active_deadline_notice.j2:10
#: app/templates/email/notice/experience_fee/send_coupon_notice.j2:11
#: app/templates/email/notice/investment_increase_rate/active_deadline_notice.j2:11
#: app/templates/email/notice/investment_increase_rate/send_coupon_notice.j2:11
msgid "立即领取"
msgstr ""

#: app/templates/email/notice/cashback_fee/delivery_coupon_notice.j2:7
#, python-format
msgid "你已获得%(value)s %(value_type)s手续费返现券。"
msgstr ""

#: app/templates/email/notice/cashback_fee/delivery_coupon_notice.j2:8
msgid "即刻去交易，符合条件的交易订单手续费将于次日返现。"
msgstr ""

#: app/templates/email/notice/cashback_fee/delivery_coupon_notice.j2:11
#: app/templates/email/notice/cashback_fee/using_deadline_notice.j2:11
#: app/templates/email/notice/investment_increase_rate/delivery_coupon_notice.j2:11
msgid "前往激活"
msgstr ""

#: app/templates/email/notice/cashback_fee/send_coupon_notice.j2:3
#, python-format
msgid "恭喜获得%(value)s %(value_type)s 返现券，快来领取吧～"
msgstr ""

#: app/templates/email/notice/cashback_fee/send_coupon_notice.j2:7
#, python-format
msgid "恭喜你获得 %(value)s %(value_type)s手续费返现券。"
msgstr ""

#: app/templates/email/notice/cashback_fee/used_deadline_notice.j2:3
msgid "返现券即将过期"
msgstr ""

#: app/templates/email/notice/cashback_fee/used_deadline_notice.j2:7
#, python-format
msgid ""
"你的 %(value)s %(value_type)s 手续费返现券-%(trade_type)s "
"将于%(expired_at)s过期，请尽快使用。"
msgstr ""

#: app/templates/email/notice/cashback_fee/used_deadline_notice.j2:8
msgid "返现券到期后，后续交易订单产生的手续费，将不再享受此返现券权益。"
msgstr ""

#: app/templates/email/notice/cashback_fee/using_deadline_notice.j2:3
#, python-format
msgid "%(value)s%(value_type)s 返现券即将截止激活"
msgstr ""

#: app/templates/email/notice/cashback_fee/using_deadline_notice.j2:7
#, python-format
msgid "你的%(value)s %(value_type)s手续费返现券将于%(expired_at)s截止激活。"
msgstr ""

#: app/templates/email/notice/cashback_fee/using_deadline_notice.j2:8
msgid "激活后，符合条件的订单手续费将于次日返还。"
msgstr ""

#: app/templates/email/notice/copy_trading/follower_close_position.j2:4
#, python-format
msgid ""
"你的交易员@%(trader_nickname)s 在市场%(market)s跟单交易中平仓成功，平仓均价 %(price)s "
"%(asset)s。请在<a href=\"%(detail_url)s\">我的跟单</a>中查看详情。"
msgstr ""

#: app/templates/email/notice/copy_trading/follower_open_position.j2:4
#, python-format
msgid ""
"你已跟随交易员@%(trader_nickname)s 开仓成功，开仓均价 %(price)s %(asset)s，请在<a "
"href=\"%(detail_url)s\">我的跟单</a>中查看详情。"
msgstr ""

#: app/templates/email/notice/copy_trading/follower_open_position_failed.j2:4
#, python-format
msgid "由于跟单保证金余额不足，你未能跟随交易员@%(trader_nickname)s 成功开仓/加仓，请追加跟单金额。"
msgstr ""

#: app/templates/email/notice/copy_trading/follower_open_position_failed.j2:6
msgid "追加跟单资金"
msgstr ""

#: app/templates/email/notice/copy_trading/follower_position_liquidation.j2:6
#, python-format
msgid "请在<a href=\"%(detail_url)s\">我的跟单</a>中查看详情。"
msgstr ""

#: app/templates/email/notice/copy_trading/follower_profit_share.j2:4
#, python-format
msgid ""
"你的交易员@%(trader_nickname)s 获得本次分润结算金额 %(amount)s %(asset)s，本结算周期为 "
"%(start_time)s - %(end_time)s（UTC）"
msgstr ""

#: app/templates/email/notice/copy_trading/follower_profit_share_rate_changed.j2:4
#, python-format
msgid ""
"你的交易员@%(trader_nickname)s "
"分润比例已从%(old_rate)s调整为%(new_rate)s，新的分润比例将于下个结算周期开始生效。"
msgstr ""

#: app/templates/email/notice/copy_trading/follower_stop_loss.j2:4
#: app/templates/email/notice/copy_trading/trader_stop_loss.j2:4
#, python-format
msgid "你的交易员@%(trader_nickname)s 止损设置已成功触发平仓。同时，你的跟单已结束。"
msgstr ""

#: app/templates/email/notice/copy_trading/follower_stop_loss.j2:5
#: app/templates/email/notice/copy_trading/follower_take_profit.j2:5
#: app/templates/email/notice/copy_trading/trader_stop_loss.j2:5
#, python-format
msgid "如需继续跟单交易，请<a href=\"%(site_url)s\">登录官网</a>重新选择交易员。"
msgstr ""

#: app/templates/email/notice/copy_trading/follower_suggest_add_margin.j2:4
#, python-format
msgid ""
"你的交易员@%(trader_nickname)s 已转入%(amount)s "
"%(asset)s保证金，建议同步增加同等数量的保证金，并注意控制风险。"
msgstr ""

#: app/templates/email/notice/copy_trading/follower_take_profit.j2:4
#, python-format
msgid "你的交易员@%(trader_nickname)s 止盈设置已成功触发平仓。同时，你的跟单已结束。"
msgstr ""

#: app/templates/email/notice/copy_trading/trader_adjust_profit_share_rate.j2:4
#, python-format
msgid "你的分润比例已从%(old_rate)s调整为%(new_rate)s。"
msgstr ""

#: app/templates/email/notice/copy_trading/trader_adjust_profit_share_rate.j2:5
msgid "新的分润比例已对新的跟单员开始生效，现有跟单员则于下个结算周期开始生效。"
msgstr ""

#: app/templates/email/notice/copy_trading/trader_apply_success.j2:4
#, python-format
msgid "恭喜成为CoinEx合约跟单交易员。如需开始合约带单，请<a href=\"%(site_url)s\">登录官网</a>进行操作。"
msgstr ""

#: app/templates/email/notice/copy_trading/trader_close_position.j2:4
#, python-format
msgid ""
"你已在%(market)s合约市场成功平仓，平仓均价 %(price)s %(asset)s。请在带单数据-<a "
"href=\"%(detail_url)s\">历史带单</a>中查看详情。"
msgstr ""

#: app/templates/email/notice/copy_trading/trader_liquidation.j2:5
#, python-format
msgid ""
"强制平仓原因：保证金余额低于所需维持保证金，请在带单数据，请在带单数据-<a "
"href=\"%(detail_url)s\">历史带单</a>中查看详情。"
msgstr ""

#: app/templates/email/notice/copy_trading/trader_open_position.j2:4
#, python-format
msgid ""
"你已在%(market)s合约市场成功开仓，开仓均价 %(price)s %(asset)s。请在带单数据-<a "
"href=\"%(detail_url)s\">当前带单</a>中查看详情。"
msgstr ""

#: app/templates/email/notice/copy_trading/trader_trade_system_finished.j2:4
#, python-format
msgid "由于你已超过%(days)s天未进行合约带单交易，系统已取消你的交易员身份。"
msgstr ""

#: app/templates/email/notice/copy_trading/trader_trade_system_finished.j2:5
msgid "如需重新成为合约交易员，请重新申请带单，通过申请后才可发起带单交易。"
msgstr ""

#: app/templates/email/notice/copy_trading_experience_fee/active_deadline_notice.j2:7
#, python-format
msgid "你的合约跟单体验金券即将到期作废，请于%(expired_at)s(领券有效期)前，立即前往卡券中心领取。"
msgstr ""

#: app/templates/email/notice/copy_trading_experience_fee/active_deadline_notice.j2:10
#: app/templates/email/notice/copy_trading_experience_fee/send_coupon_notice.j2:12
#: app/templates/email/notice/perpetual_subsidy/active_deadline_notice.j2:10
#: app/templates/email/notice/perpetual_subsidy/send_coupon_notice.j2:10
#: app/templates/email/notice/trading_gift/random_send_coupon_notice.j2:11
#: app/templates/email/notice/trading_gift/send_coupon_notice.j2:12
#: app/templates/email/notice/vip_upgrade/active_deadline_notice.j2:10
#: app/templates/email/notice/vip_upgrade/send_coupon_notice.j2:12
msgid "点此领取"
msgstr ""

#: app/templates/email/notice/copy_trading_experience_fee/send_coupon_notice.j2:3
msgid "CoinEx给你送合约跟单体验金券了！"
msgstr ""

#: app/templates/email/notice/copy_trading_experience_fee/send_coupon_notice.j2:7
msgid "恭喜你获得合约跟单体验金券！"
msgstr ""

#: app/templates/email/notice/copy_trading_experience_fee/send_coupon_notice.j2:8
msgid "卡券有效期内，你可以在卡券指定的适用范围内，在跟单或带单时使用卡券。立即前往卡券中心领取。"
msgstr ""

#: app/templates/email/notice/copy_trading_experience_fee/send_coupon_notice.j2:9
#, python-format
msgid "领取截止时间：%(expired_at)s"
msgstr ""

#: app/templates/email/notice/copy_trading_experience_fee/using_deadline_notice.j2:7
msgid "你的合约跟单体验金券即将到期，立即前往卡券中心查看"
msgstr ""

#: app/templates/email/notice/copy_trading_experience_fee/using_deadline_notice.j2:8
#: app/templates/email/notice/vip_upgrade/used_deadline_notice.j2:8
#, python-format
msgid "权益到期时间：%(expired_at)s。"
msgstr ""

#: app/templates/email/notice/copy_trading_experience_fee/using_deadline_notice.j2:11
#: app/templates/email/notice/perpetual_subsidy/used_deadline_notice.j2:10
#: app/templates/email/notice/trading_gift/active_deadline_notice.j2:10
#: app/templates/email/notice/trading_gift/early_maturity_notice.j2:10
#: app/templates/email/notice/trading_gift/random_active_deadline_notice.j2:10
#: app/templates/email/notice/trading_gift/used_deadline_notice.j2:11
#: app/templates/email/notice/vip_upgrade/used_deadline_notice.j2:12
msgid "立即查看"
msgstr ""

#: app/templates/email/notice/equity_center/airdrop_delivery.j2:7
#, python-format
msgid "恭喜你获得 %(amount)s %(asset)s 空投奖励，请前往<a href='%(site_url)s'>「我的奖励」</a>查看详情。"
msgstr ""

#: app/templates/email/notice/equity_center/cashback_delivery.j2:7
#, python-format
msgid ""
"恭喜你获得 %(amount)s %(asset)s 手续费返现权益，请尽快使用。返现到期时间：%(expired_at)s 。请前往<a "
"href='%(site_url)s'>「我的奖励」</a>查看详情。"
msgstr ""

#: app/templates/email/notice/equity_center/cashback_expiring.j2:7
#, python-format
msgid "你的%(amount)s %(asset)s 手续费返现权益即将到期，请尽快使用。"
msgstr ""

#: app/templates/email/notice/equity_center/cashback_expiring.j2:8
#, python-format
msgid "返现到期时间：%(expired_at)s "
msgstr ""

#: app/templates/email/notice/equity_center/cashback_expiring.j2:9
#: app/templates/email/notice/mission_center/mission_reward_sent.j2:6
#, python-format
msgid "请前往<a href='%(site_url)s'>「我的奖励」</a>查看详情。"
msgstr ""

#: app/templates/email/notice/experience_fee/active_deadline_notice.j2:3
msgid "合约体验金即将到期，请尽快领取！"
msgstr ""

#: app/templates/email/notice/experience_fee/active_deadline_notice.j2:7
#, python-format
msgid ""
"你有 %(value)s %(value_type)s 合约体验金将于 %(expired_at)s "
"截止领取。领取后即可在CoinEx免费体验合约交易。先到先得！"
msgstr ""

#: app/templates/email/notice/experience_fee/active_deadline_notice.j2:13
#: app/templates/email/notice/experience_fee/send_coupon_notice.j2:14
#: app/templates/email/notice/investment_increase_rate/active_deadline_notice.j2:14
#: app/templates/email/notice/investment_increase_rate/delivery_coupon_notice.j2:14
#: app/templates/email/notice/investment_increase_rate/send_coupon_notice.j2:14
#: app/templates/email/notice/investment_increase_rate/using_deadline_notice.j2:14
msgid "新手指南"
msgstr ""

#: app/templates/email/notice/experience_fee/active_deadline_notice.j2:13
#: app/templates/email/notice/experience_fee/send_coupon_notice.j2:14
msgid "CoinEx合约交易教程"
msgstr ""

#: app/templates/email/notice/experience_fee/early_maturity_notice.j2:3
#, python-format
msgid "%(value)s %(value_type)s合约体验金已赠送"
msgstr ""

#: app/templates/email/notice/experience_fee/early_maturity_notice.j2:7
msgid "恭喜你，合约交易额已达标。"
msgstr ""

#: app/templates/email/notice/experience_fee/early_maturity_notice.j2:8
#, python-format
msgid "%(value)s %(value_type)s 合约体验金已赠送，过期后不收回，你可前往CoinEx合约交易继续使用。"
msgstr ""

#: app/templates/email/notice/experience_fee/send_coupon_notice.j2:3
#, python-format
msgid "恭喜获得%(value)s %(value_type)s合约体验金，快来领取吧～"
msgstr ""

#: app/templates/email/notice/experience_fee/send_coupon_notice.j2:7
#, python-format
msgid "恭喜你获得%(value)s %(value_type)s合约体验金，可免费体验合约交易。"
msgstr ""

#: app/templates/email/notice/experience_fee/send_coupon_notice.j2:8
msgid "领取后即可使用，先到先得！"
msgstr ""

#: app/templates/email/notice/experience_fee/used_deadline_notice.j2:3
#, python-format
msgid "%(value)s %(value_type)s合约体验金即将过期"
msgstr ""

#: app/templates/email/notice/experience_fee/used_deadline_notice.j2:7
#, python-format
msgid "你的 %(value)s %(value_type)s 合约体验金将于 %(expired_at)s 过期。请尽快使用。"
msgstr ""

#: app/templates/email/notice/experience_fee/used_deadline_notice.j2:8
#, python-format
msgid "如在使用有效期内完成 %(qualified_trade_amount)s %(value_type)s 正向合约交易额要求，则体验金到期不回收。"
msgstr ""

#: app/templates/email/notice/experience_fee/used_deadline_notice.j2:11
msgid "点击查看"
msgstr ""

#: app/templates/email/notice/investment_increase_rate/active_deadline_notice.j2:3
#, python-format
msgid "%(value)s%(value_type)s 加息券即将到期，请尽快领取！"
msgstr ""

#: app/templates/email/notice/investment_increase_rate/active_deadline_notice.j2:7
#, python-format
msgid "你有一张 %(value)s%(value_type)s 理财加息券将于%(expired_at)s截止领取，先到先得！"
msgstr ""

#: app/templates/email/notice/investment_increase_rate/active_deadline_notice.j2:8
msgid "即刻领取，享受更高理财收益！"
msgstr ""

#: app/templates/email/notice/investment_increase_rate/active_deadline_notice.j2:14
#: app/templates/email/notice/investment_increase_rate/delivery_coupon_notice.j2:14
#: app/templates/email/notice/investment_increase_rate/send_coupon_notice.j2:14
#: app/templates/email/notice/investment_increase_rate/using_deadline_notice.j2:14
msgid "什么是CoinEx理财赚币？"
msgstr ""

#: app/templates/email/notice/investment_increase_rate/delivery_coupon_notice.j2:3
#, python-format
msgid "恭喜获得 %(value)s%(value_type)s 加息券，请尽快激活使用"
msgstr ""

#: app/templates/email/notice/investment_increase_rate/delivery_coupon_notice.j2:7
#: app/templates/email/notice/investment_increase_rate/send_coupon_notice.j2:7
#, python-format
msgid "恭喜你获得 %(value)s%(value_type)s 理财加息券一张。"
msgstr ""

#: app/templates/email/notice/investment_increase_rate/delivery_coupon_notice.j2:8
#: app/templates/email/notice/investment_increase_rate/using_deadline_notice.j2:8
msgid "即刻激活使用，让你的闲置资产“赚”起来！"
msgstr ""

#: app/templates/email/notice/investment_increase_rate/send_coupon_notice.j2:3
#, python-format
msgid "恭喜获得 %(value)s%(value_type)s 加息券，快来领取吧～"
msgstr ""

#: app/templates/email/notice/investment_increase_rate/send_coupon_notice.j2:8
msgid "即刻领取使用，让你的闲置资产“赚”起来，先到先得！"
msgstr ""

#: app/templates/email/notice/investment_increase_rate/using_deadline_notice.j2:3
#, python-format
msgid "%(value)s%(value_type)s 加息券即将失效，请尽快使用！"
msgstr ""

#: app/templates/email/notice/investment_increase_rate/using_deadline_notice.j2:7
#, python-format
msgid "你的 %(value)s%(value_type)s 理财加息券将于%(expired_at)s失效。"
msgstr ""

#: app/templates/email/notice/mission_center/mission_expiring.j2:7
#, python-format
msgid "你的新用户专属任务即将到期，完成即可获得%(value)s %(value_type)s%(reward_type)s奖励！"
msgstr ""

#: app/templates/email/notice/mission_center/mission_expiring.j2:9
#, python-format
msgid "请前往<a href='%(site_url)s'>「奖励中心」</a>完成任务。"
msgstr ""

#: app/templates/email/notice/mission_center/mission_reward_sent.j2:4
msgid "恭喜，你已完成新用户专属任务！"
msgstr ""

#: app/templates/email/notice/mission_center/mission_reward_sent.j2:5
#, python-format
msgid "任务名称：%(title)s"
msgstr ""

#: app/templates/email/notice/mission_center/new_mission_notice.j2:5
#, python-format
msgid "“%(title)s”等多个任务已上线，快来奖励中心接受挑战吧！"
msgstr ""

#: app/templates/email/notice/mission_center/new_mission_notice.j2:7
#, python-format
msgid "“%(title)s”已上线，快来奖励中心接受挑战吧！"
msgstr ""

#: app/templates/email/notice/mission_center/new_mission_notice.j2:9
#, python-format
msgid "请前往<a href='%(site_url)s'>「奖励中心」</a>查看详情。"
msgstr ""

#: app/templates/email/notice/p2p/add_payment_channel.j2:4
#, python-format
msgid "你已成功增加P2P收款方式：%(pay_channel)s。"
msgstr ""

#: app/templates/email/notice/p2p/add_payment_channel.j2:5
#: app/templates/email/notice/p2p/auto_offline_advertising.j2:7
#: app/templates/email/notice/p2p/buyer_cancelled_order.j2:6
#: app/templates/email/notice/p2p/canceled_complaint.j2:6
#: app/templates/email/notice/p2p/created_complaint.j2:6
#: app/templates/email/notice/p2p/finished_complaint.j2:6
#: app/templates/email/notice/p2p/merchant_reject_order.j2:6
#: app/templates/email/notice/p2p/operation_complaint_for_cancel_to_buyer.j2:7
#: app/templates/email/notice/p2p/operation_complaint_for_cancel_to_seller.j2:7
#: app/templates/email/notice/p2p/operation_complaint_for_release_to_seller.j2:7
#: app/templates/email/notice/p2p/order_auto_cancelled.j2:6
#: app/templates/email/notice/p2p/payment_channel_invalid.j2:5
#: app/templates/email/notice/p2p/restart_complaint.j2:6
#, python-format
msgid "详情请<a href='%(site_url)s'>点击查看</a>。"
msgstr ""

#: app/templates/email/notice/p2p/auto_offline_advertising.j2:4
msgid "您的P2P广告单被自动下架。"
msgstr ""

#: app/templates/email/notice/p2p/auto_offline_advertising.j2:5
#, python-format
msgid "原因是：%(reason)s。"
msgstr ""

#: app/templates/email/notice/p2p/auto_offline_advertising.j2:6
#, python-format
msgid "广告单编号：%(adv_number)s。"
msgstr ""

#: app/templates/email/notice/p2p/become_merchant.j2:4
#, python-format
msgid "恭喜，您的商家身份已开通，点此<a href='%(site_url)s'>前往商家后台</a>，立即开启P2P交易。"
msgstr ""

#: app/templates/email/notice/p2p/become_merchant_not_2fa.j2:4
#, python-format
msgid ""
"恭喜，您的商家身份已开通。您的账户目前安全验证等级较低，为了保障您的资金安全，请您<a "
"href='%(site_url)s'>前往账户安全设置</a>绑定TOTP或通行密钥。"
msgstr ""

#: app/templates/email/notice/p2p/buyer_cancelled_order.j2:4
#, python-format
msgid "您的P2P订单已被买家取消，原因是：%(cancel_reason)s。"
msgstr ""

#: app/templates/email/notice/p2p/buyer_cancelled_order.j2:5
#: app/templates/email/notice/p2p/canceled_complaint.j2:5
#: app/templates/email/notice/p2p/created_complaint.j2:5
#: app/templates/email/notice/p2p/finished_complaint.j2:5
#: app/templates/email/notice/p2p/finished_order.j2:5
#: app/templates/email/notice/p2p/merchant_reject_order.j2:5
#: app/templates/email/notice/p2p/operation_complaint_for_cancel_to_buyer.j2:5
#: app/templates/email/notice/p2p/operation_complaint_for_release_to_buyer.j2:5
#: app/templates/email/notice/p2p/operation_complaint_for_release_to_seller.j2:5
#: app/templates/email/notice/p2p/order_auto_cancelled.j2:5
#: app/templates/email/notice/p2p/paid_wait_release_asset.j2:7
#: app/templates/email/notice/p2p/received_order_to_payment.j2:5
#: app/templates/email/notice/p2p/received_order_wait_payment.j2:5
#: app/templates/email/notice/p2p/remind_receive_order.j2:5
#: app/templates/email/notice/p2p/restart_complaint.j2:5
#: app/templates/email/notice/p2p/updated_complaint.j2:5
#, python-format
msgid "订单编号：%(order_id)s。"
msgstr ""

#: app/templates/email/notice/p2p/buyer_payment_deadline.j2:4
#, python-format
msgid "您的P2P买币订单将在5分钟后超时，超时后将自动取消订单，请<a href='%(site_url)s'>查看订单</a>并尽快完成付款。"
msgstr ""

#: app/templates/email/notice/p2p/buyer_payment_deadline.j2:5
#: app/templates/email/notice/p2p/operation_complaint_for_cancel_to_seller.j2:5
#, python-format
msgid "订单编号：%(order_id)s"
msgstr ""

#: app/templates/email/notice/p2p/buyer_payment_deadline.j2:7
#: app/templates/email/notice/p2p/received_order_to_payment.j2:6
#, python-format
msgid "您需支付：%(quote_amount)s %(quote)s"
msgstr ""

#: app/templates/email/notice/p2p/buyer_payment_deadline.j2:8
#: app/templates/email/notice/p2p/received_order_to_payment.j2:7
msgid "请使用本人实名认证的支付方式，确认完成付款后再点击“我已付款”按钮。"
msgstr ""

#: app/templates/email/notice/p2p/canceled_complaint.j2:4
#, python-format
msgid "您的P2P订单已被%(user_type)s取消申诉。"
msgstr ""

#: app/templates/email/notice/p2p/created_complaint.j2:4
#, python-format
msgid "您的P2P订单%(user_type)s已发起申诉，原因是：%(reason)s。"
msgstr ""

#: app/templates/email/notice/p2p/finished_complaint.j2:4
msgid "您的P2P订单申诉已完成。"
msgstr ""

#: app/templates/email/notice/p2p/finished_order.j2:4
msgid "您的P2P订单卖家已放币，订单已完成。"
msgstr ""

#: app/templates/email/notice/p2p/finished_order.j2:6
#, python-format
msgid "您已收到：%(to_amount)s%(base)s。"
msgstr ""

#: app/templates/email/notice/p2p/finished_order.j2:8
msgid "数字货币已经划转到您的现货账户，<a href='https://www.coinex.com/asset/spot'>立即查看</a>。"
msgstr ""

#: app/templates/email/notice/p2p/mer_act_audit_fail.j2:3
#, python-format
msgid "P2P %(act_name)s 报名结果通知"
msgstr ""

#: app/templates/email/notice/p2p/mer_act_audit_fail.j2:5
msgid "很遗憾地通知，你未能通过P2P商家活动的报名审核。"
msgstr ""

#: app/templates/email/notice/p2p/mer_act_audit_fail.j2:6
#: app/templates/email/notice/p2p/mer_act_audit_pass.j2:6
#, python-format
msgid "活动名称：%(act_name)s "
msgstr ""

#: app/templates/email/notice/p2p/mer_act_audit_fail.j2:7
msgid "感谢报名，如对审核结果有疑问，请联系CoinEx客服。"
msgstr ""

#: app/templates/email/notice/p2p/mer_act_audit_fail.j2:9
#: app/templates/email/notice/p2p/mer_act_audit_pass.j2:9
#, python-format
msgid "查看<a href='%(act_help_url)s'>详细活动规则>></a>"
msgstr ""

#: app/templates/email/notice/p2p/mer_act_audit_pass.j2:3
#, python-format
msgid "P2P %(act_name)s 报名成功通知"
msgstr ""

#: app/templates/email/notice/p2p/mer_act_audit_pass.j2:5
msgid "恭喜您已成功通过P2P商家活动的报名审核。"
msgstr ""

#: app/templates/email/notice/p2p/mer_act_audit_pass.j2:7
#, python-format
msgid "请前往<a href='%(mer_url)s'>商家后台</a>发布广告单，赢取活动奖励。"
msgstr ""

#: app/templates/email/notice/p2p/mer_act_reward_fail.j2:3
#, python-format
msgid "P2P %(act_name)s 奖励发放失败通知"
msgstr ""

#: app/templates/email/notice/p2p/mer_act_reward_fail.j2:5
#, python-format
msgid "由于您的账户异常，%(act_name)s 奖励发放失败，请立即联系客服处理"
msgstr ""

#: app/templates/email/notice/p2p/mer_act_reward_fail.j2:6
#: app/templates/email/notice/p2p/mer_act_reward_success.j2:6
#, python-format
msgid "详细活动规则请<a href='%(act_help_url)s'>点此查看</a>。"
msgstr ""

#: app/templates/email/notice/p2p/mer_act_reward_success.j2:3
#, python-format
msgid "P2P %(act_name)s 奖励发放通知"
msgstr ""

#: app/templates/email/notice/p2p/mer_act_reward_success.j2:5
#, python-format
msgid ""
"恭喜您获得 %(reward_date)s %(act_name)s 的奖励瓜分资格，奖金已发放至您的现货账户，您可前往<a "
"href='%(act_url)s'>活动主页</a>查看详情。"
msgstr ""

#: app/templates/email/notice/p2p/merchant_reject_order.j2:4
msgid "您发起的P2P订单商家拒绝接单。"
msgstr ""

#: app/templates/email/notice/p2p/operation_complaint_for_cancel_to_buyer.j2:4
msgid "您的P2P订单客服已处理，数字货币已经释放给卖家。"
msgstr ""

#: app/templates/email/notice/p2p/operation_complaint_for_cancel_to_buyer.j2:6
#, python-format
msgid "释放数字货币：%(to_amount)s %(base)s。"
msgstr ""

#: app/templates/email/notice/p2p/operation_complaint_for_cancel_to_seller.j2:4
#, python-format
msgid "您的P2P订单客服已处理，冻结的数字货币已经释放，请前往<a href='%(spot_url)s'>现货账户</a>查看记录。"
msgstr ""

#: app/templates/email/notice/p2p/operation_complaint_for_cancel_to_seller.j2:6
#, python-format
msgid "释放数字货币：%(from_amount)s %(base)s"
msgstr ""

#: app/templates/email/notice/p2p/operation_complaint_for_release_to_buyer.j2:4
msgid ""
"您的P2P订单客服已处理，数字货币已经划转到您的现货账户，<a "
"href='https://www.coinex.com/asset/spot'>立即查看</a>。"
msgstr ""

#: app/templates/email/notice/p2p/operation_complaint_for_release_to_buyer.j2:6
#, python-format
msgid "您已收到：%(to_amount)s %(base)s。"
msgstr ""

#: app/templates/email/notice/p2p/operation_complaint_for_release_to_seller.j2:4
msgid "您的P2P订单客服已处理，您的数字货币已经划转给买家。"
msgstr ""

#: app/templates/email/notice/p2p/operation_complaint_for_release_to_seller.j2:6
#, python-format
msgid "划转数字货币：%(from_amount)s %(base)s。"
msgstr ""

#: app/templates/email/notice/p2p/order_auto_cancelled.j2:4
#, python-format
msgid "您的P2P订单已取消，原因是：%(cancel_reason)s。"
msgstr ""

#: app/templates/email/notice/p2p/p2p_margin_change.j2:4
#, python-format
msgid "基于商家所在地区和账户风险评估，系统已将您的保证金要求调整为 %(amount)s USDT。请及时补充保证金，以免广告发布受到限制。"
msgstr ""

#: app/templates/email/notice/p2p/p2p_margin_change.j2:6
#: app/templates/email/notice/p2p/p2p_margin_payment.j2:7
#: app/templates/email/notice/p2p/p2p_margin_shortfall.j2:8
msgid "补充方式：前往商家后台 > 总览 > 去补充，按页面提示完成操作"
msgstr ""

#: app/templates/email/notice/p2p/p2p_margin_payment.j2:4
#: app/templates/email/notice/p2p/p2p_margin_shortfall.j2:4
msgid "为保障P2P交易安全、维护商家服务信誉，CoinEx已于2025-03-07（UTC）起实施商家保证金制度。"
msgstr ""

#: app/templates/email/notice/p2p/p2p_margin_payment.j2:5
#, python-format
msgid "我们发现您当前的保证金余额低于P2P商家保证金最低要求，请在 %(grace_deadline)s（UTC）前补充保证金，以免P2P交易受到限制。"
msgstr ""

#: app/templates/email/notice/p2p/p2p_margin_shortfall.j2:5
#, python-format
msgid "由于您未在 %(grace_deadline)s（UTC）前补充保证金，目前已被限制发布P2P广告单。"
msgstr ""

#: app/templates/email/notice/p2p/p2p_margin_shortfall.j2:6
msgid "补充保证金后即可恢复广告发布权益，请尽快补充。"
msgstr ""

#: app/templates/email/notice/p2p/p2p_mer_cancel.j2:4
msgid "P2P商家身份已取消，商家保证金将于48小时内自动退回现货账户，如需查看历史交易记录，请登录商家后台。"
msgstr ""

#: app/templates/email/notice/p2p/p2p_mer_cancel.j2:6
msgid "感谢您对CoinEx P2P的支持和信任，期待未来再次合作。"
msgstr ""

#: app/templates/email/notice/p2p/p2p_mer_compensation.j2:4
#, python-format
msgid ""
"尊敬的商家，平台已根据<a href='%(rule_url)s'>《平台商家行为规范》</a>处理P2P交易赔付，赔付金额 %(amount)s"
" USDT 已从您的保证金内扣除并转至用户现货账户。请及时登录<a "
"href='%(mer_url)s'>商家后台</a>查看保证金余额，确保后续交易正常进行。"
msgstr ""

#: app/templates/email/notice/p2p/p2p_mer_compensation.j2:6
#: app/templates/email/notice/p2p/p2p_mer_excess_refund.j2:6
#: app/templates/email/notice/p2p/p2p_mer_penalty.j2:6
#: app/templates/email/notice/p2p/p2p_user_compensation.j2:6
#, python-format
msgid "如有疑问请<a href='%(support_url)s/hc/requests/new'>提交工单</a>联系客服，感谢您的理解与支持！"
msgstr ""

#: app/templates/email/notice/p2p/p2p_mer_excess_refund.j2:4
#, python-format
msgid ""
"尊敬的商家，您超额缴纳的P2P保证金已完成返还，金额 %(amount)s USDT 已划转至您的现货账户。请及时登录<a "
"href='%(mer_url)s'>商家后台</a>查看保证金余额，确保后续交易正常进行。"
msgstr ""

#: app/templates/email/notice/p2p/p2p_mer_penalty.j2:4
#, python-format
msgid ""
"尊敬的商家，经核实您近期存在违反<a href='%(rule_url)s'>《平台商家行为规范》</a>的行为，现依据规则扣除保证金 "
"%(amount)s USDT。款项已从您的商家保证金余额内划扣。请及时登录<a "
"href='%(mer_url)s'>商家后台</a>查看保证金余额，确保后续交易正常进行。"
msgstr ""

#: app/templates/email/notice/p2p/p2p_user_compensation.j2:4
#, python-format
msgid "尊敬的客户，您的P2P交易赔付已完成，赔付金额 %(amount)s USDT，已划转至您的现货账户，资金可随时用于交易或提现。"
msgstr ""

#: app/templates/email/notice/p2p/paid_wait_release_asset.j2:4
#, python-format
msgid "您的P2P卖币订单商家已点击完成付款，请尽快前往<a href='%(site_url)s'>收款账号<a>核实款项。"
msgstr ""

#: app/templates/email/notice/p2p/paid_wait_release_asset.j2:5
msgid "收款时，请确保资金来自商家实名认证的支付渠道，金额正确，再点击“确认放币”按钮。"
msgstr ""

#: app/templates/email/notice/p2p/paid_wait_release_asset.j2:6
msgid "如需帮助，请联系客服。"
msgstr ""

#: app/templates/email/notice/p2p/paid_wait_release_asset.j2:8
#: app/templates/email/notice/p2p/received_order_wait_payment.j2:6
#, python-format
msgid "您将收到：%(quote_amount)s %(quote)s。"
msgstr ""

#: app/templates/email/notice/p2p/received_order_to_payment.j2:4
#, python-format
msgid "您的P2P买币订单商家已确认接单，请<a href='%(site_url)s'>查看订单</a>并尽快完成付款。"
msgstr ""

#: app/templates/email/notice/p2p/received_order_wait_payment.j2:4
#, python-format
msgid "您发起的卖币订单商家已确认，请<a href='%(site_url)s'>前往查看订单</a>并等待商家付款。"
msgstr ""

#: app/templates/email/notice/p2p/received_order_wait_payment.j2:7
#, python-format
msgid "收到后需放币：%(from_amount)s %(base)s。"
msgstr ""

#: app/templates/email/notice/p2p/remind_receive_order.j2:4
#, python-format
msgid "您发布的广告已有用户下单，<a href='%(site_url)s'>请前往确认订单</a>。"
msgstr ""

#: app/templates/email/notice/p2p/remind_receive_order.j2:6
#, python-format
msgid "下单方向：%(side)s。"
msgstr ""

#: app/templates/email/notice/p2p/remind_receive_order.j2:7
#, python-format
msgid "数字货币：%(base_amount)s %(base)s；法币：%(quote_amount)s %(quote)s。"
msgstr ""

#: app/templates/email/notice/p2p/restart_complaint.j2:4
msgid "您的P2P订单申诉被重新开启。"
msgstr ""

#: app/templates/email/notice/p2p/updated_complaint.j2:4
#, python-format
msgid "您的P2P订单有新的通知<a href='%(site_url)s'>立即查看</a>。"
msgstr ""

#: app/templates/email/notice/perpetual_subsidy/active_deadline_notice.j2:7
#, python-format
msgid "你的%(value)s %(value_type)s合约补贴金即将到期作废，请于%(expired_at)s前，前往卡券中心领取。"
msgstr ""

#: app/templates/email/notice/perpetual_subsidy/delivery_coupon_notice.j2:3
#: app/templates/email/notice/perpetual_subsidy/send_coupon_notice.j2:3
#, python-format
msgid "送你%(value)s %(value_type)s合约补贴金！"
msgstr ""

#: app/templates/email/notice/perpetual_subsidy/delivery_coupon_notice.j2:7
#: app/templates/email/notice/perpetual_subsidy/send_coupon_notice.j2:7
#, python-format
msgid "恭喜你获得 %(value)s %(value_type)s 合约补贴金，立即前往卡券中心查看！"
msgstr ""

#: app/templates/email/notice/perpetual_subsidy/delivery_coupon_notice.j2:10
msgid "点此查看"
msgstr ""

#: app/templates/email/notice/perpetual_subsidy/used_deadline_notice.j2:7
#, python-format
msgid "你的%(value)s %(value_type)s合约补贴金暂未使用完毕。请于%(expired_at)s前，前往卡券中心使用。"
msgstr ""

#: app/templates/email/notice/trading_gift/active_deadline_notice.j2:3
#: app/templates/email/notice/trading_gift/used_deadline_notice.j2:3
#, python-format
msgid "%(value)s %(value_type)s 交易赠金券，即将过期"
msgstr ""

#: app/templates/email/notice/trading_gift/active_deadline_notice.j2:7
#, python-format
msgid "你的%(value)s %(value_type)s 交易赠金券即将到期作废，请于%(expired_at)s前，前往卡券中心领取。"
msgstr ""

#: app/templates/email/notice/trading_gift/early_maturity_notice.j2:3
msgid "恭喜你，交易赠金券已激活"
msgstr ""

#: app/templates/email/notice/trading_gift/early_maturity_notice.j2:7
#, python-format
msgid "恭喜你成功激活一张%(value)s %(value_type)s 交易赠金券，现金已发放至你的现货账户。"
msgstr ""

#: app/templates/email/notice/trading_gift/random_active_deadline_notice.j2:7
#, python-format
msgid "你有一个交易赠金券盲盒即将到期，请于%(expired_at)s前，前往卡券中心领取。"
msgstr ""

#: app/templates/email/notice/trading_gift/random_send_coupon_notice.j2:7
msgid "恭喜您获得抽盲盒机会，选中幸运盲盒，即可获得超值交易赠金券，立即前往卡券中心查看！"
msgstr ""

#: app/templates/email/notice/trading_gift/random_send_coupon_notice.j2:8
#: app/templates/email/notice/trading_gift/send_coupon_notice.j2:9
#: app/templates/email/notice/vip_upgrade/send_coupon_notice.j2:9
#, python-format
msgid "领取截止日期：%(expired_at)s"
msgstr ""

#: app/templates/email/notice/trading_gift/send_coupon_notice.j2:3
#, python-format
msgid "CoinEx送你%(value)s %(value_type)s 现金福利了！"
msgstr ""

#: app/templates/email/notice/trading_gift/send_coupon_notice.j2:8
#, python-format
msgid "恭喜你获得%(value)s %(value_type)s交易赠金券，立即前往卡券中心查看！"
msgstr ""

#: app/templates/email/notice/trading_gift/used_deadline_notice.j2:7
#, python-format
msgid "你的%(value)s %(value_type)s 交易赠金券暂未激活。"
msgstr ""

#: app/templates/email/notice/trading_gift/used_deadline_notice.j2:8
#, python-format
msgid "在%(expired_at)s前，完成%(trade_type)s交易额要求，即可激活解锁%(value)s %(value_type)s。"
msgstr ""

#: app/templates/email/notice/vip_upgrade/active_deadline_notice.j2:7
#, python-format
msgid "你的VIP +%(value)s升级券即将到期作废，请于%(expired_at)s(领券有效期)前，立即前往卡券中心领取。"
msgstr ""

#: app/templates/email/notice/vip_upgrade/send_coupon_notice.j2:3
msgid "CoinEx给你送VIP升级券了！"
msgstr ""

#: app/templates/email/notice/vip_upgrade/send_coupon_notice.j2:7
#, python-format
msgid "恭喜你获得 VIP+%(value)s 升级券，立即前往卡券中心领取。"
msgstr ""

#: app/templates/email/notice/vip_upgrade/send_coupon_notice.j2:8
#, python-format
msgid "卡券有效期内，你的VIP等级将被提升%(value)s级，最高可提升到VIP5。"
msgstr ""

#: app/templates/email/notice/vip_upgrade/used_deadline_notice.j2:7
#, python-format
msgid "你的VIP+%(value)s 升级券即将到期。"
msgstr ""

#: app/templates/email/notice/vip_upgrade/used_deadline_notice.j2:9
msgid "请注意：由于VIP更新可能延迟，已过期的卡券可能会继续生效（不超过24小时）。具体VIP等级以实际展示为准。"
msgstr ""

#: app/templates/email/verification_code/add_anti_phishing_code.j2:2
#: app/templates/email/verification_code/add_api_withdraw_address.j2:2
#: app/templates/email/verification_code/add_email.j2:2
#: app/templates/email/verification_code/add_mobile.j2:2
#: app/templates/email/verification_code/add_totp.j2:2
#: app/templates/email/verification_code/add_trade_password.j2:2
#: app/templates/email/verification_code/bind_third_party_account.j2:2
#: app/templates/email/verification_code/edit_anti_phishing_code.j2:2
#: app/templates/email/verification_code/edit_mobile.j2:2
#: app/templates/email/verification_code/edit_new_email.j2:2
#: app/templates/email/verification_code/edit_old_email.j2:2
#: app/templates/email/verification_code/edit_totp.j2:2
#: app/templates/email/verification_code/edit_trade_password.j2:2
#: app/templates/email/verification_code/non_login_bind_third_party_account.j2:2
#: app/templates/email/verification_code/non_login_edit_new_email.j2:2
#: app/templates/email/verification_code/non_login_reset_login_password.j2:2
#: app/templates/email/verification_code/remove_trade_password.j2:2
#: app/templates/email/verification_code/reset_2fa.j2:2
#: app/templates/email/verification_code/reset_login_password.j2:2
#: app/templates/email/verification_code/send_c_box.j2:2
#: app/templates/email/verification_code/set_login_password.j2:2
#: app/templates/email/verification_code/sign_off.j2:2
#: app/templates/email/verification_code/unfreeze_account.j2:2
msgid "邮箱验证"
msgstr ""

#: app/templates/email/verification_code/add_anti_phishing_code.j2:4
#, python-format
msgid ""
"您正在设置防钓鱼码，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/add_api_withdraw_address.j2:4
#, python-format
msgid ""
"您正在新建白名单地址，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/add_email.j2:4
#, python-format
msgid ""
"您正在绑定邮箱，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/add_mobile.j2:4
#, python-format
msgid ""
"您正在绑定手机，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/add_totp.j2:4
#, python-format
msgid ""
"您正在绑定谷歌验证器，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/add_trade_password.j2:4
#, python-format
msgid ""
"您正在设置交易密码，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/add_withdraw_password.j2:2
#: app/templates/email/verification_code/edit_withdraw_password.j2:2
#: app/templates/email/verification_code/reset_withdraw_password.j2:2
msgid "提现密码验证"
msgstr ""

#: app/templates/email/verification_code/add_withdraw_password.j2:4
#, python-format
msgid ""
"您正在设置提现密码，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/bind_admin_webauthn.j2:2
msgid "绑定Admin安全密钥"
msgstr ""

#: app/templates/email/verification_code/bind_admin_webauthn.j2:4
#, python-format
msgid ""
"您正在绑定Admin安全密钥，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/bind_third_party_account.j2:4
#: app/templates/email/verification_code/non_login_bind_third_party_account.j2:4
#, python-format
msgid ""
"您正在绑定第三方账号，您的安全验证码为：<b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/bind_webauthn.j2:2
msgid "创建通行密钥"
msgstr ""

#: app/templates/email/verification_code/bind_webauthn.j2:4
#, python-format
msgid ""
"您正在创建通行密钥，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/edit_anti_phishing_code.j2:4
#, python-format
msgid ""
"您正在修改防钓鱼码，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/edit_mobile.j2:4
#, python-format
msgid ""
"您正在修改绑定手机，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/edit_new_email.j2:4
#: app/templates/email/verification_code/edit_old_email.j2:4
#: app/templates/email/verification_code/non_login_edit_new_email.j2:4
#, python-format
msgid ""
"您正在修改绑定邮箱，您的安全验证码为：<b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/edit_totp.j2:4
#, python-format
msgid ""
"您正在修改谷歌验证器，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/edit_trade_password.j2:4
#, python-format
msgid ""
"您正在修改交易密码，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/edit_withdraw_password.j2:4
#, python-format
msgid ""
"您正在修改提现密码，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/non_login_reset_login_password.j2:4
#: app/templates/email/verification_code/reset_login_password.j2:4
#, python-format
msgid ""
"您正在重置登录密码，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/remove_trade_password.j2:4
#, python-format
msgid ""
"您正在关闭交易密码，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/reset_2fa.j2:4
#, python-format
msgid ""
"您正在重置安全项，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/reset_webauthn.j2:2
msgid "重置通行密钥"
msgstr ""

#: app/templates/email/verification_code/reset_webauthn.j2:4
#, python-format
msgid ""
"您正在重置通行密钥，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/reset_withdraw_password.j2:4
#, python-format
msgid ""
"您正在重置提现密码，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/send_c_box.j2:4
#, python-format
msgid ""
"您正在发送C-Box，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/set_login_password.j2:4
#, python-format
msgid ""
"您正在设置登录密码，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/sign_in.j2:2
msgid "授权新设备"
msgstr ""

#: app/templates/email/verification_code/sign_in.j2:4
#, python-format
msgid ""
"您正在尝试从一个新设备登录，我们需要您授权新设备才能进入您的CoinEx账户。您的安全验证码为： <b style=\"font-"
"size:18px; font-weight:bold; color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/sign_off.j2:4
#, python-format
msgid ""
"您正在注销账户，您的安全验证码为：<b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/sign_up.j2:2
msgid "欢迎注册"
msgstr ""

#: app/templates/email/verification_code/sign_up.j2:4
#, python-format
msgid ""
"欢迎来到CoinEx，您的注册安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/sign_up.j2:5
msgid "出于安全原因，该验证码将于30分钟后失效。"
msgstr ""

#: app/templates/email/verification_code/unbind_mobile.j2:2
msgid "解绑手机"
msgstr ""

#: app/templates/email/verification_code/unbind_mobile.j2:4
#, python-format
msgid ""
"您正在解绑手机，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/unbind_totp.j2:2
msgid "解绑谷歌验证器"
msgstr ""

#: app/templates/email/verification_code/unbind_totp.j2:4
#, python-format
msgid ""
"您正在解绑谷歌验证器，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/unbind_webauthn.j2:2
msgid "删除通行密钥"
msgstr ""

#: app/templates/email/verification_code/unbind_webauthn.j2:4
#, python-format
msgid ""
"您正在删除通行密钥，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/templates/email/verification_code/unfreeze_account.j2:4
#, python-format
msgid ""
"您正在申请自助解冻账号，您的安全验证码为： <b style=\"font-size:18px; font-weight:bold; "
"color:#1BBEB2;\">%(validate_code)s</b>"
msgstr ""

#: app/utils/importer.py:21
msgid "无法识别的文件格式"
msgstr ""

#: app/utils/importer.py:46
msgid "文件格式错误,请上传csv, xls, xlsx格式文件"
msgstr ""

#: app/utils/importer.py:48
msgid "字段数量不匹配"
msgstr ""

