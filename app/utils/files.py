# -*- coding: utf-8 -*-
import io
import os
import traceback
from enum import Enum
from logging import getLogger
from secrets import token_hex
from typing import I<PERSON>, Generator, Iterator
from urllib.parse import urlparse

from boto3 import client as boto_client, resource as boto_resource
from botocore.client import Config
from azure.storage.blob import BlobServiceClient
from tqdm import tqdm

from . import today
from .file import handle_file_thumbnail
from ..config import config


_logger = getLogger(__name__)


class ACLType(Enum):
    PRIVATE = "private"
    PUBLIC_READ = "public-read"


class _AWSBucketBase:
    DEFAULT_TTL = 3600
    ACL = ACLType.PRIVATE  # 默认acl权限为private

    def __init__(self,
                 bucket_name: str,
                 region_name: str,
                 access_key_id: str,
                 secret_access_key: str,
                 path: str = ""):
        self._bucket_name = bucket_name
        self._resource = boto_resource(
            's3',
            region_name=region_name,
            aws_access_key_id=access_key_id,
            aws_secret_access_key=secret_access_key,
            config=Config(max_pool_connections=100)
        )
        self._client = boto_client(
            's3',
            region_name=region_name,
            aws_access_key_id=access_key_id,
            aws_secret_access_key=secret_access_key,
            endpoint_url=f"https://s3.{region_name}.amazonaws.com",
            config=Config(signature_version='s3v4', s3={'addressing_style': 'virtual'}, max_pool_connections=100)
        )
        self.path = path.strip('/')

    @property
    def bucket_name(self):
        return self._bucket_name

    def _new_object(self, key: str):
        return self._resource.Object(self._bucket_name, key)

    def new_file_key(self, key: str = "", length: int = 32, suffix: str = '') -> str:
        if not key:
            key = f'{today().strftime("%Y-%m-%d")}/{token_hex(length // 2).upper()}'
            if suffix:
                key = f'{key}.{suffix}'

        if self.path:
            return f'{self.path}/{key}'
        else:
            return key

    def get_file_url(self, key: str, ttl=None) -> str:
        return self._client.generate_presigned_url(
            'get_object',
            Params=dict(Bucket=self._bucket_name, Key=key),
            ExpiresIn=ttl or self.DEFAULT_TTL,
            HttpMethod='GET'
        )

    def put_file(self, key: str, file: IO, **kwargs) -> bool:
        if self.path:
            if not key.startswith(self.path):
                # key = self.new_file_key(key)
                raise KeyError('invalid file key')

        response = self._new_object(key).put(ACL=self.ACL.value, Body=file, **kwargs)
        return response['ResponseMetadata']['HTTPStatusCode'] == 200

    def put_file_thumbnail(self, key: str, file: IO | str, suffix: str, size, **kwargs) -> bool:
        if not isinstance(file, str):  # str的情况下传入的是url
            file = file.read()
        for thumbnail_key, thumbnail_file in handle_file_thumbnail(key, file, suffix, size).items():
            if not self.put_file(self.new_file_key(thumbnail_key), thumbnail_file, **kwargs):
                return False
        return True

    def put_file_with_thumbnail(self, key: str, file: IO, suffix: str, size, **kwargs) -> bool:
        file_bytes = file.read()

        if not self.put_file(key, io.BytesIO(file_bytes)):
            return False
        return self.put_file_thumbnail(key, io.BytesIO(file_bytes), suffix, size, **kwargs)

    def put_file_with_pb(self, key: str, file_path: str) -> bool:
        total_size = os.path.getsize(file_path)
        extra_args = {'ACL': self.ACL.value}
        with tqdm(total=total_size, unit='B', unit_scale=True) as progress_bar:
            def progress_callback(bytes_transferred):
                progress_bar.update(bytes_transferred)

            self._client.upload_file(file_path, self._bucket_name, key,
                                     ExtraArgs=extra_args,
                                     Callback=progress_callback)
        return True

    def put_large_file(self, key: str, file_path: str):
        """S3限制单次上传大小为5GB，该方法实现分片上传大文件"""
        multi = self._client.create_multipart_upload(Bucket=self._bucket_name, Key=key, ACL=self.ACL.value)
        parts = []
        with open(file_path, 'rb') as f:
            part_number = 1
            while True:
                data = f.read(1024 * 1024 * 1024)
                if not data:
                    break
                part = self._client.upload_part(Bucket=self._bucket_name, Key=key, UploadId=multi['UploadId'],
                                                PartNumber=part_number, Body=data)
                parts.append({'ETag': part['ETag'], 'PartNumber': part_number})
                part_number += 1

        self._client.complete_multipart_upload(Bucket=self._bucket_name, Key=key, UploadId=multi['UploadId'],
                                               MultipartUpload={'Parts': parts})

    def iter_objects(self) -> Generator[str, None, None]:
        marker = ''
        while True:
            res = self._client.list_objects(Bucket=self._bucket_name, Marker=marker)
            for item in res['Contents']:
                yield item['Key']
            if not res['IsTruncated']:
                break
            marker = res['Contents'][-1]['Key']

    def check_exists(self, key: str) -> bool:
        try:
            self._client.head_object(Bucket=self._bucket_name, Key=key)
            return True
        except Exception:
            return False


class _AWSBucketPublic(_AWSBucketBase):
    ACL = ACLType.PUBLIC_READ

    def get_file_url(self, key: str, ttl=None) -> str:
        r = urlparse(super().get_file_url(key, ttl))
        url = f"{config['UPLOAD_STATIC_URL']}{r.path}"
        return url

    def delete_file(self, key: str) -> bool:
        """
        目前只有清理用户的脚本中会用到，不要扩散使用
        """
        response = self._new_object(key).delete()
        return response['ResponseMetadata']['HTTPStatusCode'] // 100 == 2

    def get_thumbnail_url(self, key: str):
        url = f"{config['UPLOAD_CDN_URL']}/public?key={key}"
        return url


class _AWSBucketPrivate(_AWSBucketBase):
    def get_file_url(self, key: str, ttl=None) -> str:
        if not key.startswith(self.path):
            # fixme 为了不对根目录做参数转发，迁移前的文件仍使用aws域名返回，迁移完成后需去掉
            # 理论上第三阶段已完成全部业务历史数据迁移，再进入这里属于遗漏点，需记录调用栈排查数据来源，需在第四阶段移除 fixme
            stack_info = traceback.format_stack()
            stack_info_str = ''.join(stack_info)
            _logger.error(f"err file key without migrate : {key} \n {stack_info_str}")

            return super().get_file_url(key, ttl)

        r = urlparse(super().get_file_url(key, ttl))
        url = f"{config['UPLOAD_STATIC_URL']}{r.path}?{r.query}"
        return url

    def get_thumbnail_url(self, key: str):
        url = f"{config['UPLOAD_CDN_URL']}?key={key}"
        return url


class _AWSBackupBucket(_AWSBucketBase):

    def delete_file(self, key: str) -> bool:
        """
        只有backup桶有删除权限
        """
        response = self._new_object(key).delete()
        return response['ResponseMetadata']['HTTPStatusCode'] // 100 == 2


class _AzureBlob:
    
    def __init__(self, account_name: str, container_name: str, sas_token: str):
        account_url = f'https://{account_name}.blob.core.windows.net'
        self._blob_service_client = BlobServiceClient(account_url, credential=sas_token)
        self._container_client = self._blob_service_client.get_container_client(container_name)

    def put_file(self, key: str, file: IO):
        self._container_client.upload_blob(name=key, data=file, overwrite=True)

    def iter_objects(self) -> Iterator[str]:
        return self._container_client.list_blob_names()

    def delete_file(self, key: str):
        self._container_client.delete_blob(key)


_aws_config = config['AWS_FILE']

AWSBucketPublic = _AWSBucketPublic(
    _aws_config['bucket_name'],
    _aws_config['region_name'],
    _aws_config['access_key_id'],
    _aws_config['secret_access_key']
)

AWSBucketPrivate = _AWSBucketPrivate(
    _aws_config['bucket_name'],
    _aws_config['region_name'],
    _aws_config['access_key_id'],
    _aws_config['secret_access_key'],
    _aws_config['private_path'],
)

AWSBucketTmp = _AWSBucketPrivate(
    _aws_config['bucket_name'],
    _aws_config['region_name'],
    _aws_config['access_key_id'],
    _aws_config['secret_access_key'],
    _aws_config['tmp_path'],
)

# this bucket is used for backup, cannot put public files.
AWSBackupBucket = _AWSBackupBucket(
    _aws_config['backup_bucket'],
    _aws_config['region_name'],
    _aws_config['access_key_id'],
    _aws_config['secret_access_key'],
)

_azure_config = config['AZURE_FILE']
# this bucket is used for backup, cannot put public files.
AzureBackupBlob = _AzureBlob(
    _azure_config['account_name'],
    _azure_config['container_name'],
    _azure_config['sas_token']
)
